<svg viewBox="0 0 116 116" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1_1392)">
<path d="M36.6114 29.4385C36.6114 29.4385 49.8028 39.4487 69.857 20.7313C87.6672 4.10849 102.209 30.3355 102.311 42.8951C102.443 59.1651 84.501 72.1831 93.2082 82.8692C101.915 93.5554 75.9405 111.202 61.9413 95.93C44.5269 76.9324 39.8091 92.368 29.8829 92.368C22.7589 92.368 8.1323 74.6664 18.0095 61.4969C26.3209 50.415 21.788 46.7371 19.5929 42.8951C16.4265 37.3542 23.9463 22.3144 36.6114 29.4385Z" fill="currentColor"/>
</g>
<defs>
<filter id="filter0_d_1_1392" x="-4" y="0" width="124" height="124" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_1392"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_1392" result="shape"/>
</filter>
</defs>
</svg>
