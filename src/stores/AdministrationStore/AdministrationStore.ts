import { RootStore } from "stores/RootStore";
import { observable, action, makeAutoObservable, runInAction } from "mobx";
import { administration } from "api/administration";
import { IGetGroupInfoByRoleResponseGroup } from "../../api/administration/administration-controller";
import { generateUUID } from "../../helpers/GenerationUUID";

// Удалены выводы в консоль в связи с обращением СИБов (SRPG-2840)
export class AdministrationStore {
  roles: any[];
  unlinkedGroups: any[];
  ad: any[];
  isLoading: boolean;
  userGroups: IGetGroupInfoByRoleResponseGroup[];
  rootStore: RootStore;

  constructor(rootStore: RootStore) {
    makeAutoObservable(this, {
      roles: observable,
      unlinkedGroups: observable,
      isLoading: observable,
      ad: observable,
      init: action,
    });
    this.rootStore = rootStore;
    this.roles = [];
    this.unlinkedGroups = [];
    this.ad = [];
    this.isLoading = true;
    this.userGroups = [];
  }

  prepareAd(array: any[], level: number, role: any) {
    return array.map((item: any) => {
      let childs = item?.groups?.length > 0 ? item?.groups : item?.users?.length > 0 ? item.users : [];
      if (childs.length > 0) {
        childs = this.prepareAd(childs, item?.groups?.length > 0 ? 1 : 2, item.roleName ? item.roleName : role);
      }
      const groups = item?.adGroup ? item?.adGroup : typeof item === "string" ? item : "";
      return { ...item, tabId: item.roleName || item.adGroup || item, name: item.roleName, groups, childs, level, role: item.roleName ? item.roleName : role };
    });
  }

  async init() {
    try {
      runInAction(() => {
        this.isLoading = true;
      });
      const { items } = await administration.administrationController.getRoles();
      const { items: groups } = await administration.administrationController.getGroups();
      const { items: ad } = await administration.administrationController.getAd();
      runInAction(() => {
        this.roles = items;
      });
      runInAction(() => {
        this.unlinkedGroups = groups.map((item, index) => ({ ...item, id: index, tabId: `${generateUUID()}` })).sort((a, b) => a.id - b.id);
      });
      runInAction(() => {
        this.ad = this.prepareAd(ad, 0, null);
      });
    } catch (e) {
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  }

  async saveGroups(array: any, roleCode: any) {
    try {
      const items = array.map((item: any) => item.dnGroup);
      await administration.administrationController.saveGroups(items, roleCode);
      this.rootStore.notificationStore.addNotification({
        title: "Добавление",
        description: "Группы добавлены",
        icon: "save",
        type: "information",
      });
    } catch (e) {}
  }

  async deleteSelectedObject(adRoleId: any) {
    try {
      await administration.administrationController.deleteObjects(adRoleId);
      this.rootStore.notificationStore.addNotification({
        title: "Удаление",
        description: "Группа удалена",
        icon: "trash",
        type: "error",
      });
    } catch (e) {}
  }

  async editSelectedObject(adRoleId: any) {
    try {
      await administration.administrationController.editObjects(adRoleId);
      this.rootStore.notificationStore.addNotification({
        title: "Изменения",
        description: "Группа изменена",
        icon: "edit",
        type: "done",
      });
    } catch (e) {}
  }

  getGroupInfoByRole = async (role: string) => {
    try {
      const resp = await administration.administrationController.getGroupInfoByRole(role);
      runInAction(() => {
        const { groups } = resp;
        if (groups !== null) {
          this.userGroups = groups;
        }
      });
    } catch (e) {}
  };
}
