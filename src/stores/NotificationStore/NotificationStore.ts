import { observable, action, computed, makeAutoObservable, runInAction } from "mobx";
// @ts-ignore
import { v4 as uuid } from "uuid";
import { RootStore } from "../RootStore";
export class NotificationStore {
  toastList: any[];
  isOpen: boolean;
  rootStore: RootStore;

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore;
    this.toastList = [];
    this.isOpen = true;
    makeAutoObservable(this);
  }

  deleteNotification(id: string) {
    runInAction(() => {
      this.toastList = this.toastList.filter((toast) => {
        return toast.id !== id;
      });
    });
  }

  deleteAllNotification() {
    this.toastList = [];
  }

  toggleNotification() {
    this.isOpen = !this.isOpen;
  }

  addNotification(item: any) {
    const isWordWrap = item?.isWordWrap ?? false
    const isFind =
      this.toastList.some((el) => el?.title?.toUpperCase() === item?.title?.toUpperCase() && el?.description?.toUpperCase() === item?.description?.toUpperCase()) ??
      false;
    if (!isFind) {
      const id = uuid();
      const selectedItem = { ...item, isTimer: item?.isTimer ?? true };
      this.isOpen = !this.isOpen;
      runInAction(() => {
        this.toastList = [
          ...this.toastList,
          {
            ...selectedItem,
            id,
            timer: 10,
            isPaused: false,
            isTimer: selectedItem.isTimer,
            isWordWrap
          },
        ];
        if (selectedItem.isTimer) {
          this.startTimer(id);
        }
      });
    } else {
      runInAction(() => {
        const find = this.toastList.find(
          (el) => el?.title?.toUpperCase() === item?.title?.toUpperCase() && el?.description?.toUpperCase() === item?.description?.toUpperCase()
        );
        this.toastList = this.toastList.map((el) => {
          if (find.id === el.id) {
            return { ...el, timer: 10 };
          }
          return el;
        });
        this.startTimer(find.id);
      });
    }
  }

  playTimer(id: any) {
    this.toastList = this.toastList.map((item) => {
      if (item.id === id) return { ...item, isPaused: false };
      return item;
    });
    const isTimer = this.toastList.find((el) => el.id === id)?.isTimer ?? true;
    if (isTimer) {
      this.startTimer(id);
    }
  }

  stopTimer(id: any) {
    this.toastList = this.toastList.map((item) => {
      if (item.id === id) return { ...item, isPaused: true };
      return item;
    });
  }

  startTimer(id: any) {
    this.toastList.map((toast) => {
      if (toast.id === id) {
        const num = toast.timer;
        const timerId = setInterval(() => {
          if (!toast.isPaused) {
            runInAction(() => {
              toast.timer = toast.timer - 1;
            });
          }
        }, 1000);

        setTimeout(function () {
          clearInterval(timerId);
        }, (num + 1) * 1000);
      }
    });
  }

  addErrorNotification(options: any) {
    const { title = "Ошибка", description = "Что-то пошло не так", icon = "error", type = "error" } = options;

    this.addNotification({
      title,
      description,
      icon,
      type,
    });
  }
}
