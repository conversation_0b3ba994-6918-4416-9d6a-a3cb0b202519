import { RootStore } from "stores/RootStore";
import { observable, action, makeAutoObservable, runInAction } from "mobx";
import { nsi } from "api/nsi";
import { delay } from "helpers/delay";
import { DEFAULT_DAY, DEFAULT_MONTH, DEFAULT_YEAR, prepareDate, prepareDateTable } from "helpers/DateUtils";
import { isModeCenter } from "../../utils/getMode";
import { saveAs } from "file-saver";
import { generateUUID } from "../../helpers/GenerationUUID";
import { prepareDataConnection } from "../../api/nsi/nci-controller";

interface InitProps {
  day: string;
  month: string;
  year: string;
  selectItem: string;
  selectedSegment: string;
  isInit: boolean;
}

interface SrpgProps {
  archmGroups: any[];
  area2s: any[];
  areas: any[];
  consumers: any[];
  nBlocks: any[];
  nGroups: any[];
  powerSystems: any[];
  rges: any[];
  sechens: any[];
  vetvs: any[];
  wSums: any[];
}

interface statusDistributionDCProps {
  status: string;
  info: string;
  startedAt: string;
  finishedAt: string;
}

interface InitPrev {
  date: {
    year: number;
    month: number;
    day: number;
  };
  registry: string;
  subRegistry: string;
}

// Удалены выводы в консоль в связи с обращением СИБов (SRPG-2840)
export class NsiStore {
  characteristics: any[];
  characteristicsFlat: any[];
  department: any[];
  departmentOriginal: any[];
  registries: any[];
  objectPakEss: any[];
  typeObjects: any[];
  infoModalSync: any[];
  rootStore: RootStore;
  isLoadingLeftTable: boolean;
  isLoadingRightTable: boolean;
  isLoadingViewError: boolean;
  hasMoreElements: boolean;
  isLoadingProtocolChanges: boolean;
  isChangeCharacteristics: boolean;
  isLoadingPublication: boolean;
  isLoadingDistribution: boolean;
  isLoadingDateComparison: boolean;
  isLoadingUnrelated: boolean;
  isLoadingModalInputDC: boolean;
  isStatusDistribution: boolean;
  isLoadingGeneric: boolean;
  isLoadingSK11: boolean;
  ProcessingEss: boolean;
  isProcessing: boolean;
  isLoadingDuplicate: boolean;
  IsModalComparison: boolean;
  isLoadingGenericTable: boolean;
  isLoadingMultipleConnections: boolean;
  isLoadingCharacters: boolean;
  isLoadingCheckSK11: boolean;
  srpgs: SrpgProps;
  srpgsOriginal: SrpgProps;
  isLoadingEss: boolean;
  isLoadingModalSync: boolean;
  isLoadingSubs: boolean;
  isProcessingCharacteristicsDistribution: boolean;
  isProcessingDistributionProtocol: boolean;
  isLoadingCharacteristics: boolean;
  essStatus: object;
  statusDistributionDC: statusDistributionDCProps;
  errorByTask: { duplication: any[]; errorGou: any[]; errorKartaVed: any[] };
  dateComparison: { addEntries: any[]; deleteEntries: any[]; changeEntries: any[] };
  protocolChanges: { addEntries: any[]; deleteEntries: any[]; changeEntries: any[] };
  protocolDistributionInfo: { status: string; startedAt: string; finishedAt: string; error: string; notSentTo: any[] };
  distribution: any[];
  dcListAtTask: any[];
  loadedDayNsi: string[];
  sk11: any[];
  objectModalSettingsCharacteristicsOriginal: any;
  originalGenerics: any[];
  multipleConnections: any[];
  listParams: any[];
  duplicateListById: any[];
  characteristicsOriginal: any[];
  subs: any[];
  type: any;
  timerIdProtocolSettings: any;
  rge: any;
  lastTaskIdNsi: any;
  dataSK11: any;
  isLoadingFileEss: boolean;
  statusMeasurements: any;
  lastTaskMeasurements: any;
  generics: any;
  infoStatusGeneric: any;
  mainStatusLoading: any;
  timerIdPublication: any;
  distributedInfo: any;
  statusSK11: any;
  statusGeneric: any;
  statusGenericSK11: any;
  typesForModal: any[];
  downloadInfo: any;
  timerCheckSK11: any;
  timerCheckGeneric: any;
  multipleConnectionsTimerId: any;
  timerId: any;
  nsiDistributionTimer: any;
  mapData: any;
  infoMultipleConnections: any;
  originalSK11: any;
  isLoadingRightTableComparisonSK: boolean;
  isCheckConnectionTry: boolean;
  unrelated: any[];
  infoDuplicates: { duplicates: any[]; duplicatesFromRequest: any[] };
  isProcessingMeasurements: boolean;
  objectModalSettingsCharacteristics: any; //{ name: string; code: string; id: any; params: any; subParams: any }
  protocolCharacteristics: { status: any; startedAt: string; finishedAt: string; error: any; notSentTo: any[] };
  inputFilter: Record<string, string> = {}; // Хранит значения инпутов (TextFieldStyled) таблиц в формате [`${tableKey}-${columnName}`]: value
  initPrev: InitPrev | null = JSON.parse(localStorage.getItem("prevParams") ?? "null");
  dateObject: InitPrev["date"] = this.initPrev?.date ?? {
    year: DEFAULT_YEAR,
    month: DEFAULT_MONTH,
    day: DEFAULT_DAY,
  };
  isExportingCSV: boolean = false;

  constructor(rootStore: RootStore) {
    makeAutoObservable(this, {
      department: observable,
      isCheckConnectionTry: observable,
      originalSK11: observable,
      departmentOriginal: observable,
      registries: observable,
      typeObjects: observable,
      isLoadingLeftTable: observable,
      isLoadingGeneric: observable,
      isLoadingRightTable: observable,
      distributedInfo: observable,
      downloadInfo: observable,
      isLoadingViewError: observable,
      isLoadingProtocolChanges: observable,
      isLoadingPublication: observable,
      isLoadingModalInputDC: observable,
      isLoadingGenericTable: observable,
      isLoadingMultipleConnections: observable,
      isLoadingUnrelated: observable,
      isChangeCharacteristics: observable,
      isLoadingDuplicate: observable,
      isStatusDistribution: observable,
      typesForModal: observable,
      loadedDayNsi: observable,
      isProcessingDistributionProtocol: observable,
      infoDuplicates: observable,
      protocolDistributionInfo: observable,
      multipleConnectionsTimerId: observable,
      mainStatusLoading: observable,
      isLoadingSubs: observable,
      listParams: observable,
      sk11: observable,
      dcListAtTask: observable,
      srpgs: observable,
      srpgsOriginal: observable,
      isLoadingEss: observable,
      dataSK11: observable,
      statusDistributionDC: observable,
      hasMoreElements: observable,
      subs: observable,
      lastTaskIdNsi: observable,
      originalGenerics: observable,
      generics: observable,
      type: observable,
      IsModalComparison: observable,
      characteristicsFlat: observable,
      isProcessingMeasurements: observable,
      essStatus: observable,
      errorByTask: observable,
      dateComparison: observable,
      protocolChanges: observable,
      multipleConnections: observable,
      objectModalSettingsCharacteristicsOriginal: observable,
      ProcessingEss: observable,
      objectPakEss: observable,
      isLoadingSK11: observable,
      isLoadingCheckSK11: observable,
      duplicateListById: observable,
      protocolCharacteristics: observable,
      timerIdProtocolSettings: observable,
      isProcessingCharacteristicsDistribution: observable,
      timerIdPublication: observable,
      initLeft: action,
      changeCharacteristics: action,
      resetCharacteristics: action,
      stopModalEss: action,
      checkStatusSK11: action,
      stopCheckStatusSK11: action,
      getLastTask: action,
      initTypesModal: action,
      initRight: action,
      // initType: action,
      createTask: action,
      publication: action,
      startSK11: action,
      saveSub: action,
      saveSelectedSub: action,
      initModalEss: action,
      getStatusSK11: action,
      exportXMLMeasurements: action,
      getErrorsTask: action,
      initCharacteristics: action,
      initDateComparison: action,
      initProtocolChanges: action,
      resetProtocolChanges: action,
      restartDistribution: action,
      initDistribution: action,
      initModalComparison: action,
      stopCheckStatus: action,
      getTablePrepareSK11: action,
      startChecking: action,
      getTaskList: action,
      checkStatus: action,
      editCharacters: action,
      getStatusProtocolDistribution: action,
      startProtocolDistribution: action,
      stopProtocolDistribution: action,
      onClickOpenAndCloseChild: action,
      getMultipleConnections: action,
      getCalendar: action,
      stopStatusGeneric: action,
      resetLeft: action,
      resetRight: action,
      getUnrelated: action,
      getDuplicateById: action,
      initModalSettingsCharacteristics: action,
      isLoadingDistribution: observable,
      timerCheckSK11: observable,
      timerCheckGeneric: observable,
      isLoadingModalSync: observable,
      isLoadingRightTableComparisonSK: observable,
      rge: observable,
      infoModalSync: observable,
      infoStatusGeneric: observable,
      isLoadingFileEss: observable,
      objectModalSettingsCharacteristics: observable,
      isLoadingCharacteristics: observable,
      isLoadingCharacters: observable,
      statusMeasurements: observable,
      statusGenericSK11: observable,
      mapData: observable,
      infoMultipleConnections: observable,
      statusSK11: observable,
      statusGeneric: observable,
      characteristics: observable,
      unrelated: observable,
      characteristicsOriginal: observable,
      nsiDistributionTimer: observable,
      timerId: observable,
      startDistributionCharacteristics: action,
      startCheckCharacteristicsProtocol: action,
      stopCheckCharacteristicsProtocol: action,
      resetPublication: action,
    });
    this.rootStore = rootStore;
    this.department = [];
    this.departmentOriginal = [];
    this.registries = [];
    this.typeObjects = [];
    this.distribution = [];
    this.subs = [];
    this.sk11 = [];
    this.originalSK11 = [];
    this.dataSK11 = [];
    this.isLoadingLeftTable = true;
    this.isLoadingRightTable = false;
    this.isProcessingCharacteristicsDistribution = false;
    this.isLoadingEss = false;
    this.isLoadingProtocolChanges = true;
    this.isLoadingPublication = false;
    this.isLoadingDistribution = true;
    this.isCheckConnectionTry = false;
    this.isLoadingDateComparison = false;
    this.isStatusDistribution = false;
    this.isLoadingMultipleConnections = false;
    this.isLoadingModalSync = false;
    this.isProcessing = false;
    this.isLoadingModalInputDC = false;
    this.isLoadingDuplicate = false;
    this.isLoadingUnrelated = false;
    this.IsModalComparison = false;
    this.isLoadingSK11 = false;
    this.isLoadingGeneric = false;
    this.isLoadingCheckSK11 = false;
    this.isLoadingSubs = false;
    this.isLoadingCharacters = false;
    this.isChangeCharacteristics = false;
    this.isProcessingMeasurements = false;
    this.isLoadingRightTableComparisonSK = false;
    this.hasMoreElements = false;
    this.isProcessingDistributionProtocol = true;
    this.isLoadingCharacteristics = false;
    this.essStatus = {};
    this.distributedInfo = { isDistributed: false };
    this.downloadInfo = { isDownloaded: false };
    this.errorByTask = { duplication: [], errorGou: [], errorKartaVed: [] };
    this.protocolChanges = { addEntries: [], deleteEntries: [], changeEntries: [] };
    this.infoMultipleConnections = { status: null };
    this.srpgs = {
      archmGroups: [],
      area2s: [],
      areas: [],
      consumers: [],
      nBlocks: [],
      nGroups: [],
      powerSystems: [],
      rges: [],
      sechens: [],
      vetvs: [],
      wSums: [],
    };
    this.srpgsOriginal = {
      archmGroups: [],
      area2s: [],
      areas: [],
      consumers: [],
      nBlocks: [],
      nGroups: [],
      powerSystems: [],
      rges: [],
      sechens: [],
      vetvs: [],
      wSums: [],
    };
    this.dateComparison = { addEntries: [], deleteEntries: [], changeEntries: [] };
    this.isLoadingViewError = false;
    this.ProcessingEss = false;
    this.isLoadingGenericTable = false;
    this.isLoadingFileEss = false;
    this.infoModalSync = [];
    this.dcListAtTask = [];
    this.objectPakEss = [];
    this.generics = [];
    this.originalGenerics = [];
    this.loadedDayNsi = [];
    this.characteristics = [];
    this.characteristicsOriginal = [];
    this.characteristicsFlat = [];
    this.duplicateListById = [];
    this.typesForModal = [];
    this.unrelated = [];
    this.multipleConnections = [];
    this.listParams = [];
    this.mapData = [];
    this.statusDistributionDC = { status: "", info: "", startedAt: "", finishedAt: "" };
    this.protocolDistributionInfo = { status: "SENDING", error: "", startedAt: "", finishedAt: "", notSentTo: [] };
    this.type = null;
    this.lastTaskMeasurements = null;
    this.mainStatusLoading = "-";
    this.lastTaskIdNsi = null;
    this.rge = {};
    this.statusSK11 = { status: undefined, activeTaskId: undefined, activeLoadedAt: undefined, activeModelVersion: undefined };
    this.statusGeneric = { status: undefined, activeTaskId: undefined, activeLoadedAt: undefined, activeModelVersion: undefined };
    this.infoStatusGeneric = {};
    this.statusMeasurements = {};
    this.statusGenericSK11 = { status: undefined, activeTaskId: undefined, activeLoadedAt: undefined, activeModelVersion: undefined };
    this.objectModalSettingsCharacteristics = { name: "", code: "", id: null, params: { pbr: [], pdg: [], ppbr: [], per: [], subParams: [] } };
    this.objectModalSettingsCharacteristicsOriginal = { name: "", code: "", id: null, params: { pbr: [], pdg: [], ppbr: [], per: [], subParams: [] } };
    this.protocolCharacteristics = { status: null, startedAt: "", finishedAt: "", error: null, notSentTo: [] };
    this.infoDuplicates = { duplicates: [], duplicatesFromRequest: [] };
    this.timerId = () => {};
    this.nsiDistributionTimer = () => {};
    this.timerIdProtocolSettings = () => {};
    this.timerCheckSK11 = () => {};
    this.timerCheckGeneric = () => {};
    this.timerIdPublication = () => {};
  }

  async initLeft({ day, month, year }: InitProps) {
    try {
      runInAction(() => {
        this.isLoadingLeftTable = true;
      });

      const date = prepareDate(year, month, day);

      const { department } = await nsi.nsiController.getRegistryDC({ date, isCenter: this.rootStore.authStore.isCenter });
      await runInAction(async () => {
        this.department = this.prepareDepartment([department], 0);
        this.departmentOriginal = this.prepareDepartment([department], 0);
      });
    } catch (e) {
      runInAction(() => {
        this.department = [];
        this.departmentOriginal = [];
      });
    } finally {
      runInAction(() => {
        this.isLoadingLeftTable = false;
      });
    }
  }

  resetLeft() {
    this.department = [];
  }

  resetInitPrev() {
    this.initPrev = null;
  }

  resetInitDate() {
    this.dateObject = {
      year: DEFAULT_YEAR,
      month: DEFAULT_MONTH,
      day: DEFAULT_DAY,
    };
  }

  setDateObject(date: InitPrev["date"]) {
    this.dateObject = date;
  }

  get formattedDate() {
    const { year, month, day } = this.dateObject;
    return prepareDate(year, month, day);
  }

  /**
   * changeInputFilter - изменяет значение для инпута (TextFieldStyled)
   * @param tableKey - уникальный идентификатор таблицы. Нужен для избежания мутации значения инпута в других таблицах
   * @param columnName - колонка, для которой изменяется значение для инпута
   * @param value - новое значение для инпута
   */
  changeInputFilter(tableKey: string, columnName: string, value: string) {
    const key = `${tableKey}-${columnName}`;
    this.inputFilter[key] = value;
  }

  resetRight() {
    this.srpgs = {
      archmGroups: [],
      area2s: [],
      areas: [],
      consumers: [],
      nBlocks: [],
      nGroups: [],
      powerSystems: [],
      rges: [],
      sechens: [],
      vetvs: [],
      wSums: [],
    };
    this.srpgsOriginal = {
      archmGroups: [],
      area2s: [],
      areas: [],
      consumers: [],
      nBlocks: [],
      nGroups: [],
      powerSystems: [],
      rges: [],
      sechens: [],
      vetvs: [],
      wSums: [],
    };
    this.registries = [];
  }

  prepareRows = (data: any, parentId: any) => {
    let result: any[] = [];
    data.map((el: any) => {
      let childs = [];
      if (el?.childs?.length > 0) {
        childs = this.prepareRows(el?.childs, el.tabId);
      }
      result.push({ ...el, parentId });
      if (childs?.length > 0) {
        childs.map((item) => {
          result.push({ ...item });
        });
      }
    });
    return result;
  };

  async initRight({ day, month, year, selectItem }: InitProps) {
    try {
      this.isLoadingRightTable = true;
      const date = prepareDate(year, month, day);
      if (selectItem !== null) {
        const items: any = await nsi.nsiController.getSrpgs({
          date,
          selectDc: selectItem,
          isCenter: this.rootStore.authStore.isCenter,
        });
        runInAction(() => {
          const keys = Object.keys(items).filter((el) => el !== "timestamp" && el !== "rid");
          const finalObj: any = {};
          keys.forEach((el) => {
            finalObj[el] = items[el].map((item: any) => ({ tabId: `${item.id}-${item.name}`, ...item }));
          });
          this.srpgs = finalObj;
          this.srpgsOriginal = finalObj;
        });
      } else {
        const items: any = await nsi.nsiController.getAllSrpgs({
          date,
          isCenter: this.rootStore.authStore.isCenter,
        });
        runInAction(() => {
          const keys = Object.keys(items).filter((el) => el !== "timestamp" && el !== "rid");
          const finalObj: any = {};
          keys.forEach((el) => {
            finalObj[el] = items[el].map((item: any) => ({ tabId: `${item.id}-${item.name}`, ...item }));
          });
          this.srpgs = finalObj;
          this.srpgsOriginal = finalObj;
        });
      }
    } catch (e) {
      runInAction(() => {
        this.registries = [];
        this.srpgs = {
          archmGroups: [],
          area2s: [],
          areas: [],
          consumers: [],
          nBlocks: [],
          nGroups: [],
          powerSystems: [],
          rges: [],
          sechens: [],
          vetvs: [],
          wSums: [],
        };
      });
    } finally {
      runInAction(() => {
        this.isLoadingRightTable = false;
      });
    }
  }

  getTypeModalMap(type: string) {
    switch (type) {
      case "goy":
        return "gous";
      case "srpg":
        return "srpgs";
      case "summator":
        return "adders";
      default:
        return "";
    }
  }

  async getModalMapData(objectMap: any) {
    try {
      const type = this.getTypeModalMap(objectMap.segment);
      const objectFinal = { ...objectMap };
      delete objectFinal.segment;
      delete objectFinal.name;
      const { departments } = await nsi.nsiController.getModalMap(type, objectFinal, this.rootStore.authStore.isCenter);
      this.mapData = departments.map((el: any) => ({ ...el, tabId: generateUUID(), parentId: "" }));
    } catch (e) {}
  }

  prepareDepartment(array: any[], level: number): any[] {
    return array.map((item) => {
      let childs;
      if (item.childs && item.childs.length > 0) {
        childs = this.prepareDepartment(item.childs, level + 1);
      }
      return { ...item, childs, tabId: item.id, id: item.kpo, level, isDisableChecked: item.type === "POWER_PLANT" };
    });
  }

  //${item.id}-${item?.type ?? ""}-${item?.name ?? ""}

  async createTaskFile({ day, month, year, file }: { day: any; month: any; year: any; file: any }) {
    try {
      this.isLoadingFileEss = true;
      if (day === null) {
        this.rootStore.notificationStore.addNotification({
          title: "Ошибка",
          description: "День не выбран. Пожалуйста выберите день",
          icon: "error",
          type: "error",
        });
      } else {
        const date: string = prepareDate(year, month, day);
        const formData: any = new FormData();
        formData.append("file", file);
        formData.append("date", date);
        const { taskId } = await nsi.nsiController.createTaskFile(formData);
        this.lastTaskIdNsi = taskId;
        // localStorage.setItem("taskIdNsi", taskId);
        this.rootStore.notificationStore.addNotification({
          title: "Загрузка НСИ",
          description: "Задача запущена",
          icon: "create",
          type: "done",
        });
      }
    } catch (e) {
    } finally {
      this.isLoadingFileEss = false;
    }
  }

  async createTask({ day, month, year }: { day: any; month: any; year: any }) {
    try {
      if (day === null) {
        this.rootStore.notificationStore.addNotification({
          title: "Ошибка",
          description: "День не выбран. Пожалуйста выберите день",
          icon: "error",
          type: "error",
        });
      } else {
        const date = prepareDate(year, month, day);
        const { taskId } = await nsi.nsiController.createTaskEss(date);
        // localStorage.setItem("taskIdNsi", taskId);
        this.lastTaskIdNsi = taskId;
        this.rootStore.notificationStore.addNotification({
          title: "Загрузка НСИ",
          description: "Задача запущена",
          icon: "create",
          type: "done",
        });
      }
    } catch (e) {}
  }

  resetPublication() {
    this.timerIdPublication();
  }

  async publication(Ids: any) {
    try {
      this.isLoadingPublication = true;
      await new Promise(async (resolve, reject) => {
        runInAction(() => {
          this.timerIdPublication = reject;
        });
        const controlIds = Ids.map((el: any) => {
          const item = el.split("-");
          return item[0];
        });
        if (this.lastTaskIdNsi) {
          let lastTaskId: any = null;
          await nsi.nsiController
            .publishTaskEss(this.lastTaskIdNsi, controlIds)
            .then(async () => {
              const { taskId } = await nsi.nsiController.getLastTaskDistribution();
              lastTaskId = taskId;
            })
            .then(async () => {
              await this.getStatusDistributionAll(lastTaskId);
            })
            .then(async () => {
              await this.checkStatusDistributionDC(lastTaskId);
            });
          // return this.lastTaskIdNsi;
        } else {
          reject("Публикация завершилась ошибкой");
        }
      });
    } catch (e) {
    } finally {
      this.isLoadingPublication = false;
    }
  }

  async cancelTask() {
    try {
      // const taskId = localStorage.getItem("taskIdNsi");
      if (this.lastTaskIdNsi) {
        await nsi.nsiController.cancelTaskEss(this.lastTaskIdNsi);
        this.rootStore.notificationStore.addNotification({
          title: "Загрузка НСИ",
          description: "Задача отменена",
          icon: "error",
          type: "information",
        });
        return this.lastTaskIdNsi;
      } else {
        return new Promise(() => {
          throw new Error("Отмена завершилась ошибкой");
        });
      }
    } catch (e) {}
  }

  async getErrorsTask(loadMode: string) {
    try {
      this.isLoadingViewError = true;
      // const taskId = localStorage.getItem("taskIdNsi");
      if (this.lastTaskIdNsi) {
        const { duplication, errorGou, errorKartaVed } = await nsi.nsiController.getErrorsTaskEss(this.lastTaskIdNsi);
        this.errorByTask = { duplication, errorGou, errorKartaVed };
      } else {
        return new Promise(() => {
          throw new Error("Ошибка");
        });
      }
    } catch (e) {
    } finally {
      this.isLoadingViewError = false;
    }
  }

  prepareVariable(variable: number) {
    return variable < 10 ? `0${variable}` : variable;
  }

  async initDateComparison(filterDate1: { month: number; day: number; year: number }, filterDate2: { month: number; day: number; year: number }) {
    try {
      this.isLoadingDateComparison = true;
      const date1 = `${filterDate1.year}-${this.prepareVariable(filterDate1.month)}-${this.prepareVariable(filterDate1.day)}`;
      const date2 = `${filterDate2.year}-${this.prepareVariable(filterDate2.month)}-${this.prepareVariable(filterDate2.day)}`;
      const { addEnties, deleteEntries, changeEntries } = await nsi.nsiController.getDateComparison({
        date1,
        date2,
        isCenter: this.rootStore.authStore.isCenter,
      });
      this.dateComparison = { addEntries: addEnties, deleteEntries, changeEntries };
    } catch (e) {
      this.dateComparison = { addEntries: [], deleteEntries: [], changeEntries: [] };
    } finally {
      this.isLoadingDateComparison = false;
    }
  }

  stopLoadingEss() {
    runInAction(() => {
      this.ProcessingEss = false;
    });
  }

  startLoadingEss() {
    runInAction(() => {
      this.ProcessingEss = true;
    });
  }

  stopModalEss() {
    this.timerId("stop modal ess");
  }

  async getLastTask() {
    try {
      const { lastTaskId, distributedInfo, downloadInfo } = await nsi.nsiController.getLastTaskId();
      this.lastTaskIdNsi = lastTaskId;
      this.distributedInfo = distributedInfo;
      this.downloadInfo = downloadInfo;
      if (this.lastTaskIdNsi) {
        const { error, status, loadedAt, availableFrom } = await nsi.nsiController.checkTaskEss(this.lastTaskIdNsi);

        const { initiator, name } = await nsi.nsiController.checkStatusInitLoading(this.lastTaskIdNsi);
        this.mainStatusLoading = name ?? "-";

        //
        const { publishedAt: loadedAtPublication, availableFrom: availableFromPublication } = await nsi.nsiController.getLastPublish(this.rootStore.authStore.isCenter);
        runInAction(() => {
          this.essStatus = {
            error,
            status,
            loadedAt: loadedAtPublication ? prepareDateTable(loadedAtPublication) : "",
            availableFrom: availableFromPublication ? availableFromPublication.split("-").reverse().join(".") : "",
            loadedAtCurrent: loadedAt ? prepareDateTable(loadedAt) : "",
            availableFromCurrent: availableFrom ? availableFrom.split("-").reverse().join(".") : "",
          };
        });

        if ((status === "CREATED" && this.ProcessingEss) || this.distributedInfo?.isDistributed || this.downloadInfo?.isDownloaded) {
          this.ProcessingEss = true;
        } else {
          if (status === "CANCELED" || status === "PUBLISHED") {
            if (status === "PUBLISHED") {
              const { taskId: taskPublish } = await nsi.nsiController.getLastTaskDistribution();
              const { initiator, name } = await nsi.nsiController.checkStatusDistLoading(taskPublish);
              this.mainStatusLoading = name ?? "-";
            }
          } else {
            runInAction(() => {
              this.essStatus = {
                error,
                status,
                loadedAt: loadedAtPublication ? prepareDateTable(loadedAtPublication) : "",
                availableFrom: availableFromPublication ? availableFromPublication.split("-").reverse().join(".") : "",
                loadedAtCurrent: loadedAt ? prepareDateTable(loadedAt) : "",
                availableFromCurrent: availableFrom ? availableFrom.split("-").reverse().join(".") : "",
              };
            });
          }
        }
      } else {
        return new Promise(() => {
          throw new Error("Ошибка");
        });
      }
    } catch (e) {}
  }

  async initModalEss() {
    try {
      this.isLoadingEss = true;
      this.ProcessingEss = false;
      await new Promise(async (resolve, reject) => {
        const { lastTaskId, distributedInfo, downloadInfo } = await nsi.nsiController.getLastTaskId();
        this.lastTaskIdNsi = lastTaskId;
        this.distributedInfo = distributedInfo;
        this.downloadInfo = downloadInfo;
        if (this.lastTaskIdNsi) {
          const { error, status, loadedAt, availableFrom } = await nsi.nsiController.checkTaskEss(this.lastTaskIdNsi);

          const { initiator, name } = await nsi.nsiController.checkStatusInitLoading(this.lastTaskIdNsi);
          this.mainStatusLoading = name ?? "-";

          //
          const { publishedAt: loadedAtPublication, availableFrom: availableFromPublication } = await nsi.nsiController.getLastPublish(this.rootStore.authStore.isCenter);
          runInAction(() => {
            this.essStatus = {
              error,
              status,
              loadedAt: loadedAtPublication ? prepareDateTable(loadedAtPublication) : "",
              availableFrom: availableFromPublication ? availableFromPublication.split("-").reverse().join(".") : "",
              loadedAtCurrent: loadedAt ? prepareDateTable(loadedAt) : "",
              availableFromCurrent: availableFrom ? availableFrom.split("-").reverse().join(".") : "",
            };
          });

          if ((status === "CREATED" && this.ProcessingEss) || this.distributedInfo?.isDistributed || this.downloadInfo?.isDownloaded) {
            this.ProcessingEss = true;
          } else {
            if (status === "CANCELED" || status === "PUBLISHED") {
              if (status === "PUBLISHED") {
                const { taskId: taskPublish } = await nsi.nsiController.getLastTaskDistribution();
                const { initiator, name } = await nsi.nsiController.checkStatusDistLoading(taskPublish);
                this.mainStatusLoading = name ?? "-";
              }
            } else {
              runInAction(() => {
                this.essStatus = {
                  error,
                  status,
                  loadedAt: loadedAtPublication ? prepareDateTable(loadedAtPublication) : "",
                  availableFrom: availableFromPublication ? availableFromPublication.split("-").reverse().join(".") : "",
                  loadedAtCurrent: loadedAt ? prepareDateTable(loadedAt) : "",
                  availableFromCurrent: availableFrom ? availableFrom.split("-").reverse().join(".") : "",
                };
              });
            }
          }
        } else {
          reject("Ошибка");
        }
        setTimeout(resolve, 15000);
        runInAction(() => {
          this.timerId = reject;
        });
      });
      await this.initModalEss();
    } catch (e) {
      this.ProcessingEss = false;
      this.isLoadingEss = false;
    } finally {
      runInAction(() => {
        this.isLoadingEss = false;
        this.ProcessingEss = false;
      });
    }
  }

  async initModalFile() {
    try {
      this.isLoadingEss = true;
      await new Promise(async (resolve, reject) => {
        const { lastTaskId, distributedInfo, downloadInfo } = await nsi.nsiController.getLastTaskId();
        this.lastTaskIdNsi = lastTaskId;
        this.distributedInfo = distributedInfo;
        this.downloadInfo = downloadInfo;
        if (this.lastTaskIdNsi) {
          const { error, status, loadedAt, availableFrom } = await nsi.nsiController.checkTaskEss(this.lastTaskIdNsi);
          const { initiator, name } = await nsi.nsiController.checkStatusInitLoading(this.lastTaskIdNsi);
          this.mainStatusLoading = name ?? "-";
          const { publishedAt: loadedAtPublication, availableFrom: availableFromPublication } = await nsi.nsiController.getLastPublish(this.rootStore.authStore.isCenter);
          if ((status === "CREATED" && this.ProcessingEss) || this.distributedInfo?.isDistributed || this.downloadInfo?.isDownloaded) {
            this.ProcessingEss = true;
          } else {
            this.ProcessingEss = false;
            if (status === "CANCELED" || status === "PUBLISHED") {
              if (status === "PUBLISHED") {
                const { taskId: taskPublish } = await nsi.nsiController.getLastTaskDistribution();
                const { initiator, name } = await nsi.nsiController.checkStatusDistLoading(taskPublish);
                this.mainStatusLoading = name ?? "-";
              }
              runInAction(() => {
                this.essStatus = {
                  error,
                  status,
                  loadedAt: loadedAtPublication ? prepareDateTable(loadedAtPublication) : "",
                  availableFrom: availableFromPublication ? availableFromPublication.split("-").reverse().join(".") : "",
                  loadedAtCurrent: loadedAt ? prepareDateTable(loadedAt) : "",
                  availableFromCurrent: availableFrom ? availableFrom.split("-").reverse().join(".") : "",
                };
              });
            } else {
              runInAction(() => {
                this.essStatus = {
                  error,
                  status,
                  loadedAt: loadedAtPublication ? prepareDateTable(loadedAtPublication) : "",
                  availableFrom: availableFromPublication ? availableFromPublication.split("-").reverse().join(".") : "",
                  loadedAtCurrent: loadedAt ? prepareDateTable(loadedAt) : "",
                  availableFromCurrent: availableFrom ? availableFrom.split("-").reverse().join(".") : "",
                };
              });
            }
          }
        } else {
          reject("Ошибка");
        }
        setTimeout(resolve, 15000);
        runInAction(() => {
          this.timerId = reject;
        });
      });
      await this.initModalFile();
    } catch (e) {
      this.ProcessingEss = false;
      this.isLoadingEss = false;
    } finally {
      runInAction(() => {
        this.isLoadingEss = false;
        this.ProcessingEss = false;
      });
    }
  }

  async initProtocolChanges(loadMode: any) {
    try {
      this.isLoadingProtocolChanges = true;
      // const taskId = localStorage.getItem("taskIdNsi");
      if (this.lastTaskIdNsi) {
        const { addEnties, deleteEntries, changeEntries }: any = await nsi.nsiController.getProtocolChanges(this.lastTaskIdNsi);

        this.protocolChanges = {
          addEntries: addEnties ? addEnties : [],
          deleteEntries: deleteEntries ? deleteEntries : [],
          changeEntries: changeEntries ? changeEntries : [],
        };
      } else {
        return new Promise(() => {
          throw new Error("Ошибка");
        });
      }
    } catch (e) {
      this.protocolChanges = { addEntries: [], deleteEntries: [], changeEntries: [] };
    } finally {
      this.isLoadingProtocolChanges = false;
    }
  }

  resetProtocolChanges() {
    this.protocolChanges = { addEntries: [], deleteEntries: [], changeEntries: [] };
  }

  prepareDistribution(array: any[]) {
    return array.map((item: any) => {
      const dateAndTime = item?.distribution?.finishedAt ? prepareDateTable(item?.distribution?.finishedAt) : "";

      let childs = item?.childs ? item?.childs : item?.items ? item?.items : [];

      if (childs.length > 0) {
        childs = this.prepareDistribution(childs);
      }

      const getStatus = () => {
        if (item?.distribution?.status === "CONFIRMED") {
          return "Готово";
        }
        if (item?.distribution?.status === "CREATED") {
          return "В процессе";
        }
        if (item?.distribution?.status === "FAILED") {
          return "Ошибка";
        }
        return "Недоступен";
      };

      return {
        ...item,
        name: item.depName ? item.depName : item.name ? item.name : "-",
        information: item?.distribution ? `${dateAndTime} ${item?.distribution?.error ?? ""}` : "",
        status: getStatus(),
        // tabId: item?.depId ? item?.depId : item?.id,
        tabId: `${item?.depId ? item?.depId : item?.id}-${item.depName ? item.depName : item.name ? item.name : "-"}`,
        childs,
      };
    });
  }

  prepareOnClickChild(array: any, id: number | string, name: string, value: number | string) {
    return array.map((item: any) => {
      let items = null;
      if (item.items && item.items.length > 0) {
        items = this.prepareOnClickChild(item.items, id, name, value);
      }
      if (item.name === name && item.id === id) return { ...item, isOpen: !value, items };
      return { ...item, items };
    });
  }

  onClickOpenAndCloseChild(id: number | string, name: string, value: number | string) {
    this.distribution = this.prepareOnClickChild(this.distribution, id, name, value);
  }

  checkStatus(array: any) {
    return array.some(({ status, items }: { status: string; items: any[] }) => {
      let isCheckChilds = false;
      if (items && items.length > 0) {
        isCheckChilds = this.checkStatus(items);
      }
      return status === "CREATED" || isCheckChilds;
    });
  }

  prepareDistributionFlat(array: any) {
    let result: any[] = [];
    array.map((item: any) => {
      let childs = item.items ? item.items : item.childs ? item.childs : [];
      if (childs && childs.length > 0) {
        childs = this.prepareDistributionFlat(childs);
      } else {
        childs = null;
      }

      const dateAndTime = item?.distribution?.finishedAt ? prepareDateTable(item?.distribution?.finishedAt) : "";

      result.push({
        ...item,
        name: item.depName ? item.depName : item.name ? item.name : "-",
        information: item?.distribution ? `${dateAndTime} ${item?.distribution?.error ?? ""}` : "",
        status: item?.distribution?.status,
        tabId: item.depId,
      });
      if (childs) {
        result = [...result, ...childs];
      }
    });
    return result;
  }

  async getTaskList() {
    try {
      const { taskId } = await nsi.nsiController.getLastTaskDistribution();
      const { item } = await nsi.nsiController.getStatusDistribution(taskId);
      runInAction(() => {
        this.distribution = this.prepareDistribution([item]);
        localStorage.removeItem("tableOpened-1");
        const flatData = this.prepareDistributionFlat([item]).map((el) => el.tabId);
        localStorage.setItem("tableOpened-1", JSON.stringify(flatData));
      });
    } catch (e) {}
  }

  async startChecking() {
    try {
      this.isProcessing = true;
      while (this.isProcessing) {
        await delay(1000);

        const processingTasks = this.checkStatus(this.distribution);
        if (!processingTasks) {
          this.isProcessing = false;
        } else {
          await this.getTaskList();
        }
      }
    } catch (e) {
    } finally {
      this.isProcessing = false;
    }
  }

  stopCheckStatus() {
    this.isProcessing = false;
  }

  stopNsiDistribution() {
    this.nsiDistributionTimer("STOP NSI DISTRIBUTION");
  }

  async initDistribution() {
    try {
      runInAction(() => {
        this.isLoadingDistribution = true;
      });
      await this.getTaskList();
      runInAction(() => {
        this.isLoadingDistribution = false;
      });
      // await this.startChecking();
      // await delay(5000);
      await new Promise((resolve, reject) => {
        setTimeout(resolve, 15000);
        runInAction(() => {
          this.nsiDistributionTimer = reject;
        });
      });
      await this.initDistribution();
    } catch (e) {
      this.distribution = [];
    } finally {
      runInAction(() => {
        this.isLoadingDistribution = false;
      });
    }
  }

  async restartDistribution() {
    try {
      const { taskId } = await nsi.nsiController.getLastTaskDistribution();
      await nsi.nsiController.restartDistribution(taskId);
      this.rootStore.notificationStore.addNotification({
        title: "Повтор",
        description: "Распространение перезапущено",
        icon: "reset",
        type: "information",
      });
      await this.initDistribution();
    } catch (e) {}
  }

  async initModalSync() {
    try {
      this.isLoadingModalSync = true;
      const { items } = await nsi.nsiController.getInfoSync(this.rootStore.authStore.isCenter);
      const prepareDate = (value: any) => {
        const [year, month, day] = value.split("-");
        return `${day}.${month}.${year}`;
      };

      this.infoModalSync = items.reverse().map((el: any, index: number) => {
        const info = el?.actions?.reverse().map((item: any) => {
          const uuid = generateUUID();
          return { date: index, name: item.action, initiator: item.initiator, tabId: uuid };
        });
        const uuid = generateUUID();
        return { date: index, name: prepareDate(el.date), childs: info, tabId: uuid };
      });
    } catch (e) {
    } finally {
      this.isLoadingModalSync = false;
    }
  }

  async getStatusDistributionAll(taskId: any) {
    try {
      const { status, info, startedAt, finishedAt } = await nsi.nsiController.getStatusAllDC(taskId);
      this.statusDistributionDC = { status, info, startedAt, finishedAt };
    } catch (e) {}
  }

  async checkStatusDistributionDC(taskId: any) {
    try {
      this.isLoadingPublication = true;
      while (this.isLoadingPublication) {
        await delay(15000);

        const processingTasks = this.checkStatus([this.statusDistributionDC]);
        if (!processingTasks) {
          this.isLoadingPublication = false;
          // this.rootStore.notificationStore.addNotification({
          //   title: "Сохранение",
          //   description: this.statusDistributionDC?.info ?? "Неизвестная ошибка",
          //   icon: this.statusDistributionDC?.status === "CONFIRMED" ? "success" : "error",
          //   type: this.statusDistributionDC?.status === "CONFIRMED" ? "information" : "error",
          // });
        } else {
          await this.getStatusDistributionAll(taskId);
        }
      }
    } catch (e) {
      this.isLoadingPublication = false;
    } finally {
      this.isLoadingPublication = false;
    }
  }

  async initModalInputDC() {
    try {
      this.isLoadingModalInputDC = true;
      // const taskId = localStorage.getItem("taskIdNsi");
      const { department } = await nsi.nsiController.getListDcAtTask(this.lastTaskIdNsi);
      this.dcListAtTask = this.prepareDistribution([department]);
      localStorage.removeItem("tableOpened-89");
      const flatData = this.prepareDistributionFlat([department]).map((el) => el.id);
      localStorage.setItem("tableOpened-89", JSON.stringify(flatData));
    } catch (e) {
    } finally {
      this.isLoadingModalInputDC = false;
    }
  }

  prepareObjectModalComparison(array: any[]) {
    return array.map((item) => {
      const childs: any[] = item?.childs && item?.childs?.length > 0 ? this.prepareObjectModalComparison(item.childs) : [];
      return { ...item, tabId: `${item.id}-${item.type}-${generateUUID()}`, childs, isDisableChecked: !item.isTarget }; //  || type === "goy"
    });
  }

  prepareSk11(array: any[]) {
    return array.map((item) => {
      const childs: any[] = item.childs ? this.prepareSk11(item.childs) : [];
      return { ...item, tabId: item.id11, childs };
    });
  }
  getSrpgType(type: any) {
    switch (type) {
      case "LINE":
        return "vetv";
      case "N_BLOCK":
        return "Nblock";
      case "W_SUM":
        return "Wsum";
      case "N_GROUP":
        return "ngroup";
      case "CONSUMER_2":
        return "consumer2";
      case "RGE":
        return "generator";
      case "SECHEN":
        return "sechen";
      case "AREA_2":
        return "area2";
      case "AREA":
        return "area";
      case "POWER_SYSTEM":
        return "power_system";
      default:
        return type;
    }
  }

  async initModalComparison({ day, month, year, filter, registry, type }: { day: any; month: any; year: any; filter: string; registry?: string; type?: any }) {
    try {
      runInAction(() => {
        this.IsModalComparison = true;
        this.objectPakEss = [];
      });
      const date = prepareDate(year, month, day);
      if (registry === "gou") {
        const finalType = type === "N_BLOCK" ? "NBLOCK" : type;
        if (filter === "all") {
          const { items } = await nsi.nsiController.getGouListIA(date, this.rootStore.authStore.isCenter, finalType);
          runInAction(() => {
            this.objectPakEss = this.prepareObjectModalComparison(items);
          });
        }
        if (filter === "matched") {
          const { items } = await nsi.nsiController.getGouListIAMatched(date, this.rootStore.authStore.isCenter, finalType);
          runInAction(() => {
            this.objectPakEss = this.prepareObjectModalComparison(items);
          });
        }
        if (filter === "notMatched") {
          const { items } = await nsi.nsiController.getGouListIANotMatched(date, this.rootStore.authStore.isCenter, finalType);
          runInAction(() => {
            this.objectPakEss = this.prepareObjectModalComparison(items);
          });
        }
      }
      if (registry === "srpg") {
        const finalType = this.getSrpgType(type);
        if (filter === "all") {
          const { items } = await nsi.nsiController.getSRPGListIA(date, finalType);
          runInAction(() => {
            this.objectPakEss = this.prepareObjectModalComparison(items);
          });
        }
        if (filter === "matched") {
          const { items } = await nsi.nsiController.getSRPGListIAMatched(date, finalType);
          runInAction(() => {
            this.objectPakEss = this.prepareObjectModalComparison(items);
          });
        }
        if (filter === "notMatched") {
          const { items } = await nsi.nsiController.getSRPGListIANotMatched(date, finalType);
          runInAction(() => {
            this.objectPakEss = this.prepareObjectModalComparison(items);
          });
        }
      }
      if (registry === "adder") {
        if (filter === "all") {
          const { items } = await nsi.nsiController.getSummatorsListIA(date, type);
          runInAction(() => {
            this.objectPakEss = this.prepareObjectModalComparison(items);
          });
        }
        if (filter === "matched") {
          const { items } = await nsi.nsiController.getSummatorsListIAMatched(date, type);
          runInAction(() => {
            this.objectPakEss = this.prepareObjectModalComparison(items);
          });
        }
        if (filter === "notMatched") {
          const { items } = await nsi.nsiController.getSummatorsListIANotMatched(date, type);
          runInAction(() => {
            this.objectPakEss = this.prepareObjectModalComparison(items);
          });
        }
      }
    } catch (e) {
    } finally {
      runInAction(() => {
        this.IsModalComparison = false;
      });
    }
  }

  async changeRegistry(registry: any) {
    try {
      if (registry === "goy") {
        if (this.rootStore.authStore.isCenter && isModeCenter) {
          runInAction(() => {
            this.typeObjects = [{ value: "goy", label: "ГОУ" }];
          });
        } else {
          const { items } = await nsi.nsiController.getTypesGou();
          runInAction(() => {
            this.typeObjects = items.map((el) => ({ value: el.code, label: el.name }));
          });
        }
      }
      if (registry === "srpg") {
        const { items } = await nsi.nsiController.getTypesSrpg();
        runInAction(() => {
          this.typeObjects = items.map((el) => ({ value: el.code, label: el.name }));
        });
      }
      if (registry === "summator") {
        const { items } = await nsi.nsiController.getTypesSummator();
        runInAction(() => {
          this.typeObjects = items.map((el) => ({ value: el.code, label: el.name }));
        });
      }
      runInAction(() => {
        this.type = this.typeObjects[0].value ?? null;
      });
    } catch (e) {}
  }

  changeType(value: any) {
    this.type = value;
  }

  async getSk11(registry: any, type: any) {
    try {
      runInAction(() => {
        this.isLoadingSK11 = true;
      });
      if (registry === "goy") {
        if (type === "NBLOCK") {
          const { items } = await nsi.nsiController.getBlockSk11();
          runInAction(() => {
            this.sk11 = this.prepareSk11(items);
          });
        }
        if (type === "RGE") {
          const { rgeNoGou, rgeWithGou } = await nsi.nsiController.getRgeSk11();
          runInAction(() => {
            this.rge = { rgeNoGou: this.prepareSk11(rgeNoGou), rgeWithGou: this.prepareSk11(rgeWithGou) };
          });
        }
        if (type === "goy") {
          const { items } = await nsi.nsiController.getSK11();
          runInAction(() => {
            this.sk11 = this.prepareSk11(items);
          });
        }
        if (type === "GOU") {
          const { items } = await nsi.nsiController.getSK11Gou();
          runInAction(() => {
            this.sk11 = this.prepareSk11(items);
          });
        }
      }
      if (registry === "srpg") {
        if (type === "generator") {
          const { rgeNoGou, rgeWithGou } = await nsi.nsiController.getRgeSk11();
          this.rge = { rgeNoGou: this.prepareSk11(rgeNoGou), rgeWithGou: this.prepareSk11(rgeWithGou) };
        } else {
          const { items } = await nsi.nsiController.getSrpgSk11(type);
          runInAction(() => {
            this.sk11 = this.prepareSk11(items);
          });
        }
      }
      if (registry === "summator") {
        const { items } = await nsi.nsiController.getRgeSummator();
        runInAction(() => {
          this.sk11 = this.prepareSk11(items);
        });
      }
    } catch (e) {
    } finally {
      runInAction(() => {
        this.isLoadingSK11 = false;
      });
    }
  }

  //
  // {id: 1100018, id11: '45e0aee7-17ac-4b11-8077-e22a1477baa7', type: 'Nblock'}

  localSaveObjectPakEss(arr: any, connections: any) {
    return arr.map((el: any) => {
      let childs: any[] = [];
      if (el?.childs?.length > 0) {
        childs = this.localSaveObjectPakEss(el?.childs, connections);
      }
      const find = connections.find((item: any) => item.id === el.id && el.type === item.type);
      if (find) {
        return { ...el, id11: find?.id11 ?? null, childs };
      }
      return { ...el, childs };
    });
  }

  localSaveObjectPakEssNotMatched(arr: any, connections: any) {
    return arr
      .map((el: any) => {
        let childs: any[] = [];
        if (el?.childs?.length > 0) {
          childs = this.localSaveObjectPakEssNotMatched(el?.childs, connections);
        }
        const find = connections.find((item: any) => item.id === el.id && el.type === item.type);
        if (find) {
          return null;
        } else {
          return { ...el, childs };
        }
      })
      .filter((el: any) => el !== null);
  }

  //connections: any[], registry: any, type: any, operation: any, paramsFilter: any
  async saveModalComparison(connections: any[], operation: any, paramsFilter: any) {
    try {
      const { duplicates = [], duplicatesFromRequest = [] } = await nsi.nsiController.saveConnections(connections, this.rootStore.authStore.isCenter, operation);
      if (duplicates.length > 0 || duplicatesFromRequest.length > 0) {
        this.infoDuplicates = { duplicates, duplicatesFromRequest };
        // return duplicates.length > 0 || duplicatesFromRequest.length > 0;
      } else {
        this.infoDuplicates = { duplicates: [], duplicatesFromRequest: [] };
        this.rootStore.notificationStore.addNotification({
          title: "Сохранение",
          description: "Данные сохранены",
          icon: "success",
          type: "information",
        });
      }
      if (paramsFilter === "notMatched") {
        this.objectPakEss = this.localSaveObjectPakEssNotMatched(this.objectPakEss, connections);
      } else {
        this.objectPakEss = this.localSaveObjectPakEss(this.objectPakEss, connections);
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  async startDistribution() {
    try {
      this.isStatusDistribution = true;
      await nsi.nsiController.startDistribution(this.formattedDate);
      this.rootStore.notificationStore.addNotification({
        title: "Распространение",
        description: "Распространение запущено",
        icon: "start",
        type: "done",
      });
    } catch (e) {
    } finally {
      this.isStatusDistribution = false;
    }
  }

  async getStatusProtocolDistribution() {
    try {
      const { status, startedAt, finishedAt, error, notSentTo } = await nsi.nsiController.getStatusDistributionProtocol(this.formattedDate);
      runInAction(() => {
        this.protocolDistributionInfo = { status, startedAt, finishedAt, error, notSentTo };
      });
    } catch (e) {}
  }

  async startProtocolDistribution() {
    try {
      this.isProcessingDistributionProtocol = true;
      while (this.isProcessingDistributionProtocol) {
        await delay(15000);
        const processingTasks = this.protocolDistributionInfo.status === "SENDING";
        if (!processingTasks) {
          this.isProcessingDistributionProtocol = false;
        } else {
          await this.getStatusProtocolDistribution();
        }
      }
    } catch (e) {
    } finally {
      this.isProcessingDistributionProtocol = false;
    }
  }

  async stopProtocolDistribution() {
    this.isProcessingDistributionProtocol = false;
  }

  async checkStatusGeneric(taskId: string) {
    try {
      const { status, activeLoadedAt } = await nsi.nsiController.infoGeneric();
      this.infoStatusGeneric = { status, loadedAt: activeLoadedAt };
      return status === "CREATED";
    } catch (e) {}
  }

  cleanGenerics() {
    this.infoStatusGeneric = {};
    this.generics = [];
    this.sk11 = [];
  }

  async startGeneric() {
    try {
      const { taskId } = await nsi.nsiController.startGeneric();
      localStorage.setItem("taskIdGeneric", taskId);
      return taskId;
    } catch (e) {}
  }

  getTypeWith(type: any) {
    switch (type) {
      case "rgeWithGou":
        return "RGE_WITH_GOU";
      case "rgeNoGou":
        return "RGE_WITHOUT_GOU";
    }
  }

  async getSubTypesSK11(type: any, registry: any) {
    try {
      if (type && registry) {
        const { items } = await nsi.nsiController.getTypeForSk11(type, registry);
        return items[0]?.code ?? null;
      }
    } catch (e) {}
  }

  async getStatusSK11(lType: any) {
    try {
      const loadType = lType === "GEN" || lType === "POTR" || lType === "SALDO" ? "ADDER" : lType;
      const { status, activeTaskId, activeLoadedAt, activeModelVersion } = await nsi.nsiController.infoSK11(loadType);
      this.statusSK11 = { status, activeTaskId, activeLoadedAt, activeModelVersion };
      return { status, activeTaskId, activeLoadedAt, activeModelVersion };
    } catch (e) {}
  }

  async getStatusGeneric() {
    try {
      const loadType = "GENERIC";
      const { status, activeTaskId, activeLoadedAt, activeModelVersion } = await nsi.nsiController.infoSK11(loadType);
      this.statusGenericSK11 = { status, activeTaskId, activeLoadedAt, activeModelVersion };
    } catch (e) {}
  }

  stopCheckStatusSK11() {
    this.timerCheckSK11("STOP SK 11");
  }

  stopCheckStatusGeneric() {
    this.timerCheckGeneric();
  }

  prepareItems(arr: any[]) {
    let result: any = [];
    arr.map((el: any) => {
      let childs: any[] = [];
      if (el?.childs?.length) {
        childs = this.prepareItems(el?.childs);
      }
      result.push({
        tabId: el.id11,
        name: el.name,
        parentId: el.parentId ? el.parentId : "",
        hasChildren: el.hasChildren ? el.hasChildren : false,
        id11: el.id11 ? el.id11 : null,
        isEntry: el.isEntry ? el.isEntry : null,
        isTarget: el.isTarget ? el.isTarget : null,
        isBound: el.isBound ? el.isBound : null,
        childs: el.childs ? el.childs : [],
      });
      childs.map((item) => {
        result.push(item);
      });
    });
    return result;
  }

  async getTableSK11Search(taskId: string, id11: string, name: string, lType: any, typeTableSk11: any) {
    try {
      const loadType = lType === "GEN" || lType === "POTR" || lType === "SALDO" ? "ADDER" : lType;
      if (typeTableSk11 === "sk11") {
        const { items, hasMoreElements } = await nsi.nsiController.getSearchSK11(taskId, id11, name, loadType, this.rootStore.authStore.isCenter);
        const finalItems = this.prepareItems(items);
        return { items: finalItems, hasMoreElements };
      } else {
        const { items, hasMoreElements } = await nsi.nsiController.getGenericOnSearch(taskId, id11, name, this.rootStore.authStore.isCenter);
        const finalItems = this.prepareItems(items);
        return { items: finalItems, hasMoreElements };
      }
    } catch (e) {}
  }

  async getTableLeftSearch(name: any, id: any, registry: any, date: any, subRegistry: any, paramsFilter: any, uid11: any, typeSearch: any) {
    try {
      const mainType: any = this.getTypePakEss(registry);
      const loadType = this.getLoadType(registry, subRegistry);
      const objectPost = { date, type: loadType, mapped: this.getMapped(paramsFilter), id, name, uid11, typeSearch };
      const { items, hasMoreElements } = await nsi.nsiController.getSearchPakEss(mainType, objectPost, this.rootStore.authStore.isCenter);
      return { items, hasMoreElements };
    } catch (e) {}
  }

  async getTableNsiSearch(dcId: any, selectedSegment: any, id: any, name: any, date: any) {
    try {
      const type = this.getTypeNsiRight(selectedSegment);
      const objectPost = {
        date,
        id,
        name,
      };
      if (dcId) {
        const { items, hasMoreElements } = await nsi.nsiController.getSearchNsi(dcId, type, objectPost, this.rootStore.authStore.isCenter);
        return { items, hasMoreElements };
      } else {
        const { items, hasMoreElements } = await nsi.nsiController.getSearchNsiNoSelect(type, objectPost, this.rootStore.authStore.isCenter);
        return { items, hasMoreElements };
      }
    } catch (e) {}
  }

  resetSearchSK11() {
    this.dataSK11 = [...this.originalSK11];
    this.hasMoreElements = false;
  }

  addLoadDataSK11(array: any, tabId: any, loadType: any, newChilds: any) {
    return array.map((el: any) => {
      let childs: any[] = [];
      if (el?.childs?.length > 0) {
        childs = this.addLoadDataSK11(el?.childs, tabId, loadType, newChilds);
      }
      if (el.tabId === tabId) {
        return { ...el, childs: newChilds };
      }
      return { ...el, childs };
    });
  }

  async getTablePrepareSK11(tabId: any, lType: any, type: any) {
    try {
      if (type === "sk11") {
        const statusSK11: any = await this.getStatusSK11(lType);
        if (statusSK11.activeTaskId) {
          const loadType = lType === "GEN" || lType === "POTR" || lType === "SALDO" ? "ADDER" : lType;
          const { items } = await nsi.nsiController.getSK11OnParentId(statusSK11.activeTaskId, tabId, loadType, this.rootStore.authStore.isCenter);
          return items;
        }
      } else {
        const { items } = await nsi.nsiController.getGenericOnParentId(this.statusGenericSK11.activeTaskId, tabId, this.rootStore.authStore.isCenter);
        return items;
      }
    } catch (e) {
    } finally {
      this.isLoadingRightTableComparisonSK = false;
    }
  }

  getTypePakEss = (type: any) => {
    if (type === "gou") {
      return "gous";
    }
    if (type === "srpg") {
      return "srpgs";
    }
    if (type === "adder") {
      return "adders";
    }
  };

  getMapped(type: any) {
    if (type === "all") {
      return null;
    }
    if (type === "matched") {
      return true;
    }
    if (type === "notMatched") {
      return false;
    }
  }

  getLoadType(registry: any, subRegistry: any) {
    if (registry === "gou") {
      return subRegistry === "N_BLOCK" ? "NBLOCK" : subRegistry;
    }
    if (registry === "srpg") {
      switch (subRegistry) {
        case "LINE":
          return "vetv";
        case "N_BLOCK":
          return "Nblock";
        case "W_SUM":
          return "Wsum";
        case "N_GROUP":
          return "ngroup";
        case "CONSUMER_2":
          return "consumer2";
        case "RGE":
          return "generator";
        case "SECHEN":
          return "sechen";
        case "AREA_2":
          return "area2";
        case "AREA":
          return "area";
        case "POWER_SYSTEM":
          return "power_system";
        default:
          return subRegistry;
      }
    }
    if (registry === "adder") {
      return subRegistry;
    }
  }

  async prepareDataSaveConnection(tabIds: any[]) {
    try {
      const objectPost: any = {
        tabIds,
      };
      const { items } = await nsi.nsiController.prepareDataConnection(this.rootStore.authStore.isCenter, objectPost);
      return items || [];
    } catch (e) {}
  }

  async getTableLeftSK11(registry: any, paramsFilter: any, subRegistry: any, parentId: any, date: any) {
    try {
      const mainType: any = this.getTypePakEss(registry);
      const loadType = this.getLoadType(registry, subRegistry);
      const objectPost = { date, type: loadType, mapped: this.getMapped(paramsFilter), parentId: parentId === "" ? null : parentId };
      const { items } = await nsi.nsiController.getComparisonParentId(mainType, objectPost, this.rootStore.authStore.isCenter);
      return items;
    } catch (e) {}
  }

  getTypeNsiRight(type: any) {
    if (type === "goy") return "gous";
    if (type === "summator") return "adders";
  }

  async getTablePrepareNsiRight(dcId: any, tabId: any, selectedSegment: any, date: any) {
    try {
      const type = this.getTypeNsiRight(selectedSegment);
      const id = tabId && tabId.length > 0 ? tabId : null;
      if (dcId) {
        const { items } = await nsi.nsiController.getNsiOnParentId(dcId, id, type, date, this.rootStore.authStore.isCenter);
        return items;
      } else {
        if (type) {
          const { items } = await nsi.nsiController.getNsiOnParentIdNoSelected(id, type, date, this.rootStore.authStore.isCenter);
          return items;
        }
      }
    } catch (e) {}
  }

  async loadDataSK11(parentId: any, loadType: any, taskId: any) {
    try {
      this.isLoadingRightTableComparisonSK = true;
      const { items } = await nsi.nsiController.getSK11OnParentId(taskId, parentId, loadType, this.rootStore.authStore.isCenter);
      this.dataSK11 = this.prepareGenerics(items);
      this.originalSK11 = [...this.dataSK11];
    } catch (e) {
    } finally {
      this.isLoadingRightTableComparisonSK = false;
    }
  }

  //type: any, selectedRge: any, registry: any
  async checkStatusSK11(loadType: any) {
    try {
      this.isLoadingCheckSK11 = true;
      // const version = localStorage.getItem("status-sk11");
      // if (version === loadType) {
      await this.getStatusSK11(loadType);
      const processingTasks = this.statusSK11.status === "CREATED";
      if (!processingTasks) {
        if (this?.statusSK11?.status === "ACTIVE") {
          //this?.statusSK11?.activeTaskId
          // const loadType = type === "RGE" || (registry === "srpg" && type === "generator") ? this.getTypeWith(selectedRge) : await this.getSubTypesSK11(type, registry);
          // return await this.loadDataSK11(null, loadType, this.statusSK11.activeTaskId);
          return true;
        } else {
          return false;
        }
      } else {
        this.isLoadingCheckSK11 = false;
        await new Promise((resolve, reject) => {
          setTimeout(resolve, 15000);
          runInAction(() => {
            this.timerCheckSK11 = reject;
          });
        });
        await this.checkStatusSK11(loadType);
      }
      // }
    } catch (e) {
      this.isLoadingCheckSK11 = false;
    } finally {
      this.isLoadingCheckSK11 = false;
    }
  }

  async startSK11(lType: any) {
    try {
      const loadType = lType === "GEN" || lType === "POTR" || lType === "SALDO" ? "ADDER" : lType;
      const { taskId } = await nsi.nsiController.startSK11(loadType);
      localStorage.setItem("taskIdSK11", taskId);
      return taskId;
    } catch (e) {}
  }

  prepareGenerics(array: any) {
    return array.map((el: any) => {
      let childs = el.childs ? el.childs : null;
      if (childs && childs?.length > 0) {
        childs = this.prepareGenerics(childs);
      }
      return { ...el, tabId: el.id11, childs: el.hasChildren && childs ? childs : [] };
    });
  }

  async prepareGenericsWithChilds(originalArray: any[], selected: any[], taskId: any, loadType: any) {
    try {
      return await Promise.all(
        originalArray.map(async (item: any) => {
          const parentId = selected.find((el) => el.tabId === item.tabId)?.tabId ?? null;
          if (parentId) {
            const { items } = await nsi.nsiController.getSK11OnParentId(taskId, parentId, loadType, this.rootStore.authStore.isCenter);
            const childs: any = await this.prepareGenericsWithChilds(
              items,
              selected.filter((el) => el.tabId === item.tabId),
              taskId,
              loadType
            );
            const resultChilds: any = await this.prepareGenericsWithChilds(
              this.prepareGenerics(childs),
              selected.filter((el) => el.tabId !== parentId),
              taskId,
              loadType
            );
            return { ...item, tabId: item.id11, childs: resultChilds };
          } else {
            return { ...item, tabId: item.id11 };
          }
        })
      );
    } catch (e) {}
  }

  prepareGenericsSearch(original: any[], taskId: any, parentId: any, paths: any, newChilds: any, isEntryChilds?: boolean) {
    try {
      return original.map((el) => {
        if (el.tabId === parentId) {
          if (el?.childs?.length > 0) {
            return { ...el, childs: isEntryChilds ? newChilds : [], isEntry: false };
          } else {
            return { ...el, childs: newChilds, isEntry: false };
          }
        }
        if (el.tabId === paths[0]) {
          const newPath = paths.filter((item: any) => item !== el.tabId);
          const childs: any = this.prepareGenericsSearch(el.childs, taskId, parentId, newPath, newChilds, isEntryChilds);
          return { ...el, childs };
        }
        return el;
      });
    } catch (e) {}
  }

  async getTableGeneric(taskId: string, selectedChildsGenerics: any, _?: any, isSearchModeGenerics?: any, paths?: any) {
    try {
      this.isLoadingGenericTable = true;
      if ((this.generics.length === 0 || selectedChildsGenerics.length === 0) && !isSearchModeGenerics) {
        const { items } = await nsi.nsiController.getGenericOnParentId(taskId, null, this.rootStore.authStore.isCenter);
        this.generics = this.prepareGenerics(items);
        this.originalGenerics = [...this.generics];
      } else {
        const parentId = paths[paths.length - 1];
        const { items } = await nsi.nsiController.getGenericOnParentId(taskId, parentId, this.rootStore.authStore.isCenter);
        const childs = items.map((el) => ({ ...el, tabId: el.id11, childs: [] }));
        this.generics = this.prepareGenericsSearch(this.generics, taskId, parentId, paths, childs);
        // if (isSearchModeGenerics) {
        //   const parentId = paths[paths.length - 1];
        //   const { items } = await nsi.nsiController.getGenericOnParentId(taskId, parentId);
        //   const childs = items.map((el) => ({ ...el, tabId: el.id11, childs: [] }));
        //   this.generics = this.prepareGenericsSearch(this.generics, taskId, parentId, paths, childs);
        // } else {
        //   this.generics = await this.prepareGenericsWithChilds(this.originalGenerics, selectedChildsGenerics, taskId, "GENERIC");
        // }
      }
    } catch (e) {
    } finally {
      this.isLoadingGenericTable = false;
    }
  }

  async getTableGenericSearch(taskId: string, uid: string, name: string) {
    try {
      this.isLoadingGenericTable = true;
      this.hasMoreElements = false;
      const { items, hasMoreElements } = await nsi.nsiController.getGenericOnSearch(taskId, uid, name, this.rootStore.authStore.isCenter);
      this.generics = this.prepareGenerics(items);
      this.hasMoreElements = hasMoreElements ?? false;
    } catch (e) {
    } finally {
      this.isLoadingGenericTable = false;
    }
  }

  resetSearch() {
    this.generics = [...this.originalGenerics];
    this.hasMoreElements = false;
  }

  async getLastStatusGeneric() {
    try {
      this.isLoadingGeneric = true;
      await this.getStatusGeneric();
      const processingTasks = this.statusGenericSK11.status === "CREATED";
      if (!processingTasks) {
        if (this.statusGenericSK11.status === "ACTIVE") {
          const loadType = "GENERIC";
          // const { items } = await nsi.nsiController.getSK11OnParentId(this.statusGenericSK11.activeTaskId, null, loadType);
          // this.generics = this.prepareGenerics(items);
          // this.originalGenerics = [...this.generics];
        } else {
          // this.dataSK11 = [];
          // this.originalSK11 = [];
        }
      } else {
        await new Promise((resolve, reject) => {
          setTimeout(resolve, 15000);
          runInAction(() => {
            this.timerCheckGeneric = reject;
          });
        });
        await this.getLastStatusGeneric();
      }
    } catch (e) {
      this.isLoadingGeneric = false;
    } finally {
      this.isLoadingGeneric = false;
    }
  }

  stopStatusGeneric() {
    this.isLoadingGeneric = false;
  }

  async getCalendar(year: number | string) {
    try {
      const { dates } = await nsi.nsiController.getCalendar(year, this.rootStore.authStore.isCenter);
      runInAction(() => {
        this.loadedDayNsi = dates;
      });
    } catch (e) {}
  }

  async initModalSettingsCharacteristics(type: any, idObject: any) {
    try {
      this.isLoadingCharacters = true;
      this.isChangeCharacteristics = false;
      const { name, code, id, pbr, ppbr, pdg, per, subParams } = await nsi.nsiController.getCharacterByType(type, idObject);
      this.objectModalSettingsCharacteristics = {
        name,
        code,
        id,
        subParams,
        params: {
          pbr: pbr.params
            .map((el: any) => {
              return { ...el, tabId: el.opamCode, isDisableChecked: !el.isActive };
            })
            .sort((a: any, b: any) => a.name - b.name),
          ppbr: ppbr.params
            .map((el: any) => {
              return { ...el, tabId: el.opamCode, isDisableChecked: !el.isActive };
            })
            .sort((a: any, b: any) => a.name - b.name),
          pdg: pdg.params
            .map((el: any) => {
              return { ...el, tabId: el.opamCode, isDisableChecked: !el.isActive };
            })
            .sort((a: any, b: any) => a.name - b.name),
          per: per.params
            .map((el: any) => {
              return { ...el, tabId: el.opamCode, isDisableChecked: !el.isActive };
            })
            .sort((a: any, b: any) => a.name - b.name),
        },
      };
      this.objectModalSettingsCharacteristicsOriginal = {
        name,
        code,
        id,
        subParams,
        params: {
          pbr: pbr.params
            .map((el: any) => {
              return { ...el, tabId: el.opamCode, isDisableChecked: !el.isActive };
            })
            .sort((a: any, b: any) => a.name - b.name),
          ppbr: ppbr.params
            .map((el: any) => {
              return { ...el, tabId: el.opamCode, isDisableChecked: !el.isActive };
            })
            .sort((a: any, b: any) => a.name - b.name),
          pdg: pdg.params
            .map((el: any) => {
              return { ...el, tabId: el.opamCode, isDisableChecked: !el.isActive };
            })
            .sort((a: any, b: any) => a.name - b.name),
          per: per.params
            .map((el: any) => {
              return { ...el, tabId: el.opamCode, isDisableChecked: !el.isActive };
            })
            .sort((a: any, b: any) => a.name - b.name),
        },
      };
    } catch (e) {
    } finally {
      this.isLoadingCharacters = false;
    }
  }

  getParams(obj: any, selectedButton: any, value: any) {
    let res = obj;
    res[selectedButton] = res[selectedButton].map((item: any) => {
      const isFind = value?.some((el: any) => el === item.tabId);
      if (isFind) {
        return { ...item, isUsed: true };
      } else {
        return { ...item, isUsed: false };
      }
    });
    return res;
  }

  changeCharacteristics(selectedButton: any, value: any) {
    this.isChangeCharacteristics = true;
    this.objectModalSettingsCharacteristics = {
      ...this.objectModalSettingsCharacteristics,
      params: this.getParams(this.objectModalSettingsCharacteristics?.params, selectedButton, value),
    };
  }

  resetCharacteristics() {
    this.isChangeCharacteristics = false;
    this.objectModalSettingsCharacteristics = { ...this.objectModalSettingsCharacteristicsOriginal };
  }

  async saveCharacteristics(type: any, idObject: any) {
    try {
      let items = [];
      const params = this.objectModalSettingsCharacteristics.params;
      for (let key in params) {
        items.push({
          pgType: key.toUpperCase(),
          items: params[key].map((el: any) => ({ code: el.tabId, toSend: el.isUsed })),
        });
      }
      await nsi.nsiController.pushCharactersNotConnect(type, idObject, items);
      this.rootStore.notificationStore.addNotification({
        title: "Сохранение",
        description: "Изменения сохранены",
        icon: "save",
        type: "information",
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  async deleteCharacteristics(object: any) {
    try {
      if (object.id) {
      } else {
        const [type, opamCode] = object.tabId.split("-");
        await nsi.nsiController.deleteCharacteristics(type, opamCode);
      }
      this.rootStore.notificationStore.addNotification({
        title: "Удаление",
        description: `Объект ${object.name} удален !`,
        icon: "trash",
        type: "information",
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  async deleteSubs(object: any) {
    try {
      await nsi.nsiController.deleteSubs(object.id);
      this.rootStore.notificationStore.addNotification({
        title: "Удаление",
        description: `Объект ${object.name} удален !`,
        icon: "trash",
        type: "error",
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  prepareCharacteristics(array: any, level: number, type: any, parent: any) {
    return array.map((item: any) => {
      const childs = item.params
        ? this.prepareCharacteristics(item.params, level + 1, item.code, item.code)
        : item.subs
        ? this.prepareCharacteristics(item.subs, level + 1, item.code, item.code)
        : null;
      // const opamCode = item.code ? item.code : item.opamCode;
      const finalType = type ? `${type}` : ``;
      const finalOpamCode = item.opamCode ? `${item.opamCode}` : ``;
      const tabId = item.id ? String(item.id) : `${finalType}-${finalOpamCode}`;
      return { ...item, childs, tabId: tabId.length > 1 ? tabId : item.code, level, parent, isSubs: !!item.id };
    });
  }

  prepareCharacteristicsFlat(array: any, type: any) {
    let childsArray: any = [];
    const initArray = array.map((item: any) => {
      const childs: any = item.params ? this.prepareCharacteristicsFlat(item.params, item.code) : null;
      if (childs) {
        childs.map((el: any) => {
          childsArray.push(el);
        });
      }
      const finalType = type ? `${type}` : ``;
      const finalOpamCode = item.opamCode ? `${item.opamCode}` : ``;
      const tabId = `${finalType}-${finalOpamCode}`;
      return { ...item, childs, tabId: tabId.length > 1 ? tabId : item.code };
    });
    return [...initArray, ...childsArray];
  }

  async initCharacteristics() {
    try {
      this.isLoadingCharacteristics = true;
      const { items } = await nsi.nsiController.getListCharacteristics(this.rootStore.authStore.isCenter);
      this.characteristics = this.prepareCharacteristics(items, 0, null, null);
      this.characteristicsOriginal = this.prepareCharacteristics(items, 0, null, null);
      this.characteristicsFlat = this.prepareCharacteristicsFlat(items, null);
    } catch (e) {
    } finally {
      this.isLoadingCharacteristics = false;
    }
  }

  async getUnrelated(type: any) {
    try {
      this.isLoadingUnrelated = true;
      const { items } = await nsi.nsiController.getUnrelated(type);
      this.unrelated = items.map((el) => ({ value: el, label: el }));
    } catch (e) {
    } finally {
      this.isLoadingUnrelated = false;
    }
  }

  async saveUnrelated({ opamCode, name, type }: { opamCode: any; name: any; type: any }) {
    try {
      const items = [{ opamCode, name }];
      await nsi.nsiController.saveCharacteristics(type, items);
      this.rootStore.notificationStore.addNotification({
        title: "Создание",
        description: "Характеристика создана",
        icon: "create",
        type: "information",
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  async editCharacters(items: any) {
    try {
      const isComplete = await Promise.all(
        items.map(async (el: any) => {
          const type = el.tabId.split("-")[0];
          const opamCode = el.tabId.split("-")[1];
          const items = [
            { opamCode, uuidCk11: el.uuidCk11, megaPointCode: el.megaPointCode, isPbr: el.isPbr, isPpbr: el.isPpbr, isPdg: el.isPdg, isPer: el.isPer, name: el.name },
          ];
          await nsi.nsiController.editCharacteristics(type, items);
        })
      )
        .then(() => {
          this.rootStore.notificationStore.addNotification({
            title: "Сохранение",
            description: "Данные сохранены",
            icon: "save",
            type: "information",
          });
          return true;
        })
        .catch((e) => {
          return false;
        });
      return isComplete;
    } catch (e) {
      return false;
    }
  }

  async startDistributionCharacteristics() {
    try {
      await nsi.nsiController.startDistributionCharacteristics();
      this.rootStore.notificationStore.addNotification({
        title: "Распространение",
        description: "Распространение запущено",
        icon: "start",
        type: "done",
      });
    } catch (e) {}
  }

  async getProtocolCharacteristics() {
    try {
      const { status, startedAt, finishedAt, error, notSentTo } = await nsi.nsiController.getProtocolDistributionCharacteristics();
      this.protocolCharacteristics = { status, startedAt, finishedAt, error, notSentTo };
    } catch (e) {}
  }

  stopProtocolSettings() {
    this.timerIdProtocolSettings();
  }

  async startCheckCharacteristicsProtocol() {
    try {
      await this.getProtocolCharacteristics();
      this.isProcessingCharacteristicsDistribution = true;
      while (this.isProcessingCharacteristicsDistribution) {
        await new Promise((resolve, reject) => {
          setTimeout(resolve, 15000);
          runInAction(() => {
            this.timerIdProtocolSettings = reject;
          });
        });
        const processingTasks = this.protocolCharacteristics.status === "SENDING";
        if (!processingTasks) {
          this.isProcessingCharacteristicsDistribution = false;
        } else {
          await this.startCheckCharacteristicsProtocol();
        }
      }
    } catch (e) {
      this.isProcessingCharacteristicsDistribution = false;
    } finally {
      this.isProcessingCharacteristicsDistribution = false;
    }
  }

  stopCheckCharacteristicsProtocol() {
    this.isProcessingCharacteristicsDistribution = false;
  }

  async saveTypeCharacteristics(value: any, paramCode: any, paramType: any, id: any, uuidCk11: any) {
    try {
      if (id) {
        const objectPost = { name: value, uuidCk11 };
        await nsi.nsiController.editSubs(objectPost, id);
        this.rootStore.notificationStore.addNotification({
          title: "Редактирование",
          description: "Характеристика изменена",
          icon: "create",
          type: "done",
        });
      } else {
        const objectPost = { name: value, paramCode, paramType, uuidCk11 };
        await nsi.nsiController.saveSubs(objectPost);
        this.rootStore.notificationStore.addNotification({
          title: "Создание",
          description: "Характеристика создана",
          icon: "create",
          type: "done",
        });
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  async exportXLSProtocolErrors() {
    try {
      const response: any = await nsi.nsiController.getProtocolErrorsXLS(this.lastTaskIdNsi);
      saveAs(new Blob([response]), `Протокол ошибок.xls`);
    } catch (e) {}
  }

  async exportXLSProtocolChanges() {
    try {
      const response: any = await nsi.nsiController.getProtocolChangesXLS(this.lastTaskIdNsi);
      saveAs(new Blob([response]), `Протокол изменения данных.xls`);
    } catch (e) {}
  }

  async exportXLSProtocolComparison(filterDate1: { month: number; day: number; year: number }, filterDate2: { month: number; day: number; year: number }) {
    try {
      const date1 = `${filterDate1.year}-${this.prepareVariable(filterDate1.month)}-${this.prepareVariable(filterDate1.day)}`;
      const date2 = `${filterDate2.year}-${this.prepareVariable(filterDate2.month)}-${this.prepareVariable(filterDate2.day)}`;
      const date = { date1, date2 };
      const response: any = await nsi.nsiController.getProtocolComparisonXLS(date, this.rootStore.authStore.isCenter);
      saveAs(new Blob([response]), `Протокол сравнения данных.xls`);
    } catch (e) {}
  }

  getTitleNotification(type: any, date: string) {
    const [data, time] = date.split("T");
    const [year, month, day] = data.split("-");
    const [hour, min] = time.split(":");
    const finalDate = `${day}-${month}-${year} ${hour}_${min}`;
    if (type === "ALL") {
      return `Перечень сопоставленных и не сопоставленных объектов СРПГ с ОИК СК-11 (${finalDate})`;
    }
    if (type === "WITH_ID11") {
      return `Перечень сопоставленных объектов СРПГ с ОИК СК-11 (${finalDate})`;
    }
    if (type === "NO_ID11") {
      return `Перечень не сопоставленных объектов СРПГ с ОИК СК-11 (${finalDate})`;
    }
    if (type === "filter") {
      return `Объекты СРПГ с ОИК СК-11 по критериям фильтрации (${finalDate})`;
    }
  }

  async exportCSV(year: any, month: any, day: any, type: any, dateTime: any, registry: any, subRegistry: any, rk: any) {
    try {
      runInAction(() => {
        this.isExportingCSV = true;
      });

      if (type === "filter") {
        const date = `${year}-${this.prepareVariable(month)}-${this.prepareVariable(day)}`;
        const reportKind = type;
        const objectPost = { date, reportKind };
        const registryType: any = this.getTypePakEss(registry);
        const objectType = this.getLoadType(registry, subRegistry);
        const message = this.getTitleNotification(reportKind, dateTime);
        const response: any = await nsi.nsiController.getProtocolComparisonCSVFilter(registryType, objectType, date, this.rootStore.authStore.isCenter, rk);
        saveAs(new Blob([response]), `${message}.csv`);
      } else {
        const date = `${year}-${this.prepareVariable(month)}-${this.prepareVariable(day)}`;
        const reportKind = type;
        const objectPost = { date, reportKind };
        const message = this.getTitleNotification(reportKind, dateTime);
        const response: any = await nsi.nsiController.getProtocolComparisonCSV(objectPost, this.rootStore.authStore.isCenter);
        saveAs(new Blob([response]), `${message}.csv`);
      }
    } catch (e) {
    } finally {
      runInAction(() => {
        this.isExportingCSV = false;
      });
    }
  }

  async getSearchForMeasurements() {
    try {
      this.isProcessingMeasurements = true;
      while (this.isProcessingMeasurements) {
        const { taskId } = await nsi.nsiController.getSearchForMeasurementsLastTaskId();
        runInAction(() => {
          this.lastTaskMeasurements = taskId;
        });
        if (this.lastTaskMeasurements) {
          const { status, startedAt, finishedAt, error } = await nsi.nsiController.getSearchForMeasurements(this.lastTaskMeasurements);
          this.statusMeasurements = { status, startedAt, finishedAt, error };
        }
        const processingTasks = !!this.lastTaskMeasurements && this?.statusMeasurements?.status === "CREATED";
        await delay(1000);
        if (!processingTasks) {
          this.isProcessingMeasurements = false;
        } else {
          await this.getSearchForMeasurements();
        }
      }
    } catch (e) {
    } finally {
      this.isProcessingMeasurements = false;
    }
  }

  async searchForMeasurements() {
    try {
      const { taskId } = await nsi.nsiController.startSearchForMeasurements();
      runInAction(() => {
        this.lastTaskMeasurements = taskId;
      });
      await this.getSearchForMeasurements();
    } catch (e) {}
  }

  async exportXMLMeasurements(date: Date) {
    try {
      const response: any = await nsi.nsiController.getSearchForMeasurementsXML(this.lastTaskMeasurements);
      // saveAs(new Blob([response]), `Поиск измерений в ОИК СК-11 (${day}-${month}-${year}).xml`);
      const day = date.getDate() > 9 ? date.getDate() : `0${date.getDate()}`;
      const month = Number(date.getMonth() + 1) > 9 ? Number(date.getMonth() + 1) : `0${Number(date.getMonth() + 1)}`;
      const year = date.getFullYear();
      const hours = date.getHours() > 9 ? date.getHours() : `0${date.getHours()}`;
      const min = date.getMinutes() > 9 ? date.getMinutes() : `0${date.getMinutes()}`;
      saveAs(new Blob([response]), `Поиск измерений в ОИК СК-11 (${day}-${month}-${year} ${hours}_${min}).xml`);
    } catch (e) {}
  }

  async getDuplicateById(uuid: any, date: string) {
    try {
      this.isLoadingDuplicate = true;
      const { items, nameCk11 } = await nsi.nsiController.getDuplicateById(uuid, date, this.rootStore.authStore.isCenter);
      this.duplicateListById = items.map((el, index) => {
        const parents = el.parents && el.parents.length > 0 ? el.parents.join(" / ") : " - ";
        return {
          ...el,
          tabId: el.id,
          name: el.foundNsiDate ?? el.name,
          nameCk11,
          parents,
          isRemovePossible: el.isRemovePossible,
          isAfter: el?.isAfter ?? null,
          isDate: !!el.foundNsiDate,
          key: index,
        };
      });
    } catch (e) {
    } finally {
      this.isLoadingDuplicate = false;
    }
  }

  generateColor() {
    return "#" + Math.floor(Math.random() * 16777215).toString(16);
  }

  async getMultipleConnections(date: any) {
    try {
      this.isLoadingMultipleConnections = true;
      const objectPost = {
        date,
      };
      const { items } = await nsi.nsiController.getMultipleConnections(this.rootStore.authStore.isCenter, objectPost);
      const result: any = [];
      items.map((el: any, index: number) => {
        const color = this.generateColor();
        el.links.map((item: any) => {
          const essParents = item.essParents ? item.essParents.join(" / ") : " - ";
          const ck11Parents = el.ck11Parents ? el.ck11Parents.join(" / ") : " - ";
          result.push({
            essParents,
            essName: item.foundNsiDate ?? item.essName,
            type: item.type.name,
            ck11Parents,
            ck11Name: el.ck11Name,
            uuid: el.uuid,
            id: item.id,
            // tabId: String(item.id),
            tabId: generateUUID(),
            typeCode: item.type.code,
            group: index,
            color,
            isRemovePossible: item.isRemovePossible,
            isAfter: item?.isAfter ?? null,
            isDate: !!item.foundNsiDate,
          });
        });
      });
      this.multipleConnections = result;
    } catch (e) {
    } finally {
      this.isLoadingMultipleConnections = false;
    }
  }

  async exportXlsMultipleConnections(date: any) {
    try {
      const objectPost = { date };
      const response: any = await nsi.nsiController.exportXlsMultipleConnections(this.rootStore.authStore.isCenter, objectPost);
      saveAs(new Blob([response]), `Множественные связи.xls`);
    } catch (e) {}
  }

  async multipleConnectionsTry(date: any) {
    try {
      this.isCheckConnectionTry = true;
      const objectPost = { date };
      const { taskId } = await nsi.nsiController.startTryMultipleConnections(this.rootStore.authStore.isCenter, objectPost);
      await this.multipleConnectionsTryGetInfo(taskId);
    } catch (e) {}
  }

  stopMultipleConnections() {
    this.multipleConnectionsTimerId();
    this.isCheckConnectionTry = false;
    this.infoMultipleConnections = { status: null };
  }

  async multipleConnectionsTryGetInfo(taskId: any) {
    try {
      const { status, startedAt, finishedAt, error, result } = await nsi.nsiController.getTryMultipleConnections(this.rootStore.authStore.isCenter, taskId);
      this.infoMultipleConnections = { status };
      if (status === "DONE" || status === "FAILED") {
        this.isCheckConnectionTry = false;
        if (result?.items?.length > 0) {
          const final: any = [];
          result?.items?.map((el: any, index: number) => {
            const color = this.generateColor();
            el.links.map((item: any) => {
              const essParents = item.essParents ? item.essParents.join(" / ") : " - ";
              const ck11Parents = el.ck11Parents ? el.ck11Parents.join(" / ") : " - ";
              final.push({
                essParents,
                essName: item.foundNsiDate ?? item.essName,
                type: item.type.name,
                ck11Parents,
                ck11Name: el.ck11Name,
                uuid: el.uuid,
                id: item.id,
                // tabId: String(item.id),
                // tabId: generateUUID(),
                typeCode: item.type.code,
                group: index,
                color,
                isRemovePossible: item.isRemovePossible,
                isAfter: item?.isAfter ?? null,
                isDate: !!item.foundNsiDate,
                rowColor: "orange",
              });
            });
          });
          const res = final.map((el: any) => {
            const find = this.multipleConnections.find((item) => item.id === el.id); //tabId
            if (find) {
              const findEssName = find.essName === "Не найдено имя" ? el.essName : find.essName;
              const essParents = find.essName === "Не найдено имя" ? el.essName : find.essParents;
              return { ...el, color: find.color, tabId: find.tabId, essName: findEssName, essParents, isDate: find?.isDate ?? el.isDate };
            }
            return { ...el };
          });
          this.multipleConnections = res;
        } else {
          this.multipleConnections = [];
        }
      } else {
        await new Promise((resolve, reject) => {
          setTimeout(resolve, 15000);
          runInAction(() => {
            this.multipleConnectionsTimerId = reject;
          });
        });
        if (this.isCheckConnectionTry) {
          await this.multipleConnectionsTryGetInfo(taskId);
        }
      }
    } catch (e) {}
  }

  async multipleSearchNameById(date: string, essId: string, essTypeCode: string, tabId: string) {
    try {
      const objectPost = { date, essId, essTypeCode };
      const { essName, essParents: essParentsTemp } = await nsi.nsiController.getMultipleById(this.rootStore.authStore.isCenter, objectPost);
      this.multipleConnections = this.multipleConnections.map((el) => {
        if (el.tabId === tabId) {
          const essParents = essParentsTemp ? essParentsTemp.join(" / ") : " - ";
          return { ...el, essName, essParents, isDate: false };
        }
        return { ...el };
      });
    } catch (e) {}
  }

  async duplicateSearchNameById(date: string, essId: string, essTypeCode: string) {
    try {
      const objectPost = { date, essId, essTypeCode };
      const { essName, essParents: essParentsTemp } = await nsi.nsiController.getMultipleById(this.rootStore.authStore.isCenter, objectPost);
      this.duplicateListById = this.duplicateListById.map((el) => {
        if (String(el.tabId) === String(essId)) {
          const essParents = essParentsTemp ? essParentsTemp.join(" / ") : " - ";
          return { ...el, name: essName, parents: essParents, isDate: false };
        }
        return { ...el };
      });
    } catch (e) {}
  }

  resetDuplicate() {
    this.duplicateListById = [];
  }

  prepareUnLinkSk11(array: any[], uuid: any) {
    return array.map((el) => {
      let childs = el?.childs ?? [];
      if (childs.length > 0) {
        childs = this.prepareUnLinkSk11(childs, uuid);
      }
      if (el.tabId === uuid) {
        return { ...el, childs, isBound: false };
      }
      return { ...el, childs };
    });
  }

  prepareUnLink(array: any[], selectedArray: any[]) {
    return array.map((el: any) => {
      let childs = el?.childs ?? [];
      if (childs.length > 0) {
        childs = this.prepareUnLink(childs, selectedArray);
      }
      const isFind = selectedArray.some((item) => item.id === el.id);
      if (isFind) {
        return { ...el, id11: null, childs };
      }
      return { ...el, childs };
    });
  }

  unLinkMultiConnection(selectedArray: any[], uuid: any, isRge: boolean, selectedRge: any) {
    if (this.duplicateListById.length === selectedArray.length) {
      if (isRge) {
        this.rge[selectedRge] = this.prepareUnLinkSk11(this.rge[selectedRge], uuid);
      } else {
        this.sk11 = this.prepareUnLinkSk11(this.sk11, uuid);
      }
      runInAction(() => {
        this.objectPakEss = this.prepareUnLink(this.objectPakEss, selectedArray);
      });
    }
  }

  unLinkMultipleConnections(array: any[], isRge: boolean, selectedRge: any) {
    const allGroups: any = {};
    this.multipleConnections.map((el: any) => {
      allGroups[el.group] = allGroups[el.group] ? allGroups[el.group] + 1 : 1;
    });
    const selectedGroups: any = {};
    array.map((el: any) => {
      const group = this.multipleConnections.find((item) => item.id == el.tabId).group;
      selectedGroups[group] = selectedGroups[group] ? selectedGroups[group] + 1 : 1;
    });
    const keys = Object.keys(selectedGroups);

    keys.map((el) => {
      if (allGroups[el] === selectedGroups[el]) {
        const id11 = this.multipleConnections.find((item) => item.group === Number(el)).uuid;
        if (isRge) {
          this.rge[selectedRge] = this.prepareUnLinkSk11(this.rge[selectedRge], id11);
        } else {
          this.sk11 = this.prepareUnLinkSk11(this.sk11, id11);
        }
      }
    });

    this.objectPakEss = this.prepareUnLink(this.objectPakEss, array);
  }

  async saveSub(subId: any, type: any, id: any) {
    try {
      await nsi.nsiController.saveParamsSubs(subId, type, id);
      return true;
    } catch (e) {
      return false;
    }
  }

  async initTypesModal(type: any) {
    try {
      const { items } = await nsi.nsiController.getListSubsWithParams(type, this.rootStore.authStore.isCenter);
      this.typesForModal = items.map((el: any) => {
        const childs = el.params;
        const uuid = generateUUID();
        return {
          ...el,
          tabId: uuid,
          type: "params",
          childs: childs.map((item: any) => {
            const uuidChild = generateUUID();
            return { ...item, tabId: uuidChild, type: "subParams", parentId: uuid };
          }),
        };
      });
      const { items: params } = await nsi.nsiController.getListParamsByObject(type);
      this.listParams = params.map((el: any) => ({ value: el.code, label: el.name, ...el }));
    } catch (e) {}
  }

  async saveTypesModal(data: any, type: any) {
    try {
      const items = data.map((el: any) => {
        return {
          name: el.name,
          params: el.childs.map((item: any) => {
            return { subParamId: item.subParamId, code: item.code, uuidCk11: item.uuidCk11 };
          }),
        };
      });
      await nsi.nsiController.saveTypesWithParams(type, items);
      this.rootStore.notificationStore.addNotification({
        title: "Сохранение",
        description: "Данные сохранены",
        icon: "save",
        type: "information",
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  async getListSubs(type: any, id: any) {
    try {
      this.isLoadingSubs = true;
      const finalType = "gen";
      const { names } = await nsi.nsiController.getSubsForObject(finalType, id);
      this.subs = names.map((el: any) => ({ value: el, label: el }));
    } catch (e) {
    } finally {
      this.isLoadingSubs = false;
    }
  }

  prepareSub(arr: any, type: any, id: any, name: any) {
    return arr.map((el: any) => {
      let childs = [];
      if (el?.childs?.length > 0) {
        childs = this.prepareSub(el?.childs, type, id, name);
      }
      if (el.type === type && el.id === id) {
        return { ...el, childs, paramType: name };
      }
      return { ...el, childs };
    });
  }

  async saveSelectedSub(type: any, id: any, name: any) {
    try {
      const finalType = "gen";
      // this.objectPakEss = this.prepareSub(this.objectPakEss, type, id, name);
      await nsi.nsiController.saveSubsForObject(finalType, id, name);
      this.rootStore.notificationStore.addNotification({
        title: "Сохранение",
        description: "Данные сохранены",
        icon: "success",
        type: "information",
      });
    } catch (e) {}
  }

  clearTables() {
    this.registries = [];
    this.srpgs = {
      archmGroups: [],
      area2s: [],
      areas: [],
      consumers: [],
      nBlocks: [],
      nGroups: [],
      powerSystems: [],
      rges: [],
      sechens: [],
      vetvs: [],
      wSums: [],
    };
    this.distribution = [];
    this.dataSK11 = [];
    this.objectPakEss = [];
  }
}
