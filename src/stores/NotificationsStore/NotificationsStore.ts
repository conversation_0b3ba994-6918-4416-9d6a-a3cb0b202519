import { RootStore } from "stores/RootStore";
import { makeAutoObservable, runInAction } from "mobx";
import { settingsApi } from "../../api/settings";

export type RowNotificationItem = {
  tabId: string;
  name: string;
  email: boolean | null;
  originalEmail: boolean | null;
  type: "center" | "control";
  isGroup?: boolean;
  kind?: string;
};

// Удалены выводы в консоль в связи с обращением СИБов (SRPG-2840)
export class NotificationsStore {
  notifications: RowNotificationItem[] = [];
  rootStore: RootStore;

  constructor(rootStore: RootStore) {
    makeAutoObservable(this);
    this.rootStore = rootStore;
  }

  getNotifications = async () => {
    try {
      const isCenter = this.rootStore.authStore.isCenter;

      const { groups } = await settingsApi.settingsController.getNotifications();

      // Сортируем группы по полю order
      const sortedGroups = [...groups].sort((a, b) => a.order - b.order);

      const formattedNotifications: RowNotificationItem[] = [];

      // Формируем плоский список групп и их уведомлений
      sortedGroups.forEach((group) => {
        // Добавляем заголовок группы
        formattedNotifications.push({
          tabId: `group-${group.order}-${isCenter ? "center" : "control"}`,
          name: group.title,
          email: null, // Для группы нет чекбокса
          originalEmail: null,
          type: isCenter ? "center" : "control",
          isGroup: true,
        });

        // Добавляем уведомления группы
        group.mails.forEach((mail) => {
          formattedNotifications.push({
            tabId: `mail-${mail.kind}-${isCenter ? "center" : "control"}`,
            name: mail.title,
            email: mail.selected,
            originalEmail: mail.selected,
            type: isCenter ? "center" : "control",
            kind: mail.kind,
          });
        });
      });

      runInAction(() => {
        this.notifications = formattedNotifications;
      });
    } catch (e) {}
  };

  toggleNotification = (tabId: string) => {
    const index = this.notifications.findIndex((item) => item.tabId === tabId);
    if (index !== -1 && this.notifications[index].email !== null) {
      this.notifications[index].email = !this.notifications[index].email;
    }
  };

  get hasChanges() {
    return this.notifications.some((item) => item.email !== item.originalEmail && !item.isGroup);
  }

  resetChanges = () => {
    this.notifications = this.notifications.map((item) => ({
      ...item,
      email: item.originalEmail,
    }));
  };

  saveData = async () => {
    try {
      // Сохраняем только значения kind для выбранных уведомлений
      const items = this.notifications.filter((item) => !item.isGroup && item.email).map((item) => item.kind!);

      await settingsApi.settingsController.saveNotifications(items);

      return true;
    } catch (e) {
      return false;
    }
  };
}
