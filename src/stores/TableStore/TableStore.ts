import { RootStore } from "stores/RootStore";
import { observable, action, makeAutoObservable, runInAction } from "mobx";
import { userStorageApi } from "../../api/userStorage";

// Удалены выводы в консоль в связи с обращением СИБов (SRPG-2840)
export class TableStore {
  rootStore: RootStore;
  constructor(rootStore: RootStore) {
    makeAutoObservable(this, {
      setTableParams: action,
    });
    this.rootStore = rootStore;
  }

  @action
  async getTableParams(tableKey: any) {
    try {
      const key = `table${tableKey}`;
      const { value } = await userStorageApi.userStorage.getTableParams(key);
      if (value) {
        return JSON.parse(value) ?? null;
      } else {
        return null;
      }
    } catch (e) {}
  }

  @action
  async setTableParams(data: any, tableKey: any, isSave: boolean) {
    try {
      const key = `table${tableKey}`;
      const objectPost = { value: JSON.stringify(data) };
      const isWidth = data.some((el: any) => !Number(el.width));
      if (!isWidth) {
        await userStorageApi.userStorage.setTableParams(key, objectPost);
      }
    } catch (e) {}
  }

  @action
  async setSortParams(tableKey: any, data: any) {
    try {
      const key = `sort${tableKey}`;
      const objectPost = { value: JSON.stringify(data) };
      await userStorageApi.userStorage.setTableParams(key, objectPost);
    } catch (e) {}
  }

  @action
  async getSortParams(tableKey: any) {
    try {
      const key = `sort${tableKey}`;
      const { value } = await userStorageApi.userStorage.getTableParams(key);
      if (value) {
        return JSON.parse(value) ?? null;
      } else {
        return null;
      }
    } catch (e) {}
  }

  @action
  async resetTableParams(tableKey: any) {
    const key = `table${tableKey}`;
    const objectPost = { value: JSON.stringify(null) };
    await userStorageApi.userStorage.setTableParams(key, objectPost);
  }
}
