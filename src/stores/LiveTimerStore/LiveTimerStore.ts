import { RootStore } from "../RootStore";
import { action, makeAutoObservable, observable, runInAction } from "mobx";
import { timeApi } from "../../api/time";
import { currentDate } from "../../helpers/DateUtils";

// Удалены выводы в консоль в связи с обращением СИБов (SRPG-2840)
export class LiveTimerStore {
  rootStore: RootStore;
  dateTime: any;
  dateTimeLocale: any;
  isLoadingTime: any;
  DEFAULT_YEAR: any;
  DEFAULT_MONTH: any;
  DEFAULT_DAY: any;

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore;
    makeAutoObservable(this, {
      dateTime: observable,
      isLoadingTime: observable,
      getTimeServer: action,
    });
    this.dateTime = null;
    this.DEFAULT_YEAR = null;
    this.DEFAULT_MONTH = null;
    this.DEFAULT_DAY = null;
    this.dateTimeLocale = null;
    this.isLoadingTime = true;
  }

  async getTimeServer() {
    try {
      this.isLoadingTime = true;
      const { dateTime } = await timeApi.timeController.getTime();

      runInAction(() => {
        this.dateTime = dateTime;
        this.DEFAULT_YEAR = new Date(dateTime).getFullYear();
        this.DEFAULT_MONTH = new Date(dateTime).getMonth() + 1;
        this.DEFAULT_DAY = new Date(dateTime).getDate();
      });
      return { isComplete: true, dateTime };
    } catch (e) {
      this.dateTime = null;
      return { isComplete: false };
    } finally {
      runInAction(() => {
        this.isLoadingTime = false;
      });
    }
  }

  async getTimeServerLocale() {
    try {
      this.isLoadingTime = true;
      const { dateTime } = await timeApi.timeController.getTime();
      runInAction(() => {
        this.dateTimeLocale = dateTime;
      });
      return { isComplete: true, dateTime };
    } catch (e) {
      this.dateTime = null;
      return { isComplete: false };
    } finally {
      this.isLoadingTime = false;
    }
  }

  stopTimerLocale() {
    runInAction(() => {
      this.dateTimeLocale = null;
    });
  }

  stopTimer() {
    runInAction(() => {
      this.dateTime = null;
    });
  }
}
