import { RootStore } from "../RootStore";
import { action, makeAutoObservable, observable, runInAction } from "mobx";
import { userActivityApi } from "../../api/userActivity";
import { prepareDate } from "../../helpers/DateUtils";
// @ts-ignore
import { saveAs } from "file-saver";

// Удалены выводы в консоль в связи с обращением СИБов (SRPG-2840)
export class UserActionsStore {
  rootStore: RootStore;
  isLoadingUsersPage: boolean;
  isLoadingExternalSystems: boolean;
  isOverfilled: boolean;
  usersJournal: any[];
  usersJournalOriginal: any[];
  externalSystems: any[];
  externalSystemsOriginal: any[];

  constructor(rootStore: RootStore) {
    makeAutoObservable(this, {
      initUserPage: action,
      usersJournal: observable,
      usersJournalOriginal: observable,
      isOverfilled: observable,
      isLoadingUsersPage: observable,
      isLoadingExternalSystems: observable,
      externalSystems: observable,
      externalSystemsOriginal: observable,
    });
    this.rootStore = rootStore;
    this.isLoadingUsersPage = false;
    this.isLoadingExternalSystems = false;
    this.isOverfilled = false;
    this.usersJournal = [];
    this.usersJournalOriginal = [];
    this.externalSystems = [];
    this.externalSystemsOriginal = [];
  }

  async initUserPage(params: any) {
    this.isLoadingUsersPage = true;
    try {
      const monthFrom = params.fromDate.getMonth() + 1 > 9 ? `${params.fromDate.getMonth() + 1}` : `0${params.fromDate.getMonth() + 1}`;
      const monthTo = params.toDate.getMonth() + 1 > 9 ? `${params.toDate.getMonth() + 1}` : `0${params.toDate.getMonth() + 1}`;
      const dayFrom = params.fromDate.getDate() > 9 ? `${params.fromDate.getDate()}` : `0${params.fromDate.getDate()}`;
      const dayTo = params.toDate.getDate() > 9 ? `${params.toDate.getDate()}` : `0${params.toDate.getDate()}`;
      const fromDate = `${params.fromDate.getFullYear()}-${monthFrom}-${dayFrom}`;
      const toDate = `${params.toDate.getFullYear()}-${monthTo}-${dayTo}`;
      const { items, isOverfilled } = await userActivityApi.userActivityController.getUsersJournal(fromDate, toDate, this.rootStore.authStore.isCenter);
      runInAction(() => {
        this.usersJournal = items
          // .sort((a, b) => b.recordId - a.recordId)
          .map((el) => ({ ...el, tabId: el.recordId }));
        this.usersJournalOriginal = items
          // .sort((a, b) => b.recordId - a.recordId)
          .map((el) => ({ ...el, tabId: el.recordId }));
        this.isOverfilled = isOverfilled;
      });
    } catch (e) {
    } finally {
      runInAction(() => {
        this.isLoadingUsersPage = false;
      });
    }
  }

  async initExternalSystems(params: any) {
    this.isLoadingExternalSystems = true;
    try {
      const monthFrom = params.fromDate.getMonth() + 1 > 9 ? `${params.fromDate.getMonth() + 1}` : `0${params.fromDate.getMonth() + 1}`;
      const monthTo = params.toDate.getMonth() + 1 > 9 ? `${params.toDate.getMonth() + 1}` : `0${params.toDate.getMonth() + 1}`;
      const dayFrom = params.fromDate.getDate() > 9 ? `${params.fromDate.getDate()}` : `0${params.fromDate.getDate()}`;
      const dayTo = params.toDate.getDate() > 9 ? `${params.toDate.getDate()}` : `0${params.toDate.getDate()}`;
      const fromDate = `${params.fromDate.getFullYear()}-${monthFrom}-${dayFrom}`;
      const toDate = `${params.toDate.getFullYear()}-${monthTo}-${dayTo}`;
      const { items, isOverfilled } = await userActivityApi.userActivityController.getExternalSystems(fromDate, toDate, this.rootStore.authStore.isCenter);
      runInAction(() => {
        this.externalSystems = items
          // .sort((a, b) => b.id - a.id)
          .map((el) => ({ ...el, tabId: el.id }));
        this.externalSystemsOriginal = items
          // .sort((a, b) => b.id - a.id)
          .map((el) => ({ ...el, tabId: el.id }));
        this.isOverfilled = isOverfilled;
      });
    } catch (e) {
    } finally {
      runInAction(() => {
        this.isLoadingExternalSystems = false;
      });
    }
  }

  async exportXLS(viewPage: string, fDate: any, tDate: any) {
    try {
      const monthFrom = fDate.getMonth() + 1 > 9 ? `${fDate.getMonth() + 1}` : `0${fDate.getMonth() + 1}`;
      const monthTo = tDate.getMonth() + 1 > 9 ? `${tDate.getMonth() + 1}` : `0${tDate.getMonth() + 1}`;
      const dayFrom = fDate.getDate() > 9 ? `${fDate.getDate()}` : `0${fDate.getDate()}`;
      const dayTo = tDate.getDate() > 9 ? `${tDate.getDate()}` : `0${tDate.getDate()}`;
      const fromDate = `${fDate.getFullYear()}-${monthFrom}-${dayFrom}`;
      const toDate = `${tDate.getFullYear()}-${monthTo}-${dayTo}`;
      let response: any;
      if (viewPage === "externalSystems") {
        response = await userActivityApi.userActivityController.exportXLSExternalSystems(fromDate, toDate, this.rootStore.authStore.isCenter);
      } else {
        response = await userActivityApi.userActivityController.exportXLSUsersJournal(fromDate, toDate, this.rootStore.authStore.isCenter);
      }
      saveAs(new Blob([response]), `${viewPage === "externalSystems" ? "Журнал взаимодействия с внешними системами" : "Журнал действий пользователей"}.xls`);
    } catch (e) {}
  }
}
