import { RootStore } from "stores/RootStore";
import { observable, action, makeAutoObservable, runInAction } from "mobx";
import { userManagerApi } from "api/userManager";
import * as storage from "utils/localStorage";
import { delay } from "../../helpers/delay";
import { getNotifications } from "../../api/userManager/userManager";
import { getRefreshToken } from "utils/localStorage";
import queryString from "query-string";
import { isModeCenter } from "../../utils/getMode";
import { logTokenEvent, maskToken } from "~/utils/tokenLogger";
import axios from "axios";

// Удалены выводы в консоль в связи с обращением СИБов (SRPG-2840)
export class AuthStore {
  department: any[];
  rootStore: RootStore;
  isAuth: boolean;
  ratio: number;
  theme: any;
  timerId: any;
  comboboxStyle: string;
  isCenter: boolean;
  isLoadingLogin: boolean;
  userInfo: {
    fio: string;
    department: string;
    mail: string;
    login: string;
    roles: string[];
    rolesRus: string[];
  };

  constructor(rootStore: RootStore) {
    makeAutoObservable(this, {
      department: observable,
      isAuth: observable,
      comboboxStyle: observable,
      userInfo: observable,
      isCenter: observable,
      isLoadingLogin: observable,
      timerId: observable,
      login: action,
      getNotifications: action,
      changeTheme: action,
    });
    this.rootStore = rootStore;
    this.department = [];
    this.isAuth = false;
    this.isLoadingLogin = false;
    this.isCenter = true;
    this.theme = null;
    this.ratio = 100;
    this.userInfo = {
      fio: "",
      department: "",
      login: "",
      mail: "",
      roles: [],
      rolesRus: [],
    };
    this.comboboxStyle = "default";
    this.timerId = () => {};
  }

  @action
  async login(login: any, password: any) {
    try {
      this.isLoadingLogin = true;
      const { token, refreshToken } = await userManagerApi.userManager.login({ login, password, toRemember: true });
      if (token && refreshToken) {
        localStorage.removeItem("notifications");
        storage.setTokens({ token, refreshToken });
        // ЛОГИРОВАНИЕ ПРИ УСПЕШНОМ ВХОДЕ
        logTokenEvent("LOGIN_SUCCESS", {
          refreshToken: maskToken(refreshToken),
        });
        await this.getUserInfo();
        this.isAuth = true;
        return { isAuth: true, userInfo: this.userInfo };
      } else {
        // ЛОГИРОВАНИЕ ПРИ НЕУСПЕШНОМ ВХОДЕ
        logTokenEvent("LOGIN_FAILED", {
          refreshToken: maskToken(refreshToken),
        });
        return { isAuth: false };
      }
    } catch (e) {
      // ЛОГИРОВАНИЕ ПРИ ОШИБКЕ ПРИ ВХОДЕ
      logTokenEvent("LOGIN_API_FAILED", { error: e instanceof Error ? e.message : String(e) });
      if (axios.isAxiosError(e)) {
        this.rootStore.notificationStore.addNotification({
          title: "Ошибка авторизации",
          description: e.response?.data?.error,
          icon: "error",
          type: "error",
        });
      }

      return { isAuth: false };
    } finally {
      this.isLoadingLogin = false;
    }
  }

  async getUserInfo() {
    try {
      const { fio, department, mail, roles, rolesRus, login } = await userManagerApi.userManager.getUserInformation();
      storage.setLogin(login);
      runInAction(() => {
        this.userInfo = { fio, department, mail, roles, rolesRus, login };
      });
      localStorage.setItem("roles", JSON.stringify(roles));
    } catch (e) {}
  }

  @action
  async logout() {
    try {
      const refreshToken: any = storage.getRefreshToken();
      // ЛОГИРОВАНИЕ ПРИ ВЫХОДЕ
      logTokenEvent("LOGOUT_INITIATED", {
        refreshToken: maskToken(refreshToken),
      });
      await userManagerApi.userManager.logout(refreshToken);
      // localStorage.removeItem("isCenter");
      // localStorage.setItem("fio", JSON.stringify(this.userInfo.fio));
    } catch (e) {
      // ЛОГИРОВАНИЕ ПРИ ОШИБКЕ ПРИ ВЫХОДЕ
      logTokenEvent("LOGOUT_API_FAILED", { error: e instanceof Error ? e.message : String(e) });
    } finally {
      // storage.clearStorage();
      this.isAuth = false;
      // window.location.replace("/login");
    }
  }

  saveCombobox(value: any) {
    this.comboboxStyle = value;
  }
  saveCenterMode(value: any) {
    this.isCenter = value;
  }

  //INFO - Стандартное уведомление (инфо) (синий)
  //
  // ERROR - Ошибка (красный)
  //
  // WARNING - Предупреждение (незначительная ошибка) (жёлтый)
  //
  // Ответ:

  getIcon(type: any) {
    if (type === "INFO") {
      return "information";
    }
    if (type === "ERROR") {
      return "error";
    }
    if (type === "WARNING") {
      return "warning";
    }
  }

  getType(type: any) {
    if (type === "INFO") {
      return "information";
    }
    if (type === "ERROR") {
      return "error";
    }
    if (type === "WARNING") {
      return "warning";
    }
  }

  stopNotifications() {
    this.timerId("Notifications stop");
  }

  async getNotifications(isModeCenter: any) {
    try {
      const { events } = await userManagerApi.userManager.getNotifications(this.rootStore.authStore.isCenter);

      // update planned schedules
      const isPg = events.some((el) => el.pgId);
      if (location.pathname === "/planned-schedules" && isPg) {
        const { day, month, year } = queryString.parse(location.search);
        const pgId = events?.find((el) => el.pgId)?.pgId ?? null;
        await this.rootStore.plannedSchedulesStore.getTableViewLoad(year, month, day, pgId, isModeCenter);
      }
      // update planned schedules
      if (events && events?.length > 0) {
        events.map((el) => {
          this.rootStore.notificationStore.addNotification({
            title: el.title,
            description: el.message,
            icon: this.getIcon(el.type),
            type: this.getType(el.type),
            isTimer: !el?.isKeepOnScreen,
          });
        });
      }
      // await delay(15000);
      await new Promise((resolve, reject) => {
        setTimeout(resolve, 15000);
        runInAction(() => {
          this.timerId = reject;
        });
      });
      await this.getNotifications(isModeCenter);
    } catch (e) {}
  }

  async changeTheme(theme: string) {
    this.theme = theme;
  }
}
