import { RootStore } from "../RootStore";
import { action, makeAutoObservable, observable, runInAction, toJS } from "mobx";
import { settingsApi } from "../../api/settings";
import { plannedSchedules } from "api/plannedSchedules";
import {
  addNoValue,
  getListNoValue,
  getListNoValueCodes,
  getMODES,
  IGetPgTypeListResponseItem,
  saveMODES,
  saveStorageDepth,
  testExternalSystem,
} from "../../api/settings/settings-controller";
import { loadRecordsEG, loadUDDG } from "../../api/plannedSchedules/plannedSchedules-controller";
import { isModeCenter } from "../../utils/getMode";
import { generateUUID } from "../../helpers/GenerationUUID";
import { prepareDataTable } from "../../utils";

// Удалены выводы в консоль в связи с обращением СИБов (SRPG-2840)
export class SettingsStore {
  rootStore: RootStore;
  pgList: any[];
  recordsEG: any[];
  recordsEGOriginal: any[];
  tableData: any[];
  pbrList: any[];
  uddgList: any[];
  uddgListOriginal: any[];
  pgListForLoadPf: any[];
  pgListForLoadPfOriginal: any[];
  pbrListOriginal: any[];
  typeAccept: any[];
  listStorageDepth: any[];
  listStorageDepthOriginal: any[];
  lackOfDataCodes: any[];
  lackOfDataOriginal: any[];
  lackOfData: any[];
  modes: any[];
  modesFlat: any[];
  isLoadingTableDC: boolean;
  isLoading: boolean;
  isLoadingLoadPG: boolean;
  isLoadingModes: boolean;
  isLoadingPg: boolean;
  isTesting: boolean;
  externalSystems: any;
  externalSystemsOriginal: any;
  pgTypeList: IGetPgTypeListResponseItem[];
  constructor(rootStore: RootStore) {
    makeAutoObservable(this, {
      initDC: action,
      deleteObject: action,
      initExternalSystems: action,
      initNumberUDDG: action,
      saveEditUDDG: action,
      initModes: action,
      saveRecordEG: action,
      changePpbr: action,
      changeStorageMonth: action,
      pgList: observable,
      uddgList: observable,
      uddgListOriginal: observable,
      tableData: observable,
      isLoadingTableDC: observable,
      isLoadingModes: observable,
      isLoadingPg: observable,
      isLoading: observable,
      pbrList: observable,
      pbrListOriginal: observable,
      typeAccept: observable,
      isLoadingLoadPG: observable,
      listStorageDepth: observable,
      listStorageDepthOriginal: observable,
      lackOfData: observable,
      lackOfDataOriginal: observable,
      lackOfDataCodes: observable,
      externalSystems: observable,
      externalSystemsOriginal: observable,
      isTesting: observable,
      modes: observable,
      modesFlat: observable,
      recordsEG: observable,
      recordsEGOriginal: observable,
    });
    this.rootStore = rootStore;
    this.pgList = [];
    this.pbrList = [];
    this.pbrListOriginal = [];
    this.uddgList = [];
    this.uddgListOriginal = [];
    this.tableData = [];
    this.pgListForLoadPf = [];
    this.pgListForLoadPfOriginal = [];
    this.listStorageDepth = [];
    this.listStorageDepthOriginal = [];
    this.typeAccept = [];
    this.lackOfData = [];
    this.lackOfDataOriginal = [];
    this.lackOfDataCodes = [];
    this.modes = [];
    this.modesFlat = [];
    this.isLoadingTableDC = false;
    this.isLoading = false;
    this.isLoadingLoadPG = false;
    this.isLoadingModes = false;
    this.isLoadingPg = false;
    this.isTesting = false;
    this.externalSystems = { names: [], systems: [] };
    this.externalSystemsOriginal = { names: [], systems: [] };
    this.pgTypeList = [];
    this.recordsEG = [];
    this.recordsEGOriginal = [];
  }

  async initDC() {
    try {
      this.isLoading = true;
      const { items } = await settingsApi.settingsController.getListPG();
      runInAction(() => {
        this.pgList = items.map(({ code, name }: { code: string; name: string }) => ({ value: code, label: name }));
      });
    } catch (e) {
    } finally {
      this.isLoading = false;
    }
  }

  async getTableDC(typePG: any) {
    try {
      runInAction(() => {
        this.isLoadingTableDC = true;
      });
      const { types } = await settingsApi.settingsController.getTableData(typePG ?? this.pgList[0]?.value);
      runInAction(() => {
        this.tableData = types.map((item: any) => {
          const childs = item.params.map((par: any) => ({ ...par, tabId: item.code + "-" + par.code, value: par.code, label: par.name }));
          return { ...item, tabId: item.code, childs };
        });
      });
    } catch (e) {
    } finally {
      runInAction(() => {
        this.isLoadingTableDC = false;
      });
    }
  }

  async saveTableDC(typePG: any, selected: any) {
    try {
      const data = {
        params: selected
          .map((sel: any) => {
            const isEl = sel.split("").some((item: any) => item === "-");
            if (isEl) {
              const [typeCode, paramCode] = sel.split("-");
              return { typeCode, paramCode };
            }
            return null;
          })
          .filter((item: any) => item !== null),
      };
      await settingsApi.settingsController.saveTableData(typePG ?? this.pgList[0].value, data);
      this.rootStore.notificationStore.addNotification({
        title: "Сохранение",
        description: "Данные сохранены",
        icon: "save",
        type: "information",
      });
    } catch (e) {}
  }

  prepareTimePpbr(time: any) {
    return time ? Number(time.split(":")[0]) : -1;
  }

  async initNumberPBR(isModeCenter: any) {
    try {
      const { items: pbrList } = await plannedSchedules.plannedSchedulesController.loadPBR(isModeCenter);
      this.pbrList = [...pbrList].map((item) => ({
        ...item,
        tabId: item.number,
        name: item.number,
        time: this.prepareTimePpbr(item.time),
      }));
      this.pbrListOriginal = [...pbrList].map((item) => ({
        ...item,
        tabId: item.number,
        name: item.number,
        time: this.prepareTimePpbr(item.time),
      }));
    } catch (e) {}
  }

  changePpbr(number: any, value: any) {
    this.pbrList = this.pbrList.map((el) => {
      if (number === el.number) {
        return { ...el, time: value };
      }
      return el;
    });
  }

  resetPbr() {
    this.pbrList = [...this.pbrListOriginal];
  }

  async initNumberUDDG(isModeCenter: any) {
    try {
      const { items: uddgList } = await plannedSchedules.plannedSchedulesController.loadUDDG(isModeCenter);
      this.uddgList = uddgList.map((item) => ({
        ...item,
        tabId: item.number,
        name: item.number,
        time: this.prepareTimePpbr(item.time),
      }));
      this.uddgListOriginal = uddgList.map((item) => ({
        ...item,
        tabId: item.number,
        name: item.number,
        time: this.prepareTimePpbr(item.time),
      }));
    } catch (e) {}
  }

  changeUddg(number: any, value: any) {
    this.uddgList = this.uddgList.map((el) => {
      if (number === el.number) {
        return { ...el, time: value };
      }
      return el;
    });
  }

  resetUddg() {
    this.uddgList = [...this.uddgListOriginal];
  }

  async initRecordEG() {
    try {
      const { items: recordsEG } = await plannedSchedules.plannedSchedulesController.loadRecordsEG();
      this.recordsEG = recordsEG.map((el) => {
        return { ...el, tabId: el.type };
      });
      this.recordsEGOriginal = recordsEG.map((el) => {
        return { ...el, tabId: el.type };
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  async saveRecordEG(items: any[]) {
    try {
      await plannedSchedules.plannedSchedulesController.saveRecordEG(items);
      this.rootStore.notificationStore.addNotification({
        title: "Сохранение",
        description: "Данные сохранены",
        icon: "save",
        type: "information",
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  preparePbrTimeForSave(time: any) {
    if (time === -1) {
      return null;
    }
    if (time >= 10) {
      return `${time}:00`;
    }
    if (time < 10) {
      return `0${time}:00`;
    }
  }

  async saveEdit(newArray: any[]) {
    try {
      const finalArray = newArray.map((item) => ({ number: item.number, time: this.preparePbrTimeForSave(item.time), nextDay: item.nextDay }));
      await settingsApi.settingsController.updatePG(finalArray);
      this.rootStore.notificationStore.addNotification({
        title: "Сохранение",
        description: "Данные сохранены",
        icon: "save",
        type: "information",
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  async saveEditUDDG(newArray: any[]) {
    try {
      const finalArray = newArray.map((item) => ({ number: item.number, time: this.preparePbrTimeForSave(item.time), nextDay: item.nextDay }));
      await settingsApi.settingsController.updateUDDG(finalArray);
      this.rootStore.notificationStore.addNotification({
        title: "Сохранение",
        description: "Данные сохранены",
        icon: "save",
        type: "information",
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  async initLoadPG() {
    try {
      this.isLoadingLoadPG = true;
      const { items } = await settingsApi.settingsController.getLoadPG();
      const { items: typeAccept } = await settingsApi.settingsController.getTypeAccept();
      runInAction(() => {
        this.typeAccept = typeAccept.map((item: any) => ({ ...item, value: item.code, label: item.name }));
      });
      runInAction(() => {
        this.pgListForLoadPf = items
          .map((item: any, index: number) => {
            const acceptType = this.typeAccept.find((el) => el.label.toUpperCase() === item.acceptType.toUpperCase()).code;
            return {
              ...item,
              acceptValue: acceptType,
              originalValue: acceptType,
              acceptTimeOriginal: item.acceptTime,
              tabId: generateUUID(),
              accept: `${item.acceptType} ${acceptType === "AUTO" ? "через" : "согласование через"} ${item.acceptTime ?? ""}`,
            };
          })
          .sort((a: any, b: any) => a.pgType - b.pgType);
        this.pgListForLoadPfOriginal = items
          .map((item: any, index: number) => {
            const acceptType = this.typeAccept.find((el) => el.label.toUpperCase() === item.acceptType.toUpperCase()).code;
            return {
              ...item,
              acceptValue: acceptType,
              originalValue: acceptType,
              acceptTimeOriginal: item.acceptTime,
              tabId: index,
              accept: `${item.acceptType} ${acceptType === "AUTO" ? "через" : "согласование через"} ${item.acceptTime ?? ""}`,
            };
          })
          .sort((a: any, b: any) => a.pgType - b.pgType);
      });
    } catch (e) {
    } finally {
      this.isLoadingLoadPG = false;
    }
  }

  async saveLoadPG(editTypes: any[], editTimes: any[], editChecked: any[], editOffset: any[]) {
    try {
      this.isLoadingLoadPG = true;
      const items = this.pgListForLoadPf.map((item) => {
        const acceptType = editTypes.find((el) => el.id === item.tabId)?.value ?? item.acceptValue;
        const acceptTime = editTimes.find((el) => el.id === item.tabId)?.value ?? item.acceptTime;
        const loadOffsetInDays = editOffset.find((el) => el.id === item.tabId)?.value ?? item.loadOffsetInDays;
        const addersCalculation = editChecked.some((el) => el === item.tabId);
        return {
          pgType: item.pgType,
          pgCode: item.pgCode,
          acceptType,
          acceptTime: acceptType === "AUTO" ? acceptTime : null,
          addersCalculation,
          loadOffsetInDays,
        };
      });
      await settingsApi.settingsController.saveLoadPG(items);
      await this.initLoadPG();
      this.rootStore.notificationStore.addNotification({
        title: "Сохранение",
        description: "Изменения сохранены",
        icon: "save",
        type: "information",
      });
    } catch (e) {
    } finally {
      this.isLoadingLoadPG = false;
    }
  }

  async initStorageDepth() {
    try {
      const type: any = { NSI: "НСИ", PG: "ПГ", JOURNAL: "Журналы системы" };
      const { items } = await settingsApi.settingsController.loadStorageDepth(this.rootStore.authStore.isCenter);
      this.listStorageDepth = items
        .map((item) => {
          const name = type[item.name];
          return { ...item, name, tabId: generateUUID(), originalMonths: String(item.months), months: String(item.months) };
        })
        .sort((a, b) => a.name - b.name);
    } catch (e) {}
  }

  changeStorageMonth(name: any, value: any) {
    this.listStorageDepth = this.listStorageDepth.map((el) => {
      if (el.name === name) {
        return { ...el, months: value };
      }
      return el;
    });
  }

  resetStorageMonths() {
    this.listStorageDepth = this.listStorageDepth.map((el) => {
      return { ...el, months: el.originalMonths };
    });
  }

  async saveStorageDepth(res: any) {
    try {
      const type: any = { НСИ: "NSI", ПГ: "PG", "Журналы системы": "JOURNAL" };
      const items = res.map((el: any) => ({ name: type[el.name], months: el.months }));
      await settingsApi.settingsController.saveStorageDepth(items, this.rootStore.authStore.isCenter);
      this.rootStore.notificationStore.addNotification({
        title: "Сохранение",
        description: "Изменения сохранены",
        icon: "save",
        type: "information",
      });
    } catch (e) {}
  }

  async initLackOfData() {
    try {
      if (this.rootStore.authStore.isCenter && isModeCenter) {
        const { items: codes } = await settingsApi.settingsController.getListNoValueCodes();
        this.lackOfDataCodes = codes.map((el: string) => ({ value: el, label: el }));
      }
      const { items } = await settingsApi.settingsController.getListNoValue(this.rootStore.authStore.isCenter);
      this.lackOfData = items.map((el: any) => ({ name: el.code, value: el.noValue, tabId: el.code }));
      this.lackOfDataOriginal = items.map((el: any) => ({ name: el.code, value: el.noValue, tabId: el.code }));
    } catch (e) {}
  }

  async addObject(object: any, isEditMode: boolean) {
    try {
      const { code, noValue } = object;
      const items = [{ code, noValue }];
      if (isEditMode) {
        await settingsApi.settingsController.editNoValue(items);
        this.rootStore.notificationStore.addNotification({
          title: "Сохранение",
          description: "Объект сохранен",
          icon: "save",
          type: "information",
        });
      } else {
        await settingsApi.settingsController.addNoValue(items);
        this.rootStore.notificationStore.addNotification({
          title: "Добавление",
          description: "Объект добавлен",
          icon: "save",
          type: "information",
        });
      }
      return true;
    } catch (e) {
      return false;
    }
  }
  async deleteObject(id: any) {
    try {
      const items = [id];
      await settingsApi.settingsController.deleteNoValue(items);
      this.rootStore.notificationStore.addNotification({
        title: "Удаление",
        description: "Объект удален",
        icon: "trash",
        type: "information",
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  async initExternalSystems() {
    try {
      const { names, systems } = await settingsApi.settingsController.getInformationExternalSystem(this.rootStore.authStore.isCenter);
      runInAction(() => {
        this.externalSystems = { names: names.map((el: any) => ({ value: el.code, label: el.name })), systems };
      });
      runInAction(() => {
        this.externalSystemsOriginal = { names: names.map((el: any) => ({ value: el.code, label: el.name })), systems };
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  async saveExternalSystems(objectPOST: any) {
    try {
      await settingsApi.settingsController.saveExternalSystem(objectPOST);
      this.rootStore.notificationStore.addNotification({
        title: "Сохранение",
        description: "Данные сохранены",
        icon: "save",
        type: "information",
      });
    } catch (e) {}
  }

  async testExternalSystems(objectPOST: any) {
    try {
      this.isTesting = true;
      const { results, services } = await settingsApi.settingsController.testExternalSystem(objectPOST);
      services.map((el: any) => {
        this.rootStore.notificationStore.addNotification({
          title: `Не удалось установить соединение с сервисом`,
          description: el,
          icon: "error",
          type: "error",
          isWordWrap: true,
        });
      });
      results.map((el: any) => {
        const { system, isSuccess, message, address, service } = el;
        if (isSuccess) {
          this.rootStore.notificationStore.addNotification({
            // title: `Соединение с ${system} для сервиса ${service} успешно установлено по адресу ${address}`,
            title: `Соединение с ${system} из сервиса ${service} успешно`,
            description: `Успешное соединение по адресу ${address}`,
            icon: "success",
            type: "information",
            isWordWrap: true,
          });
        } else {
          this.rootStore.notificationStore.addNotification({
            title: `Не удалось установить соединение с ${system} для сервиса ${service} по адресу ${address}`,
            description: message,
            icon: "error",
            type: "error",
            isWordWrap: true,
          });
        }
      });
    } catch (e) {
    } finally {
      this.isTesting = false;
    }
  }

  prepareModes(array: any, level: number = 0) {
    return array.map((el: any) => {
      const childs = el.params ? this.prepareModes(el.params, level + 1) : [];
      return { ...el, tabId: (el.type ? `${el.type}-` : "") + el.code, srpg: el.code, childs, level };
    });
  }

  prepareModesFlat(array: any, level: number = 0) {
    let childsArray: any = [];
    const mainArray = array.map((el: any) => {
      const childs: any = el.params ? this.prepareModes(el.params) : null;
      if (childs) {
        childs.map((el: any) => {
          childsArray.push(el);
        });
      }
      return { ...el, tabId: (el.type ? `${el.type}-` : "") + el.code, srpg: el.code };
    });
    return [...mainArray.map((el: any) => ({ ...el, level: 0 })), ...childsArray.map((el: any) => ({ ...el, level: 1 }))];
  }

  async initModes(isModeCenter: any) {
    try {
      this.isLoadingModes = true;
      const { items } = await settingsApi.settingsController.getMODES(isModeCenter);
      this.modes = prepareDataTable(this.prepareModes(items));
    } catch (e) {
    } finally {
      this.isLoadingModes = false;
    }
  }

  async saveModes(types: any, params: any) {
    const finalTypes = types.map((el: any) => {
      return { type: el.tabId, modesCode: el?.modesCode?.trim()?.length === 0 ? null : el?.modesCode };
    });
    const finalParams = params.map((el: any) => {
      return {
        type: el.type,
        code: el.code,
        modesCode: el?.modesCode?.trim()?.length === 0 ? null : el.modesCode,
        isPbr: el.isPbr ?? false,
        isPpbr: el.isPpbr ?? false,
      };
    });
    try {
      await settingsApi.settingsController.saveMODES(finalTypes, finalParams);
      this.rootStore.notificationStore.addNotification({
        title: "Сохранение",
        description: "Данные сохранены",
        icon: "save",
        type: "information",
      });
    } catch (e) {}
  }

  getPgTypeList = async () => {
    try {
      this.isLoadingPg = true;
      const resp = await settingsApi.settingsController.getPgTypeList();
      runInAction(() => {
        this.pgTypeList = resp.items;
      });
    } catch (e) {
    } finally {
      this.isLoadingPg = false;
    }
  };

  updatePgTypeList = async (items: any) => {
    try {
      await settingsApi.settingsController.updatePgTypeList(items);
      this.rootStore.notificationStore.addNotification({
        title: "Сохранение",
        description: "Данные сохранены",
        icon: "save",
        type: "information",
      });
      return true;
    } catch (e) {
      return false;
    }
  };
}
