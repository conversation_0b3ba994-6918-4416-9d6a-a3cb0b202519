import React from "react";
import ReactDOM from "react-dom";
import { <PERSON>rowserRouter as Router } from "react-router-dom";
import { App } from "./App";

const root = document.querySelector("#root");

const render = () => {
  if (root) {
    ReactDOM.render(
      <Router>
        <App />
      </Router>,
      root
    );
  }
  return <>Ошибка ! Не удалось загрузить приложение !</>;
};

render();
