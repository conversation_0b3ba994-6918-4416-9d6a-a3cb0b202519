import React, { useEffect } from "react";
import { observer } from "mobx-react";
import { useNavigate } from "react-router-dom";
import { getToken } from "../../../utils/localStorage";
import { logTokenEvent } from "~/utils/tokenLogger";

export const HomePage = observer(() => {
  const history = useNavigate();

  const redirectToApp = () => {
    const pathname = localStorage.getItem(`pathname`) && localStorage.getItem(`pathname`) !== "/" ? localStorage.getItem(`pathname`) : null;

    if (pathname) {
      history(pathname);
      return;
    }

    // Определяем маршрут по роли
    const roles = JSON.parse(localStorage.getItem("roles") as string) || [];
    const [role] = roles;

    let targetRoute = "/nci";

    switch (role) {
      case "sys_admin":
        targetRoute = "/settings";
        break;
      case "nsi_admin":
        break;
      case "engineer":
        targetRoute = "/planned-schedules";
        break;
      case "viewer":
        break;
      case "nsi_ess_admin":
        break;
      default:
        break;
    }

    history(targetRoute);
  };

  useEffect(() => {
    const token = getToken();
    const hasToken = token && token !== "null" && token !== "undefined";

    logTokenEvent("HomePage/useEffect", {
      hasToken: !!hasToken,
    });

    if (hasToken) {
      // Есть токен - пытаемся войти в приложение
      // Вся логика обновления токена будет обработана axios interceptors
      redirectToApp();
    } else {
      // Нет токена - на логин
      logTokenEvent("HomePage/redirect_to_login", {
        reason: "Нет токена в localStorage",
      });
      history("/login");
    }
  }, []);

  return <div></div>;
});
