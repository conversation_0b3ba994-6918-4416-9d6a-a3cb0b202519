import React, { FC, useRef, useState, useEffect } from "react";
import styled, { css } from "styled-components";
import { Combobox } from "../../../components/Combobox";
import { Checkbox } from "../../../components/Checkbox";
import { Icon } from "components/Icon";
import { Avatar } from "components/Avatar";
import { useOnClickOutside } from "../../../hooks/useOnClickOutside";
import combo1 from "assets/image/combo1.jpg";
import combo2 from "assets/image/combo2.jpg";
import { Button } from "components/Button";
import { Tooltip } from "components/Tooltip";
import { getTableParams, setTableParams } from "../../../api/userStorage/userStorage";
import { Modal } from "~/components/Modal";
import { Loader } from "~/components/Loader";
import { observer } from "mobx-react";

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
`;

export const Row = styled.div<React.ComponentProps<"div"> & { isBig?: boolean }>`
  height: 30px;
  width: 100%;
  display: flex;
  align-items: center;
  ${(p) =>
    p.isBig &&
    css`
      height: 130px;
    `}
`;

export const CheckboxLabel = styled.div`
  margin-left: 10px;
`;

export const Arrow = styled.div<React.ComponentProps<"div"> & { isOpen?: boolean }>`
  position: absolute;
  top: auto;
  bottom: 8px;
  left: 65px;
  color: ${(p) => p.theme.primaryColor};
  transition: all 0.3s;
  opacity: 0;
  ${(p) =>
    p.isOpen &&
    css`
      opacity: 0;
    `};
`;

export const AvatarsContainer = styled.div<React.ComponentProps<"div"> & { isActivate?: boolean }>`
  cursor: pointer;
  margin: 20px 0;
  min-height: 104px;
  max-height: 104px;
  height: 104px;
  border: solid 2px transparent;
  transition: all 0.3s;
  border-radius: 50%;
  position: relative;
  &:hover {
    border: solid 2px ${(p) => p.theme.primaryColor};
  }

  &:hover ${Arrow} {
    opacity: 1;
  }

  ${(p) =>
    p.isActivate &&
    css`
      border: solid 2px ${(p) => p.theme.primaryColor};
    `}
`;

export const AvatarsModal = styled.div<React.ComponentProps<"div">>`
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  width: 345px;
  height: 345px;
  overflow-x: auto;
  position: absolute;
  z-index: 9999;
  top: 180px;
  left: 100px;
  border-radius: 8px;
  box-shadow: 0 0 49px 4px rgba(34, 60, 80, 0.18);
`;

export const IconStyled = styled(Icon)<{ isActive?: boolean }>`
  display: inline-block;
  margin: 6px;
  margin-bottom: 6px;
  border: solid 2px transparent;
  border-radius: 50%;
  transition: all 0.3s;
  cursor: pointer;
  background: #556080;
  &:hover {
    border: solid 2px ${(p) => p.theme.primaryColor};
  }
  ${(p) =>
    p.isActive &&
    css`
      border: solid 2px ${(p) => p.theme.primaryColor};
    `}
`;

export const AvatarStyled = styled(Avatar)<{ isActive?: boolean }>`
  display: inline-block;
  margin: 6px;
  border: solid 2px transparent;
  border-radius: 50%;
  transition: all 0.3s;
  cursor: pointer;
  &:hover {
    border: solid 2px ${(p) => p.theme.primaryColor};
  }

  ${(p) =>
    p.isActive &&
    css`
      border: solid 2px ${(p) => p.theme.primaryColor};
    `}
`;

export const ComboboxPic = styled.div<React.ComponentProps<"div"> & { isSelected?: boolean }>`
  width: 45%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 10px;
  transition: all 0.3s;
  border: solid 2px transparent;
  margin: auto;

  &:hover {
    border: solid 2px ${(p) => p.theme.primaryColor};
  }

  ${(p) =>
    p.isSelected &&
    css`
      border: solid 2px ${(p) => p.theme.primaryColor};
    `}
`;

export const UserAvatarDefault = styled.img<React.ComponentProps<"img"> & { isActive?: boolean }>`
  width: 100px;
  height: 100px;
  margin: 6px;
  border: solid 2px transparent;
  border-radius: 50%;
  transition: all 0.3s;
  display: inline-block;
  cursor: pointer;
  &:hover {
    border: solid 2px ${(p) => p.theme.primaryColor};
    background-color: ${(p) => p.theme.primaryColor};
  }

  ${(p) =>
    p.isActive &&
    css`
      border: solid 2px ${(p) => p.theme.primaryColor};
      background-color: ${(p) => p.theme.primaryColor};
    `}
`;

export const InfoIcon = styled(Icon)`
  color: ${(p) => p.theme.blueActiveSupport};
  margin-right: 5px;
`;

export const ResetButton = styled(Button)`
  width: 20px;
  height: 20px;
  margin-left: 10px;
`;

export interface ModalSettingsProps {
  onCancel: any;
  selectedTheme: any;
  setSelectedTheme: any;
  isShowVersion: any;
  setIsShowVersion: any;
  nameAvatars: any;
  setNameAvatars: any;
  comboboxStyle: any;
  setComboboxStyle: any;
  themeEnable: boolean;
  isPgAckSettingAvailable: boolean;
}

const PG_ACK_KEY = "pg_ack_key"; // Ключ для сохранения настроек квитирования ПГ

export const ModalSettings: FC<ModalSettingsProps> = observer((props) => {
  const {
    onCancel,
    selectedTheme,
    setSelectedTheme,
    isShowVersion,
    setIsShowVersion,
    nameAvatars,
    setNameAvatars,
    comboboxStyle,
    setComboboxStyle,
    themeEnable,
    isPgAckSettingAvailable,
  } = props;

  // @ts-ignore
  const defaultTheme = window?.THEME_DEFAULT ? window?.THEME_DEFAULT : "SRPG_DEFAULT";

  const themes = [
    { value: "SRPG_DEFAULT", label: "СРПГ светлая (по умолчанию)" },
    { value: "SRPG_DARK", label: "СРПГ темная" },
    { value: "SRDK_LIGHT", label: "СРПГ светлая 2" },
    { value: "SRDK_DARK", label: "СРПГ темная 2" },
  ];

  const [isLoading, setIsLoading] = useState(true); // Загрузка настроек с сервера
  const [theme, setTheme] = useState(selectedTheme);
  const [isVersion, setIsVersion] = useState(isShowVersion);
  const [avatar, setAvatar] = useState(nameAvatars);
  const [isModalAvatar, setIsModalAvatar] = useState(false);
  const [combo, setCombo] = useState(comboboxStyle);
  const [isPgAck, setIsPgAck] = useState(false);

  const avatars = ["default", ...new Array(11).fill(null).map((_: any, index: number) => `avatar${index + 1}`)];

  const modalAvatarsRef = useRef(null);

  useOnClickOutside(modalAvatarsRef, () => setIsModalAvatar(false));

  useEffect(() => {
    const fetchPgAckSetting = async () => {
      // Показываем Loader при изменении условий доступности настройки
      setIsLoading(true);

      try {
        // Загружаем настройку только если она доступна пользователю
        if (isPgAckSettingAvailable) {
          const response = await getTableParams(PG_ACK_KEY);
          // Может вернуться пустой объект, если настройка не сохранялась ранее,
          // поэтому проверяем на наличие value
          if (response.value) {
            setIsPgAck(response.value === "true");
          } else {
            setIsPgAck(false);
          }
        } else {
          setIsPgAck(false);
        }
      } catch {
        setIsPgAck(false);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPgAckSetting();
  }, [isPgAckSettingAvailable]);

  const handleConfirm = async () => {
    try {
      // 1. Попытка сохранить настройку квитирования ПГ на сервере (только если доступна)
      if (isPgAckSettingAvailable) {
        await setTableParams(PG_ACK_KEY, { value: String(isPgAck) });
      }

      // 2. Если успешно, сохраняем остальные настройки и закрываем окно
      setSelectedTheme(theme);
      localStorage.setItem("theme", theme);
      setIsShowVersion(isVersion);
      localStorage.setItem("isShowVersion", String(isVersion));
      setNameAvatars(avatar);
      localStorage.setItem("avatar", avatar);
      setComboboxStyle(combo);
      localStorage.setItem("comboboxStyle", combo);
      onCancel(); // Закрываем модальное окно только в случае успеха
    } catch {
      /* игнорируем ошибку */
    }
  };

  return (
    <Modal
      onCancel={onCancel}
      width={500}
      height={500}
      isOverLay
      title="Настройки пользователя"
      onConfirm={handleConfirm}
      cancelText="Отменить"
      confirmText="Сохранить"
      scrollableContent
      isDisabledConfirm={isLoading}
    >
      {isLoading ? (
        <Loader style={{ height: "100%", display: "flex", alignItems: "center", justifyContent: "center" }} spinnerSize={100} />
      ) : (
        <>
          {isModalAvatar && (
            <AvatarsModal ref={modalAvatarsRef}>
              {avatars.map((item: string, index) => {
                return (
                  <AvatarStyled
                    onClick={() => {
                      setAvatar(item);
                      setIsModalAvatar(false);
                    }}
                    isActive={avatar === item}
                    key={`avatar-${index}`}
                    size={100}
                    name={item}
                  />
                );
              })}
            </AvatarsModal>
          )}
          <Container>
            <AvatarsContainer onClick={() => setIsModalAvatar(true)} isActivate={isModalAvatar}>
              <Avatar size={100} name={avatar} />
              <Arrow isOpen={isModalAvatar}>
                <Icon width={20} name="edit" />
              </Arrow>
            </AvatarsContainer>
            <Row>
              <Combobox
                items={themes}
                width={390}
                disabled={!themeEnable}
                placeholder="Выбирете тему"
                selectedValue={theme}
                onChange={({ value }) => {
                  setTheme(value);
                }}
              />
              {themeEnable ? (
                <ResetButton
                  icon="reset"
                  type="secondary"
                  onClick={() => {
                    setTheme(defaultTheme);
                  }}
                />
              ) : (
                <Tooltip content={"Для изменения темы обратитесь к Администратору системы"}>
                  <InfoIcon width={12} name="information" />
                </Tooltip>
              )}
            </Row>
            <Row>
              <Checkbox
                status={isVersion}
                onChange={({ status }) => {
                  setIsVersion(status);
                }}
              />
              <CheckboxLabel>Показывать версию</CheckboxLabel>
            </Row>
            {isPgAckSettingAvailable && (
              <Row>
                <Checkbox
                  status={isPgAck}
                  onChange={({ status }) => {
                    if (typeof status === "boolean") {
                      setIsPgAck(status);
                    }
                  }}
                />
                <CheckboxLabel>Отображать квитирование ПГ</CheckboxLabel>
              </Row>
            )}
            <Row>Вид выпадающего списка :</Row>
            <Row isBig>
              <ComboboxPic isSelected={combo === "default"} onClick={() => setCombo("default")}>
                <img src={combo1} alt="combo1" />
              </ComboboxPic>
              <ComboboxPic isSelected={combo === "secondary"} onClick={() => setCombo("secondary")}>
                <img src={combo2} alt="combo2" />
              </ComboboxPic>
            </Row>
          </Container>
        </>
      )}
    </Modal>
  );
});
