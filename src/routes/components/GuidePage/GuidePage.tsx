import { Title, LeftContent, Content, RightContent, Container, Row, Circle } from "./GuidePage.style";
import React from "react";
import { GeneralProvisions } from "./components/GeneralProvisions";
import { PurposeAndFunctions } from "./components/PurposeAndFunctions";
import { UserInterface } from "./components/UserInterface";
import { AdministrationGuide } from "./components/AdministrationGuide";
import { SystemSettings } from "./components/SystemSettings";
import { Logging } from "./components/Logging";

const tableOfContents = [
  { title: "1. ОБЩИЕ ПОЛОЖЕНИЯ", type: "category", href: "#general_provisions" },
  { title: "1.1. Полное наименование системы и её условное обозначение", type: "subcategory", href: "#full_name_of_the_system_and_its_symbol" },
  { title: "1.2. Основные понятия, определения и сокращения", type: "subcategory", href: "#basic_concepts_definitions_and_abbreviations" },
  { title: "2. НАЗНАЧЕНИЕ И ФУНКЦИИ СИСТЕМЫ", type: "category", href: "#purpose_and_functions_of_the_system" },
  { title: "2.1. Назначение системы", type: "subcategory", href: "#purpose_of_the_system" },
  { title: "2.2. Описание Системы", type: "subcategory", href: "#description_of_the_system" },
  { title: "3. ИНТЕРФЕЙС ПОЛЬЗОВАТЕЛЯ", type: "category", href: "#user_interface" },
  { title: "3.1. Авторизация пользователей", type: "subcategory", href: "#user_authorization" },
  { title: "3.2. Работа с разделами в Централизованной части Системы (ИА, ОДУ Востока)", type: "subcategory", href: "#working_with_partitions_in_a_centralized" },
  { title: "3.2.1. Работа с НСИ", type: "subsubcategory", href: "#nsi" },
  { title: "3.2.2. Работа с Плановыми графиками", type: "subsubcategory", href: "#scheduled_schedules" },
  { title: "3.3. Работа с разделами в Распределенных частях Системы (ОДУ, РДУ)", type: "subcategory", href: "#working_with_partitions_in_distributed" },
  { title: "3.3.1. Работа с НСИ", type: "subsubcategory", href: "#work_nsi" },
  { title: "3.3.2. Работа с Плановыми графиками", type: "subsubcategory", href: "#working_with_scheduled_schedules" },
  { title: "3.4. Уведомления", type: "subcategory", href: "#event_notifications" },
  { title: "3.5. Настройка пользовательского интерфейса", type: "subcategory", href: "#customizing_user_interface" },
  { title: "3.5.1. Изменение аватара пользователя", type: "subsubcategory", href: "#changing_the_users_avatar" },
  { title: "3.5.2. Изменение цветовой схемы приложения", type: "subsubcategory", href: "#changing_interface_theme" },
  { title: "3.5.3. Отображение версии приложения", type: "subsubcategory", href: "#displaying_application_version" },
  { title: "3.5.4. Изменение вида выпадающих списков", type: "subsubcategory", href: "#changing_appearance_drop_down_lists" },
  { title: "3.5.5. Выход из системы", type: "subsubcategory", href: "#log_out_system" },
  { title: "4. АДМИНИСТРИРОВАНИЕ", type: "category", href: "#administration" },
  { title: "5. НАСТРОЙКИ СИСТЕМЫ", type: "category", href: "#system_settings" },
  { title: "5.1. Общие настройки", type: "subcategory", href: "#general_settings" },
  { title: "5.1.1. Настройка глубины хранения данных", type: "subsubcategory", href: "#setting_the_data_storage_depth" },
  { title: "5.1.2. Настройки взаимодействия с внешними системами", type: "subsubcategory", href: "#settings_for_interaction_with_external_systems" },
  { title: "5.2. Настройки ПГ ", type: "subcategory", href: "#pg_settings" },
  { title: "5.2.1. Настройка Номер ПБР [1СЗ]", type: "subsubcategory", href: "#pbr" },
  { title: "5.2.2. Настройка Номер ПБР [2СЗ]", type: "subsubcategory", href: "#uddg" },
  { title: "5.2.3. Настройка характеристик ПГ", type: "subsubcategory", href: "#setting_up_pg_characteristics" },
  { title: "5.2.4. Настройка константы отсутствия данных", type: "subsubcategory", href: "#empty_data" },
  { title: "5.2.5. Настройка загрузки ПГ", type: "subsubcategory", href: "#setting_up_pg_loading" },
  {
    title: "5.2.6. Настройка состава передаваемых данных графика в MODES-Terminal",
    type: "subsubcategory",
    href: "#configuring_the_composition_of_transmitted_graph_data_in_MODES_Terminal",
  },
  { title: "5.3. Распространение настроек в ДЦ", type: "subcategory", href: "#distribution_of_settings_in_DC" },
  { title: "6. ЖУРНАЛИРОВАНИЕ", type: "category", href: "#logging" },
];

export const GuidePage = () => {
  return (
    <Container>
      <Title>Справочная информация</Title>
      <Content>
        <LeftContent>
          {tableOfContents.map((el, index) => {
            return (
              <Row type={el.type} key={`row-${index}`} href={el.href}>
                <Circle type={el.type} />
                {el.title}
              </Row>
            );
          })}
        </LeftContent>
        <RightContent>
          <GeneralProvisions /> {/* ОБЩИЕ ПОЛОЖЕНИЯ */}
          <PurposeAndFunctions /> {/* НАЗНАЧЕНИЕ И ФУНКЦИИ СИСТЕМЫ */}
          <UserInterface /> {/* ИНТЕРФЕЙС ПОЛЬЗОВАТЕЛЯ */}
          <AdministrationGuide /> {/* АДМИНИСТРИРОВАНИЕ */}
          <SystemSettings /> {/* НАСТРОЙКИ СИСТЕМЫ */}
          <Logging /> {/* ЖУРНАЛИРОВАНИЕ */}
        </RightContent>
      </Content>
    </Container>
  );
};
