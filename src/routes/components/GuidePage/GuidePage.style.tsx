import { ComponentPropsWithRef } from "react";
import styled, { css } from "styled-components";

type DivProps = ComponentPropsWithRef<"div">;

export const NumericOl = styled.ol`
  list-style: none;
  counter-reset: li;
`;
export const NumericLi = styled.li`
  &:before {
    counter-increment: li;
    content: counters(li, ".") ". ";
  }
`;

export const StatusText = styled.div<{ status: "GRAY" | "GREEN" | "YELLOW" | "RED" }>`
  font-weight: bold;
  display: inline-block;
  ${(p) =>
    p.status === "GRAY" &&
    css`
      color: ${(p) => p.theme.gray};
    `}
  ${(p) =>
    p.status === "GREEN" &&
    css`
      color: ${(p) => p.theme.greenActiveSupport};
    `}
 ${(p) =>
    p.status === "YELLOW" &&
    css`
      color: ${(p) => p.theme.orangeActiveSupport};
    `}
 ${(p) =>
    p.status === "RED" &&
    css`
      color: ${(p) => p.theme.redActiveSupport};
    `}
`;

export const BigPicture = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  img {
    max-width: 800px;
  }
`;

export const AltPicture = styled.div`
  font-style: italic;
`;

export const Container = styled.div`
  width: 100%;
  height: 100%;
  background-color: ${(p) => p.theme.backgroundColor};
  padding: 20px;
  color: ${(p) => p.theme.textColor};
`;

export const Left = styled.div`
  height: 100%;
  width: 30%;
`;

export const Right = styled.div`
  height: 100%;
  width: 70%;
  padding: 0 10px;
`;

export const Title = styled.div`
  font-size: 24px;
  font-weight: bold;
  margin-left: 20px;
  cursor: default;
  user-select: none;
`;

export const Content = styled.div`
  width: 100%;
  height: 98%;
  border: solid 1px ${(p) => p.theme.gray};
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  display: flex;
  border-radius: 6px;
  font-size: 16px;
`;

export const LeftContent = styled.div`
  border-right: solid 1px ${(p) => p.theme.gray};
  width: 20%;
  padding: 10px;
  display: flex;
  flex-direction: column;
  cursor: pointer;
`;

export const Circle = styled.div<{ type?: string }>`
  width: 10px;
  height: 10px;
  min-height: 10px;
  min-width: 10px;
  background-color: ${(p) => p.theme.gray};
  border-radius: 50%;
  margin-right: 5px;
  ${(p) =>
    p.type === "subcategory" &&
    css`
      margin-left: 10px;
    `}
  ${(p) =>
    p.type === "subsubcategory" &&
    css`
      margin-left: 30px;
    `}
`;

export const Status = styled.div<{ type?: any }>`
  width: 16px;
  height: 16px;
  min-width: 16px;
  min-height: 16px;
  border-radius: 50%;
  border: solid 1px lightgray;
  margin-right: 10px;
  ${(p) =>
    p.type === "gray" &&
    css`
      background-color: lightgray;
      border: solid 1px lightgray;
    `}
  ${(p) =>
    p.type === "green" &&
    css`
      background-color: #339844;
      border: solid 1px lightgray;
    `}
  ${(p) =>
    p.type === "yellow" &&
    css`
      background-color: #efaf1b;
      border: solid 1px lightgray;
    `}
  ${(p) =>
    p.type === "red" &&
    css`
      background-color: #b71414;
      border: solid 1px lightgray;
    `}
  ${(p) =>
    p.type === "redYellow" &&
    css`
      background-color: #efaf1b;
      border: solid 3px #b71414;
    `}
  ${(p) =>
    p.type === "redGreen" &&
    css`
      background-color: #339844;
      border: solid 3px #b71414;
    `}
`;

export const Row = styled.a<DivProps & { type?: string; href?: string }>`
  display: flex;
  align-items: center;
  text-decoration: none;
  color: ${(p) => p.theme.textColor};
  &:hover {
    color: ${(p) => p.theme.blueActiveSupport} !important;
    text-decoration: underline ${(p) => p.theme.blueActiveSupport};
  }
  &:hover ${Circle} {
    background-color: ${(p) => p.theme.blueActiveSupport};
  }
  &:active {
    color: ${(p) => p.theme.textColor};
  }
  &:visited {
    color: ${(p) => p.theme.textColor};
  }
`;

export const RightContent = styled.div`
  width: 80%;
  padding: 10px;
  overflow-y: scroll;
  overflow-x: hidden;
`;

export const TitlePage = styled.a<DivProps & { href?: string; name?: string }>`
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  &:active {
    color: ${(p) => p.theme.textColor};
  }
  &:visited {
    color: ${(p) => p.theme.textColor};
  }
`;

export const DescriptionPage = styled.a<DivProps & { href?: string; name?: string }>`
  font-size: 14px;
  font-weight: bold;
  text-decoration: none;
  display: block;
  &:active {
    color: ${(p) => p.theme.textColor};
  }
  &:visited {
    color: ${(p) => p.theme.textColor};
  }
`;

export const Page = styled.div<DivProps>``;

export const TextPage = styled.div`
  margin: 6px 0;
`;

export const Table = styled.table`
  border-collapse: collapse;
`;

export const TH = styled.th`
  border: 1px solid black;
  padding: 3px;
`;

export const TD = styled.td`
  border: 1px solid black;
  padding: 3px;
`;

export const IconContainer = styled.div`
  display: flex;
  //justify-content: center;
  align-items: center;
`;
