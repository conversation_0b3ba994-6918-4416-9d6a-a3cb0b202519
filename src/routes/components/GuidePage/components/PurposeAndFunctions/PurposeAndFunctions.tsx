import { DescriptionPage, Page, TitlePage } from "../../GuidePage.style";

export const PurposeAndFunctions = () => {
  return (
    <Page>
      <TitlePage name={`purpose_and_functions_of_the_system`}>2. НАЗНАЧЕНИЕ И ФУНКЦИИ СИСТЕМЫ</TitlePage>
      <DescriptionPage name={`purpose_of_the_system`}>2.1. Назначение системы</DescriptionPage>
      <p>
        Система предназначена для гарантированной доставки плановых графиков, сформированных при краткосрочном планировании электроэнергетических режимов, в ОИК СК-11 и в
        ИУС «MODES-Terminal».
      </p>
      <DescriptionPage name={`description_of_the_system`}>2.2. Описание Системы</DescriptionPage>
      <p>Основные функции, выполняемые в Системе:</p>
      <ul style={{ listStyleType: "disc" }}>
        {/* 1. Централизованная часть ИА */}
        <li>
          1. В Централизованной части Системы ИА выполняются:
          <ul style={{ listStyleType: "disc" }}>
            <li>
              1.1. Обработка НСИ:
              <ul style={{ listStyleType: "disc" }}>
                <li>1.1.1. Загрузка НСИ из ИУС ЕСС.</li>
                <li>1.1.2. Распространение НСИ в СРПГ ДЦ.</li>
                <li>1.1.3. Контроль распространения НСИ в СРПГ ДЦ.</li>
              </ul>
            </li>
            <li>
              1.2. Обработка ПГ:
              <ul style={{ listStyleType: "disc" }}>
                <li>1.2.1. Загрузка (запрос, инициированный Системой, и синхронный ответ ИУС ОпАМ) ПГ из ИУС ОпАМ ИА.</li>
                <li>1.2.2. Загрузка ПГ из Мегаточки.</li>
                <li>1.2.3. Распространение ПГ и результатов расчета агрегированных параметров (п.1.3) в СРПГ ДЦ.</li>
                <li>1.2.4. Акцепт ПГ 1 СЗ и СЗ энергосистемы Калининградской области.</li>
                <li>1.2.5. Получение и обработка квитанций об успешной/неуспешной передаче и записи ПГ в СРПГ ДЦ.</li>
                <li>1.2.6. Контроль распространения ПГ в СРПГ ДЦ.</li>
                <li>1.2.7. Контроль передачи ПГ из СРПГ ДЦ во внешние системы соответствующего ДЦ.</li>
                <li>
                  1.2.8. Формирование сводного статуса распространения ПГ в СРПГ ДЦ / во внешние ИУС (с учетом результатов распространения и записи ПГ во внешние ИУС ИА,
                  ОДУ, РДУ).
                </li>
                <li>1.2.9. Формирование и отправка почтовых уведомлений на события ПГ, НСИ (расширение перечня уведомлений и изменение их визуализации).</li>
                <li>1.2.10. Отображение и квитирование сообщения об ошибке распространения и записи ПГ.</li>
              </ul>
            </li>
            <li>
              1.3. Обработка данных:
              <ul style={{ listStyleType: "disc" }}>
                <li>1.3.1. Расчет агрегированных параметров «сумматоры».</li>
                <li>1.3.2. Расчет агрегированных параметров «ГОУ».</li>
              </ul>
            </li>
            <li>
              1.4. Передача данных:
              <ul style={{ listStyleType: "disc" }}>
                <li>1.4.1. Передача признака акцепта ПГ в ИУС ОпАМ и в Распределенные части Системы.</li>
                <li>1.4.2. Передача технологической НСИ в СРПГ ДЦ.</li>
                <li>1.4.3. Передача сопоставлений ГОУ в СРПГ ДЦ.</li>
              </ul>
            </li>
            <li>
              1.5. Сопоставление объектов ГОУ ИУС ЕСС с объектами ОИК СК-11:
              <ul style={{ listStyleType: "disc" }}>
                <li>1.5.1. Загрузка объектов ОИК СК-11.</li>
                <li>1.5.2. Установка связей объектов ИУС ЕСС с объектами ОИК СК-11.</li>
                <li>1.5.3. Просмотр множественных связей объектов ИУС ЕСС с объектами ОИК СК-11.</li>
                <li>1.5.4. Выгрузка связей в файл формата csv.</li>
              </ul>
            </li>
          </ul>
        </li>

        {/* 2. Распределенные части ИА, ОДУ, РДУ */}
        <li>
          2. В Распределенных частях Системы ИА, ОДУ 1 СЗ, РДУ 1 СЗ, Балтийском РДУ выполняется:
          <ul style={{ listStyleType: "disc" }}>
            <li>
              2.1. Получение данных:
              <ul style={{ listStyleType: "disc" }}>
                <li>2.1.1. Получение НСИ из Централизованной части Системы ИА.</li>
                <li>2.1.2. Получение ПГ и результатов расчета агрегированных параметров (п.1.3) из Централизованной части Системы ИА.</li>
                <li>2.1.3. Получение акцепта ПГ в СРПГ ДЦ.</li>
                <li>2.1.4. Получение от Централизованной части ОДУ Востока ПГ (ППБР, ПБР 2 СЗ) и информации об его акцепте.</li>
                <li>2.1.5. Получение технологической НСИ из Централизованной части Системы ИА.</li>
                <li>2.1.6. Получение сопоставлений ГОУ из Централизованной части Системы ИА.</li>
              </ul>
            </li>
            <li>
              2.2. Передача данных:
              <ul style={{ listStyleType: "disc" }}>
                <li>2.2.1. Настройка состава параметров, передаваемых в ОИК СК-11 ДЦ.</li>
                <li>2.2.2. Передача ПГ в ОИК СК-11 ДЦ.</li>
                <li>2.2.3. Передача акцептованного ПГ в ИУС «Modes-Terminal».</li>
                <li>2.2.4. Передача акцептованного ПГ в ИУС «СРДК».</li>
                <li>2.2.5. Передача информации в ёЖ-3 текущего ДЦ о получении ПГ в ДЦ.</li>
                <li>2.2.6. Передача информации в ёЖ-3 ИА о получении ПГ 2 СЗ (ППБР, ПБР 2 СЗ) и информации об его акцепте в Системе ОДУ Востока.</li>
                <li>2.2.7. Передача информации в ёЖ-3 текущего ДЦ об акцепте ПГ.</li>
                <li>2.2.8. Передача в Централизованную часть Системы ИА квитанций об успешном/неуспешном получении и записи ПГ в ДЦ.</li>
                <li>2.2.9. Формирование и отправка почтовых уведомлений на события ПГ, НСИ.</li>
              </ul>
            </li>
            <li>
              2.3. Сопоставление объектов ИУС ЕСС и ОИК СК-11 (за исключением ГОУ):
              <ul style={{ listStyleType: "disc" }}>
                <li>2.3.1. Загрузка объектов ОИК СК-11.</li>
                <li>2.3.2. Установка связей объектов ИУС ЕСС с объектами ОИК СК-11.</li>
                <li>2.3.3. Просмотр множественных связей объектов ИУС ЕСС с объектами ОИК СК-11.</li>
                <li>2.3.4. Выгрузка связей в файл формата csv.</li>
              </ul>
            </li>
          </ul>
        </li>

        {/* 3. Централизованная часть ОДУ Востока */}
        <li>
          3. В Централизованной части Системы ОДУ Востока выполняются:
          <ul style={{ listStyleType: "disc" }}>
            <li>
              3.1. Обработка НСИ:
              <ul style={{ listStyleType: "disc" }}>
                <li>3.1.1. Получении НСИ из Централизованной части Системы на уровне ИА.</li>
              </ul>
            </li>
            <li>
              3.2. Обработка ПГ:
              <ul style={{ listStyleType: "disc" }}>
                <li>3.2.1. Загрузка (запрос, инициированный Системой, и синхронный ответ ИУС ОпАМ) ПГ из ИУС ОпАМ ОДУ Востока.</li>
                <li>3.2.2. Загрузка ПГ из Мегаточки.</li>
                <li>3.2.3. Распространение ПГ и результатов расчета агрегированных параметров (п.3.3) в ИА и РДУ ОЗ ОДУ Востока.</li>
                <li>3.2.4. Получение и обработка квитанций об успешной/неуспешной обработке ПГ в ИА, РДУ ОЗ ОДУ Востока.</li>
                <li>3.2.5. Акцепт ПГ 2СЗ.</li>
                <li>3.2.6. Контроль распространения ПГ и результатов расчета агрегированных параметров (п.3.3) в ИА, РДУ ОЗ ОДУ Востока.</li>
                <li>3.2.7. Контроль передачи ПГ из ИА, РДУ ОЗ ОДУ Востока во внешние системы: ОИК СК-11, ИУС Modes-Terminal.</li>
                <li>3.2.8. Формирование сводного статуса распространения ПГ в СРПГ ДЦ / во внешние ИУС.</li>
                <li>3.2.9. Формирование и отправка почтовых уведомлений на события ПГ, НСИ.</li>
                <li>3.2.10. Отображение и квитирование сообщения об ошибке распространения и записи ПГ.</li>
              </ul>
            </li>
            <li>
              3.3. Обработка данных:
              <ul style={{ listStyleType: "disc" }}>
                <li>3.3.1. Расчет агрегированных параметров «сумматоры» по объектам 2 СЗ.</li>
                <li>3.3.2. Расчет агрегированных параметров «ГОУ» по объектам 2 СЗ.</li>
              </ul>
            </li>
            <li>
              3.4. Передача данных:
              <ul style={{ listStyleType: "disc" }}>
                <li>3.4.1. Передача признака акцепта ПГ в ИУС ОпАМ ОДУ Востока и ИА, РДУ ОЗ ОДУ Востока.</li>
                <li>3.4.2. Передача в Распределенную часть уровня ИА ПГ (ППБР, ПБР) и информации об его акцепте.</li>
              </ul>
            </li>
          </ul>
        </li>

        {/* 4. Распределенные части ОДУ Востока */}
        <li>
          4. В Распределенных частях Системы ОДУ Востока, РДУ ОЗ ОДУ Востока выполняется:
          <ul style={{ listStyleType: "disc" }}>
            <li>
              4.1. Получение данных:
              <ul style={{ listStyleType: "disc" }}>
                <li>4.1.1. Получение НСИ из Централизованной части Системы уровня ИА.</li>
                <li>4.1.2. Получение ПГ и результатов расчета агрегированных параметров (п.3.3) из Централизованной части Системы ОДУ Востока.</li>
                <li>4.1.3. Получение технологической НСИ из Централизованной части Системы уровня ИА.</li>
                <li>4.1.4. Получение сопоставлений ГОУ из Централизованной части Системы уровня ИА.</li>
              </ul>
            </li>
            <li>
              4.2. Передача данных:
              <ul style={{ listStyleType: "disc" }}>
                <li>4.2.1. Настройка состава параметров, передаваемых в ОИК СК-11 ДЦ.</li>
                <li>4.2.2. Передача ПГ в ОИК СК-11 ДЦ.</li>
                <li>4.2.3. Передача квитанций в Централизованную часть Системы на уровне ОДУ Востока об успешной/неуспешной обработке ПГ в ИА, РДУ ОЗ ОДУ Востока.</li>
                <li>4.2.4. Передача акцептованного ПГ в ИУС «Modes-Terminal».</li>
                <li>4.2.5. Передача акцептованного ПГ в ИУС «СРДК».</li>
                <li>4.2.6. Передача информации в ёЖ-3 ДЦ об акцепте ПГ.</li>
                <li>4.2.7. Передача информации в ёЖ-3 ДЦ о получении ПГ в ДЦ.</li>
                <li>4.2.8. Формирование и отправка почтовых уведомлений на события ПГ, НСИ.</li>
              </ul>
            </li>
            <li>
              4.3. Сопоставление объектов ИУС ЕСС и ОИК СК-11 (за исключением ГОУ):
              <ul style={{ listStyleType: "disc" }}>
                <li>4.3.1. Загрузка объектов ОИК СК-11.</li>
                <li>4.3.2. Установка связей объектов ИУС ЕСС с объектами ОИК СК-11.</li>
                <li>4.3.3. Просмотр множественных связей объектов ИУС ЕСС с объектами ОИК СК-11.</li>
              </ul>
            </li>
          </ul>
        </li>
      </ul>
    </Page>
  );
};
