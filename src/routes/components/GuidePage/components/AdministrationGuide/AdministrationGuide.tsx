import React from "react";
import { image18, image58, image111, image112, image113, image114, image115, image116, image117, image118 } from "../../images";
import { AltPicture, BigPicture, DescriptionPage, Page, TextPage, TitlePage } from "../../GuidePage.style";

export const AdministrationGuide = () => {
  return (
    <>
      <Page>
        <TitlePage name="administration">4. АДМИНИСТРИРОВАНИЕ </TitlePage>
        <TextPage>
          Данный раздел Системы предназначен для добавления групп пользователей, зарегистрированных в AD в Систему, связи добавляемых групп пользователей с
          предопределенными ролями.
          <br />
          Добавлять группы пользователей, устанавливать связи предопределённых ролей с группами пользователей, просматривать доступный функционал для ролей может только
          пользователь с ролью Администратор Системы.
          <br />
          Для перехода к функционалу администрирования необходимо нажать раздел <img src={image111} alt="image150" /> на главной рабочей панели. Откроется раздел
          Администрирования. (Рисунок 43)
        </TextPage>
        <BigPicture>
          <img src={image112} alt="image151" />
          <AltPicture>Рисунок 43 - Интерфейс по Администрированию групп пользователей в Системе</AltPicture>
        </BigPicture>
      </Page>
      <Page>
        <DescriptionPage name="unknown">Изменение связи роли с группой AD</DescriptionPage>
        <TextPage>
          Для изменения связи роли с группой AD в Системе, необходимо нажать в строке с наименованием роли кнопку в виде карандаша <img src={image113} alt="image152" /> . Отроется форма для добавления изменения связи роли с группой AD. (Рисунок 44)
        </TextPage>
        <BigPicture>
          <img src={image114} alt="image153" />
          <AltPicture>Рисунок 44 - Форма добавления групп пользователей</AltPicture>
        </BigPicture>
        <TextPage>
          Для добавления группы в Систему необходимо нажать на кнопку в виде плюса в строке с названием группы в столбце «Действие».
          <br />
          Группа переместится в верхнюю часть формы.
          <br />
          После выбора роли, на форме станет активна кнопка <img src={image18} alt="save" /> .
          <br />
          Для сохранения связи группы с ролью и добавления группу в Системы, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
          <br />
          После сохранения, группа пользователей будет отображается на форме списка групп.
          <br />
          Для подробного просмотра списка связанных с ролью групп AD, необходимо нажать на кнопку <img src={image115} alt="copy" /> . Откроется форма с подробным перечнем
          вложенных групп AD, связанных с ролью. (Рисунок 45)
        </TextPage>
        <BigPicture>
          <img src={image116} alt="image154" />
          <AltPicture>Рисунок 45 - Форма списка связанных групп пользователей с ролью</AltPicture>
        </BigPicture>
        <TextPage>
          Для просмотра списка функций роли в Системе, необходимо нажать в строке с наименованием роли кнопку <img src={image117} alt="user" /> .
          <br />
          Откроется форма со списком предустановленных ролей в Системе. (Рисунок 46)
        </TextPage>
        <BigPicture>
          <img src={image118} alt="image155" />
          <AltPicture>Рисунок 46 - Форма просмотра списка ролей</AltPicture>
        </BigPicture>
        <TextPage>Для просмотра доступных функций роли, необходимо нажать на название роли в списке ролей.</TextPage>
      </Page>
    </>
  );
};
