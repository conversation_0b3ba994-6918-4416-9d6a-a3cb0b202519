import React from "react";
import {
  image23,
  image24,
  image32,
  image33,
  image35,
  image36,
  image37,
  image74,
  image186,
  image187,
  image188,
  image189,
  image190,
  image191,
  image192,
  image193,
  image194,
  image195,
  image196,
  image197,
  image198,
  image199,
  image200,
  image201,
  image202,
  image204,
  image205,
  image206,
  image207,
  image208,
  image209,
  image210,
  image211,
  image213,
  image214,
  image215,
  image216,
  image217,
  image218,
  image219,
  image220,
  image221,
  image222,
  image223,
  image224,
  image225,
  image227,
  image228,
  image229,
  image230,
  image231,
  image232,
  image233,
  image234,
  image235,
  image236,
  image237,
  image238,
  image239,
  image240,
  image241,
  image242,
  image243,
  image245,
  image246,
  image247,
  image248,
  image249,
  image250,
  image251,
  image252,
  image253,
  image254,
  image255,
  image257,
  image258,
  image259,
  image260,
  image261,
  image262,
  image263,
  image264,
  image265,
  image266,
  image267,
  image268,
  image269,
  image270,
  image271,
  image272,
  image273,
  image274,
  image275,
  image276,
  image277,
  image278,
  image279,
  image280,
  image281,
  image282,
  image285,
  image286,
  image287,
  image288,
  image289,
  image290,
  image291,
  image292,
  image293,
  image294,
  image295,
  image296,
  image297,
  image299,
  image300,
  image301,
  image302,
  image303,
  image304,
  image305,
  image306,
  image307,
  image308,
} from "../../images";
import { AltPicture, BigPicture, DescriptionPage, IconContainer, NumericLi, NumericOl, Page, TextPage, TitlePage } from "../../GuidePage.style";
import { Icon } from "~/components/Icon";
import { StatusCircle } from "~/pages/PlannedSchedules/components/DistributionContainer/DistributionContainer.style";
import styled from "styled-components";

const StyledTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 16px;
  margin-bottom: 16px;

  th,
  td {
    border: 1px solid #ddd;
    padding: 5px;
    text-align: left;
    vertical-align: top;
  }

  /* убираем верхний margin только у первого <p> */
  td p:first-child {
    margin-top: 0;
  }

  th {
    background-color: #f2f2f2;
    font-weight: bold;
    white-space: nowrap;
  }

  td ul {
    padding-left: 20px;
    margin-top: 5px;
    margin-bottom: 5px;
  }
`;

const NoBulletItem = styled.li`
  list-style-type: none;
  margin-left: 0;
  padding-left: 0;
`;

const Footnote = styled.p`
  font-size: 14px;
  margin-top: 8px;
  margin-bottom: 0;
`;

const IndicatorCell = styled.div`
  display: flex;
  align-items: center;
`;

export const UserInterface = () => {
  return (
    <>
      <Page>
        <TitlePage name={`user_interface`}>3.ИНТЕРФЕЙС ПОЛЬЗОВАТЕЛЯ</TitlePage>
        <DescriptionPage name={`user_authorization`}>3.1. Авторизация пользователей</DescriptionPage>
        <p>При первом переходе в систему пользователь попадает на форму авторизации (Рисунок 1). </p>
        <BigPicture>
          <img src={image186} alt="image186" />
          <AltPicture>Рисунок 1 – Форма авторизации пользователей</AltPicture>
        </BigPicture>
        <p>
          Пользователю необходимо авторизоваться в системе при помощи Логина и Пароля от доменной учетной записи (AD). После корректного ввода данных авторизации
          необходимо нажать на кнопку<b> «Войти»</b>.
        </p>
        <p>
          Если при попытке авторизации выводится уведомление об ошибке, тогда необходимо проверить введенные учетные данные и повторить попытку входа в Систему. Если
          учетные данные верны, тогда необходимо обратиться к Администратору системы для проверки доступа учетной записи.
        </p>
        <p>
          При успешной авторизации пользователь попадает на форму согласно своей роли в Системе по умолчанию (Администратор Системы – форма «Настройки», Администратор
          НСИ, Администратор НСИ ЕСС, Наблюдатель – форма «НСИ», Технолог – форма «Плановые графики»), Рисунок 2.
          <BigPicture>
            <img src={image187} alt="image187" />
            <AltPicture>Рисунок 2 – Раздел системы Плановые графики</AltPicture>
          </BigPicture>
        </p>
        <p>
          Для переключения между Централизованной и Распределенной частями Системы в экземплярах СПРГ ИА, ОДУ Востока отображается переключатель на главной панели
          (Рисунок 3){" "}
          <BigPicture>
            <img src={image188} alt="image188" />
            <AltPicture>Рисунок 3 – Кнопка переключения между Централизованной и Распределенной частями Системы</AltPicture>
          </BigPicture>
        </p>
        <p>Для пользователя, в зависимости от роли, доступны разделы:</p>

        <ul>
          <li>
            1) <b>НСИ.</b> В Централизованной части Системы раздел предназначен для загрузки, просмотра, сравнения загруженных версий НСИ, распространения и контроля
            распространения НСИ в Распределенные части Системы, а также для сопоставления объектов ГОУ с объектами ОИК СК-11 и распространения связей в Распределенные
            части Системы.
          </li>
          <p>
            В Распределенной части Системы раздел «НСИ» предназначен для просмотра НСИ, просмотра истории загрузки НСИ, сравнения загруженных версий НСИ, сопоставления
            реестров СРПГ и Сумматоры с объектами ОИК СК-11.
          </p>
          <li>
            2) <b>Плановые графики.</b> В Централизованной части Системы раздел предназначен для загрузки и распространения ПГ в Распределенные части Системы, акцепта ПГ,
            контроля распространения и акцепта ПГ, просмотра распространенных ПГ.{" "}
          </li>
          <p>
            В Распределенной части Системы раздел предназначен для просмотра загруженных ПГ, распространения и контроля распространения ПГ во внешние системы, резервной
            загрузки ПГ в случае аварийных ситуаций.
          </p>
          <li>
            3) <b>Администрирование.</b> Данный раздел Системы предназначен для добавления групп пользователей, зарегистрированных в AD в Систему, связи добавляемых групп
            пользователей с предопределенными ролями.
          </li>
          <li>
            4) <b>Настройки.</b> Данный раздел Системы предназначен для настройки взаимодействия с внешними системами, для настройки принципов работы СРПГ. Данный раздел
            присутствует в распределенной части (во всех ДЦ) и централизованной части (ИА, ОДУ Востока) Системы. Описание по работе с настройками СРПГ представлено в
            Руководстве администратора.
          </li>
          <li>
            5) <b>Журналирование.</b> Данный раздел Системы предназначен для журналирования действий пользователей и взаимодействия с внешними системами в Системе СРПГ
            СК-11.
          </li>
          <li>
            6) <b>Уведомления.</b> Раздел предназначен для настройки почтового уведомления (подписки) текущего пользователя на события распространения НСИ в ИУС «СРПГ».
          </li>
        </ul>
      </Page>

      <Page>
        <DescriptionPage name={`working_with_partitions_in_a_centralized`}>3.2. Работа с разделами в Централизованной части Системы (ИА, ОДУ Востока)</DescriptionPage>
        <DescriptionPage name={`nsi`}>3.2.1. Работа с НСИ</DescriptionPage>
        <p>
          В Централизованной части Системы раздел предназначен для загрузки, просмотра, сравнения загруженных версий НСИ, распространения и контроля распространения НСИ в
          Распределенные части Системы, а также для сопоставления объектов ГОУ с объектами ОИК СК-11 и распространения связей в Распределенные части Системы.{" "}
        </p>
        <p>Загрузка и распространение НСИ доступна только пользователю с ролью Администратор НСИ ЕСС (ИА).</p>
        <p>Просмотр данных НСИ доступен для пользователей с ролью Администратор НСИ, Администратор НСИ ЕСС, Наблюдатель, Технолог.</p>
        <p>Для перехода к функциональности по работе с НСИ необходимо перейти в раздел на главной рабочей панели.</p>
        <p>
          Откроется раздел «НСИ» <img src={image189} alt="image189" /> (Рисунок 4).
          <BigPicture>
            <img src={image190} alt="image190" />
            <AltPicture>Рисунок 4 – Интерфейс раздела работы с НСИ</AltPicture>
          </BigPicture>
        </p>
        <h5>Загрузка НСИ</h5>
        <p>
          Для выполнения загрузки НСИ необходимо нажать на рабочей панели кнопку <img src={image191} alt="image191" /> , затем в выпадающем списке выбрать вариант
          загрузки НСИ из ИУС ЕСС или из файла.
        </p>
        <p>
          Откроется модальное окно, в котором необходимо выбрать дату в календаре, с которой изменения должны начать действовать и нажать кнопку{" "}
          <img src={image192} alt="image192" /> (Рисунок 5). По умолчанию, в календаре выбрана следующая за текущей дата. Можно выбрать только дату больше текущей.
        </p>
        <p>
          Так же в верхней части окна отображается информация о последней сохраненной версии НСИ <img src={image193} alt="image193" /> . Указывается дата НСИ, с которой
          изменения начинают действовать, а также дата и время загрузки НСИ.
          <BigPicture>
            <img src={image194} alt="image194" />
            <AltPicture>Рисунок 5 – Окно загрузки НСИ за выбранную дату</AltPicture>
          </BigPicture>
        </p>
        <p>При загрузке данных НСИ выполняются проверки:</p>
        <ul>
          <li>наличие дублей в реестрах;</li>
          <li>наличие связи ГОУ с РГЕ, которое отсутствует в Реестре СРПГ;</li>
          <li>наличие связи ГОУ с сечением, которое отсутствует в Реестре СРПГ;</li>
          <li>наличие связи ГОУ с сечением и РГЕ одновременно;</li>
          <li>наличие в Карте ведения ссылок на несуществующее объекты;</li>
          <li>наличие в Карте ведения ссылок на несуществующие родительские объекты.</li>
        </ul>
        <p>При наличии одного из ограничений, ошибки фиксируются в протокол ошибок (Рисунок 7).</p>
        <p>
          После успешной загрузки НСИ, на форме отображается статус загрузки, время загрузки НСИ, начало действия НСИ, инициатор загрузки НСИ. (Рисунок 6).
          <BigPicture>
            <img src={image195} alt="image195" />
            <AltPicture>Рисунок 6 – Форма настроек загрузки НСИ</AltPicture>
          </BigPicture>
        </p>
        <p>
          Для просмотра ошибок по результатам загрузки НСИ, необходимо нажать на кнопку <img src={image196} alt="image196" />.
        </p>
        <p>
          После нажатия откроется форма протокола ошибок (Рисунок 7).
          <BigPicture>
            <img src={image197} alt="image197" />
            <AltPicture>Рисунок 7 – Форма протокол ошибок</AltPicture>
          </BigPicture>
        </p>
        <p>
          Для выгрузки протокола ошибок необходимо нажать на кнопку <img src={image198} alt="image198" />.
        </p>
        <p>
          Для просмотра результатов сравнения загруженной версии с последней сохраненной в Системе версией НСИ, необходимо нажать на кнопку{" "}
          <img src={image199} alt="image199" /> на форме загрузки НСИ.
        </p>
        <p>
          После нажатия откроется форма протокола изменений НСИ (Рисунок 8).
          <BigPicture>
            <img src={image200} alt="image200" />
            <AltPicture>Рисунок 8 – Форма протокола сравнения версий НСИ </AltPicture>
          </BigPicture>
        </p>
        <p>
          Для выгрузки результатов изменений НСИ необходимо нажать на кнопку <img src={image198} alt="image198" />.
        </p>
        <p>
          Для отмены загрузки версии НСИ необходимо нажать на кнопку <img src={image201} alt="image201" /> на форме загрузки НСИ. Загруженная версия НСИ будет отменена и
          недоступна для распространения в ДЦ.
        </p>
        <p>
          Для сохранения и распространения загруженной версии НСИ необходимо нажать на кнопку <img src={image202} alt="image202" /> на форме загрузки НСИ.
        </p>
        <p>
          Откроется форма выбора ДЦ для распространения загруженной версии НСИ. (Рисунок 9).
          <BigPicture>
            <img src={image200} alt="image200" />
            <AltPicture>Рисунок 9 – Форма выбора ДЦ для распространения загруженной НСИ </AltPicture>
          </BigPicture>
        </p>
        <p>На форме доступны следующие инструменты:</p>
        <TextPage>
          <br />
          <IconContainer>
            <div>
              <Icon width={16} name="search" />
            </div>
            - Выполнить фильтр по полю. <br />
          </IconContainer>
        </TextPage>
        <p>
          <img src={image204} alt="image204" /> - Выбрать все объекты/Сбросить выделение всех выбранных объектов.
        </p>
        <p>
          Для распространения НСИ в выбранные в списке ДЦ необходимо нажать <img src={image205} alt="image205" /> , для отмены выбора нажать кнопку{" "}
          <img src={image206} alt="image206" /> .
        </p>
        <p>
          По результату распространения НСИ в выбранные ДЦ, выводится уведомление со статусом передачи НСИ в выбранные ДЦ. (Рисунок 10).
          <BigPicture>
            <img src={image207} alt="image207" />
            <AltPicture>10 – Уведомление об общем результате распространения НСИ в выбранные ДЦ </AltPicture>
          </BigPicture>
        </p>
        <h5>Просмотр реестров НСИ</h5>
        <p>
          Для просмотра сохраненной НСИ за выбранную дату, необходимо открыть календарь (Рисунок 11), затем выбрать год, месяц, день и нажать кнопку{" "}
          <img src={image208} alt="image208" /> .
        </p>
        <p>
          Зеленым цветом выделены даты, на которые была выполнена загрузка НСИ.
          <BigPicture>
            <img src={image209} alt="image209" />
            <AltPicture>Рисунок 11 – Форма выбора даты для просмотра НСИ </AltPicture>
          </BigPicture>
        </p>
        <p>
          После выбора даты, будут загружены актуальные на выбранную дату реестры НСИ (Рисунок 12).
          <BigPicture>
            <img src={image210} alt="image210" />
            <AltPicture>Рисунок 12 – Форма просмотра реестров НСИ</AltPicture>
          </BigPicture>
        </p>
        <p>В левой области формы отображается реестр ДЦ, в правой области формы отображаются реестры ГОУ, СРПГ, Сумматоры. По умолчанию выбран реестр ГОУ.</p>
        <p>
          При выборе ДЦ, объекты в правой части формы отображаются согласно картам ведения. Если ни один ДЦ не выбран, то в правой части формы отображаются все
          загруженные объекты НСИ.
        </p>
        <p>
          Для просмотра реестров СРПГ необходимо нажать на вкладку «СРПГ» в правой области формы <img src={image211} alt="image211" />, затем выбрать из списка
          необходимый тип объектов реестра СРПГ (Рисунок 13).
          <BigPicture>
            <img src={image210} alt="image210" />
            <AltPicture>Рисунок 13 – Список типов объектов реестра СРПГ</AltPicture>
          </BigPicture>
        </p>
        <p>Для просмотра реестров Сумматоры, необходимо нажать на вкладку «Сумматоры».</p>
        <TextPage>
          На форме доступны следующие инструменты:
          <br />
          <IconContainer>
            <div>
              <Icon width={16} name="search" />
            </div>
            - Выполнить фильтр по полю. <br />
          </IconContainer>
          <img src={image74} alt="image31" /> - Выполнить сортировку по полю . <br />
          <br />
        </TextPage>
        <p>
          Для просмотра карт ведения по объектам, необходимо напротив объекта нажать на кнопку <img src={image213} alt="image213" /> . Откроется окно с просмотром
          заинтересованных в объекте ДЦ.
          <BigPicture>
            <img src={image214} alt="image214" />
          </BigPicture>
        </p>
        <h5>Сравнение НСИ за 2 выбранные даты</h5>
        <p>
          Для просмотра результатов сравнения НСИ за даты, необходимо нажать на кнопку <img src={image215} alt="image215" /> .
        </p>
        <p>
          Откроется форма просмотра сравнения НСИ за 2 выбранные даты. (Рисунок 14).
          <BigPicture>
            <img src={image216} alt="image216" />
            <AltPicture>Рисунок 14 – Форма сравнения НСИ за 2 выбранные даты</AltPicture>
          </BigPicture>
        </p>
        <p>
          На форме необходимо выбрать 2 даты в полях <img src={image217} alt="image217" /> за которые необходимо выполнить сравнение версий НСИ, затем нажать кнопку
          сравнить <img src={image218} alt="image218" /> .
        </p>
        <p>
          На форме выводится информация по результатам сравнения НСИ за 2 выбранные даты (Рисунок 15).
          <BigPicture>
            <img src={image219} alt="image219" />
            <AltPicture>Рисунок 15 – Результаты сравнения НСИ за 2 выбранные даты</AltPicture>
          </BigPicture>
        </p>
        <p>
          Для выгрузки результатов сравнений НСИ необходимо нажать на кнопку <img src={image220} alt="image220" />.{" "}
        </p>
        <h5>Распространение НСИ</h5>
        <p>
          Для перехода к форме контроля распространения НСИ необходимо перейти на вкладку <img src={image221} alt="image221" /> .
        </p>
        <p>
          Откроется форма с деревом ДЦ и статусами распространения последней загруженной версии НСИ (Рисунок 16).
          <BigPicture>
            <img src={image222} alt="image222" />
            <AltPicture>Рисунок 16 – Форма отображение контроля распространения НСИ в ДЦ</AltPicture>
          </BigPicture>
        </p>
        <TextPage>
          На форме доступны следующие инструменты:
          <br />
          <IconContainer>
            <div>
              <Icon width={16} name="search" />
            </div>
            - Выполнить фильтр по полю. <br />
          </IconContainer>
          <img src={image74} alt="image31" /> - Выполнить сортировку по полю . <br />
          <br />
        </TextPage>
        <p>
          Для повтора распространения НСИ во все ДЦ со статусом «Ошибка» необходимо нажать на кнопку <img src={image223} alt="image223" /> .
        </p>
        <h5>Сопоставление объектов НСИ с объектами ОИК СК-11 в ИА</h5>
        <p>Централизованная часть СРПГ СК-11 ИА.</p>
        <p>
          Возможность сопоставления объектов, распространения в ДЦ и выполнение настроек характеристик по объектам доступна только пользователю с ролью Администратор НСИ.
        </p>
        <p>
          Для сопоставления объектов ГОУ реестра НСИ ИУС ЕСС с объектами ОИК СК-11 в Централизованной части Системы, необходимо перейти на вкладку{" "}
          <img src={image227} alt="image227" /> .
        </p>
        <p>
          Откроется форма редактора сопоставления объектов НСИ ИУС ЕСС и объектов ОИК СК-11. (Рисунок 17).
          <BigPicture>
            <img src={image224} alt="image224" />
            <AltPicture>Рисунок 17 – Форма редактора сопоставления объектов типа ГОУ с ОИК СК-11</AltPicture>
          </BigPicture>
        </p>
        <p>
          Для получения списка объектов из ОИК СК-11 необходимо нажать на кнопку <img src={image225} alt="image225" /> в области объектов ОИК СК-11. Загруженные объекты
          временно хранятся в СРПГ в соответствии с настройками, заданными Администратором Системы.{" "}
        </p>
        <p>
          Загрузка объектов из ОИК СК-11 может иметь несколько состояний:
          <BigPicture>
            <img src={image228} alt="image228" />
          </BigPicture>
          <BigPicture>
            <img src={image229} alt="image229" />
          </BigPicture>
          <BigPicture>
            <img src={image230} alt="image230" />
          </BigPicture>
        </p>
        <p>После успешной загрузки объектов из ОИК СК-11, загруженные объекты отображаются в интерфейсе. </p>
        <p>
          Для выполнения сопоставления объектов необходимо найти в списке объектов ИУС ЕСС объект, далее нажать на объект, чтобы загорелся индикатор{" "}
          <img src={image231} alt="image231" /> , в списке объектов ОИК СК-11 необходимо также найти объект, с которым необходимо выполнить сопоставление и нажать на
          кнопку <img src={image232} alt="image232" /> (если объект еще не имеет связей) или <img src={image233} alt="image233" /> (если объект уже имеет связи). При
          успешном выполнении сопоставления в области объекты ИУС ЕСС у сопоставляемого объекта установится UID.{" "}
        </p>
        <p>
          Для удаления связи объектов в области «Объекты ИУС ЕСС» необходимо напротив объекта нажать на кнопку <img src={image234} alt="image234" />.{" "}
        </p>
        <p>
          Для сохранения изменений на форме необходимо нажать на кнопку <img src={image235} alt="image235" /> .{" "}
        </p>
        <p>
          Для отмены изменений на форме необходимо нажать на кнопку <img src={image236} alt="image236" />
        </p>
        <p>
          Для фильтрации по сопоставленным и не сопоставленным объектам необходимо в фильтре установить значение <img src={image237} alt="image237" />.
        </p>
        <p>
          Для выгрузки результатов сопоставления необходимо нажать на <img src={image238} alt="image238" /> и выбрать из списка интересующий состав объектов.{" "}
          <img src={image239} alt="image239" />
        </p>
        <p>
          {" "}
          Для проверки наличия связи одного объекта ОИК СК-11 с несколькими объектами СРПГ необходимо нажать на кнопку <img src={image240} alt="image240" />. Откроется
          окно со списком всех дублирующих связей с UID ОИК СК-11 (Рисунок 18).{" "}
        </p>
        <p>
          В окне «Множественные связи» пункта меню «НСИ» -&gt; «Сопоставление объектов с СК-11» реализована возможность проверки выполненного сопоставления объектов СРПГ
          с объектами ОИК СК-11. Для запуска проверки необходимо нажать на кнопку <img src={image241} alt="image241" /> , в результате выполнения которой отобразятся
          сопоставленные (действующие и недействующие) объекты, имеющие ошибки. В случае отсутствия ошибок в табличной части окна «Множественные связи» отобразится
          уведомление «Нет данных».
        </p>
        <p>Автоматически выполняемые проверки в СРПГ (недопустимые множественные связи):</p>
        <p>− множественная связь нескольких объектов СРПГ одного типа с одним объектом ОИК СК-11;</p>
        <p>
          − множественная связь разных типов объектов СРПГ, имеющих одинаковый UID характеристики (UID типа измерения ОИК СК-11 - MeasurementType), с одним объектом ОИК
          СК-11.
        </p>
        <p> По завершению выполнения проверки, объекты с некорректными связями будут выведены на экран с оранжевой заливкой фона. </p>
        <p>
          {" "}
          Для сброса результата проверки необходимо открыть заново окно «Множественные связи».
          <BigPicture>
            <img src={image242} alt="image242" />
            <AltPicture>Рисунок 18 – Форма множественные связи сопоставления объектов типа ГОУ с ОИК СК-11</AltPicture>
          </BigPicture>{" "}
        </p>
        <p>
          Если ранее сопоставленного объекта нет в выбранной версии НСИ, то такие объекты отображаются в виде даты вместо наименования. Дата красным цветом отображается
          для объектов, которые ранее действовали в НСИ, дата зеленым цветом отображается для объектов, которые еще не действуют.{" "}
        </p>
        <p>
          На форме «Множественные связи» есть возможность сбросить связи, для этого необходимо нажать на кнопку <img src={image234} alt="image234" /> в поле «Действия»
          напротив объекта.{" "}
        </p>
        <p>
          Для выгрузки результата проверки, необходимо нажать на кнопку <img src={image198} alt="image198" /> .
        </p>
        <p>
          Для сохранения изменений на форме необходимо нажать на кнопку <img src={image235} alt="image235" /> .{" "}
        </p>
        <p>
          Для отмены изменений на форме необходимо нажать на кнопку <img src={image236} alt="image236" />
        </p>
        <p>
          Для просмотра связей по объекту в области объектов ОИК СК-11 у объектов с зеленым индикатором необходимо нажать на кнопку <img src={image213} alt="image213" />{" "}
          (Рисунок 19).
        </p>
        <BigPicture>
          <img src={image243} alt="image243" />
          <AltPicture>Рисунок 19 – Просмотр связей</AltPicture>
        </BigPicture>
        <p>
          После нажатия на «Просмотр связей» откроется окно «Просмотр связи по объекту» с отображением перечня объектов сопоставленных с выбранным UID ОИК СК-11 (Рисунок
          20).
          <BigPicture>
            <img src={image243} alt="image243" />
            <AltPicture>Рисунок 20 – Форма просмотра связей объекта типа ГОУ с ОИК СК-11</AltPicture>
          </BigPicture>
        </p>
        <p>
          В окне «Просмотр связи по объекту» есть возможность сбросить связь, для этого необходимо нажать на кнопку <img src={image234} alt="image234" /> в поле
          «Действия» напротив объекта.
        </p>
        <p>
          Для сохранения изменений в окне «Просмотр связи по объекту» необходимо нажать на кнопку <img src={image235} alt="image235" /> .{" "}
        </p>
        <p>
          Для отмены изменений в окне «Просмотр связи по объекту» необходимо нажать на кнопку <img src={image236} alt="image236" />{" "}
        </p>
        <h5>Распространение связей ГОУ в ДЦ</h5>
        <p>
          Для передачи списка сопоставленных ГОУ в СРПГ ДЦ, необходимо нажать на форме «Сопоставление объектов с СК-11» на кнопку <img src={image245} alt="image245" /> .
        </p>
        <p>
          По результатам успешного распространения ГОУ во все ДЦ станет активна кнопка <img src={image246} alt="image246" />.
        </p>
        <p>
          В случае, если ГОУ распространены не во все ДЦ станет активна кнопка <img src={image247} alt="image247" /> .
        </p>
        <p>
          При нажатии на кнопку протокола, откроется форма для просмотра результатов распространения. (Рисунок 21)
          <BigPicture>
            <img src={image248} alt="image248" />
            <AltPicture>Рисунок 21 – Форма протокола с результатами распространения результатов сопоставления ГОУ</AltPicture>
          </BigPicture>
        </p>
        <p>
          В случае, если распространение не выполнилось ни в один ДЦ, станет активна кнопка <img style={{ width: 90, height: 30 }} src={image249} alt="image249" /> .
        </p>
      </Page>

      <Page>
        <DescriptionPage name={`scheduled_schedules`}>3.2.2. Работа с Плановыми графиками</DescriptionPage>
        <p>Раздел «Плановые графики» предназначен для загрузки ПГ, просмотра информации по ПГ, выгрузке ПГ в xml, контролю распространения ПГ в ДЦ, акцепту ПГ.</p>
        <p>Раздел доступен для пользователей с ролью Администратор Системы, Администратор НСИ, Администратор НСИ ЕСС, Наблюдатель, Технолог.</p>
        <p>
          Для перехода к функциональности работы с плановыми графиками необходимо нажать раздел <img src={image250} alt="image250" /> на главной рабочей панели.
        </p>
        <h5>Общее описание загрузки и распространения ПГ в СРПГ СК-11</h5>
        <NumericOl>
          <NumericLi>
            При загрузке ПГ из ОПАМ/Мегаточки/файла выполняется разбор полученного ПГ в СРПГ.
            <NumericOl>
              <NumericLi>
                Согласно дате начала действия ПГ определяется актуальная версия НСИ, согласно настройкам в конфигурации определяется СЗ экземпляра СРПГ СК-11. Актуальная
                версия НСИ фильтруется по СЗ и полученные объекты сравниваются с объектами в ПГ. Объекты, отсутствующие в отфильтрованной версии НСИ исключаются из ПГ.
              </NumericLi>
              <NumericLi>
                Согласно настройке «Характеристики» определяются для каждого типа ПГ характеристики, которые необходимо включить в ПГ. Если в ПГ присутствуют
                характеристики, не удовлетворяющие настройке, то такие характеристики исключаются из исходного ПГ. Преобразованный ПГ записывается в СРПГ СК-11.
              </NumericLi>
            </NumericOl>
          </NumericLi>
          <NumericLi>
            Выполняется расчет значений ГОУ, согласно иерархической структуре объектов ГОУ из НСИ. Рассчитанные значения записываются в ПГ СРПГ СК-11.
          </NumericLi>
          <NumericLi>
            Выполняется проверка на необходимость расчета сумматоров, согласно настройке «Загрузка ПГ» для каждого типа ПГ. При наличии включенной опции, выполняется
            расчет значений сумматоров, согласно иерархической структуре объектов сумматоров из НСИ. Рассчитанные значения записываются в ПГ СРПГ СК-11.
          </NumericLi>
          <NumericLi>Выгрузка ПГ формата XML содержит состав ПГ, подготовленный по результатам действий пунктов 1,2 и 3.</NumericLi>
          <NumericLi>
            Распространение ПГ в ДЦ выполняется автоматически по факту завершения процесса загрузки ПГ в СРПГ. Распространение ПГ выполняется во все ДЦ параллельно.
          </NumericLi>
          <NumericLi>
            Формирование ПГ для отправки в ДЦ. Из БД считывается загруженный и рассчитанный ПГ, далее согласно картам ведения для каждого ДЦ определяется состав ПГ и
            формируются отдельные пакеты, которые не хранятся в БД ИА, а сразу распространяются в ДЦ.
          </NumericLi>
        </NumericOl>
        <h5>Загрузка и просмотр ПГ</h5>
        <p>Загрузка ПГ доступна пользователю с ролью Технолог.</p>
        <p>
          Для загрузки ПГ необходимо выбрать тип ПГ, Номер ПБР [1СЗ]/ПБР [2СЗ] (при необходимости), дату (при необходимости) и нажать кнопку{" "}
          <img src={image192} alt="image192" /> . При успешной загрузке ПГ отобразится в списке загруженных ПГ.
        </p>
        <p>
          Распространение ПГ в ДЦ выполняется автоматически по факту завершения процесса загрузки ПГ в СРПГ СК-11. Распространение ПГ выполняется во все ДЦ одновременно.{" "}
        </p>
        <p>
          Необходимый состав ПГ для каждого ДЦ определяется согласно картам ведения, формируются отдельные пакеты, которые не хранятся в СРПГ СК-11 ИА, а сразу
          распространяются в ДЦ.{" "}
        </p>
        <p>Для просмотра детальной информации по ПГ необходимо выделить ПГ, внизу формы откроется детальная информация.</p>
        <p>
          Для выгрузки ПГ в файл формата xml, необходимо в детализации ПГ выбрать действие <img src={image251} alt="image251" /> и выбрать вид выгрузки обычный или
          детальный ПГ. В детальном ПГ выполняется выгрузка с учетом наименований объектов для быстрого поиска необходимой информации для пользователей Системы.
          <BigPicture>
            <img src={image252} alt="image252" />
            <AltPicture>Рисунок 22 – Форма загрузки и просмотра ПГ</AltPicture>
          </BigPicture>
        </p>
        <TextPage>
          На форме также доступны следующие инструменты:
          <br />
          <IconContainer>
            <div>
              <Icon width={16} name="search" />
            </div>
            - Выполнить фильтр по полю. <br />
          </IconContainer>
          <img src={image74} alt="image31" /> - Выполнить сортировку по полю . <br />
          <br />
        </TextPage>
        <p>В табличной части формы «Список плановых графиков» централизованной части СРПГ отображаются следующие параметры:</p>
        <p>
          Опция выбора ПГ <img src={image253} alt="image253" /> – при включении опции (<img src={image254} alt="image254" /> ) внизу табличной части отображается более
          детальная информация о ПГ:
        </p>
        <ul>
          <li>Название – отображает тип и дату ПГ в формате «&lt;Тип ПГ&gt; на &lt;ДД.ММ.ГГГГ&gt;»;</li>
          <li>Тип пакета – отображается тип отправляемого пакета ПГ (одно из следующих значений: ППБР, ПБР, ПЭР, ПДГ, УДДГ, ДДГ);</li>
          <li>Временной интервал (начало) – отображает время начала действия, выбранного ПГ в формате «ДД.ММ.ГГГГ чч:мм:сс»;</li>
          <li>Временной интервал (конец) – отображает время окончания действия, выбранного ПГ в формате «ДД.ММ.ГГГГ чч:мм:сс»;</li>
          <li>Действие – позволяет выбрать тип (Детальный XML, Обычный XML) файла XML с ПГ, который необходимо экспортировать; </li>
          <li>Акцепт в ОпАМ – отображает дату и время команды акцепта, направленной в ИУС «ОпАМ» из СРПГ ИА (ОДУ Востока) в формате «ДД.ММ.ГГГГ чч:мм:сс». </li>
          <li>Название – отображает тип и дату ПГ в формате «&lt;Тип ПГ &gt; на &lt;ДД.ММ.ГГГГ&gt;»;</li>
          <li>­Время формирования – указывается время формирования ПГ в СРПГ в формате «ДД.ММ.ГГГГ чч:мм:сс»;</li>
          <li>Тип – указывается тип ПГ в формате «ПБР-&lt;номер&gt;», «УДДГ-&lt;номер&gt;», «ППБР», «ПБР», «ПЭР», «ПДГ», «ДДГ»;</li>
          <li>­СЗ – указывается синхронная зона ПГ: значение «1» или «2»;</li>
          <li>­Инициатор загрузки ПГ – указывается пользователь (в формате фамилия и инициалы), который инициировал загрузку ПГ;</li>
          <li>­Статус ПГ – указывается статус распространения/записи ПГ;</li>
          <li>­Время акцепта – указывается дата и время команды акцепта ПГ в формате «ДД.ММ.ГГГГ чч:мм:сс»;</li>
          <li>
            ­Инициатор команды акцепта – в поле фиксируется пользователь (в формате фамилия и инициалы) или автоматический способ акцепта, в случае настройки
            автоматической команды акцепта для данного типа ПГ.
          </li>
        </ul>
        <h5>Просмотр статуса распространения загруженных ПГ</h5>
        <p>
          Для просмотра детальной информации по ПГ, необходимо нажать на кнопку <img src={image255} alt="image255" /> у необходимого ПГ.
        </p>
        <p>
          Откроется форма со статусами распространения выбранного ПГ в ОИК СК-11 ДЦ, ИУС «MODES-Terminal» ДЦ, ИУС СРДК ДЦ (Рисунок 23).
          <BigPicture>
            <img src={image23} alt="image23" />
            <AltPicture>Рисунок 23 – Форма контроля распространения ПГ в ДЦ</AltPicture>
          </BigPicture>
        </p>
        <p> При необходимости пользователь может изменить фильтры просмотра распространения ПГ и выбрать другую дату, а также ПГ.</p>
        <p>
          Если в настройках для типа ПГ установлен способ акцепта «ручной», то на форме будет активна кнопка <img src={image257} alt="image257" /> . При нажатии на кнопку
          выполняется акцепт ПГ и распространение команды акцепта в ДЦ, от которых получена квитанция об успешной записи неакцептованного ПГ в ОИК СК-11. Если из каких-то
          ДЦ не получена квитанция об успешной записи неакцептованного ПГ в ОИК СК-11, то команда акцепта будет автоматически отправляться по факту поступления квитанций
          от ДЦ в ИА.
        </p>
        <p>
          При возникновении ошибки распространения ПГ в ДЦ необходимо выбрать ДЦ, выставив опцию напротив ДЦ <img src={image258} alt="image258" /> и нажать на кнопку{" "}
          <img src={image223} alt="image223" /> . При повторной отправке ПГ в ДЦ повторно формируются пакеты с ПГ, только в разрезе тех ДЦ, которые выбрал пользователь и
          далее ПГ распространяются в ДЦ.
        </p>
        <p>
          При возникновении ошибки распространения команды акцепта в ДЦ необходимо выбрать ДЦ, выставив опцию напротив ДЦ <img src={image258} alt="image258" /> и нажать
          на кнопку <img src={image259} alt="image259" /> .
        </p>
        <TextPage>
          На форме доступны следующие инструменты:
          <br />
          <IconContainer>
            <div>
              <Icon width={16} name="search" />
            </div>
            - Выполнить фильтр по полю. <br />
          </IconContainer>
          <img src={image74} alt="image31" /> - Выполнить сортировку по полю . <br />
          <br />
        </TextPage>
        <p>
          {" "}
          <img src={image204} alt="image204" /> - Выбрать все объекты/Сбросить выделение всех выбранных объектов.
        </p>
        <h5>Просмотр сводного статуса распространения и записи ПГ во все внешние системы всех ДЦ</h5>
        <p>
          Для просмотра сводного статуса распространения и записи ПГ во все внешние системы всех ДЦ в столбце «Статус ПГ» необходимо нажать индикатор статуса у
          определенного ПГ, индикаторы расписаны ниже, в Таблице 1.
        </p>
        <p>
          При нажатии на индикатор статуса ПГ откроется модальное окно сводного статуса ПГ, при этом, сводный статус ПГ автоматически обновляется в этом модальном окне.
        </p>
        <p>
          <b>Таблица 1. Виды статусов ПГ и условия их формирования.</b>
        </p>
        <StyledTable>
          <thead>
            <tr>
              <th>№</th>
              <th>Индикатор</th>
              <th>Условие формирования статуса в столбце «Статус ПГ»</th>
              <th>Текст статуса, отображаемый в окне с информацией о ПГ</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>1.</td>
              <td>
                <StatusCircle />
              </td>
              <td>-</td>
              <td>Отправка ПГ до команды акцепта в ДЦ</td>
            </tr>
            <tr>
              <td>2.</td>
              <td>
                <StatusCircle />
              </td>
              <td>-</td>
              <td>ПГ до команды акцепта получен в ДЦ. Ожидается квитанция о записи ПГ до команды акцепта в ОИК СК-11</td>
            </tr>
            <tr>
              <td>3.</td>
              <td>
                <StatusCircle status="AWAIT" />
              </td>
              <td>-</td>
              <td>Квитанция о записи ПГ до команды акцепта в ОИК СК-11 получена. Ожидается команда акцепта ПГ</td>
            </tr>
            <tr>
              <td>4.</td>
              <td>
                <StatusCircle status="AWAIT" />
              </td>
              <td>-</td>
              <td>Отправка команды акцепта ПГ в ДЦ</td>
            </tr>
            <tr>
              <td>5.</td>
              <td>
                <StatusCircle status="AWAIT" />
              </td>
              <td>-</td>
              <td>Запись команды акцепта ПГ во внешние системы</td>
            </tr>
            <tr>
              <td>6.</td>
              <td>
                <StatusCircle status="DONE" />
              </td>
              <td>
                <p>Одновременное выполнение следующих условий:</p>
                <ul>
                  <li>6.1 ПГ записан во все ОИК СК-11 ДЦ.</li>
                  <li>6.2 ПГ записан во все Modes-Terminal ДЦ.</li>
                  <li>6.3 ПГ записан во все СРДК ДЦ.</li>
                </ul>
                <p>В логике формирования индикатора не учитываются экземпляры ИУС, в которые не выполняется отправка ПГ, например, Донбасское РДУ</p>
              </td>
              <td>ПГ записан в ОИК СК-11, Modes-Terminal, СРДК всех ДЦ</td>
            </tr>
            <tr>
              <td>7.</td>
              <td>
                <StatusCircle status="COND_FAIL" />
              </td>
              <td>
                <ul>
                  <li>7.1 ПГ записан в ОИК СК-11 ИА и в ОИК СК-11 всех ОДУ 1СЗ (ПГ не записан в ОИК СК-11 одного или нескольких РДУ 1 СЗ).</li>
                  <NoBulletItem>и/или</NoBulletItem>
                  <li>7.2 ПГ записан в Modes-Terminal ИА и в Modes-Terminal всех ОДУ 1 СЗ (ПГ не записан в Modes-Terminal одного или нескольких РДУ 1 СЗ).</li>
                  <NoBulletItem>и/или</NoBulletItem>
                  <li>7.3 ПГ не записан в СРДК одного или нескольких ДЦ любого уровня.</li>
                </ul>
                <p>В логике формирования индикатора не учитываются экземпляры ИУС, в которые не выполняется отправка ПГ, например, Донбасское РДУ</p>
              </td>
              <td>
                <p>Ошибка записи ПГ в ОИК СК-11: &lt;Наименование РДУ&gt;, &lt;Наименование РДУ&gt;</p>
                <p>Ошибка записи ПГ в Modes Terminal: &lt;Наименование РДУ&gt;, &lt;Наименование РДУ&gt;</p>
                <p>Ошибка записи ПГ в СРДК: &lt;Наименование ДЦ&gt;, &lt;Наименование ДЦ&gt;</p>
                <p>
                  Порядок следования ДЦ в тексте статуса отображается в соответствии с иерархической структурой СО (ЦДУ -&gt; ОДУ -&gt; РДУ). В рамках одного уровня ДУ
                  порядок следования ДЦ выстраивается в алфавитном порядке. Текст статуса автоматически (динамически) формируется на основании возникших ошибок (например,
                  при отсутствии ошибок записи ПГ в ОИК СК-11 ДЦ, текст ошибки о записи ПГ в ОИК СК-11 не отображается).
                </p>
              </td>
            </tr>
            <tr>
              <td>8.</td>
              <td>
                <StatusCircle status="FAIL" />
              </td>
              <td>-</td>
              <td>
                <p>Ошибка отправки ПГ до команды акцепта в СРПГ: &lt;Наименование ДЦ&gt;, &lt;Наименование ДЦ&gt;</p>
                <p>
                  Порядок следования ДЦ в тексте статуса отображается в соответствии с иерархической структурой СО (ЦДУ -&gt; ОДУ -&gt; РДУ). В рамках одного уровня ДУ
                  порядок следования ДЦ выстраивается в алфавитном порядке (например, Архангельское РДУ, Балтийское РДУ).
                </p>
              </td>
            </tr>
            <tr>
              <td>9.</td>
              <td>
                <StatusCircle status="FAIL" />
              </td>
              <td>-</td>
              <td>
                <p>Ошибка отправки команды акцепта ПГ в СРПГ: &lt;Наименование ДЦ&gt;, &lt;Наименование ДЦ&gt;</p>
                <p>
                  Порядок следования ДЦ в тексте статуса отображается в соответствии с иерархической структурой СО (ЦДУ -&gt; ОДУ -&gt; РДУ). В рамках одного уровня ДУ
                  порядок следования ДЦ выстраивается в алфавитном порядке.
                </p>
              </td>
            </tr>
            <tr>
              <td>10.</td>
              <td>
                <StatusCircle status="FAIL" />
              </td>
              <td>
                <ul>
                  <li>10.1. ПГ до команды акцепта не записан в ОИК СК-11 ИА</li>
                  <NoBulletItem>и/или</NoBulletItem>
                  <li>10.2. ПГ до команды акцепта не записан в ОИК СК-11 ОДУ (одно или несколько ОДУ).</li>
                </ul>
                <p>В логике формирования индикатора не учитываются экземпляры ИУС, в которые не выполняется отправка ПГ, например, Донбасское РДУ</p>
              </td>
              <td>
                <p>Ошибка записи ПГ до команды акцепта в ОИК СК-11: &lt;Наименование ДЦ&gt;, &lt;Наименование ДЦ&gt;</p>
                <p>
                  Порядок следования ДЦ в тексте статуса отображается в соответствии с иерархической структурой СО (ЦДУ -&gt; ОДУ -&gt; РДУ). В рамках одного уровня ДУ
                  порядок следования ДЦ выстраивается в алфавитном порядке.
                </p>
              </td>
            </tr>
            <tr>
              <td>11.</td>
              <td>
                <StatusCircle status="FAIL" />
              </td>
              <td>
                <ul>
                  <li>10.1. Команда акцепта ПГ не записана в ОИК СК-11 ИА</li>
                  <NoBulletItem>и/или</NoBulletItem>
                  <li>10.2. Команда акцепта ПГ не записана в ОИК СК-11 ОДУ (одно или несколько ОДУ);</li>
                  <NoBulletItem>и/или</NoBulletItem>
                  <li>10.3. Команда акцепта ПГ не записана в Modes-Terminal ИА</li>
                  <NoBulletItem>и/или</NoBulletItem>
                  <li>10.4 Команда акцепта ПГ не записана в Modes-Terminal ОДУ (одно или несколько ОДУ).</li>
                </ul>
                <p>В логике формирования индикатора не учитываются экземпляры ИУС, в которые не выполняется отправка ПГ, например, Донбасское РДУ</p>
              </td>
              <td>
                <p>Ошибка записи команды акцепта ПГ в ОИК СК-11: &lt;Наименование ДЦ&gt;, &lt;Наименование ДЦ&gt;</p>
                <p>Ошибка записи команды акцепта ПГ в Modes Terminal: &lt;Наименование ДЦ&gt;, &lt;Наименование ДЦ&gt;</p>
                <p>Ошибка записи команды акцепта ПГ в СРДК: &lt;Наименование ДЦ&gt;, &lt;Наименование ДЦ&gt;</p>
                <p>
                  Порядок следования ДЦ в тексте статуса отображается в соответствии с иерархической структурой СО (ЦДУ -&gt; ОДУ -&gt; РДУ). В рамках одного уровня ДУ
                  порядок следования ДЦ выстраивается в алфавитном порядке.
                </p>
                <p>
                  Текст статуса автоматически (динамически) формируется на основании возникших ошибок (например, при отсутствии ошибок записи ПГ в ОИК СК-11 ДЦ, текст
                  ошибки о записи ПГ в ОИК СК-11 не отображается в тексте статуса). Ошибки записи ПГ в СРДК не влияют на формирование индикатора статуса, в тексте статуса
                  информация о записи ПГ в СРДК отображается информационно.
                </p>
              </td>
            </tr>
            <tr>
              <td colSpan={4}>
                <p>
                  В случае одновременной фиксации различных статусов распространения, записи ПГ, описанных в пунктах 8, 9, 10, 11 таблицы 1, то в тексте статуса
                  отображается результирующая совокупность ошибок (например, при одновременной фиксации ошибок пп. 9, 10 в тексте статуса отражается информация об
                  ошибках: отправки команды акцепта ПГ в ДЦ; записи ПГ в ОИК СК-11, которые визуально отделены).
                </p>
                <p>
                  В случае одновременной фиксации статуса пункта 7 (таблицы 1) со статусами пунктов 8, 9, то отображается индикатор статуса пункта 8 или 9 таблицы 1 (
                  <StatusCircle status="FAIL" style={{ display: "inline-block", verticalAlign: "middle" }} />
                  ), а в тексте статуса отображается результирующая совокупность ошибок пункта 7 и пункта 8 и/или 9 таблицы 1 (текст ошибок разных статусов визуально
                  отделен).
                </p>
              </td>
            </tr>
          </tbody>
        </StyledTable>

        <h5>Квитирование ошибки распространения и записи ПГ</h5>
        <p>
          При фиксации в столбце «Статус ПГ» пункта меню «Плановые графики» Централизованной части интерфейса пользователя СРПГ ошибки (индикатор пп. 9-13 таблицы 2)
          пользователю СРПГ, выполняющему загрузку (акцепт) соответствующего ПГ, отображается окно с текстом, указанным в столбце «Текст уведомления» таблицы 2. При этом
          данное окно должно отображается до его квитирования пользователем – до нажатия кнопки «Ок» (Рисунок 24).
          <BigPicture>
            <img src={image24} alt="Окно квитирования ошибки" />
            <AltPicture>Рисунок 24 – Окно квитирования ошибки распространения и записи ПГ</AltPicture>
          </BigPicture>
        </p>
        <p>
          <b>Уточнение!</b> Если загрузку и акцепт ПГ выполняют разные пользователи, то при получении ошибки при распространении акцепта ПГ, уведомление получает только
          инициатор акцепта. Тот, кто грузил ПГ, не получает это уведомление.
        </p>
        <p>
          В Централизованной части (ИА, ОДУ Востока) существует возможность настраивать квитирование в части отображения/не отображения. Для этого в Настройках
          пользователя с ролью Технолог есть чекбокс &quot;Отображать квитирование ПГ&quot; (по умолчанию он = Выкл.). При включенном чекбоксе &quot;Отображать
          квитирование ПГ&quot; пользователь на текущем экземпляре СРПГ будет получать окна квитирования, если чекбокс выключен, то получать окна не будет.
        </p>
      </Page>

      <DescriptionPage name={`working_with_partitions_in_distributed`}>3.3. Работа с разделами в Распределенных частях Системы (ОДУ, РДУ)</DescriptionPage>
      <DescriptionPage name={`work_nsi`}>3.3.1. Работа с НСИ</DescriptionPage>
      <p>В Распределенных частях раздел предназначен для просмотра НСИ, сравнения загруженных версий НСИ, сопоставления реестров НСИ ДЦ с объектами ОИК СК-11.</p>
      <p>Просмотр данных НСИ доступен для пользователей с ролью Администратор НСИ, Администратор НСИ ЕСС, Наблюдатель, Технолог.</p>
      <p>
        Для перехода к функциональности работы с НСИ необходимо нажать раздел <img src={image189} alt="image189" /> на главной рабочей панели.
      </p>
      <p>Просмотр реестров НСИ и Сравнение НСИ за 2 выбранные даты описаны в разделе 3.2.1 данной инструкции.</p>
      <h5>Просмотр истории загрузки НСИ</h5>
      <p>
        Для просмотра истории полученных версий НСИ в ДЦ необходимо нажать на кнопку <img src={image260} alt="image260" /> .
      </p>
      <p>
        В окне просмотра истории загрузки НСИ отображается дата, на которую была загружена версия НСИ и информация о загрузке. (Рисунок 25)
        <BigPicture>
          <img src={image261} alt="image261" />
          <AltPicture>Рисунок 25 – Форма синхронизации НСИ в ДЦ</AltPicture>
        </BigPicture>
      </p>
      <h5>Сопоставление объектов НСИ ИУС ЕСС с объектами ОИК СК-11</h5>
      <p>
        Для сопоставления объектов реестра НСИ ИУС ЕСС с объектами ОИК СК-11 в распределенной части Системы, необходимо перейти на вкладку{" "}
        <img src={image262} alt="image262" /> .
      </p>
      <p>
        Откроется форма редактора сопоставления объектов НСИ ИУС ЕСС и объектов ОИК СК-11. (Рисунок 26).
        <BigPicture>
          <img src={image263} alt="image263" />
          <AltPicture>Рисунок 26 – Форма редактора сопоставления объектов ИУС ЕСС с ОИК СК-11</AltPicture>
        </BigPicture>
      </p>
      <p>
        Для работы с формой пользователю необходимо сначала выставить необходимые фильтры в левом верхнем углу формы. <img src={image264} alt="image264" />{" "}
      </p>
      <p>
        Список объектов НСИ отображается по состоянию на выбранную дату (по умолчанию выбран текущий день) для выбранного типа реестра (ГОУ, СРПГ, Сумматор) и типа
        объекта. Для применения фильтра необходимо нажать на кнопку <img src={image265} alt="image265" /> . Объекты типа ГОУ должны сопоставляться только в СРПГ ИА и
        далее должны быть распространены в СРПГ ДЦ.{" "}
      </p>
      <p>
        Для получения списка объектов из ОИК СК-11 необходимо нажать на кнопку <img src={image266} alt="image266" /> в области объектов ОИК СК-11. Загруженные объекты
        временно хранятся в СРПГ в соответствии с настройками заданными администратора Системы.{" "}
      </p>
      <p>
        Загрузка объектов из ОИК СК-11 может иметь несколько состояний:
        <BigPicture>
          <img src={image267} alt="image267" />
        </BigPicture>
        <BigPicture>
          <img src={image268} alt="image268" />
        </BigPicture>
        <BigPicture>
          <img src={image269} alt="image269" />
        </BigPicture>
      </p>
      <p>После успешной загрузки объектов из ОИК СК-11, загруженные объекты отображаются в интерфейсе. </p>
      <p>
        Для выполнения сопоставления объектов необходимо найти в списке объектов ИУС ЕСС объект, далее нажать на объект, чтобы загорелся индикатор{" "}
        <img src={image231} alt="image231" /> , в списке объектов ОИК СК-11 необходимо также найти объект, с которым необходимо выполнить сопоставление и нажать на кнопку{" "}
        <img src={image232} alt="image232" /> (если объект еще не имеет связей) или <img src={image233} alt="image233" /> (если объект уже имеет связи). При успешном
        выполнении сопоставления в области объекты ИУС ЕСС у сопоставляемого объекта установится UID.{" "}
      </p>
      <p>
        Для удаления сопоставления объектов необходимо напротив объекта нажать на кнопку <img src={image234} alt="image234" /> .{" "}
      </p>
      <p>
        Для сохранения изменений на форме необходимо нажать на кнопку <img src={image235} alt="image235" /> .{" "}
      </p>
      <p>
        Для отмены изменений на форме необходимо нажать на кнопку <img src={image236} alt="image236" />
      </p>
      <p>
        Для фильтрации по сопоставленным и не сопоставленным объектам необходимо в фильтре установить значение. <img src={image270} alt="image270" />
      </p>
      <p>
        Для выгрузки результатов сопоставления необходимо нажать на <img src={image271} alt="image271" /> и выбрать из списка интересующий состав объектов.{" "}
        <img src={image272} alt="image272" />
      </p>
      <p>
        {" "}
        Для проверки наличия множественной связи объектов необходимо нажать на кнопку <img src={image273} alt="image273" /> . Откроется окно со списком всех дублирующих
        связей с UID ОИК СК-11 (Рисунок 26).{" "}
      </p>
      <p>
        В окне «Множественные связи» пункта меню «НСИ» -&gt; «Сопоставление объектов с СК-11» реализована возможность проверки выполненного сопоставления объектов СРПГ с
        объектами ОИК СК-11. Для запуска проверки необходимо нажать на кнопку <img src={image274} alt="image274" /> , в результате выполнения которой отобразятся
        сопоставленные (действующие и недействующие) объекты, имеющие ошибки. В случае отсутствия ошибок в табличной части окна «Множественные связи» отобразится
        уведомление «Нет данных».
      </p>
      <p>Автоматически выполняемые проверки в СРПГ (недопустимые множественные связи):</p>
      <p>− множественная связь нескольких объектов СРПГ одного типа с одним объектом ОИК СК-11;</p>
      <p>
        − множественная связь разных типов объектов СРПГ, имеющих одинаковый UID характеристики (UID типа измерения ОИК СК-11 - MeasurementType), с одним объектом ОИК
        СК-11.
      </p>
      <p>По завершению выполнения проверки, объекты с некорректными связями будут выведены на экран с оранжевой заливкой фона. </p>
      <p>
        Для сброса результата проверки необходимо открыть заново окно «Множественные связи».
        <BigPicture>
          <img src={image275} alt="image275" />
          <AltPicture>Рисунок 27 – Форма множественные связи сопоставления объектов ИУС ЕСС с ОИК СК-11</AltPicture>
        </BigPicture>
      </p>
      <p>
        Если ранее сопоставленного объекта нет в выбранной версии НСИ, то такие объекты отображаются в виде даты вместо наименования. Дата красным цветом отображается для
        объектов, которые ранее действовали в НСИ, дата зеленым цветом отображается для объектов, которые еще не действуют.
      </p>
      <p>На форме «Множественные связи» есть возможность сбросить связи, для этого необходимо нажать на кнопку в поле «Действия» напротив объекта. </p>
      <p>
        Для выгрузки результата проверки, необходимо нажать на кнопку <img src={image198} alt="image198" /> .
      </p>
      <p>
        Для сохранения изменений на форме необходимо нажать на кнопку <img src={image235} alt="image235" /> .{" "}
      </p>
      <p>
        Для отмены изменений на форме необходимо нажать на кнопку <img src={image236} alt="image236" />
      </p>
      <p>
        {" "}
        Для просмотра связей по объекту в области объектов ОИК СК-11 у объектов с зеленым индикатором необходимо нажать на кнопку <img
          src={image213}
          alt="image213"
        />{" "}
        (Рисунок 28).
        <BigPicture>
          <img src={image276} alt="image276" />
          <AltPicture>Рисунок 28 – Просмотр связей</AltPicture>
        </BigPicture>
      </p>
      <p>После нажатия на «Просмотр связей» откроется окно с установленной связью объектов по выбранному UID ОИК СК-11 (Рисунок 29).</p>
      <BigPicture>
        <img src={image277} alt="image277" />
        <AltPicture>Рисунок 29 – Форма просмотра связей объекта ОИК СК-11</AltPicture>
      </BigPicture>
      <p>На форме «Просмотр связи» есть возможность сбросить связи, для этого необходимо нажать на кнопку в поле «Действия» напротив объекта. </p>
      <p>
        Для сохранения изменений на форме необходимо нажать на кнопку <img src={image235} alt="image235" /> .{" "}
      </p>
      <p>
        Для отмены изменений на форме необходимо нажать на кнопку <img src={image236} alt="image236" />.
      </p>
      <h5>Настройка типа генерации для сумматоров</h5>
      <p>
        Необходимо установить фильтр реестра объектов на форме «Сумматоры» и тип объектов «Сумматоры генерации» (Рисунок 30).
        <BigPicture>
          <img src={image278} alt="image278" />
          <AltPicture>Рисунок 30 – Функциональность сумматоров редактора сопоставления объектов</AltPicture>
        </BigPicture>{" "}
      </p>
      <p>
        В поле «Типы» необходимо нажать на напротив необходимого объекта и далее выбрать тип генерации из списка. При изменении типа генерации, новое значение сохраняется
        сразу после изменения.
      </p>
      <p>
        Так как характеристики для каждого типа сумматора имеют уникальный UID СК-11, после изменения типа генерации у сумматора, необходимо выполнить настройку
        характеристик по сопоставленным объектам.
      </p>
      <h5>Поиск измерений в ОИК СК-11</h5>
      <p>
        Для выполнения проверки наличия измерений для записи плановых параметров в ОИК СК-11 согласно выполненным настройкам НСИ в СРПГ необходимо нажать на кнопку{" "}
        <img src={image279} alt="image279" /> . По завершению поиска будет установлен зеленый индикатор <img src={image280} alt="image280" /> и активна кнопка{" "}
        <img src={image281} alt="image281" /> .{" "}
      </p>
      <p>
        Для выгрузки результатов проверки необходимо нажать на кнопку <img src={image281} alt="image281" /> и открыть выгруженный xml файл.
      </p>
      <h5>Настройка характеристик по сопоставленным объектам</h5>
      <p>
        Настройка характеристик по объектам доступна для сопоставленных объектов ИУС ЕСС. Напротив объекта необходимо нажать на шестеренку , откроется окно с настройкой
        (Рисунок 31).
        <BigPicture>
          <img src={image282} alt="image282" />
          <AltPicture>Рисунок 31 – Форма настройки характеристик по объектам</AltPicture>
        </BigPicture>{" "}
      </p>
      <p>Необходимо выбрать тип ПГ и выставить опции напротив необходимых характеристик. </p>
      <p>
        Для сохранения изменений на форме необходимо нажать на кнопку <img src={image235} alt="image235" /> .{" "}
      </p>
      <p>
        Для отмены изменений на форме необходимо нажать на кнопку <img src={image236} alt="image236" />.
      </p>
      <Page>
        <DescriptionPage name={`working_with_scheduled_schedules`}>3.3.2. Работа с Плановыми графиками</DescriptionPage>
        <p>
          Данный раздел Системы предназначен для просмотра информации по полученным ПГ, выгрузке ПГ в xml, контролю распространения ПГ во внешние системы (ОИК СК-11,
          ёЖ-3, ИУС «СРДК», ИУС «MODES-Terminal»), резервным функциям по загрузке ПГ из файла, ручной записи ПГ во внешние системы.
        </p>
        <p>Резервная загрузка ПГ из файла, ручная запись ПГ доступно пользователю с ролью Технолог.</p>
        <p>Резервная загрузка ПГ из файла необходима только в случае аварийных ситуаций. Технология применяется по факту отсутствия команды акцепта ПГ в ДЦ.</p>
        <p>
          Для перехода к функциональности работы с Плановыми графиками необходимо нажать раздел <img src={image250} alt="image250" /> на главной рабочей панели.
        </p>
        <h5>Просмотр списка ПГ</h5>
        <p>
          Для просмотра списка загруженных ПГ в ДЦ за выбранную дату необходимо нажать на поле даты (Рисунок 32), затем выбрать на месяце день для просмотра и нажать
          кнопку <img src={image208} alt="image208" /> . По умолчанию выставлена дата на текущие сутки.
          <BigPicture>
            <img src={image32} alt="image32" />
            <AltPicture>Рисунок 32 – Форма выбора даты для просмотра списка загруженных ПГ в ДЦ</AltPicture>
          </BigPicture>
        </p>
        <p>
          На форме отобразится список загруженных ПГ за выбранную дату. (Рисунок 33)
          <BigPicture>
            <img src={image33} alt="image33" />
            <AltPicture>Рисунок 33 – Форма просмотра загруженных ПГ в ДЦ</AltPicture>
          </BigPicture>
        </p>
        <p>
          В списке отображается наименование ПГ, время формирования ПГ, тип, Синхронная зона, а также статусы и время записи ПГ во внешние системы ДЦ (ОИК СК-11, ИУС
          «MODES-Terminal», ИУС «СРДК»).
        </p>
        <p>В табличной части формы «Список плановых графиков» распределенной части СРПГ отображаются следующие области:</p>
        <p>1) Список плановых графиков: </p>
        <ul>
          <li>
            Опция выбора ПГ <img src={image253} alt="image253" /> - при включении опции (<img src={image254} alt="image254" /> ) внизу табличной части отображается более
            детальная информация о ПГ (см. списки ниже – «Информация о ПГ», «Детальное распространение ПГ и действия»:
          </li>
          <li>Название – отображает тип и дату ПГ в формате «&lt;Тип ПГ&gt; на &lt;ДД.ММ.ГГГГ&gt;»;</li>
          <li>Время формирования – указывается время формирования ПГ в СРПГ в формате «ДД.ММ.ГГГГ чч:мм:сс»;</li>
          <li>Тип – указывается тип ПГ в формате «ПБР-&lt;номер&gt;», «УДДГ-&lt;номер&gt;», «ППБР», «ПБР», «ПЭР», «ПДГ», «ДДГ»;</li>
          <li>СЗ – указывается синхронная зона ПГ: значение «1» или «2»;</li>
          <li>
            Запись в ОИК СК-11 – указывается статус записи ПГ в ОИК СК-11, в случае выполнения записи указывается дата и время записи ПГ в ОИК-СК-11 в формате «ДД.ММ.ГГГГ
            чч:мм:сс»;{" "}
          </li>
          <li>
            Запись в ёЖ-3 – указывается статус записи ПГ в ёЖ-3, в случае выполнения записи указывается дата и время записи ПГ в ёЖ-3 в формате «ДД.ММ.ГГГГ чч:мм:сс»;{" "}
          </li>
          <li>
            Запись в Modes-Terminal – указывается статус записи ПГ в Modes-Terminal, в случае выполнения записи указывается дата и время записи ПГ в Modes-Terminal в
            формате «ДД.ММ.ГГГГ чч:мм:сс»;{" "}
          </li>
          <li>
            Запись в СРДК – указывается статус записи ПГ в СРДК, в случае выполнения записи указывается дата и время записи ПГ в СРДК в формате «ДД.ММ.ГГГГ чч:мм:сс».
          </li>
        </ul>
        <AltPicture>Примечание: статусы, отображаемые при передаче ПГ во внешние системы описаны в разделе 3.3.3.</AltPicture>
        <p>
          2) Информация о ПГ (доступен при включении опции (<img src={image254} alt="image254" />) напротив ПГ):
        </p>
        <ul>
          <li>
            Тип пакета – отображается тип отправляемого пакета ПГ (одно из следующих значений: ППБР, ПБР, ПЭР, ПДГ, УДДГ, ДДГ). В данном параметре также присутствует
            функция, позволяющая экспортировать ПГ в файл XML выбранного пользователем типа (Детальный XML, Обычный XML);
          </li>
          <li>
            Инициатор загрузки ПГ – указывается пользователь (в формате фамилия и инициалы), который инициировал загрузку ПГ. Если ПГ загружен с помощью резервной
            технологии, то данная информация будет отражена в данном параметре;
          </li>
          <li>Время получения ПГ в ДЦ – указывается дата и время получения ПГ в ДЦ в формате «ДД.ММ.ГГГГ чч:мм:сс»;</li>
          <li>
            Инициатор команды акцепта – в поле фиксируется пользователь (в формате фамилия и инициалы) или автоматический способ акцепта, в случае настройки
            автоматической команды акцепта для данного типа ПГ;
          </li>
          <li>Акцепт получен в ДЦ – указывается дата и время получения команды акцепта ПГ в формате «ДД.ММ.ГГГГ чч:мм:сс»;</li>
          <li>Действие – функции «Отправить ПГ (до акцепта)», «Повторить акцепт», описанные в разделе 3.3.3.</li>
        </ul>
        <p>
          3) Детальная информация о записи ПГ во внешние системы (доступен при включении опции (<img src={image254} alt="image254" /> ) напротив ПГ):
        </p>
        <ul>
          <li>
            Статус (заголовок таблицы) – указывается статус распространения/записи ПГ (статусы, отображаемые при передаче ПГ во внешние системы описаны в разделе 3.3.3);
          </li>
          <li>Внешняя система (заголовок таблицы) – указывается внешняя система, в которую выполнялась запись ПГ;</li>
          <li>Время записи (заголовок таблицы) – указывается время записи ПГ во внешнюю систему в формате «ДД.ММ.ГГГГ чч:мм:сс»;</li>
          <li>
            История <img src={image285} alt="image285" /> – кнопка вызывает просмотр истории записи ПГ;
          </li>
          <li>Действие – позволяет пользователю выполнить первичную/повторную отправку ПГ во внешнюю систему;</li>
        </ul>
        <AltPicture>Примечание: Запись ПГ типов «ПЭР», «ПДГ» во Внешние системы ёЖ-3, MODES-Terminal и ИУС «СРДК» не выполняется.</AltPicture>
        <p>После получения команды акцепта ПГ автоматически записываются в Системы (параллельно): ОИК СК-11, ИУС «MODES-Terminal», ИУС «СРДК».</p>
        <p>Статусы, отображаемые при передаче ПГ:</p>
        <ul>
          <li>
            {" "}
            <img src={image286} alt="image286" /> передача ПГ не выполнялась;
          </li>
          <li>
            {" "}
            <img src={image287} alt="image287" /> ПГ записан во внешней системе;
          </li>
          <li>
            {" "}
            <img src={image288} alt="image288" /> ПГ записан в ОИК СК-11, ожидается команда акцепта;
          </li>
          <li>
            {" "}
            <img src={image289} alt="image289" /> при записи ПГ возникла ошибка;
          </li>
          <li>
            {" "}
            <img src={image290} alt="image290" /> требуется повторная запись не утвержденного ПГ по причине загрузки/акцепта ранее действующего в текущих сутках ПГ типа
            ПБР, УДДГ;
          </li>
          <li>
            {" "}
            <img src={image291} alt="image291" /> требуется повторная запись утвержденного ПГ по причине загрузки/акцепта ранее действующего в текущих сутках ПГ типа ПБР,
            УДДГ;
          </li>
        </ul>
        <p>
          Повторная отправка во внешние системы полученных в регламентное время ПГ доступна при возникновении ошибки по кнопке <img src={image292} alt="image292" />{" "}
          напротив наименования системы.
        </p>
        <p>
          Полученные вне регламента ПГ типа ПБР [1СЗ]/ПБР [2СЗ] до получения команды акцепта могут быть записаны в ОИК СК-11 только по команде пользователя. Для
          возможности записи таких ПГ в детальной информации ПГ напротив ОИК СК-11 отображается кнопка «Отправить».
        </p>
        <p>
          В случае аварийных ситуаций, по факту восстановления взаимодействия экземпляров СРПГ необходимо выполнить отправку в ИА недостающих квитанций. Для отправки
          недостающих квитанций в ИА необходимо воспользоваться функциями <img src={image293} alt="image293" /> и <img src={image294} alt="image294" /> . При выполнении
          данных функций ПГ будут повторно направлены во внешние системы, а в Централизованную часть ИА будут направлены квитанции со статусом результатов отправки ПГ во
          внешние системы. В нормальном состоянии данные функции не используются.
        </p>
        <TextPage>
          На форме доступны следующие инструменты:
          <br />
          <IconContainer>
            <div>
              <Icon width={16} name="search" />
            </div>
            - Выполнить фильтр по полю. <br />
          </IconContainer>
          <img src={image74} alt="image31" /> - Выполнить сортировку по полю . <br />
          <br />
        </TextPage>
        <p>
          Для выгрузки ПГ в файл формата xml, необходимо в детализации ПГ выбрать действие и выбрать вид выгрузки обычный или детальный ПГ. В детальном ПГ выполняется
          выгрузка с учетом наименований объектов для быстрого поиска необходимой информации для пользователей Системы.
        </p>
        <h5>Резервная загрузка ПГ из файла xml</h5>
        <p>
          Кнопка <img src={image295} alt="image295" /> используется только в качестве резервной технологии при потере связи с ЦДУ (технология применяется по факту
          отсутствия команды акцепта ПГ в ДЦ). В штатном режиме работы Системы необходимо дождаться распространения ПГ из ЦДУ.
        </p>
        <p>При выявлении ошибки распространения из ИУС «СРПГ» ИА (ОДУ Востока) в ИУС «СРПГ» ДЦ первой (второй) синхронной зоны ЕЭС России:</p>
        <ul>
          <li>­ планового графика (до команды акцепта);</li>
          <li>­ команды акцепта планового графика, </li>
        </ul>
        <p>
          работник с ролью «Технолог» в ИА (ОДУ Востока) выполняет с помощью ИУС «СРПГ» повторное направление планового графика (до команды акцепта) или команды акцепта
          планового графика в ДЦ, в котором возникла ошибка.
        </p>
        <p>
          В случае неуспешного повторного направления команды акцепта планового графика работник с ролью «Технолог» в ИА (ОДУ Востока) выполняет формирование планового
          графика в формате «Обычный xml» и направляет его по электронной почте работникам роли «Технолог» в ДЦ, в которых зафиксированы проблемы получения планового
          графика.
        </p>
        <p>
          Работник с ролью «Технолог» в ДЦ, в которых зафиксированы проблемы получения планового графика, в регламентные сроки формирования планового графика выполняет:
        </p>
        <p>
          Ручную загрузку планового графика, полученного с помощью электронной почты из ИА (для ДЦ первой синхронной зоны ЕЭС России) или из ОДУ Востока (для ДЦ второй
          синхронной зоны ЕЭС России), в ИУС «СРПГ» ДЦ.
        </p>
        <p>Контроль автоматической фиксации команды акцепта планового графика в ИУС «СРПГ» и его записи во Внешние системы текущего ДЦ.</p>
        <p>
          При загрузке ПГ с помощью резервной технологии согласно дате ПГ определяется актуальная версия НСИ, согласно настройкам в конфигурации определяется СЗ
          экземпляра СРПГ. Актуальная НСИ фильтруется по СЗ и полученные объекты сравниваются с объектами в ПГ. Объекты, отсутствующие в НСИ с фильтром по СЗ исключаются
          из ПГ. Полученный ПГ пишется в БД.
        </p>
        <p>
          После успешной загрузки ПГ автоматически выполняются следующие действия последовательно: отправка до команды акцепта / команда акцепта ПГ в ОИК СК-11, отправка
          до команды акцепта ПГ в ёЖ-3, акцепт ПГ, отправка параллельно утвержденного ПГ в ОИК СК-11, Modes-Terminal и СРДК, при успешной записи в ОИК СК-11 выполняется
          отправка в ёЖ-3.
        </p>
        <p>
          Для перехода к резервной загрузке ПГ, необходимо нажать на кнопку <img src={image295} alt="image295" /> .
        </p>
        <p>
          Откроется форма для выбора файла (Рисунок 34).
          <BigPicture>
            <img src={image296} alt="image296" />
            <AltPicture>Рисунок 34 – Форма выбора файла xml для резервной загрузки ПГ</AltPicture>
          </BigPicture>
        </p>
        <p>Для выбора файла необходимо нажать на поле, затем выбрать файл или переместить файл на поле выбора файла на форме.</p>
        <p>
          Для загрузки выбранного файла необходимо нажать кнопку <img src={image192} alt="image192" /> , для сброса внесенных изменений нажать на кнопку{" "}
          <img src={image236} alt="image236" /> .
        </p>
        <p>В процессе загрузки ПГ проверяется формат загружаемого файла. Объекты, отсутствующие в НСИ с фильтром по СЗ исключаются из ПГ. Полученный ПГ пишется в БД.</p>
        <p>
          В результате загрузки ПГ выполнится автоматическая отправка не утвержденного ПГ в ОИК СК-11, отправка не утвержденного ПГ в ёЖ-3, а также выполнится
          автоматический акцепт ПГ и отправка параллельно утвержденного ПГ в ОИК СК-11, MODES-Terminal и СРДК, при успешной записи в ОИК СК-11 выполняется отправка в
          ёЖ-3. Пользователю необходимо дождаться завершения всех процессов загрузки ПГ.
        </p>
      </Page>

      <Page>
        <DescriptionPage name={`event_notifications`}>3.4 Уведомления</DescriptionPage>
        <p>
          В пункте меню «Уведомления» интерфейса пользователя СРПГ, в Распределенной части существует возможность подписки на следующий список событий, описанных в
          таблице 2.
        </p>
        <p>
          <b>Таблица 2. Новые события (почтовые уведомления) СРПГ</b>
        </p>
        <StyledTable>
          <thead>
            <tr>
              <th>№</th>
              <th>Наименование уведомления¹</th>
              <th>Индикатор срабатывания</th>
              <th>Текст уведомления¹</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td colSpan={4} style={{ textAlign: "center", fontWeight: "bold" }}>
                Уведомления СРПГ ИА, ОДУ Востока, формируемые централизованной частью ИУС²
              </td>
            </tr>
            <tr>
              <td>1.</td>
              <td>Уведомление о записи ППБР (ДДГ) до команды акцепта во все ОИК СК-11 ДЦ</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="AWAIT" /> , п.3 таблицы 1
                </IndicatorCell>
              </td>
              <td>
                &lt;тип ПГ (ППБР/ДДГ¹)&gt; на ДД.ММ.ГГГГ <strong>до команды акцепта</strong> успешно записан в ОИК СК-11 всех ДЦ первой (второй¹) синхронной зоны ЕЭС
                России
              </td>
            </tr>
            <tr>
              <td>2.</td>
              <td>Уведомление о записи ПБР (УДДГ) до команды акцепта во все ОИК СК-11 ДЦ</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="AWAIT" /> , п.3 таблицы 1
                </IndicatorCell>
              </td>
              <td>
                &lt;тип ПГ (ПБР/УДДГ)&gt; - &lt;номер ПГ&gt; на ДД.ММ.ГГГГ <strong>до команды акцепта</strong> успешно записан в ОИК СК-11 всех ДЦ первой (второй)
                синхронной зоны ЕЭС России
              </td>
            </tr>
            <tr>
              <td>3.</td>
              <td>Уведомление о записи ПДГ до команды акцепта во все ОИК СК-11 ДЦ</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="AWAIT" /> , п.3 таблицы 1
                </IndicatorCell>
              </td>
              <td>
                ПДГ на ДД.ММ.ГГГГ <strong>до команды акцепта</strong> успешно записан в ОИК СК-11 всех ДЦ первой (второй) синхронной зоны ЕЭС России
              </td>
            </tr>
            <tr>
              <td>4.</td>
              <td>Уведомление о записи ПЭР до команды акцепта во все ОИК СК-11 ДЦ</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="AWAIT" /> , п.3 таблицы 1
                </IndicatorCell>
              </td>
              <td>
                ПЭР на ДД.ММ.ГГГГ <strong>до команды акцепта</strong> успешно записан в ОИК СК-11 всех ДЦ первой (второй) синхронной зоны ЕЭС России
              </td>
            </tr>
            <tr>
              <td>5.</td>
              <td>Уведомление о записи команды акцепта ППБР (ДДГ) во все ОИК СК-11, Modes-Terminal, СРДК ДЦ</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="DONE" /> , п.6 таблицы 1
                </IndicatorCell>
              </td>
              <td>
                <strong>Команда акцепта</strong> &lt;тип ПГ (ППБР/ДДГ)&gt; на ДД.ММ.ГГГГ успешно записана в ОИК СК-11, Modes-Terminal, СРДК всех ДЦ первой (второй)
                синхронной зоны ЕЭС России
              </td>
            </tr>
            <tr>
              <td>6.</td>
              <td>Уведомление о записи команды акцепта ПБР (УДДГ) во все ОИК СК-11, Modes-Terminal, СРДК ДЦ</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="DONE" /> , п.6 таблицы 1
                </IndicatorCell>
              </td>
              <td>
                <strong>Команда акцепта</strong> &lt;тип ПГ (ПБР/УДДГ&gt; - &lt;номер ПГ&gt; на ДД.ММ.ГГГГ успешно записана в ОИК СК-11, Modes-Terminal, СРДК всех ДЦ
                первой (второй) синхронной зоны ЕЭС России
              </td>
            </tr>
            <tr>
              <td>7.</td>
              <td>Уведомление о записи команды акцепта ПДГ во все ОИК СК-11</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="DONE" /> , п.6 таблицы 1
                </IndicatorCell>
              </td>
              <td>
                <strong>Команда акцепта</strong> ПДГ на ДД.ММ.ГГГГ успешно записана в ОИК СК-11 всех ДЦ первой (второй) синхронной зоны ЕЭС России
              </td>
            </tr>
            <tr>
              <td>8.</td>
              <td>Уведомление о записи команды акцепта ПЭР во все ОИК СК-11</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="DONE" /> , п.6 таблицы 1
                </IndicatorCell>
              </td>
              <td>
                <strong>Команда акцепта</strong> ПЭР на ДД.ММ.ГГГГ успешно записана в ОИК СК-11 всех ДЦ первой (второй) синхронной зоны ЕЭС России
              </td>
            </tr>
            <tr>
              <td>9.</td>
              <td>Уведомление об ошибке записи ПГ в ОИК СК-11 РДУ и/или Modes Terminal РДУ и/или СРДК одного или нескольких ДЦ любого уровня</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="COND_FAIL" /> , п.7 таблицы 1
                </IndicatorCell>
              </td>
              <td>
                <strong>Ошибка записи команды акцепта</strong> &lt;тип ПГ&gt; - &lt;номер ПГ, для ПБР/УДДГ&gt; на ДД.ММ.ГГГГ в:
                <br />- ОИК СК-11 &lt;перечень РДУ в алфавитном порядке через запятую&gt;;
                <br />- Modes-Terminal &lt;перечень РДУ в алфавитном порядке через запятую&gt;;
                <br />- СРДК &lt;перечень ДЦ (ИА, ОДУ, РДУ) в алфавитном порядке через запятую с учетом группировки ДЦ согласно иерархической структуре СО&gt;
                <br />
                Текст уведомления автоматически (динамически) формируется на основании возникших ошибок.
              </td>
            </tr>
            <tr>
              <td>10.</td>
              <td>Уведомление об ошибке отправки ПГ до команды акцепта в ДЦ</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="FAIL" /> , п.8 таблицы 1
                </IndicatorCell>
              </td>
              <td>
                <strong>Ошибка отправки</strong> &lt;тип ПГ&gt; - &lt;номер ПГ, для ПБР/УДДГ&gt; на ДД.ММ.ГГГГ до команды акцепта в СРПГ: &lt;перечень ДЦ через запятую с
                учетом группировки ДЦ согласно иерархической структуре СО&gt;.
              </td>
            </tr>
            <tr>
              <td>11.</td>
              <td>Уведомление об ошибке отправки команды акцепта ПГ в ДЦ</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="FAIL" /> , п.9 таблицы 1
                </IndicatorCell>
              </td>
              <td>
                <strong>Ошибка отправки команды акцепта</strong> &lt;тип ПГ&gt; - &lt;номер ПГ, для ПБР/УДДГ&gt; на ДД.ММ.ГГГГ в СРПГ: &lt;перечень ДЦ через запятую с
                учетом группировки ДЦ согласно иерархической структуре СО&gt;
              </td>
            </tr>
            <tr>
              <td>12.</td>
              <td>Уведомление об ошибке записи ПГ до команды акцепта в ОИК СК-11 ДЦ</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="FAIL" /> , п.10 таблицы 1
                </IndicatorCell>
              </td>
              <td>
                <strong>Ошибка записи</strong> &lt;тип ПГ&gt; - &lt;номер ПГ, для ПБР/УДДГ&gt; на ДД.ММ.ГГГГ до команды акцепта в ОИК СК-11: &lt;перечень ДЦ через запятую
                с учетом группировки ДЦ согласно иерархической структуре СО&gt;
              </td>
            </tr>
            <tr>
              <td>13.</td>
              <td>Уведомление об ошибке записи команды акцепта ПГ в ОИК СК-11 ИА и/или в ОИК СК-11 ОДУ и/или Modes Terminal ИА и/или в Modes-Terminal ОДУ</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="FAIL" /> , п.11 таблицы 1
                </IndicatorCell>
              </td>
              <td>
                <strong>Ошибка записи команды акцепта</strong> &lt;тип ПГ&gt; - &lt;номер ПГ, для ПБР/УДДГ&gt; на ДД.ММ.ГГГГ в:
                <br />- ОИК СК-11 &lt;перечень ДЦ (ИА, ОДУ) в алфавитном порядке через запятую с учетом группировки ДЦ согласно иерархической структуре СО&gt;;
                <br />- Modes-Terminal &lt;перечень ДЦ (ИА, ОДУ) в алфавитном порядке через запятую с учетом группировки ДЦ согласно иерархической структуре СО&gt;;
                <br />- СРДК &lt;перечень ДЦ (ИА, ОДУ, РДУ) в алфавитном порядке через запятую с учетом группировки ДЦ согласно иерархической структуре СО&gt;
                <br />
                Текст уведомления автоматически формируется на основании возникших ошибок.
              </td>
            </tr>
            <tr>
              <td colSpan={4} style={{ textAlign: "center", fontWeight: "bold" }}>
                Уведомления СРПГ ИА, ОДУ, РДУ, формируемые распределенной частью ИУС
              </td>
            </tr>
            <tr>
              <td>14.</td>
              <td>Уведомление о записи ППБР (ДДГ) до команды акцепта в ОИК СК-11 текущего ДЦ</td>
              <td rowSpan={4}>
                <IndicatorCell>
                  <StatusCircle status="AWAIT" /> в распределенной части СРПГ
                </IndicatorCell>
              </td>
              <td>
                &lt;тип ПГ (ППБР/ДДГ)&gt; на ДД.ММ.ГГГГ <strong>до команды акцепта</strong> успешно записан в ОИК СК-11 &lt;наименование текущего ДЦ&gt;
              </td>
            </tr>
            <tr>
              <td>15.</td>
              <td>Уведомление о записи ПБР (УДДГ) до команды акцепта в ОИК СК-11 текущего ДЦ</td>
              <td>
                &lt;тип ПГ (ПБР/УДДГ&gt; - &lt;номер ПГ&gt; на ДД.ММ.ГГГГ <strong>до команды акцепта</strong> успешно записан в ОИК СК-11 &lt;наименование текущего ДЦ&gt;
              </td>
            </tr>
            <tr>
              <td>16.</td>
              <td>Уведомление о записи ПДГ до команды акцепта в ОИК СК-11 текущего ДЦ</td>
              <td>
                ПДГ на ДД.ММ.ГГГГ <strong>до команды акцепта</strong> успешно записан в ОИК СК-11 &lt;наименование текущего ДЦ&gt;
              </td>
            </tr>
            <tr>
              <td>17.</td>
              <td>Уведомление о записи ПЭР до команды акцепта в ОИК СК-11 текущего ДЦ</td>
              <td>
                ПЭР на ДД.ММ.ГГГГ <strong>до команды акцепта</strong> успешно записан в ОИК СК-11 &lt;наименование текущего ДЦ&gt;
              </td>
            </tr>
            <tr>
              <td>18.</td>
              <td>Уведомление об ошибке записи ПГ до команды акцепта в ОИК СК-11 и/или ёЖ-3</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="FAIL" /> в распределенной части СРПГ
                </IndicatorCell>
              </td>
              <td>
                <strong>Ошибка записи</strong> &lt;тип ПГ&gt; - &lt;номер ПГ, для ПБР/УДДГ&gt; на ДД.ММ.ГГГГ до команды акцепта в &lt;наименование ИУС (ОИК СК-11 и/или
                ёЖ-3³)&gt; &lt;наименование текущего ДЦ&gt;
              </td>
            </tr>
            <tr>
              <td>19.</td>
              <td>Уведомление о записи команды акцепта ППБР (ДДГ) в ОИК СК-11, ёЖ-3, Modes-Terminal, СРДК текущего ДЦ</td>
              <td rowSpan={4}>
                <IndicatorCell>
                  <StatusCircle status="DONE" /> в распределенной части СРПГ
                </IndicatorCell>
              </td>
              <td>
                <strong>Команда акцепта</strong> &lt;тип ПГ (ППБР/ДДГ)&gt; на ДД.ММ.ГГГГ успешно записана в ОИК СК-11, ёЖ-3³, Modes-Terminal, СРДК &lt;наименование
                текущего ДЦ&gt;
              </td>
            </tr>
            <tr>
              <td>20.</td>
              <td>Уведомление о записи команды акцепта ПБР (УДДГ) в ОИК СК-11, ёЖ-3, Modes-Terminal, СРДК текущего ДЦ</td>
              <td>
                <strong>Команда акцепта</strong> &lt;тип ПГ (ПБР/УДДГ&gt; - &lt;номер ПГ&gt; на ДД.ММ.ГГГГ успешно записана в ОИК СК-11, ёЖ-3, Modes-Terminal, СРДК
                &lt;наименование текущего ДЦ&gt;
              </td>
            </tr>
            <tr>
              <td>21.</td>
              <td>Уведомление о записи команды акцепта ПДГ в ОИК СК-11 текущего ДЦ</td>
              <td>
                <strong>Команда акцепта</strong> ПДГ на ДД.ММ.ГГГГ успешно записана в ОИК СК-11 &lt;наименование текущего ДЦ&gt;
              </td>
            </tr>
            <tr>
              <td>22.</td>
              <td>Уведомление о записи команды акцепта ПЭР в ОИК СК-11 текущего ДЦ</td>
              <td>
                <strong>Команда акцепта</strong> ПЭР на ДД.ММ.ГГГГ успешно записана в ОИК СК-11 &lt;наименование текущего ДЦ&gt;
              </td>
            </tr>
            <tr>
              <td>23.</td>
              <td>Уведомление об ошибке записи команды акцепта ПГ в ОИК СК-11 и/или ёЖ-3 текущего ДЦ</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="FAIL" /> в распределенной части СРПГ
                </IndicatorCell>
              </td>
              <td>
                <strong>Ошибка записи команды акцепта</strong> &lt;тип ПГ&gt; - &lt;номер ПГ, для ПБР/УДДГ&gt; на ДД.ММ.ГГГГ в ОИК СК-11, ёЖ-3³ &lt;наименование текущего
                ДЦ&gt;
              </td>
            </tr>
            <tr>
              <td>24.</td>
              <td>Уведомление об ошибке записи команды акцепта ПГ в Modes-Terminal текущего ДЦ</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="FAIL" /> в распределенной части СРПГ
                </IndicatorCell>
              </td>
              <td>
                <strong>Ошибка записи команды акцепта</strong> &lt;тип ПГ&gt; - &lt;номер ПГ, для ПБР/УДДГ&gt; на ДД.ММ.ГГГГ в Modes-Terminal &lt;наименование текущего
                ДЦ&gt;
              </td>
            </tr>
            <tr>
              <td>25.</td>
              <td>Уведомление об ошибке записи команды акцепта ПГ в СРДК текущего ДЦ</td>
              <td>
                <IndicatorCell>
                  <StatusCircle status="FAIL" /> в распределенной части СРПГ
                </IndicatorCell>
              </td>
              <td>
                <strong>Ошибка записи команды акцепта</strong> &lt;тип ПГ&gt; - &lt;номер ПГ, для ПБР/УДДГ&gt; на ДД.ММ.ГГГГ в СРДК &lt;наименование текущего ДЦ&gt;
              </td>
            </tr>
            <tr>
              <td colSpan={4}>
                <Footnote>
                  <sup>1</sup>Состав уведомлений, текст уведомлений формируется на основании экземпляра СРПГ ДЦ (например, в СРПГ ОДУ Востока есть ДДГ, ППБР отсутствует).
                </Footnote>
                <Footnote>
                  <sup>2</sup>Уведомление в Централизованной части СРПГ ИА (ОДУ Востока) формируется по факту завершения системной задачи распространения, записи ПГ во
                  внешние системы во всех СРПГ ДЦ первой (второй) СЗ ЕЭС России, в том числе по факту получения ответа (результат выполнения задачи) от СРПГ ДЦ первой
                  (второй) СЗ ЕЭС России.
                </Footnote>
                <Footnote>
                  <sup>3</sup>При наличии настройки записи ПГ в ёЖ-3 (применимо для всех уведомлений)
                </Footnote>
              </td>
            </tr>
          </tbody>
        </StyledTable>

        <p>Все события (уведомления) СРПГ в пункте меню «Уведомления» сгруппированы по следующим группам (состав групп уведомлений зависит от экземпляра СРПГ):</p>
        <ul>
          <li>Уведомления о взаимодействии с ИУС «ЕСС».</li>
          <li>Уведомления о распространении результатов сопоставления ГОУ, технологической НСИ.</li>
          <li>
            Уведомления о распространении ПГ <strong>до команды акцепта</strong> (пп.1-4 таблицы 2).
          </li>
          <li>
            Уведомления о распространении <strong>команды акцепта</strong> ПГ (пп. 5-8 таблицы 2).
          </li>
          <li>Уведомления об ошибках (пп. 9-13 таблицы 2).</li>
          <li>
            Уведомления о записи ПГ <strong>до команды акцепта</strong> в ОИК СК-11 (пп.14-18 таблицы 2).
          </li>
          <li>
            Уведомления о записи <strong>команды акцепта</strong> ПГ во внешние системы (пп. 19-25 таблицы 2).
          </li>
        </ul>
        <p>
          Подписку на уведомления о событиях могут настроить пользовате-ли с ролями Администратор Системы, Администратор НСИ, Администра-тор НСИ ЕСС, Технолог,
          Наблюдатель.
        </p>
        <p>
          Для перехода к функциональности работы с Уведомлениями необходимо нажать раздел <img src={image297} alt="image297" /> на главной рабочей панели.
        </p>
        <p>
          Откроется форма со списком доступных подписок для пользователя. (Рисунки 35-37).
          <BigPicture>
            <img src={image35} alt="image35" />
            <AltPicture>Рисунок 35 – Форма настройки подсписки на события и уведомления Системы в Распределенной части экземпляра СРПГ для ИА</AltPicture>
          </BigPicture>
        </p>
        <BigPicture>
          <img src={image36} alt="image36" />
          <AltPicture>Рисунок 36 – Форма настройки подсписки на события и уведомления Системы в Распределенной части экземпляра СРПГ для ОДУ Востока</AltPicture>
        </BigPicture>
        <BigPicture>
          <img src={image37} alt="image37" />
          <AltPicture>Рисунок 37 – Форма настройки подсписки на события и уведомления Системы для экземпляров СРПГ ОДУ (кроме ОДУ Востока) и РДУ</AltPicture>
        </BigPicture>
        <p>Для подписки на уведомление, необходимо установить опцию выбора напротив уведомления.</p>
        <p>Для отписки от уведомления, необходимо снять опцию выбора напротив уведомления.</p>
        <p>
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image235} alt="image235" /> , для сброса внесенных изменений нажать на кнопку{" "}
          <img src={image236} alt="image236" /> .
        </p>
      </Page>

      <Page>
        <DescriptionPage name={`customizing_user_interface`}>3.5. Настройка пользовательского интерфейса</DescriptionPage>
        <p>Для изменения настроек интерфейса пользователя используется окно «Информация о пользователе».</p>
        <p>
          Для открытия окна «Информация о пользователе» необходимо в правом верхнем углу рабочей области Системы нажать на иконку <img src={image300} alt="image300" />{" "}
          (Рисунок 38).
          <BigPicture>
            <img src={image299} alt="image299" />
            <AltPicture>Рисунок 38 – Окно «Информация о пользователе»</AltPicture>
          </BigPicture>
        </p>
        <p>В окне «Информация о пользователе» отображаются следующие элементы:</p>
        <ul>
          <li>аватар пользователя;</li>
          <li>версия ПО стенда (при наличии соответствующей настройки отображения, установленной пользователем) и экземпляр Системы (ЦДУ, наименование ОДУ или РДУ);</li>
          <li>наименование ДЦ авторизованнного пользователя;</li>
          <li>учетная запись пользователя; </li>
          <li> фамилия, имя и отчество авторизованного пользователя;</li>
          <li> роль (роли) пользователя в системе; </li>
          <li> кнопка «Настройки»;</li>
          <li> кнопка «Выход из системы».</li>
        </ul>
        <p>
          Для перехода на форму «Настройки пользователя» (Рисунок 39), необходимо в окне «Информация о пользователе» нажать на кнопку «Настройки». (Рисунок 39).
          <BigPicture>
            <img src={image301} alt="image301" />
            <AltPicture>Рисунок 39 – Форма «Настройки пользователя»</AltPicture>
          </BigPicture>
        </p>
      </Page>

      <Page>
        <DescriptionPage name={`changing_the_users_avatar`}>3.5.1. Изменение аватара пользователя</DescriptionPage>
        <p>
          Для изменения аватара пользователя, необходимо навести курсор мыши на аватар с силуэтом человека (по умолчанию) и нажать на нее. После нажатия откроется
          контекстное меню с выбором доступных аватаров. (Рисунок 40). Для выбора аватара требуется навести на него курсор и нажать левой кнопкой мыши.
          <BigPicture>
            <img src={image302} alt="image302" />
            <AltPicture>Рисунок 40 – Выбор аватара пользователя</AltPicture>
          </BigPicture>
        </p>
        <p>
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image235} alt="image235" /> , для сброса внесенных изменений нажать на кнопку{" "}
          <img src={image236} alt="image236" /> .
        </p>
      </Page>

      <Page>
        <DescriptionPage name={`changing_interface_theme`}>3.5.2. Изменение темы интерфейса</DescriptionPage>
        <p>
          На форме «Настройки пользователя» предусмотрен выбор цветовых схем пользовательского интерфейса, на выбор предлагается 4 цветовые схемы (Рисунок 41).
          <BigPicture>
            <img src={image303} alt="image303" />
            <AltPicture>Рисунок 41 – Выбор цветовой схемы пользовательского интерфейса</AltPicture>
          </BigPicture>
        </p>
        <p>
          Для сброса выбранной темы до темы по умолчанию импользуется кнопка <img src={image304} alt="image304" />. Тема по умолчанию хранится в отдельном
          конфигурационном файле <i>«config.js»</i> (детализированное описание состава конфигурационного файла представлено в разделе 8.4. Руководства системного
          администратора).
        </p>
        <p>
          Также в файле <i>«config.js»</i> расположена булева переменная THEME_EDIT_ENABLE (по умолчанию – true) для управления правами выбора темы пользователями. Если
          THEME_EDIT_ENABLE=true, то выбор пользователями темы разрешен, если THEME_EDIT_ENABLE=false, то выбор темы пользователями заблокирован. Для изменения данной
          настройки требуется обратиться к Администратору Системы.
        </p>
        <p>
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image235} alt="image235" /> , для сброса внесенных изменений нажать на кнопку{" "}
          <img src={image236} alt="image236" /> .
        </p>
      </Page>
      <Page>
        <DescriptionPage name={`displaying_application_version`}>3.5.3. Отображение версии приложения</DescriptionPage>
        <p>На форме «Настройки пользователя» присутствует опция «Показывать версию». </p>
        <p>
          При установке опции <img src={image305} alt="image305" /> в окне «Информация о пользователе» (Рисунок 35) отображается версия Системы.{" "}
        </p>
        <p>
          При снятии опции <img src={image306} alt="image306" /> в окне «Информация о пользователе» информация о версии Системы не отображается.
        </p>
        <p>
          Для сохранения внесенных изменений, необходимо нажать на кнопку <img src={image235} alt="image235" /> , для сброса внесенных изменений нажать на кнопку{" "}
          <img src={image236} alt="image236" /> .
        </p>
      </Page>
      <Page>
        <DescriptionPage name={`changing_appearance_drop_down_lists`}>3.5.4. Изменение вида выпадающих списков</DescriptionPage>
        <p>
          Для выпадающих списков интерфейса пользователя СРПГ существует возможность выбора цветовой схемы. (Рисунок 42). Для выбора вида выпадающего списка необходимо
          нажать на выбираемый вариант левой кнопкой мыши.
          <BigPicture>
            <img src={image307} alt="image307" />
            <AltPicture>Рисунок 42 – Форма выбора вида выпадающего списка</AltPicture>
          </BigPicture>{" "}
        </p>
        <p>
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image235} alt="image235" /> , для сброса внесенных изменений нажать на кнопку{" "}
          <img src={image236} alt="image236" /> .
        </p>
      </Page>
      <Page>
        <DescriptionPage name={`log_out_system`}>3.5.5. Выход из системы</DescriptionPage>
        <p>
          Для выхода из Системы в окне «Информация о пользователе» (Рисунок 38), необходимо нажать на кнопку <img src={image308} alt="image308" /> , после чего
          пользователю отобразится форма авторизации пользователей (Рисунок 1).
        </p>
      </Page>
    </>
  );
};
