import { Icon } from "~/components/Icon";
import { BigPicture, DescriptionPage, IconContainer, Page, TextPage, TitlePage } from "../../GuidePage.style";
import { image177, image178, image179, image180, image181, image74, image182, image183, image184, image185 } from "../../images";

export const Logging = () => {
  return (
    <>
      <Page>
        <DescriptionPage name="logging">6. ЖУРНАЛИРОВАНИЕ</DescriptionPage>
        <TextPage>
          Данный раздел Системы предназначен для журналирования действий пользователей, а также взаимодействия с внешними системами в Системе СРПГ СК-11.
          <br />
          Для перехода к функционалу Журналирования необходимо перейти в раздел <img src={image177} alt="image208" /> на главной рабочей панели.
        </TextPage>
      </Page>
      <Page>
        <TitlePage> Журнал взаимодействия с внешними системами</TitlePage>
        <TextPage>
          Для перехода к просмотру журнала взаимодействия с внешними системами необходимо перейти на вкладку <img src={image178} alt="image209" /> .
          <br />
          Пример формы:
        </TextPage>
        <BigPicture>
          <img src={image179} alt="image208" />
        </BigPicture>
        <TextPage>
          Для просмотра журналов действия пользователей за выбранную дату или период, необходимо нажать на поле даты, затем выбрать на месяце день или период для
          просмотра и нажать кнопку <img src={image180} alt="choose" /> .
        </TextPage>
        <BigPicture>
          <img src={image181} alt="image209" />
        </BigPicture>
        <TextPage>
          На форме доступны следующие инструменты:
          <br />
          <IconContainer>
            <div>
              <Icon width={16} name="search" />
            </div>
            - Выполнить фильтр по полю.
          </IconContainer>
          <img src={image74} alt="image43" />- Выполнить сортировку по полю.
          <br />
          <img src={image182} alt="excel" />- Выполнить экспорт журнала в файл excel.
        </TextPage>
      </Page>
      <Page>
        <TitlePage>Журнал действий пользователей</TitlePage>
        <TextPage>
          Для перехода к просмотру журнала взаимодействия с внешними системами необходимо перейти на вкладку <img src={image183} alt="image210" /> .
          <br />
          Пример формы:
        </TextPage>
        <BigPicture>
          <img src={image184} alt="image211" />
        </BigPicture>
        <TextPage>
          Для просмотра журналов действия пользователей за выбранную дату или период, необходимо нажать на поле даты, затем выбрать на месяце день или период для
          просмотра и нажать кнопку <img src={image180} alt="choose" /> .
        </TextPage>
        <BigPicture>
          <img src={image185} alt="image212" />
        </BigPicture>
        <TextPage>
          На форме доступны следующие инструменты:
          <br />
          <IconContainer>
            <div>
              <Icon width={16} name="search" />
            </div>
            - Выполнить фильтр по полю.
          </IconContainer>
          <img src={image74} alt="image43" />- Выполнить сортировку по полю.
          <br />
          <img src={image182} alt="excel" />- Выполнить экспорт журнала в файл excel.
        </TextPage>
      </Page>
    </>
  );
};
