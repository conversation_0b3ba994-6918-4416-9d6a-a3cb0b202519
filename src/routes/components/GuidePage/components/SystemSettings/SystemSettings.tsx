import React from "react";
import {
  image18,
  image58,
  image74,
  image100,
  image119,
  image120,
  image121,
  image122,
  image123,
  image124,
  image125,
  image126,
  image127,
  image128,
  image129,
  image130,
  image131,
  image132,
  image133,
  image134,
  image135,
  image136,
  image137,
  image138,
  image139,
  image140,
  image141,
  image142,
  image143,
  image144,
  image145,
  image146,
  image147,
  image148,
  image149,
  image150,
  image151,
  image152,
  image153,
  image154,
  image155,
  image156,
  image157,
  image158,
  image159,
  image160,
  image161,
  image162,
  image163,
  image164,
  image165,
  image166,
  image167,
  image168,
  image169,
  image170,
  image171,
  image172,
  image173,
  image174,
  image175,
  image176,
  image113,
} from "../../images";
import { Icon } from "~/components/Icon";
import { AltPicture, BigPicture, DescriptionPage, IconContainer, Page, Status, Table, TD, TextPage, TitlePage } from "../../GuidePage.style";

export const SystemSettings = () => {
  return (
    <>
      <Page>
        <TitlePage name="system_settings">5. НАСТРОЙКИ СИСТЕМЫ</TitlePage>
        <DescriptionPage name="general_settings">5.1. Общие настройки </DescriptionPage>
        <TextPage>
          Данный раздел Системы предназначен для настроек системы по взаимодействию с внешними системами и глубины хранения данных.
          <br />
          Изменять настройки системы может только пользователь с ролью Администратор Системы.
          <br />
          Для перехода к функционалу администрирования необходимо нажать раздел <img src={image119} alt="image156" /> на главной рабочей панели.
          <br />
          Откроется раздел «Настройки» с вкладками на рабочей панели <img src={image120} alt="image157" /> . (Рисунок 47)
        </TextPage>
        <BigPicture>
          <img src={image121} alt="image158" />
          <AltPicture>Рисунок 47 - Интерфейс настроек Системы</AltPicture>
        </BigPicture>
      </Page>
      <Page>
        <DescriptionPage name="setting_the_data_storage_depth">5.1.1. Настройка глубины хранения данных</DescriptionPage>
        <TextPage>
          Для перехода к настройкам хранения данных, необходимо нажать на рабочей панели вкладку <img src={image122} alt="image159" /> , затем в правой части нажать на
          раздел <img src={image123} alt="image160" /> .
          <br />
          Откроется форма настройки глубины хранения данных. (Рисунок 48)
        </TextPage>
        <BigPicture>
          <img src={image124} alt="image161" />
          <AltPicture>Рисунок 48 - Форма настроек глубины хранения данных</AltPicture>
        </BigPicture>
        <TextPage>
          Глубина хранения данных задается для ПГ, НСИ и Журналов системы. Глубина задается в месяцах.
          <br />
          На форме доступны следующие инструменты: <br />
          <IconContainer>
            <div>
              <Icon width={16} name="search" />
            </div>
            - Выполнить фильтр по полю.
          </IconContainer>
          Для изменения периода хранения, необходимо указать количество месяцев.
          <br />
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
        </TextPage>
      </Page>
      <Page>
        <DescriptionPage name="settings_for_interaction_with_external_systems">5.1.2. Настройки взаимодействия с внешними системами</DescriptionPage>
        <TextPage>
          Для перехода к настройкам взаимодействия с внешними системами, необходимо нажать рабочей панели вкладку <img src={image122} alt="image162" /> , затем в правой
          части нажать на раздел <img src={image125} alt="image163" />.
          <br />
          Откроется форма настроек взаимодействия с внешними системами. (Рисунок 49)
        </TextPage>
        <BigPicture>
          <img src={image126} alt="image164" />
          <AltPicture>Рисунок 49 - Форма настроек взаимодействия с внешними системами</AltPicture>
        </BigPicture>
      </Page>
      <Page>
        <TitlePage>Настройка взаимодействия с ИУС «ЕСС»</TitlePage>
        <TextPage>
          Настройка взаимодействия с ИУС «ЕСС» выполняется только в Централизованной части экземпляра СРПГ ИА.
          <br />
          Для перехода к настройкам интеграции с ИУС «ЕСС», необходимо выполнить переключение на Централизованную часть в экземпляре СРПГ ИА, далее в настройках
          взаимодействия с внешними системами выбрать вкладку <img src={image127} alt="image165" /> .
          <br />
          Откроется форма настройки интеграции с ИУС «ЕСС». (Рисунок 50)
        </TextPage>
        <BigPicture>
          <img src={image128} alt="image166" />
          <AltPicture>Рисунок 50 - Форма настроек интеграции с ИУС ЕСС</AltPicture>
        </BigPicture>
        <TextPage>
          В поле ввода «Адрес сервиса» необходимо задать адрес сервиса ИУС «ЕСС», который уточняется у администратора ИУС «ЕСС». Адрес задается в формате
          {"http://<адрес сервиса ИУС «ЕСС»:порт>/api/WebApi/startTask"}
          <br />
          В поле ввода «Логин» необходимо указать имя сервисной УЗ Системы, созданной в AD для экземпляра СРПГ ИА.
          <br />
          В поле «Пароль» необходимо указать пароль сервисной УЗ Системы, созданной в AD для экземпляра СРПГ ИА.
          <br />
          В поле «Количество повторных попыток» задать количество повторных попыток запросов к ИУС «ЕСС» в случае ошибки подключения.
          <br />
          В поле «Интервал повторных попыток» задается временной интервал в секундах между повторными попытками подключения.
          <br />
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
          <br />
          Для проверки соединения с указанными настройками с ИУС «ЕСС», необходимо нажать на кнопку <img src={image129} alt="test" /> .
          <br />
          После нажатия, будет выполнен тестовый запрос к внешней системе. По результатам тестирования выведется уведомление с информацией по результатам проверки.
        </TextPage>
      </Page>
      <Page>
        <TitlePage>Настройка взаимодействия с ИУС «ОпАМ»</TitlePage>
        <TextPage>
          Настройка взаимодействия с ИУС «ОпАМ» выполняется только в Централизованной части экземпляров СРПГ ИА и СРПГ ОДУ Востока.
          <br />
          Для перехода к настройкам интеграции с ИУС «ОпАМ», необходимо выполнить переключение на Централизованную часть, далее в настройках взаимодействия с внешними
          системами выбрать вкладку <img src={image130} alt="image167" />
          <br />
          Откроется форма настройки интеграции с ИУС «ОпАМ». (Рисунок 51)
        </TextPage>
        <BigPicture>
          <img src={image131} alt="image168" />
          <AltPicture>Рисунок 51 - Форма настроек интеграции с ИУС «ОпАМ»</AltPicture>
        </BigPicture>
        <TextPage>
          В поле ввода «Адрес сервиса» необходимо задать адрес сервиса ИУС «ОпАМ», который уточняется у администратора ИУС «ОпАМ». Адрес задается в формате{" "}
          {"http://<адрес сервиса ИУС «ОпАМ»:порт>/api/WebApi/startTask"}
          <br />
          В поле ввода «Логин» необходимо указать имя сервисной УЗ Системы, созданной в AD для экземпляра СРПГ ИА.
          <br />
          В поле «Пароль» необходимо указать пароль сервисной УЗ Системы, созданной в AD для экземпляра СРПГ ИА.
          <br />
          В поле «Количество повторных попыток» задать количество повторных попыток запросов к ИУС «ОпАМ» в случае ошибки подключения.
          <br />
          В поле «Интервал повторных попыток» задается временной интервал в секундах между повторными попытками подключения.
          <br />
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
          <br />
          Для проверки соединения с указанными настройками с ИУС «ОпАМ», необходимо нажать на кнопку <img src={image129} alt="test" /> .
          <br />
          После нажатия, будет выполнен тестовый запрос к внешней системе. По результатам тестирования выведется уведомление с информацией по результатам проверки.
        </TextPage>
      </Page>
      <Page>
        <TitlePage>Настройка взаимодействия с Мегаточкой</TitlePage>
        <TextPage>
          Настройка взаимодействия с Мегаточкой выполняется только в Централизованной части экземпляров СРПГ ИА и СРПГ ОДУ Востока. Для перехода к настройкам интеграции с
          Мегаточкой, необходимо выполнить переключение на Централизованную часть, далее в настройках взаимодействия с внешними системами выбрать вкладку{" "}
          <img src={image132} alt="image169" /> .
          <br />
          Откроется форма настройки интеграции с Мегаточкой. (Рисунок 52)
        </TextPage>
        <BigPicture>
          <img src={image133} alt="image170" />
          <AltPicture>Рисунок 52 - Форма настроек интеграции с Мегаточкой</AltPicture>
        </BigPicture>
        <TextPage>
          Посредствам кнопки <img src={image134} alt="image171" /> добавить в поля для ввода «Адрес сервиса» и задать адреса серверов приложений Windows экземпляра СРПГ
          текущего ДЦ (для взаимодействия с Мегаточкой). В поле «Адрес сервиса» указывается имя сервера приложений или ip адрес.
          <br />В полях ввода напротив названий плановых графиков задаются шаблон сетевого пути до хранилища файлов Мегаточки вида{" "}
          {"«\\Clubr-cduPDGMPT\\vostok и шаблон имени файла планового графика вида {dd}{MM}{yy}-17_outLin.mpt.»"}
          <br />
          В поле «Количество повторных попыток» задать количество повторных попыток запросов сервиса к Мегаточки.
          <br />
          В поле «Интервал повторных попыток» задается временной интервал в секундах между повторными попытками подключения.
          <br />
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
          <br />
          Для проверки соединения с указанными настройками к Мегаточке, необходимо нажать на кнопку <img src={image129} alt="test" /> .
          <br />
          После нажатия, будет выполнен тестовый запрос к внешней системе. По результатам тестирования выведется уведомление с информацией по результатам проверки.
        </TextPage>
      </Page>
      <Page>
        <TitlePage>Настройка взаимодействия с ОИК СК-11</TitlePage>
        <TextPage>
          Настройка взаимодействия с ОИК СК-11 выполняется в Централизованной части экземпляра СРПГ ИА, ОДУ Востока и Распределенных частях Системы ОДУ, РДУ.
          <br />
          Для перехода к настройкам интеграции с ОИК СК-11 в настройках взаимодействия с внешними системами выбрать вкладку <img src={image135} alt="image172" /> .
          <br />
          Откроется форма настройки интеграции с ОИК СК-11.(Рисунок 53)
        </TextPage>
        <BigPicture>
          <img src={image136} alt="image173" />
          <AltPicture>Рисунок 53 - Форма настроек интеграции с ОИК СК-11</AltPicture>
        </BigPicture>
        <TextPage>
          В поле ввода «Адрес сервиса» необходимо задать адреса сервисов ОИК СК-11 основного и резервного, которые уточняются у администратора ОИК СК-11. Адрес задается в
          формате {"https://<адрес сервиса ОИК СК-11:порт>/api/public/core/v2.1"}
          <br />В поле ввода «Логин» необходимо указать имя сервисной УЗ Системы, созданной в AD для экземпляра СРПГ.
          <br />В поле «Пароль» необходимо указать пароль сервисной УЗ Системы, созданной в AD для экземпляра СРПГ.
          <br />В поле «Количество повторных попыток» задать количество повторных попыток запросов к ОИК СК-11 в случае ошибки подключения.
          <br />В поле «Интервал повторных попыток» задается временной интервал в секундах между повторными попытками подключения.
          <br />
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" />. Для
          проверки соединения с указанными настройками с ОИК СК-11 необходимо нажать на кнопку <img src={image129} alt="test" /> .
          <br />
          После нажатия, будет выполнен тестовый запрос к внешней системе. По результатам тестирования выведется уведомление с информацией по результатам проверки.
          <br />
          Типовые ошибки ОИК СК-11.
        </TextPage>
        <Table>
          <tr>
            <TD>Ошибка</TD>
            <TD>Рекомендации</TD>
          </tr>
          <tr>
            <TD>Не найдены измерения для публикации с uid ПГ = {"'10000056-0000-0000-c000-0000006d746c'"}</TD>
            <TD>
              В составе ПГ отправляются измерения, которые отсутствуют в ОИК СК-11 в полном составе. Необходимо проверить, какие объекты настроены для отправки в
              Редакторе сопоставления объектов (объект должен быть сопоставлен с UID ОИК СК-11, у объекта должны быть включены характеристики для отправляемого типа ПГ).
              Необходимо проверить наличие измерений в модели ОИК СК-11.
            </TD>
          </tr>
          <tr>
            <TD>{"Error Rtdb Access Denied"}</TD>
            <TD>
              Ошибка связана с отсутствием доступа на запись статусов ПГ. В ошибке указан UID ({`uid:"d7cd117d-3b30-4c89-a8c3-87236ce6a6de"`}). Необходимо скопировать UID
              из ошибки и проверить настройки в ОИК СК-11 на доступ к записи значений.
            </TD>
          </tr>
          <tr>
            <TD>Rtdb Uid Not Found</TD>
            <TD>
              Ошибка связана с отсутствием ячейки с UID в ОИК СК-11 для записи значения. В ошибке UID указан измерения ({`uid:"bedbfbd9-cb5b-4b7f-9be2-99c3e544359c"`}
              ). Необходимо скопировать UID из ошибки и проверить в Навигаторе данных ОИК СК-11 наличие измерения (ячейки), куда необходимо выполнить запись значения. При
              отсутствии ячейки с таким UID, необходимо ее создать.
            </TD>
          </tr>
        </Table>
      </Page>
      <Page>
        <TitlePage>Настройка взаимодействия с почтовым сервером</TitlePage>
        <TextPage>
          Настройка взаимодействия с почтовым сервером выполняется в Централизованной части экземпляра СРПГ ИА, ОДУ Востока и Распределенных частях Системы ОДУ, РДУ.
          <br />
          Для перехода к настройкам интеграции с почтовым сервисом в настройках взаимодействия с внешними системами выбрать вкладку <img
            src={image137}
            alt="image174"
          />{" "}
          .
          <br />
          Откроется форма настройки интеграции с почтовым сервером. (Рисунок 54)
        </TextPage>
        <BigPicture>
          <img src={image138} alt="image175" />
          <AltPicture>Рисунок 54 - Форма настроек интеграции с почтовым сервером</AltPicture>
        </BigPicture>
        <TextPage>
          В поле ввода «Адрес сервиса» необходимо задать адрес почтового сервера.
          <br />
          В поле ввода «Порт», указать порт, для подключения к серверу исходящей почты SMTP.
          <br />
          В поле ввода «Логин» необходимо указать имя сервисной УЗ Системы, созданной в AD для экземпляра СРПГ.
          <br />
          В поле «Пароль» необходимо указать пароль сервисной УЗ Системы, созданной в AD для экземпляра СРПГ.
          <br />
          Из списка «Шифрование» выбрать один из вариантов шифрования: TSL, SSL, нет.
          <br />
          В поле «Количество повторных попыток» задать количество повторных попыток запросов к почтовому серверу в случае ошибки подключения.
          <br />
          В поле «Интервал повторных попыток» задается временной интервал в секундах между повторными попытками подключения.
          <br />
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
          <br />
          Для проверки соединения с указанными настройками с почтовым сервером необходимо нажать на кнопку <img src={image129} alt="test" /> .
          <br />
          После нажатия, будет выполнен тестовый запрос к внешней системе. По результатам тестирования выведется уведомление с информацией по результатам проверки.
        </TextPage>
      </Page>
      <Page>
        <TitlePage>Настройка взаимодействия с ИУС «MODES-Terminal»</TitlePage>
        <TextPage>
          Настройка взаимодействия с ИУС «MODES-Terminal» выполняется в Распределенных частях экземпляров СРПГ ИА, ОДУ, РДУ.
          <br />
          Для перехода к настройкам интеграции с ИУС «MODES-Terminal», необходимо выполнить переключение на Распределенную часть, далее в настройках взаимодействия с
          внешними системами выбрать вкладку <img src={image139} alt="image176" /> .
          <br />
          Откроется форма настройки интеграции с ИУС «MODES-Terminal», (Рисунок 55)
        </TextPage>
        <BigPicture>
          <img src={image140} alt="image177" />
          <AltPicture>Рисунок 55 - Форма настроек интеграции с ИУС «MODES-Terminal»</AltPicture>
        </BigPicture>
        <TextPage>
          В поле ввода «Адрес сервиса» необходимо задать адрес сервиса ИУС «MODES-Terminal», который уточняется у администратора ИУС «MODES-Terminal» в формате{" "}
          {"http://<адрес сервиса ИУС «MODES-Terminal»:Порт>/SetPlanEx"}
          <br />В поле «Кодировка» задается значение:
          <ul>
            <li>ИУС ЕСС» (по умолчанию). Запись ПГ в ИУС «MODES-Terminal» будет выполняться по ID ЕСС.</li>
            <li>• ОИК СК-11. Запись ПГ в ИУС «MODES-Terminal» будет выполняться по UID СК-11.</li>
          </ul>
          <br />
          В поле «Количество повторных попыток» задать количество повторных попыток запросов к ИУС ИУС «MODES-Terminal», в случае ошибки подключения.
          <br />
          В поле «Интервал повторных попыток» задается временной интервал в секундах между повторными попытками подключения.
          <br />
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
          <br />
          Для проверки соединения с указанными настройками с ИУС «MODES-Terminal», необходимо нажать на кнопку <img src={image129} alt="test" /> .
          <br />
          После нажатия, будет выполнен тестовый запрос к внешней системе. По результатам тестирования выведется уведомление с информацией по результатам проверки.
        </TextPage>
      </Page>
      <Page>
        <TitlePage>Настройка взаимодействия с ИУС «СРДК»</TitlePage>
        <TextPage>
          Настройка взаимодействия с ИУС «СРДК» выполняется в Распределенных частях экземпляров СРПГ ИА, ОДУ, РДУ.
          <br />
          Для перехода к настройкам интеграции с ИУС «СРДК» необходимо выполнить переключение на Распределенную часть, далее в настройках взаимодействия с внешними
          системами выбрать вкладку <img src={image141} alt="image178" /> .
          <br />
          Откроется форма настройки интеграции с ИУС «СРДК» (Рисунок 56)
        </TextPage>
        <BigPicture>
          <img src={image142} alt="image179" />
          <AltPicture>Рисунок 56 - Форма настроек интеграции с ИУС «СРДК»</AltPicture>
        </BigPicture>
        <TextPage>
          В поле ввода «Адрес сервиса» необходимо задать адрес сервиса Распределенной части ИУС «СРДК», который уточняется у администратора ИУС «СРДК» в формате
          {"http://<адрес сервиса ИУС «СРДК»>/api/v1/pg/saveDataPG"}
          <br />В поле ввода «Логин» необходимо указать имя сервисной УЗ Системы, созданной в AD для экземпляра СРПГ.
          <br />В поле «Пароль» необходимо указать пароль сервисной УЗ Системы, созданной в AD для экземпляра СРПГ.
          <br />В поле «Количество повторных попыток» задать количество повторных попыток запросов к ИУС «СРДК», в случае ошибки подключения.
          <br />В поле «Интервал повторных попыток» задается временной интервал в секундах между повторными попытками подключения.
          <br />
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
          <br />
          Для проверки соединения с указанными настройками с ИУС «СРДК», необходимо нажать на кнопку <img src={image129} alt="test" /> .
          <br />
          После нажатия, будет выполнен тестовый запрос к внешней системе. По результатам тестирования выведется уведомление с информацией по результатам проверки.
        </TextPage>
      </Page>
      <Page>
        <TitlePage> Подключение к ЕСМ</TitlePage>
        <TextPage>
          Подключение к ЕСМ осуществляется согласно инструкции по взаимодействию с ЕСМ предоставляемой администратором ЕСМ.
          <br />
          Для интеграции с ЕСМ нелюдимо:
          <br />
          1. Установить пакет snmpd.
          <br />
          2. sudo apt update && sudo apt install snmpd
          <br />
          3. Произвести настройки уведомлений относительно: доступности портов сервисов, нагрузки на процессор, нагрузки на систему хранения, объема свободного дискового
          пространства и объёма свободной оперативной памяти для каждого из серверов системы. Пороговые значения отражены в Руководства администратора системы в главе
          «Штатная работа сервиса».
          <br />
          4. Экспортировать в систему ЕСМ mib файл системы СРПГ. Для этого необходимо зайти доступный по &nbsp;<a href="https://asdu-fpa-gitlab.cdu.so/" target="_blank" rel="noreferrer">ссылке</a>&nbsp;, 
          авторизоваться. После зайти по &nbsp;<a href="https://asdu-fpa-gitlab.cdu.so/srpg/config/-/blob/1.0.10/install/esm-mib/SRPG.mib" target="_blank" rel="noreferrer">ссылке</a>&nbsp; .
        </TextPage>
      </Page>
      <Page>
        <DescriptionPage name="pg_settings">5.2. Настройки ПГ</DescriptionPage>
        <TextPage>Данный раздел Системы предназначен для настроек загрузки и записи ПГ.</TextPage>
        <DescriptionPage name="pbr"> 5.2.1. Настройка Номер ПБР [1СЗ]</DescriptionPage>
        <TextPage>
          Настройка выполняется в Централизованной части экземпляра СРПГ ИА. В Распределенных частях экземпляров СРПГ ОДУ, РДУ настройка отображается в режиме просмотра.
          <br />
          Для перехода к настройкам номера ПБР, необходимо нажать рабочей панели вкладку <img src={image143} alt="image180" /> , затем в правой части нажать на раздел{" "}
          <img src={image144} alt="image181" /> .
          <br />
          Откроется форма с настройками номера ПБР. (Рисунок 57)
        </TextPage>
        <BigPicture>
          <img src={image145} alt="image182" />
          <AltPicture>Рисунок 57 - Форма настроек номера ПБР</AltPicture>
        </BigPicture>
        <TextPage>
          На форме доступны следующие инструменты:
          <br />
          <IconContainer>
            <div>
              <Icon width={16} name="search" />
            </div>
            - Выполнить фильтр по полю.
          </IconContainer>
          <br />
          <img src={image74} alt="image31" /> - Выполнить сортировку по полю . <br />
          <br />
          Для изменения часа начала действия ПБР, необходимо выбрать время напротив каждого номера ПБР.
          <br />
          Для изменения признака перехода на следующий день, необходимо поставить отметку выбора в чек-боксе строки номера ПБР.
        </TextPage>
        <BigPicture>
          <img src={image146} alt="image183" />
        </BigPicture>
        Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
      </Page>
      <Page>
        <DescriptionPage name="uddg">5.2.2. Настройка Номер ПБР [2СЗ]</DescriptionPage>
        <TextPage>
          Настройка выполняется в Централизованной части экземпляра СРПГ ИА. В Распределенных частях экземпляров СРПГ РДУ Востока, а также в Централизованной части
          экземпляра ОДУ Востока настройка отображается в режиме просмотра.
          <br />
          Для перехода к настройкам Номера ПБР [2СЗ], необходимо нажать рабочей панели вкладку <img src={image147} alt="image184" /> , затем в правой части нажать на
          раздел <img src={image148} alt="image185" /> .
          <br />
          Откроется форма с настройками Номера ПБР [2СЗ]. (Рисунок 58)
        </TextPage>
        <BigPicture>
          <img src={image149} alt="image186" />
          <AltPicture>Рисунок 58 - Форма настроек Номера ПБР [2СЗ]</AltPicture>
        </BigPicture>
        <TextPage>
          На форме доступны следующие инструменты:
          <br />
          <IconContainer>
            <div>
              <Icon width={16} name="search" />
            </div>
            - Выполнить фильтр по полю.
          </IconContainer>
          <br />
          <img src={image74} alt="image31" /> - Выполнить сортировку по полю . <br />
          Для изменения часа начала действия ПБР [2СЗ], необходимо выбрать время напротив каждого Номера ПБР [2СЗ]
          <br />
          Для изменения признака перехода на следующий день, необходимо поставить отметку выбора в чек-боксе строки Номера ПБР [2СЗ].
        </TextPage>
        <BigPicture>
          <img src={image146} alt="image183" />
        </BigPicture>
        <TextPage>
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
        </TextPage>
      </Page>
      <Page>
        <DescriptionPage name="setting_up_pg_characteristics">5.2.3. Настройка характеристик ПГ</DescriptionPage>
        <TextPage>
          Настройка выполняется в Централизованной части экземпляра СРПГ ИА. В Распределенных частях экземпляров СРПГ ОДУ, РДУ, а также в Централизованной части
          экземпляра ОДУ Востока настройка отображается в режиме просмотра.
          <br />
          Для перехода к настройкам характеристик, необходимо нажать на рабочей панели вкладку <img src={image150} alt="image187" /> , затем в правой части нажать на
          раздел <img src={image151} alt="image188" /> .
          <br />
          Откроется форма настройки характеристик ПГ. (Рисунок 59)
        </TextPage>
        <BigPicture>
          <img src={image152} alt="image189" />
          <AltPicture>Рисунок 59 - Форма настройки характеристик ПГ </AltPicture>
        </BigPicture>
        <TextPage>
          Для добавления новой характеристики, полученной из ИУС «ОпАМ», необходимо нажать на кнопку в виде плюса напротив названия типа объекта{" "}
          <img src={image153} alt="plus" /> .
          <br />
          Откроется форма добавления характеристики по типу объекта.
        </TextPage>
        <BigPicture>
          <img src={image154} alt="image190" />
        </BigPicture>
        <TextPage>
          Необходимо выбрать способ создания характеристики:
          <br />- Характеристики из ИУС «ОпАМ».
          <br />- Ручной ввод.
          <br />
          Из списка характеристик необходимо выбрать новую характеристику, затем указать в поле названия характеристики – название характеристики.
          <br />
          Для добавления, необходимо нажать кнопку <img src={image155} alt="add" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
          <br />
          Для изменения значения, необходимо нажать на кнопку в виде карандаша <img src={image113} alt="edit" /> в строке с типом объекта, затем внести изменения.
          <br />
          Для определения типа планового графика, в составе которого будет характеристика, необходимо проставить опции у требуемого типа ПГ.
        </TextPage>
        <BigPicture>
          <img src={image156} alt="image191" />
        </BigPicture>
        <TextPage>
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
          <br />
          Для создания и настройки типов сумматоров агрегированной плановой генерации, необходимо напротив сумматора нажать на кнопку «<img src={image157} alt="t" /> ».
          <br />
          Пример модального окна настройки типов сумматоров плановой агрегированной генерации:
        </TextPage>
        <BigPicture>
          <img src={image158} alt="image192" />
        </BigPicture>
        <TextPage>
          В открывшемся модальном окне доступны следующие функции:
          <br />
          - Добавить тип сумматора. Необходимо нажать на кнопку <img src={image155} alt="add" /> , ввести название типа в произвольной форме и далее нажать на кнопку{" "}
          <img src={image155} alt="add" /> . В результате успешного добавления типа сумматора в списке типов появится новое значение.
          <br />
          - Редактировать тип сумматора. Доступно редактирование название типа сумматора. Необходимо нажать на кнопку «<img src={image100} alt="edit" />» напротив типа
          сумматора, внести изменения и нажать на кнопку <img src={image159} alt="apply" /> .
          <br />
          - Удалить тип сумматора. Необходимо нажать на кнопку «<img src={image160} alt="trash" />» напротив типа сумматора и подтвердить удаление по кнопке{" "}
          <img src={image161} alt="delete" /> .
          <br />
          - Добавить характеристику типа сумматора. Необходимо напротив необходимого типа сумматора нажать на кнопку «<img src={image153} alt="plus" />», 
          выбрать из выпадающего списка характеристику, указать UID СК-11 и далее нажать на кнопку <img src={image159} alt="apply" /> . В результате успешного
          добавления характеристики в списке характеристик для выбранного типа появится новое значение.
          <br />
          - Редактировать характеристику типа сумматора. Доступно редактирование характеристики и UID СК-11. Необходимо нажать на кнопку «
          <img src={image100} alt="edit" />» напротив характеристики, внести изменения и нажать на кнопку <img src={image159} alt="apply" />.
          <br />
          - Удалить характеристику типа сумматора. Необходимо нажать на кнопку «<img src={image160} alt="trash" />» напротив характеристики и подтвердить удаление по
          кнопке <img src={image161} alt="delete" />.
          <br />
          Все изменения выполненные в модальном окне сохраняются по кнопке <img src={image18} alt="save" />.
        </TextPage>
      </Page>
      <Page>
        <DescriptionPage name="empty_data">5.2.4. Настройка константы отсутствия данных </DescriptionPage>
        <TextPage>
          Настройка выполняется в Централизованной части экземпляра СРПГ ИА. В Распределенных частях экземпляров СРПГ ОДУ, РДУ, а также в Централизованной части
          экземпляра ОДУ Востока настройка отображается в режиме просмотра.
          <br />
          Для перехода к настройкам константы отсутствия данных, необходимо нажать рабочей панели вкладку <img src={image150} alt="image192" /> , затем в правой части
          нажать на раздел <img src={image162} alt="image193" /> .
          <br />
          Откроется форма настройки отсутствия данных. (Рисунок 60)
        </TextPage>
        <BigPicture>
          <img src={image163} alt="image194" />
          <AltPicture>Рисунок 60 - Форма настройки константы отсутствия данных </AltPicture>
        </BigPicture>
        <TextPage>
          Для добавления значения константы по типу объекта, необходимо нажать на кнопку <img src={image155} alt="add" /> .
          <br />
          Откроется форма добавления значения константы по типу объекта.
        </TextPage>
        <BigPicture>
          <img src={image164} alt="image195" />
        </BigPicture>
        <TextPage>
          Для добавления характеристики, необходимо нажать кнопку <img src={image155} alt="add" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
          <br />
          Для изменения значения, необходимо нажать на кнопку «<img src={image100} alt="edit" />» в строке с типов объекта.
          <br />
          Откроется форма изменения значения константы для выбранного типа объекта.
        </TextPage>
        <BigPicture>
          <img src={image165} alt="image196" />
        </BigPicture>
        <TextPage>
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
          <br />
          Для удаления заданной константы по типу объекта необходимо нажать на кнопку «<img src={image160} alt="trash" />» в строке с типом объекта.
        </TextPage>
      </Page>
      <Page>
        <DescriptionPage name="setting_up_pg_loading">5.2.5. Настройка загрузки ПГ</DescriptionPage>
        <TextPage>
          Настройка выполняется в Централизованной части экземпляров СРПГ ИА, ОДУ Востока.
          <br />
          Для перехода к настройкам номера ПБР, необходимо нажать на рабочей панели вкладку <img src={image147} alt="image197" /> , затем в правой части нажать на раздел{" "}
          <img src={image166} alt="image198" /> .
          <br />
          Откроется форма настройки загрузки типов ПГ, способа их акцепта и параметры смещения графика при загрузке. (Рисунок 61)
        </TextPage>
        <BigPicture>
          <img src={image167} alt="image199" />
          <AltPicture>Рисунок 61 - Форма настройки способа акцепта ПГ и параметров смещения</AltPicture>
        </BigPicture>
        <TextPage>
          На форме доступны следующие инструменты:
          <br />
          <IconContainer>
            <div>
              <Icon width={16} name="search" />
            </div>
            - Выполнить фильтр по полю. <br />
          </IconContainer>
          <img src={image74} alt="image31" /> - Выполнить сортировку по полю . <br />
          <br />
          Для настройки смещения от плановой даты, на которое будет выполняться поиск графика при загрузке из внешней системы необходимо в поле «Смещение» выбрать из
          списка значение.
        </TextPage>
        <BigPicture>
          <img src={image168} alt="image200" />
        </BigPicture>
        <TextPage>
          Для настройки способа акцепта необходимо в поле «Способ акцепта», выбрать из списка значение ручной или автоматический, а также указать время в секундах
          автоматического выполнения акцепта.
        </TextPage>
        <BigPicture>
          <img src={image169} alt="image201" />
          <AltPicture>Рисунок 62 - Список выбора способов акцепта ПГ</AltPicture>
        </BigPicture>
        <TextPage>Для настройки расчета сумматоров необходимо поставить отметку выбора в чек-боксе строки типа ПГ.</TextPage>
        <BigPicture>
          <img src={image170} alt="image202" />
        </BigPicture>
        <TextPage>
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
        </TextPage>
      </Page>
      <Page>
        <DescriptionPage name="configuring_the_composition_of_transmitted_graph_data_in_MODES_Terminal">
          5.2.6. Настройка состава передаваемых данных графика в MODES-Terminal
        </DescriptionPage>
        <TextPage>
          Настройка выполняется в Централизованной части экземпляра СРПГ ИА. В Распределенных частях экземпляров СРПГ ОДУ, РДУ, а также в Централизованной части
          экземпляра ОДУ Востока настройка отображается в режиме просмотра.
          <br />
          Для перехода к настройкам константы отсутствия данных, необходимо нажать рабочей панели вкладку <img src={image147} alt="image203" /> , затем в правой части
          нажать на раздел <img src={image171} alt="image204" /> .
          <br />
          Откроется форма настройки состава ПГ, передаваемая в ИУС «MODES-Terminal». (Рисунок 63)
        </TextPage>
        <BigPicture>
          <img src={image172} alt="image205" />
          <AltPicture>Рисунок 63 - Форма настройки состава ПГ для передачи в MODES-Terminal </AltPicture>
        </BigPicture>
        <TextPage>
          Для настройки типа параметра в MODES необходимо в поле «Значение MODES» ввести тип характеристики.
          <br />
          Для определения типа планового графика, в составе которого будет характеристика, необходимо проставить опции у требуемого типа ПГ.
        </TextPage>
        <BigPicture>
          <img src={image173} alt="image206" />
        </BigPicture>
        <TextPage>
          Для сохранения внесенных изменений, необходимо нажать кнопку <img src={image18} alt="save" /> , для сброса нажать кнопку <img src={image58} alt="cancel" /> .
        </TextPage>
      </Page>
      <Page>
        <DescriptionPage name="distribution_of_settings_in_DC">5.3. Распространение настроек в ДЦ</DescriptionPage>
        <TextPage>
          Для распространения настроек в ДЦ (Номер ПБР [1СЗ], Номер ПБР [2СЗ], Характеристики, Отсутствие данных, MODES-Terminal) необходимо нажать на кнопку{" "}
          <img src={image175} alt="protocol" /> .
          <br />
          Результаты распространения настроек можно посмотреть в протоколе по факту завершения распространения настроек, нажав кнопку{" "}
          <img src={image174} alt="protocol" /> .
          <br />
          Протокол распространения настроек имеет 4 состояния, под каждое состояние определена соответствующая цветовая палитра индикатора статуса протокола.
          <br />
          Статусы протокола:
          <br />
          <IconContainer>
            <Status type="gray" />- распространение настроек не выполнялось;
          </IconContainer>
          <br />
          <IconContainer>
            <Status type="yellow" />- распространение настроек успешно выполнено не во все ДЦ;
          </IconContainer>
          <br />
          <IconContainer>
            <Status type="green" />- распространение настроек успешно выполнено во все ДЦ;
          </IconContainer>
          <br />
          <IconContainer>
            <Status type="red" />- распространение настроек выполнено не успешно во все ДЦ.
          </IconContainer>
          <br />
          <IconContainer>
            Для индикатора <Status type="yellow" /> при нажатии на протокол выводится список ДЦ, в которые распространение настроек выполнилось не успешно.
          </IconContainer>
        </TextPage>
        <BigPicture>
          <img src={image176} alt="image207" />
        </BigPicture>
      </Page>
    </>
  );
};
