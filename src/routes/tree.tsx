import { lazy } from "react";
const Settings = lazy(() => import("pages/Settings"));
const Administration = lazy(() => import("pages/Administration"));
const Journaling = lazy(() => import("pages/Journaling"));
const Nsi = lazy(() => import("pages/Nsi"));
const PlannedSchedules = lazy(() => import("pages/PlannedSchedules"));
const Notifications = lazy(() => import("pages/Notifications"));

export const tree = [
  {
    name: "Главная",
    link: "/",
    exact: true,
    component: <>Home</>,
    rules: ["nsi_admin", "engineer", "sys_admin", "viewer", "nsi_ess_admin"],
  },
  {
    name: "НС<PERSON>",
    link: "/nci",
    exact: true,
    component: <Nsi />,
    rules: ["nsi_admin", "engineer", "viewer", "nsi_ess_admin", "sys_admin"],
  },
  {
    name: "Плановые графики",
    link: "/planned-schedules",
    exact: true,
    component: <PlannedSchedules />,
    rules: ["engineer", "sys_admin", "viewer"],
  },
  {
    name: "Администрирование",
    link: "/administration",
    exact: true,
    component: <Administration />,
    rules: ["sys_admin"],
  },
  {
    name: "Настройки",
    link: "/settings",
    exact: true,
    component: <Settings />,
    rules: ["sys_admin"],
  },
  {
    name: "Журналирование",
    link: "/journaling",
    exact: true,
    component: <Journaling />,
    rules: ["sys_admin", "viewer"],
  },
  {
    name: "Уведомления",
    link: "/notifications",
    exact: true,
    component: <Notifications />,
    rules: ["nsi_admin", "engineer", "sys_admin", "viewer","nsi_ess_admin"],
  },
];
