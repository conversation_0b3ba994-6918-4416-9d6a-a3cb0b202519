import { JSX, lazy, Suspense } from "react";
import { Navigate, Route, Routes as Switch } from "react-router-dom";
import { observer } from "mobx-react";
import { Login } from "pages/Login";
import { PageLayout } from "./PageLayout";
import { tree } from "./tree";
import { Spinner } from "components/Spinner";
import styled from "styled-components";
import { GuidePage } from "./components/GuidePage";
import { HomePage } from "./components/HomePage";

const NotFound = lazy(() => import("pages/NotFound"));

const LoaderContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background-color: ${(p) => p.theme.backgroundColor};
`;

export const Routes = observer(({ selectedTheme, setSelectedTheme, themeEnable }: { selectedTheme: any; setSelectedTheme: any; themeEnable: boolean }) => {
  const PrivateRoute = ({ component }: { component: JSX.Element }) => {
    const auth = localStorage.getItem("token");
    return auth ? component : <Navigate to="/login" />;
  };

  return (
    <Suspense
      fallback={
        <LoaderContainer>
          <Spinner size={100} />
        </LoaderContainer>
      }
    >
      <Switch>
        <Route
          path="/"
          // @ts-ignore
          exact
          element={<HomePage />}
        />

        {tree.map(({ component: Component, exact, link, rules }, index) => {
          return (
            <Route
              key={`route-${index}`}
              // @ts-ignore
              exact={exact}
              path={link}
              element={
                <PrivateRoute
                  component={
                    <PageLayout selectedTheme={selectedTheme} setSelectedTheme={setSelectedTheme} themeEnable={themeEnable}>
                      <>{Component}</>
                    </PageLayout>
                  }
                />
              }
            />
          );
        })}

        <Route path="/login" element={<Login />} />

        <Route path="/help" element={<GuidePage />} />

        <Route path="*" element={<NotFound />} />
      </Switch>
    </Suspense>
  );
});
