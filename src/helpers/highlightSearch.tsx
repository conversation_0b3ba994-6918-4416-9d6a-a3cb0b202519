export const highlightSearch = (searchWord: string, targetWord: string): JSX.Element => {
  let result: (JSX.Element | string)[] = [];
  let searchLen: number = searchWord.length;
  let targetLen: number = targetWord.length;
  let targetIndex: number = 0;

  // Преобразуем поисковое слово и целевое слово в нижний регистр для сравнения
  const lowerSearchWord = searchWord.toLowerCase();
  const lowerTargetWord = targetWord.toLowerCase();

  while (targetIndex < targetLen) {
    if (lowerTargetWord.substring(targetIndex, targetIndex + searchLen) === lowerSearchWord) {
      // Добавляем исходное поисковое слово с сохранением регистра
      result.push(<mark key={targetIndex}>{targetWord.substring(targetIndex, targetIndex + searchLen)}</mark>);
      targetIndex += searchLen;
    } else {
      result.push(targetWord[targetIndex]);
      targetIndex++;
    }
  }

  return <span>{result}</span>;
}
