export const getWidthModal = (width: any) => {
  if (width >= 650 && width < 700) {
    return 650;
  }
  if (width >= 700 && width < 800) {
    return 700;
  }
  if (width >= 800 && width < 900) {
    return 800;
  }
  if (width >= 900 && width < 1024) {
    return 900;
  }
  if (width >= 1024 && width < 1152) {
    return 1024;
  }
  if (width >= 1152 && width < 1280) {
    return 1152;
  }
  if (width >= 1280 && width < 1400) {
    return 1280;
  }
  if (width >= 1400 && width < 1600) {
    return 1400;
  }
  if (width >= 1600 && width < 1700) {
    return 1600;
  }
  if (width >= 1700 && width < 1800) {
    return 1700;
  }
  if (width >= 1800 && width < 1900) {
    return 1800;
  }
  if (width >= 1900 && width < 2000) {
    return 1900;
  }
  if (width >= 2000 && width < 2100) {
    return 2000;
  }
  if (width >= 2100 && width < 2200) {
    return 2100;
  }
  if (width >= 2200 && width < 2560) {
    return 2200;
  }
  if (width >= 2560 && width < 3000) {
    return 2560;
  }
  if (width >= 3000 && width < 3840) {
    return 3000;
  }
  if (width >= 3840 && width < 4000) {
    return 3840;
  }
  return 800;
};
