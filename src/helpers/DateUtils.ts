import { format } from "date-fns";

export const currentDate = new Date();
export const DEFAULT_YEAR = currentDate.getFullYear();
export const DEFAULT_MONTH = currentDate.getMonth() + 1;
export const DEFAULT_DAY = currentDate.getDate();

export const getMonthList = (): string[] => ["Январь", "Февраль", "Март", "Апрель", "Май", "Июнь", "Июль", "Август", "Сентябрь", "Октябрь", "Ноябрь", "Декабрь"];
export const getDetailedMonthList = (): { id: number; value: string; shortValue: string }[] => [
  { id: 1, value: "Январь", shortValue: "Янв." },
  { id: 2, value: "Февраль", shortValue: "Февр." },
  { id: 3, value: "Март", shortValue: "Март" },
  { id: 4, value: "Апрель", shortValue: "Апр." },
  { id: 5, value: "Май", shortValue: "Май" },
  { id: 6, value: "Июнь", shortValue: "Июнь" },
  { id: 7, value: "Июль", shortValue: "Июль" },
  { id: 8, value: "Август", shortValue: "Авг." },
  { id: 9, value: "Сентябрь", shortValue: "Сент." },
  { id: 10, value: "Октябрь", shortValue: "Окт." },
  { id: 11, value: "Ноябрь", shortValue: "Нояб." },
  { id: 12, value: "Декабрь", shortValue: "Дек." },
];

export const getDateParts = (date: Date): { year: number; month: number; day: number } => {
  return {
    year: date.getFullYear(),
    month: date.getMonth() + 1,
    day: date.getDate(),
  };
};

/**
 *
 * @param {1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12} id
 * @returns
 */
export const getMonthDetailsById = (id: number) => {
  const list = getDetailedMonthList();

  return list[id - 1];
};

export const FORMATS = {
  ui(date: Date) {
    const { year, month, day }: { year: number; month: number; day: number } = getDateParts(date);

    const monthStr = month <= 9 ? `0${month}` : month;
    const dayStr = day <= 9 ? `0${day}` : day;

    return `${dayStr}-${monthStr}-${year}`;
  },
  server(dateString: string) {
    const [day, month, year] = dateString.split("-");

    return `${year}-${month}-${day}`;
  },
};

export const isValidMonth = (input: number): boolean => {
  return input >= 1 && input <= 12;
};

export const isValidDay = (input: number): boolean => {
  return input >= 1 && input <= 31;
};

export const isValidYear = (input: number): boolean => {
  return input >= 1900 && input <= 2099;
};

export const WEEK_DAYS = [1, 2, 3, 4, 5, 6, 7];

export const NUMBER_OF_WEEKS = [1, 2, 3, 4, 5, 6];

/**
 *
 * @param {number} year
 * @param {number} month
 */
export function getNumberOfDays(year: number, month: number) {
  const numberOfDays = new Date(year, month + 1, 0).getDate();

  return numberOfDays;
}

export function getNumberOfWeek(year: number, month: number) {
  let numberOfWeek = 1;
  const firstDate = new Date(year, month, 1),
    lastDate = new Date(year, month + 1, 0),
    numDays = lastDate.getDate();

  let dayOfWeekCounter = firstDate.getDay();

  for (let date = 1; date <= numDays; date++) {
    if (dayOfWeekCounter === 0) {
      numberOfWeek++;
    }
    dayOfWeekCounter = (dayOfWeekCounter + 1) % 7;
  }

  return numberOfWeek;
}

/**
 *
 * @param {number} year
 * @param {1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 } month
 * @returns
 */
export function getStartEndDays(year: number, month: number) {
  if (month === 0) {
    month++;
  }

  const numberOfDays = getNumberOfDays(year, month - 1);

  return { startDay: 1, endDay: numberOfDays };
}

/**
 *
 * @param {number} xSize
 * @param {number} ySize
 * @returns {null[][]}
 */
export const generateNullMatrix = (xSize: number, ySize: number) => {
  const matrix: any[] = [];

  for (let y = 0; y < ySize; y++) {
    matrix.push([]);

    for (let _ = 0; _ < xSize; _++) {
      matrix[y].push(null);
    }
  }

  return matrix;
};

export const daysInMonth = (month: number, year: number): number => {
  return new Date(year, month, 0).getDate();
};

export const hoursInDay = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24];

export const transformMonths = (taskMonths: { id: number; value: string; shortValue: string; found: boolean }[]): { id: number; value: string; shortValue: string }[] => {
  return getDetailedMonthList().map(({ id, value, shortValue }) => {
    const found = taskMonths.find((item) => item.id === id);
    return { id, value, shortValue, found: !!found };
  });
};

export const prepareDate = (year: string, month: string, day: string) => {
  const parsedYear = Number(year);
  const parsedMonth = Number(month);
  const parsedDay = Number(day);

  if (isNaN(parsedYear) || isNaN(parsedMonth) || isNaN(parsedDay)) {
    return format(new Date(), "yyyy-MM-dd");
  }

  let finalYear = year ? year : new Date().getFullYear();
  let finalMonth = month ? month : new Date().getMonth() + 1;
  let finalDay = day ? day : new Date().getDate();

  if (finalMonth < 10) {
    finalMonth = `0${finalMonth}`;
  } else {
    finalMonth = `${finalMonth}`;
  }

  if (finalDay < 10) {
    finalDay = `0${finalDay}`;
  } else {
    finalDay = `${finalDay}`;
  }

  return `${finalYear}-${finalMonth}-${finalDay}`;
};

export const prepareDateTable = (time: string) => {
  const timeLength = time?.trim()?.length ?? 0;
  if (timeLength > 0) {
    const [data, t] = time ? time.split("T") : [" - - ", " . "];
    const [year, month, day] = data.split("-");
    const [hourAndMin] = t.split(".");
    return `${day}.${month}.${year} ${hourAndMin}`;
  } else {
    return "";
  }
};
