import { toJS } from "mobx";
import { resolveModuleName } from "typescript";

export const filterTree = (array: any[], searchName: string, key: string): any[] => {
  let result = array.map((item) => {
    let childs = [];
    if (item.childs && item.childs.length > 0) {
      childs = filterTree(item.childs, searchName, key);
    }
    return { ...item, childs };
  });

  let isChild = result.some((item) => {
    return item.childs.length > 0;
  });

  if (isChild) {
    result = result.filter((item) => {
      if (item.childs.length > 0) {
        return true;
      } else {
        const mainName = String(item[key]).toLowerCase();
        const searchManinName = searchName.toLowerCase();
        return mainName.includes(searchManinName);
      }
    });
  } else {
    result = result.filter((item) => {
      const mainName = String(item[key]).toLowerCase();
      const searchManinName = searchName.toLowerCase();
      return mainName.includes(searchManinName);
    });
  }
  return result;
};

// export const filterTreeManyKeys = (array: any[], searchArray: any[]): any[] => {
//   let result = array
//     ? array.map((item) => {
//         let childs = [];
//         if (item.childs && item.childs.length > 0) {
//           childs = filterTreeManyKeys(item.childs, searchArray);
//         }
//         return { ...item, childs };
//       })
//     : [];

//   let isChild = result.some((item) => {
//     return item.childs.length > 0;
//   });

//   if (isChild) {
//     result = result.filter((item) => {
//       if (item.childs.length > 0) {
//         return true;
//       } else {
//         const keys = searchArray.map((el) => el.key);
//         const itemValueOfKeys = keys.map((el) => String(item[el]).toLowerCase());
//         const searchValueOfKeys = searchArray.map((el) => el.searchName);

//         return itemValueOfKeys.some((el, index) => {
//           return el.toUpperCase() === searchValueOfKeys[index].toUpperCase();
//         });
//       }
//     });
//   } else {
//     result = result.filter((item) => {
//       const keys = searchArray.map((el) => el.key);
//       const itemValueOfKeys = keys.map((el) => String(item[el]).toLowerCase());
//       const searchValueOfKeys = searchArray.map((el) => el.searchName);

//       if (searchArray.length === 0) {
//         return false;
//       }
//       if (searchArray.length === 1) {
//         return itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase());
//       }
//       if (searchArray.length === 2) {
//         return (
//           itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) && itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase())
//         );
//       }
//       if (searchArray.length === 3) {
//         return (
//           itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) &&
//           itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase()) &&
//           itemValueOfKeys[2].toUpperCase().includes(searchValueOfKeys[2].toUpperCase())
//         );
//       }
//       if (searchArray.length === 4) {
//         return (
//           itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) &&
//           itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase()) &&
//           itemValueOfKeys[2].toUpperCase().includes(searchValueOfKeys[2].toUpperCase()) &&
//           itemValueOfKeys[3].toUpperCase().includes(searchValueOfKeys[3].toUpperCase())
//         );
//       }
//       if (searchArray.length === 5) {
//         return (
//           itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) &&
//           itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase()) &&
//           itemValueOfKeys[2].toUpperCase().includes(searchValueOfKeys[2].toUpperCase()) &&
//           itemValueOfKeys[3].toUpperCase().includes(searchValueOfKeys[3].toUpperCase()) &&
//           itemValueOfKeys[4].toUpperCase().includes(searchValueOfKeys[4].toUpperCase())
//         );
//       }
//       if (searchArray.length === 6) {
//         return (
//           itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) &&
//           itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase()) &&
//           itemValueOfKeys[2].toUpperCase().includes(searchValueOfKeys[2].toUpperCase()) &&
//           itemValueOfKeys[3].toUpperCase().includes(searchValueOfKeys[3].toUpperCase()) &&
//           itemValueOfKeys[4].toUpperCase().includes(searchValueOfKeys[4].toUpperCase()) &&
//           itemValueOfKeys[5].toUpperCase().includes(searchValueOfKeys[5].toUpperCase())
//         );
//       }
//       if (searchArray.length === 7) {
//         return (
//           itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) &&
//           itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase()) &&
//           itemValueOfKeys[2].toUpperCase().includes(searchValueOfKeys[2].toUpperCase()) &&
//           itemValueOfKeys[3].toUpperCase().includes(searchValueOfKeys[3].toUpperCase()) &&
//           itemValueOfKeys[4].toUpperCase().includes(searchValueOfKeys[4].toUpperCase()) &&
//           itemValueOfKeys[5].toUpperCase().includes(searchValueOfKeys[5].toUpperCase()) &&
//           itemValueOfKeys[6].toUpperCase().includes(searchValueOfKeys[6].toUpperCase())
//         );
//       }
//       if (searchArray.length === 8) {
//         return (
//           itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) &&
//           itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase()) &&
//           itemValueOfKeys[2].toUpperCase().includes(searchValueOfKeys[2].toUpperCase()) &&
//           itemValueOfKeys[3].toUpperCase().includes(searchValueOfKeys[3].toUpperCase()) &&
//           itemValueOfKeys[4].toUpperCase().includes(searchValueOfKeys[4].toUpperCase()) &&
//           itemValueOfKeys[5].toUpperCase().includes(searchValueOfKeys[5].toUpperCase()) &&
//           itemValueOfKeys[6].toUpperCase().includes(searchValueOfKeys[6].toUpperCase()) &&
//           itemValueOfKeys[7].toUpperCase().includes(searchValueOfKeys[7].toUpperCase())
//         );
//       }
//       if (searchArray.length === 9) {
//         return (
//           itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) &&
//           itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase()) &&
//           itemValueOfKeys[2].toUpperCase().includes(searchValueOfKeys[2].toUpperCase()) &&
//           itemValueOfKeys[3].toUpperCase().includes(searchValueOfKeys[3].toUpperCase()) &&
//           itemValueOfKeys[4].toUpperCase().includes(searchValueOfKeys[4].toUpperCase()) &&
//           itemValueOfKeys[5].toUpperCase().includes(searchValueOfKeys[5].toUpperCase()) &&
//           itemValueOfKeys[6].toUpperCase().includes(searchValueOfKeys[6].toUpperCase()) &&
//           itemValueOfKeys[7].toUpperCase().includes(searchValueOfKeys[7].toUpperCase()) &&
//           itemValueOfKeys[8].toUpperCase().includes(searchValueOfKeys[8].toUpperCase())
//         );
//       }
//       if (searchArray.length === 10) {
//         return (
//           itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) &&
//           itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase()) &&
//           itemValueOfKeys[2].toUpperCase().includes(searchValueOfKeys[2].toUpperCase()) &&
//           itemValueOfKeys[3].toUpperCase().includes(searchValueOfKeys[3].toUpperCase()) &&
//           itemValueOfKeys[4].toUpperCase().includes(searchValueOfKeys[4].toUpperCase()) &&
//           itemValueOfKeys[5].toUpperCase().includes(searchValueOfKeys[5].toUpperCase()) &&
//           itemValueOfKeys[6].toUpperCase().includes(searchValueOfKeys[6].toUpperCase()) &&
//           itemValueOfKeys[7].toUpperCase().includes(searchValueOfKeys[7].toUpperCase()) &&
//           itemValueOfKeys[8].toUpperCase().includes(searchValueOfKeys[8].toUpperCase()) &&
//           itemValueOfKeys[9].toUpperCase().includes(searchValueOfKeys[9].toUpperCase())
//         );
//       }
//     });
//   }
//   return result;
// };

const getFlatArray = (arr: any) => {
  let res: any[] = [];
  arr.forEach((el: any) => {
    res.push(el);
    if (el?.childs?.length > 0) {
      const childs = getFlatArray(el.childs);
      childs.forEach((item: any) => {
        res.push(item);
      });
    }
  });
  return res;
};

const bufferFilterTreeManyKeys = (array: any[], searchArray: any[]): any[] => {
  if (array) {
    return array.filter((item: any) => {
      const keys = searchArray.map((el) => el.key);
      const itemValueOfKeys = keys.map((el) => String(item[el]).toLowerCase());
      const searchValueOfKeys = searchArray.map((el) => el.searchName);

      if (searchArray.length === 0) {
        return true;
      }
      if (searchArray.length === 1) {
        return itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase());
      }
      if (searchArray.length === 2) {
        return (
          itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) && itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase())
        );
      }
      if (searchArray.length === 3) {
        return (
          itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) &&
          itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase()) &&
          itemValueOfKeys[2].toUpperCase().includes(searchValueOfKeys[2].toUpperCase())
        );
      }
      if (searchArray.length === 4) {
        return (
          itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) &&
          itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase()) &&
          itemValueOfKeys[2].toUpperCase().includes(searchValueOfKeys[2].toUpperCase()) &&
          itemValueOfKeys[3].toUpperCase().includes(searchValueOfKeys[3].toUpperCase())
        );
      }
      if (searchArray.length === 5) {
        return (
          itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) &&
          itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase()) &&
          itemValueOfKeys[2].toUpperCase().includes(searchValueOfKeys[2].toUpperCase()) &&
          itemValueOfKeys[3].toUpperCase().includes(searchValueOfKeys[3].toUpperCase()) &&
          itemValueOfKeys[4].toUpperCase().includes(searchValueOfKeys[4].toUpperCase())
        );
      }
      if (searchArray.length === 6) {
        return (
          itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) &&
          itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase()) &&
          itemValueOfKeys[2].toUpperCase().includes(searchValueOfKeys[2].toUpperCase()) &&
          itemValueOfKeys[3].toUpperCase().includes(searchValueOfKeys[3].toUpperCase()) &&
          itemValueOfKeys[4].toUpperCase().includes(searchValueOfKeys[4].toUpperCase()) &&
          itemValueOfKeys[5].toUpperCase().includes(searchValueOfKeys[5].toUpperCase())
        );
      }
      if (searchArray.length === 7) {
        return (
          itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) &&
          itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase()) &&
          itemValueOfKeys[2].toUpperCase().includes(searchValueOfKeys[2].toUpperCase()) &&
          itemValueOfKeys[3].toUpperCase().includes(searchValueOfKeys[3].toUpperCase()) &&
          itemValueOfKeys[4].toUpperCase().includes(searchValueOfKeys[4].toUpperCase()) &&
          itemValueOfKeys[5].toUpperCase().includes(searchValueOfKeys[5].toUpperCase()) &&
          itemValueOfKeys[6].toUpperCase().includes(searchValueOfKeys[6].toUpperCase())
        );
      }
      if (searchArray.length === 8) {
        return (
          itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) &&
          itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase()) &&
          itemValueOfKeys[2].toUpperCase().includes(searchValueOfKeys[2].toUpperCase()) &&
          itemValueOfKeys[3].toUpperCase().includes(searchValueOfKeys[3].toUpperCase()) &&
          itemValueOfKeys[4].toUpperCase().includes(searchValueOfKeys[4].toUpperCase()) &&
          itemValueOfKeys[5].toUpperCase().includes(searchValueOfKeys[5].toUpperCase()) &&
          itemValueOfKeys[6].toUpperCase().includes(searchValueOfKeys[6].toUpperCase()) &&
          itemValueOfKeys[7].toUpperCase().includes(searchValueOfKeys[7].toUpperCase())
        );
      }
      if (searchArray.length === 9) {
        return (
          itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) &&
          itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase()) &&
          itemValueOfKeys[2].toUpperCase().includes(searchValueOfKeys[2].toUpperCase()) &&
          itemValueOfKeys[3].toUpperCase().includes(searchValueOfKeys[3].toUpperCase()) &&
          itemValueOfKeys[4].toUpperCase().includes(searchValueOfKeys[4].toUpperCase()) &&
          itemValueOfKeys[5].toUpperCase().includes(searchValueOfKeys[5].toUpperCase()) &&
          itemValueOfKeys[6].toUpperCase().includes(searchValueOfKeys[6].toUpperCase()) &&
          itemValueOfKeys[7].toUpperCase().includes(searchValueOfKeys[7].toUpperCase()) &&
          itemValueOfKeys[8].toUpperCase().includes(searchValueOfKeys[8].toUpperCase())
        );
      }
      if (searchArray.length === 10) {
        return (
          itemValueOfKeys[0].toUpperCase().includes(searchValueOfKeys[0].toUpperCase()) &&
          itemValueOfKeys[1].toUpperCase().includes(searchValueOfKeys[1].toUpperCase()) &&
          itemValueOfKeys[2].toUpperCase().includes(searchValueOfKeys[2].toUpperCase()) &&
          itemValueOfKeys[3].toUpperCase().includes(searchValueOfKeys[3].toUpperCase()) &&
          itemValueOfKeys[4].toUpperCase().includes(searchValueOfKeys[4].toUpperCase()) &&
          itemValueOfKeys[5].toUpperCase().includes(searchValueOfKeys[5].toUpperCase()) &&
          itemValueOfKeys[6].toUpperCase().includes(searchValueOfKeys[6].toUpperCase()) &&
          itemValueOfKeys[7].toUpperCase().includes(searchValueOfKeys[7].toUpperCase()) &&
          itemValueOfKeys[8].toUpperCase().includes(searchValueOfKeys[8].toUpperCase()) &&
          itemValueOfKeys[9].toUpperCase().includes(searchValueOfKeys[9].toUpperCase())
        );
      }
    });
  }
  return [];
};

// const prepareFinalArray = (arr: any, flatArr: any) => {
//   return arr.map((item: any) => {
//     if (item?.childs?.length > 0) {
//       const childs = prepareFinalArray(item.childs, flatArr);
//       return { ...item, childs };
//     } else {
//       const childs = flatArr.find((el: any) => el.tabId === item.tabId)?.childs ?? [];
//       return { ...item, childs };
//     }
//   });
// };

const prepareFinalArray = (arr: any, flatArr: any) => {
  let res: any = [];
  arr.map((item: any) => {
    let parents: any = [];
    if (item.parentId !== "") {
      parents = flatArr.filter((el: any) => el.parentId === item.tabId);
    }
    res = [...res, { ...item }, ...parents];
  });
  return res;
  // return arr.map((item: any) => {
  //   if (item?.childs?.length > 0) {
  //     const childs = prepareFinalArray(item.childs, flatArr);
  //     return { ...item, childs };
  //   } else {
  //     const childs = flatArr.find((el: any) => el.tabId === item.tabId)?.childs ?? [];
  //     return { ...item, childs };
  //   }
  // });
};

const createTree = (arr: any, originalArr: any) => {
  let res: any[] = [];
  arr.map((el: any) => {
    let parents: any[] = [];
    if (el.parentId) {
      const parent = originalArr.find((item: any) => item.tabId === el.parentId);
      const arrUp = createTree([parent], originalArr);
      parents = [...arrUp];
      res.push(...parents);
    }
    res.push(el);
  });
  return res;
};

const unique = (arr: any) => {
  let result: any[] = [];

  for (let i = 0; i < arr.length; i++) {
    const isFind = result.some((el) => el.tabId === arr[i].tabId);
    if (!isFind) {
      result.push(arr[i]);
    }
  }

  return result;
};

export const filterTreeManyKeys = (array: any[], searchArray: any[], isSearchForChilds: boolean): any[] => {
  const arrSearches = searchArray?.filter((el) => el?.searchName?.length > 0);
  const temp = bufferFilterTreeManyKeys(array, arrSearches);
  const res = createTree(temp, array);
  const final = [...new Set(res)];

  if (isSearchForChilds) {
    const buffer = prepareFinalArray(temp, array);
    const prev = [...res, ...buffer];
    return unique(prev);
  } else {
    return final;
  }
};

setTimeout(filterTreeManyKeys, 0);

const getPrepareFinalTable = (arr: any[], originalArr: any[]) => {
  let res: any[] = [];
  arr.map((el) => {
    if (el.parentId) {
      const selected = originalArr.find((item) => item.tabId === el.parentId);
      const resSelected = getPrepareFinalTable([selected], originalArr);
      res.push(...resSelected);
    }
    res.push(el);
  });
  return res;
};
