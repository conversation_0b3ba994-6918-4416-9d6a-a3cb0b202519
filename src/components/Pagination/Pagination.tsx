import React, { FC } from "react";
import styled, { css } from "styled-components";
import { Combobox } from "components/Combobox";
import { Icon } from "../Icon";

export const Container = styled.div`
  width: 100%;
  //height: 70px;
  //min-height: 70px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  //padding: 10px;
  //border: solid 1px red;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
`;

export const Panel = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
`;

const TextBlock = styled.div`
  margin: 0 20px;
  user-select: none;
`;

const Arrow = styled.div<{ isDisabled?: boolean }>`
  width: 36px;
  height: 36px;
  min-width: 36px;
  min-height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(90deg);
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    background-color: ${(p) => p.theme.lightGray} !important;
  }
  &:active {
    background-color: ${(p) => p.theme.gray} !important;
  }

  ${(p) =>
    p.isDisabled &&
    css`
      color: ${(p) => p.theme.lightGray};
      cursor: default;
      &:hover {
        background-color: transparent !important;
      }
      &:active {
        background-color: transparent !important;
      }
    `}
`;

export const ArrowLeft = styled(Arrow)`
  transform: rotate(270deg);
`;

export const ArrowRight = styled(Arrow)`
  transform: rotate(90deg);
`;

export const NumberPagination = styled.div<{ isEllipsis?: boolean; isSelected?: any }>`
  padding: 6px 8px;
  font-weight: 500;
  line-height: 1.75;
  border-radius: 4px;
  letter-spacing: 0.02857em;
  text-transform: uppercase;
  cursor: default;
  user-select: none;

  ${(p) =>
    !p.isEllipsis &&
    css`
      cursor: pointer;
      &:hover {
        background-color: ${(p) => p.theme.lightGray} !important;
      }
      &:active {
        background-color: ${(p) => p.theme.gray} !important;
      }
    `}
  ${(p) =>
    p.isSelected &&
    css`
      font-weight: bold;
    `}
`;

export interface PaginationProps {
  countElementsPagination: number;
  setCountElementsPagination: any;
  data: any[];
  dataWithPagination: any[];
  paginationIndex: number;
  setPaginationIndex: any;
  isSearchMode?: boolean;
}

export const Pagination: FC<PaginationProps> = (props) => {
  const { countElementsPagination, setCountElementsPagination, data, dataWithPagination, paginationIndex, setPaginationIndex } = props;

  const countItemsOnPage = [
    // { value: 5, label: "5" },
    // { value: 10, label: "10" },
    // { value: 15, label: "15" },
    { value: 20, label: "20" },
    // { value: 30, label: "30" },
    // { value: 40, label: "40" },
    { value: 50, label: "50" },
    { value: 100, label: "100" },
  ];

  const isMinArray = dataWithPagination[paginationIndex]?.length < countElementsPagination && paginationIndex === 0;

  return (
    <Container>
      <Panel>
        <TextBlock>Строк на странице</TextBlock>
        <Combobox
          items={countItemsOnPage}
          selectedValue={countElementsPagination}
          width={60}
          position="top"
          maxHeightList={260}
          onChange={({ value }) => {
            setCountElementsPagination(value);
            setPaginationIndex(0);
          }}
        />
        <TextBlock>
          1-{countElementsPagination} из {data.length}
        </TextBlock>
        <ArrowLeft
          isDisabled={paginationIndex === 0 || isMinArray}
          onClick={() => {
            const isDisabled = paginationIndex === 0 || isMinArray;
            if (!isDisabled) {
              setPaginationIndex((prev: number) => prev - 1);
            }
          }}
        >
          <Icon name="arrow" width={14} />
        </ArrowLeft>

        {/*first*/}
        <NumberPagination id="1" onClick={() => setPaginationIndex(0)} isSelected={paginationIndex === 0}>
          1
        </NumberPagination>
        {/*first*/}

        {/*ellipsis*/}
        {paginationIndex > 3 && !isMinArray && (
          <NumberPagination id="2" isEllipsis={true}>
            ...
          </NumberPagination>
        )}
        {/*ellipsis*/}

        {/*the third element from the end*/}
        {paginationIndex === dataWithPagination.length - 1 &&
          paginationIndex !== 1 &&
          !isMinArray &&
          dataWithPagination.length > 3 && ( // add dataWithPagination.length > 3
            <NumberPagination id="3">{paginationIndex - 1}</NumberPagination>
          )}
        {/*the third element from the end*/}

        {/*prev*/}
        {paginationIndex > 1 && !isMinArray && (
          <NumberPagination id="4" onClick={() => setPaginationIndex(paginationIndex - 1)}>
            {paginationIndex}
          </NumberPagination>
        )}
        {/*prev*/}
        {/*center*/}
        {paginationIndex >= 1 && !isMinArray && (
          <NumberPagination id="5" isSelected={paginationIndex}>
            {paginationIndex + 1}
          </NumberPagination>
        )}
        {/*center*/}
        {/*next*/}
        {paginationIndex < dataWithPagination.length - 2 && !isMinArray && (
          <NumberPagination id="6" onClick={() => setPaginationIndex(paginationIndex + 1)}>
            {paginationIndex + 2}
          </NumberPagination>
        )}
        {/*next*/}

        {/*four element*/}
        {dataWithPagination.length > 3 && // 2
          paginationIndex < 2 &&
          !isMinArray &&
          paginationIndex !== 1 && ( //paginationIndex > 0 &&
            <NumberPagination id="7" onClick={() => setPaginationIndex(2)}>
              {paginationIndex + 3}
            </NumberPagination>
          )}
        {/*four element*/}

        {/*{paginationIndex > dataWithPagination.length - 1 && <NumberPagination>{paginationIndex + 3}</NumberPagination>}*/}

        {/*ellipsis*/}
        {paginationIndex < dataWithPagination.length - 4 && !isMinArray && (
          <NumberPagination id="8" isEllipsis={true}>
            ...
          </NumberPagination>
        )}
        {/*ellipsis*/}

        {/*last*/}
        {paginationIndex < dataWithPagination.length - 1 && !isMinArray && (
          <NumberPagination id="9" onClick={() => setPaginationIndex(dataWithPagination.length - 1)} isSelected={paginationIndex === dataWithPagination.length - 1}>
            {dataWithPagination.length}
          </NumberPagination>
        )}
        {/*last*/}

        <ArrowRight
          isDisabled={paginationIndex === dataWithPagination.length - 1 || isMinArray}
          onClick={() => {
            const isDisabled = paginationIndex === dataWithPagination.length - 1 || isMinArray;
            if (!isDisabled) {
              setPaginationIndex((prev: number) => prev + 1);
            }
          }}
        >
          <Icon name="arrow" width={14} />
        </ArrowRight>
      </Panel>
    </Container>
  );
};
