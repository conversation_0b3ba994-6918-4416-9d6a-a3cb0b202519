import React, { <PERSON> } from "react";
import * as avatars from "assets/avatars";
import styled, { CSSProperties } from "styled-components";

export const Img = styled.img<{ size: number }>`
  width: ${(p) => p.size}px;
  height: ${(p) => p.size}px;
`;

export interface AvatarProps {
  name: string;
  className?: string;
  size: number;
  onClick?: any;
}

export const Avatar: FC<AvatarProps> = ({ name = "avatar1", size = 100, className, onClick }) => {
  // @ts-ignore
  const pathAvatar = avatars[name];
  return (
    <>
      <Img
        onClick={() => {
          if (onClick) onClick();
        }}
        size={size}
        src={pathAvatar}
        alt={name}
        className={className}
      />
    </>
  );
};
