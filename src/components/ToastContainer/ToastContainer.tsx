import { FC } from "react";
import { observer } from "mobx-react";
import { Container, Title, IconStyled } from "./ToastContainer.style";

export const ToastContainer: FC<{ message?: string | JSX.Element }> = observer(({ message }) => {
  return (
    <>
      <Title>
        <IconStyled width={15} height={15} name="information" />
        <div style={{ marginLeft: "10px" }}>Информация</div>
      </Title>
      <Container>{message}</Container>
    </>
  );
});
