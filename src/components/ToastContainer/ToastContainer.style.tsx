import styled from "styled-components";
import { Icon } from "../Icon";

export const Container = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  max-width: 200px;
  height: auto;
`;

export const Title = styled.div`
  display: flex;
  padding: 3px 12px;
  border-radius: 5px 5px 0 0;
  background: #e2dede61;
  color: gray;
  align-items: center;
`;

export const IconStyled = styled(Icon)`
  color: ${(p) => p.theme.primaryColor};
`;
