import styled, { css } from "styled-components";
import { Placement } from "tippy.js";
import { Icon } from "components/Icon";

export const Box = styled.div`
  min-width: 60px;
  min-height: 30px;
  position: relative;
  display: flex;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  z-index: 1;
  filter: drop-shadow(0px 0px 4px #0000008c);
  color: ${(p) => p.theme.textColor};

  flex-direction: column;
  border-radius: 8px;
`;
export const Arrow = styled.div<{ placement?: Placement; size: number }>`
  color: #333;
  width: 0;
  height: 0;
  border-style: solid;

  ${({ placement, size }) =>
    placement === "top" &&
    css`
      bottom: -${size}px;
      border-width: ${size}px ${size}px 0 ${size}px;
      border-color: ${(p) => p.theme.primaryColor} transparent transparent transparent;
    `};
  ${({ placement, size }) =>
    placement === "bottom" &&
    css`
      top: -${size}px;
      border-width: 0 ${size}px ${size}px ${size}px;
      border-color: transparent transparent ${(p) => p.theme.primaryColor} transparent;
    `};

  ${({ placement, size }) =>
    placement === "left" &&
    css`
      right: -${size}px;
      border-width: ${size}px 0 ${size}px ${size}px;
      border-color: transparent transparent transparent ${(p) => p.theme.primaryColor};
    `};
  ${({ placement, size }) =>
    placement === "right" &&
    css`
      left: -${size}px;
      border-width: ${size}px ${size}px ${size}px 0;
      border-color: transparent ${(p) => p.theme.primaryColor} transparent transparent;
    `};
`;

export const ChildrenContainer = styled.div`
  position: relative;
`;

export const IconStyled = styled(Icon)`
  //margin: 0 10px;
  margin-top: 4px;
  color: ${(p) => p.theme.blueActiveSupport};
  z-index: 3;
`;

export const ErrorIcon = styled(Icon)`
  margin: 6px 10px 0 8px;
  color: ${(p) => p.theme.redActiveSupport};
  z-index: 3;
`;

export const Header = styled.div`
  height: 30px;
  display: flex;
  align-items: center;
  background-color: ${(p) => p.theme.lightGray};
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
`;

export const BackgroundIcon = styled.div`
  position: absolute;
  background-color: ${(p) => p.theme.white};
  left: 12px;
  top: 7px;
  z-index: 1;
  width: 16px;
  height: 16px;
  border-radius: 50%;
`;
