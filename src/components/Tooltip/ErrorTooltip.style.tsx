import styled from "styled-components";
import { ReactComponent as WarningIcon } from "assets/icons/warning.svg";

export const Title = styled.div`
  display: flex;
  padding: 3px 12px;
  border-radius: 5px 5px 0 0;
  background: #e2dede61;
  color: gray;
  align-items: center;
`;
export const ErrorListContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 300px;
`;
export const Errors = styled.div`
  display: flex;
  position: absolute;
  top: 0;
  right: 0;
`;
export const WarningContainer = styled.div`
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  padding: 3px;
`;
export const Warning = styled(WarningIcon)`
  width: 20px;
  color: red;
`;
export const ErrorList = styled.div`
  display: flex;

  flex-direction: column;

  width: 100%;
  border-radius: 5px;
  overflow: hidden;
`;
export const ErrorItem = styled.div`
  display: flex;
  align-items: center;
  padding: 6px 15px;
`;
export const ErrorItemNumber = styled.div`
  display: flex;
  color: gray;
  opacity: 0.5;
  width: 15px;
`;
export const ErrorItemText = styled.div`
  display: flex;
  margin-left: 10px;
`;
