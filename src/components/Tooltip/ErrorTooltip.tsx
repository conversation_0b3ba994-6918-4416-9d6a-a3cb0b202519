import { FC } from "react";
import { Error<PERSON>ist<PERSON>ontainer, <PERSON><PERSON><PERSON>, Title, Warning, WarningContainer, <PERSON>rror<PERSON>ist, <PERSON>rrorItem, ErrorItemNumber, ErrorItemText } from "./ErrorTooltip.style";
import { Icon } from "components/Icon";
import { Tooltip } from "components/Tooltip/Tooltip";

interface ErrorTooltipProps {
  errors?: string[];
}

export const ErrorTooltip: FC<ErrorTooltipProps> = ({ errors }) => {
  const moreThanOne = errors ? errors.length > 1 : false;
  return (
    <Errors>
      <Tooltip
        placement="right"
        interactive={true}
        type="error"
        content={
          <ErrorListContainer>
            <ErrorList>
              {errors &&
                errors.map((error, index) => (
                  <ErrorItem key={index}>
                    {moreThanOne && <ErrorItemNumber>{index + 1}.</ErrorItemNumber>}
                    <ErrorItemText>{error}</ErrorItemText>
                  </ErrorItem>
                ))}
            </ErrorList>
          </ErrorListContainer>
        }
      >
        <WarningContainer>
          <Warning />
        </WarningContainer>
      </Tooltip>
    </Errors>
  );
};
