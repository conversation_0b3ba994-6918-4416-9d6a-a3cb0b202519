import { FC, useState } from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Icon<PERSON><PERSON><PERSON>, BackgroundIcon, ErrorIcon } from "./Tooltip.style";
import Tippy from "@tippyjs/react/headless";
import { Placement } from "tippy.js";

// https://github.com/atomiks/tippyjs-react
// https://atomiks.github.io/tippyjs/v6/all-props/

const ARROW_HEIGHT = 8; // пробрасывается в компонент arrow

interface TooltipProps {
  visible?: boolean;
  disabled?: boolean;
  children?: JSX.Element | string;
  placement?: Placement;
  interactive?: boolean;
  content?: JSX.Element | string;
  offset?: number;
  appendTo?: HTMLElement;
  delay?: number | [number | null, number | null];
  onClickOutside?: () => void;
  type?: "information" | "error";
}

export const Tooltip: FC<TooltipProps> = (props) => {
  const {
    visible,
    disabled,
    children,
    placement = "top",
    interactive = false,
    content,
    offset = 0,
    appendTo = document.body,
    delay = [0, 0], //[800,0]
    onClickOutside,
    type = "information",
    ...rest
  } = props;
  const [arrow, setArrow] = useState<any>(null);

  return (
    <Tippy
      interactive={interactive} // Можно ли взаимодействовать с всплывающим контентом
      placement={placement} // Позиция подсказки
      visible={visible} // если controlled component
      appendTo={appendTo}
      offset={[0, ARROW_HEIGHT + offset]} // упрощенный offset (только дальность от элемента)
      onClickOutside={onClickOutside} //  если кликнули за пределы подсказки, полезно для controlled component
      delay={delay}
      {...rest}
      render={(attrs) => {
        // Режим кастомного тултипа
        return (
          <Box {...attrs}>
            {/*<Header>*/}
            {/*  {type === "information" && <IconStyled width={20} name="information" />}*/}
            {/*  {type === "error" && <ErrorIcon width={26} name="error" />}*/}
            {/*  {type === "information" ? <>Информация</> : <>Ошибка</>}*/}
            {/*</Header>*/}
            {/*<BackgroundIcon />*/}
            {content} <Arrow size={ARROW_HEIGHT} placement={attrs["data-placement"]} ref={setArrow} />
          </Box>
        );
      }}
      popperOptions={{
        modifiers: [
          {
            name: "arrow",
            options: {
              element: arrow,
            },
          },
        ],
      }}
    >
      <ChildrenContainer>{children}</ChildrenContainer>
    </Tippy>
  );
};
