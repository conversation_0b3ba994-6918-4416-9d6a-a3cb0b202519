import { FC, useState } from "react";
import { Header, IconContainer, Title, ChildrenContainer } from "./TooltipWithClose.style";
import { Tooltip } from "components/Tooltip/Tooltip";
import { Icon, namesIcons } from "components/Icon";

interface TooltipWithCloseProps {
  content?: string | JSX.Element;
  title?: string | JSX.Element;
}

export const TooltipWithClose: FC<TooltipWithCloseProps> = (props) => {
  const { children, content, title } = props;
  const [isOpen, setIsOpen] = useState(false);

  const toggleOpen = (): void => {
    setIsOpen((prevState) => {
      return !prevState;
    });
  };

  return (
    <>
      <Tooltip
        placement="right"
        interactive={true}
        visible={isOpen}
        onClickOutside={(): void => setIsOpen(false)}
        content={
          <>
            <Header>
              <Title>{title}</Title>
              <IconContainer onClick={toggleOpen}>
                <Icon name={namesIcons.close} width={20} height={20} style={{ marginTop: "1px", border: "solid 1px gray", borderRadius: "10px" }} />
              </IconContainer>
            </Header>
            <>{content}</>
          </>
        }
      >
        <ChildrenContainer onClick={toggleOpen}>{children}</ChildrenContainer>
      </Tooltip>
    </>
  );
};
