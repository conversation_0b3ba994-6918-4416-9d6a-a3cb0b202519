import { Demo<PERSON>ontainer } from "@mui/x-date-pickers/internals/demo";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { type DateRangePickerProps, DateRangePicker as MuiDateRangePicker } from "@mui/x-date-pickers-pro/DateRangePicker";
import { SingleInputDateRangeField } from "@mui/x-date-pickers-pro/SingleInputDateRangeField";
// @ts-ignore
import { type PickerChangeHandlerContext } from "@mui/x-date-pickers/internals/hooks/usePicker/usePickerValue.types";
import { type DateRangeValidationError, type DateRange } from "@mui/x-date-pickers-pro";
import { useState } from "react";
import ru from "date-fns/locale/ru";
import styled from "styled-components";
import { Icon } from "components/Icon";

const MuiDateRangePickerLocal = styled(MuiDateRangePicker)`
  min-width: unset !important;

  & .MuiOutlinedInput-root {
    border-radius: 4px;
    font-weight: 500;
    padding: 0 !important;
    flex-direction: row-reverse !important;
    height: 24px;

    & input {
      padding: 0 6px !important;
      color: ${(p) => p.theme.textColor} !important;
    }

    & fieldset {
      border-color: ${(p) => p.theme.lightGray} !important;
    }
  }
`;

const IconWrapper = styled.div`
  margin-left: 10px;
  cursor: pointer;
`;

const CalendarIcon = styled(Icon)`
  color: ${(p) => p.theme.primaryColor};
`;

interface IProps extends DateRangePickerProps<Date> {
  dateFrom: any;
  dateTo: any;
  handleChangeDate: (value: DateRange<any>, context: PickerChangeHandlerContext<DateRangeValidationError>) => void;
}

export const DateRangePicker = (props: IProps) => {
  const { dateFrom, dateTo, handleChangeDate, ...otherProps } = props;
  // const [value, setValue] = useState<DateRange<any>>([dateFrom, dateTo])
  const [context, setContext] = useState<PickerChangeHandlerContext<DateRangeValidationError>>({ validationError: [null, null] });

  const onAccept = (value: any) => {
    handleChangeDate(value, context);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ru}>
      <DemoContainer components={["SingleInputDateRangeField"]}>
        <MuiDateRangePickerLocal
          // defaultValue={[new Date('01.06.2023'), new Date()]}
          // localeText={{ start: 'Начало', end: 'Конец' }}
          slots={{ field: SingleInputDateRangeField }}
          value={[dateFrom, dateTo]}
          onChange={(newValue, context) => {
            setContext(context);
          }}
          onAccept={onAccept}
          slotProps={{
            textField: {
              InputProps: {
                endAdornment: (
                  <IconWrapper>
                    <CalendarIcon width={10} name="calendar" />
                  </IconWrapper>
                ),
              },
            },
          }}
        />
      </DemoContainer>
    </LocalizationProvider>
  );
};
