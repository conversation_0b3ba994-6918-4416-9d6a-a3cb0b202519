import { FC } from "react";
import { Container, Title } from "./Loader.style";
import { Spinner } from "components/Spinner";
import { LoaderProps } from "./Loader.types";

export const Loader: FC<LoaderProps> = (props) => {
  const { hasOverlay, title, style, children, spinnerSize } = props;

  return (
    <Container hasOverlay={hasOverlay} style={style}>
      {title && <Title>{title}</Title>}
      {children && <>{children}</>}
      <Spinner size={spinnerSize} />
    </Container>
  );
};
