import { ComponentStory, ComponentMeta } from "@storybook/react";

import { Loader, LoaderProps } from "./index";

export default {
  title: "Design System/Loader",
  component: Loader,
} as ComponentMeta<typeof Loader>;

const Template: ComponentStory<typeof Loader> = (args: LoaderProps) => (
  <div style={{ width: "200px" }}>
    <Loader {...args} />
  </div>
);

export const LoaderComponent = Template.bind({});
LoaderComponent.args = {
  spinnerSize: 100,
};
