import { FC, useEffect, useState } from "react";
import styled, { css } from "styled-components";
import { useStores } from "../../stores/useStore";
import { observer } from "mobx-react";
import { DEFAULT_DAY, DEFAULT_MONTH, DEFAULT_YEAR, prepareDate } from "../../helpers/DateUtils";
import { Tooltip } from "../Tooltip";
import { Loader } from "components/Loader";
import { isModeCenter } from "../../utils/getMode";
import { useLocation } from "react-router-dom";
import queryString from "query-string";

const Container = styled.div<{ isServer?: boolean }>`
  color: ${(p) => p.theme.textColor};
  text-align: center;
  display: flex;
  align-items: center;

  ${(p) =>
    !p.isServer &&
    css`
      color: ${(p) => p.theme.gray};
    `}
`;

const TooltipContent = styled.div`
  padding: 10px;
  max-width: 350px;
  min-width: 150px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const Status = styled.div<{ isServer?: boolean; isLoading?: boolean }>`
  width: 14px;
  height: 14px;
  background-color: ${(p) => p.theme.orangeActiveSupport};
  margin: 0 10px;
  // border: solid 1px ${(p) => p.theme.lightGray};
  border-radius: 50%;

  ${(p) =>
    p.isServer &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport} !important;
    `}
  ${(p) =>
    p.isLoading &&
    css`
      color: ${(p) => p.theme.blueActiveSupport} !important;
    `}
`;

export const Span = styled.span<{ isSRDK?: boolean; isLoading?: boolean }>`
  user-select: none;
  ${(p) =>
    p.isSRDK &&
    css`
      color: ${(p) => p.theme.topMenuTextColor};
    `}
`;

const prepareData = (data: any) => {
  if (data) {
    const buffer = data.split("T");
    const [y, month, d] = buffer[0].split("-");
    const [hours, minutes, sms] = buffer[1].split(":");
    const [s, ms] = sms.split(".");
    return new Date(Number(y), Number(month - 1), Number(d), Number(hours), Number(minutes), Number(s), Number(ms));
  } else {
    return new Date();
  }
};

interface LiveTimerProps {
  type?: "DT" | "D"; // "DATE-TIME" | "DATE"
  className?: string;
  viewStatus?: boolean;
  isSRDK?: boolean;
  keyTimer?: number;
}

export const LiveTimer: FC<LiveTimerProps> = observer((props) => {
  const { type = "DT", className, viewStatus = false, isSRDK = false } = props;
  const { liveTimerStore, authStore } = useStores();
  const { isCenter } = authStore;

  const { dateTime, isLoadingTime } = liveTimerStore;

  const [visibilityState, setVisibilityState] = useState(true);

  window.addEventListener("focus", () => {
    setVisibilityState(true);
  });
  window.addEventListener("blur", () => {
    setVisibilityState(false);
  });

  window.addEventListener("visibilitychange", function () {
    if (document.hidden) {
      setVisibilityState(false);
    } else {
      setVisibilityState(true);
    }
  });

  useEffect(() => {
    if (visibilityState) {
      liveTimerStore.getTimeServer();
    }
  }, [isModeCenter, isCenter, visibilityState]);

  useEffect(() => {
    return () => {
      liveTimerStore.stopTimer();
    };
  }, []);

  const [time, setTime] = useState<any>(prepareData(dateTime));
  useEffect(() => {
    let myInterval = setInterval(() => {
      setTime((prev: any) => {
        return new Date(prev.getTime() + 1000);
      });
    }, 1000);
    return () => clearInterval(myInterval);
  }, [dateTime]);

  useEffect(() => {
    setTime(prepareData(dateTime));
  }, [dateTime]);

  const timer = () => {
    if (type === "DT") {
      return (
        <>
          <Span isSRDK={isSRDK} className={className}>
            {time.toLocaleString()}
          </Span>
        </>
      );
    }
    if (type === "D") {
      const dateBuffer = prepareDate(String(time.getFullYear()), String(time.getMonth() + 1), String(time.getDate()));
      const [year, month, day] = dateBuffer.split("-");
      const finalData = [day, month, year].join("-");
      return (
        <>
          <Span isSRDK={isSRDK} className={className}>
            {finalData}
          </Span>
        </>
      );
    }
  };

  return (
    <Container isServer={dateTime}>
      {viewStatus && (
        <Tooltip
          content={dateTime ? <TooltipContent>Время с сервера</TooltipContent> : <TooltipContent>Время сервера недоступно.Повторите попытку позже...</TooltipContent>}
        >
          <Status isServer={dateTime} isLoading={isLoadingTime} />
        </Tooltip>
      )}
      {dateTime ? <>{timer()}</> : <Span>XX.XX.XXXX, XX:XX:XX</Span>}
    </Container>
  );
});
