import React, { FC, useEffect } from "react";
import { useStores } from "../../stores/useStore";
import { observer } from "mobx-react";

export interface AccessControlProps {
  rules?: string[];
  children?: any;
}

export const AccessControl: FC<AccessControlProps> = observer((props) => {
  const { authStore } = useStores();
  const { userInfo } = authStore;
  const { rules = [], children } = props;
  const isAccess = userInfo.roles.some((el: string) => {
    return rules.some((rule: any) => rule === el);
  });
  if (isAccess) {
    return <>{children}</>;
  } else {
    return <></>;
  }
});
