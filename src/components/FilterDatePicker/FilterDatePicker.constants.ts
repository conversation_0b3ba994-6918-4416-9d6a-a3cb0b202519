// export const WEEK_DAYS = [
//   { id: 1, name: "<PERSON><PERSON>", isWeekend: false },
//   { id: 2, name: "В<PERSON>", isWeekend: false },
//   { id: 3, name: "С<PERSON>", isWeekend: false },
//   { id: 4, name: "<PERSON><PERSON>", isWeekend: false },
//   { id: 5, name: "<PERSON><PERSON>", isWeekend: false },
//   { id: 6, name: "<PERSON><PERSON>", isWeekend: true },
//   { id: 7, name: "В<PERSON>", isWeekend: true },
// ];

export const WEEK_DAYS = [
  { id: 1, name: "П", isWeekend: false },
  { id: 2, name: "В", isWeekend: false },
  { id: 3, name: "С", isWeekend: false },
  { id: 4, name: "Ч", isWeekend: false },
  { id: 5, name: "П", isWeekend: false },
  { id: 6, name: "С", isWeekend: false },
  { id: 7, name: "В", isWeekend: false },
];

export const prepareVariable = (variable: number, defaultValue: number) => {
  return variable ? (variable < 10 ? `0${variable}` : `${variable}`) : defaultValue < 10 ? `0${defaultValue}` : `${defaultValue}`;
};
