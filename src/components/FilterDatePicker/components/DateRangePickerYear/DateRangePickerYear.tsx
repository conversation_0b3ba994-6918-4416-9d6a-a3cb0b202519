import React from "react";
import { toString } from "lodash";

import { getComboboxYearRange } from "./selectors/getComboboxYearRange";
import { Combobox } from "components/Combobox";
import styled from "styled-components";

const ComboboxStyled = styled(Combobox)`
  margin: 0 10px;
`;

export const DateRangePickerYear = (props) => {
  const { year, isDisabled, onChange } = props;

  return <ComboboxStyled placeholder="Год" items={getComboboxYearRange()} selectedValue={toString(year)} disabled={isDisabled} onChange={onChange} width={120} dataTest="date-picker.year" />;
};
