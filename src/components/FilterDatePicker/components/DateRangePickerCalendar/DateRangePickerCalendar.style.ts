import styled, { css } from "styled-components";

export const Row = styled.div`
  flex-direction: row;
  display: flex;
  justify-content: space-around;
`;

export const Day = styled.div`
  font-weight: normal;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #6e7478;
  cursor: pointer;
  user-select: none;
  width: 23px;
  height: 23px;
  border: solid 1px #daecff;
  border-radius: 3px;

  &:hover {
    border: solid 1px #daecff;
    background-color: #f3f9ff;
    color: #208cff;
  }

  ${(props) =>
    props.isActive &&
    css`
      color: #f3f9ff;
      background-color: #208cff;
    `};

  ${(props) =>
    props.isDisabled &&
    css`
      color: gray;
      background-color: #80808012;
      &:hover {
        border: solid 1px #80808012;
        background-color: #80808012;
        color: gray;
        cursor: no-drop;
      }
    `};
`;

export const Cell = styled.div`
  width: 100%;
  height: 26px;
  border-radius: 2px;
  display: flex;
  justify-content: center;
  align-items: center;

  color: ${(props) => (props.isWeek ? "#F8969B" : "#6e7478")};

  ${(props) =>
    props.isCurrentMonth &&
    css`
      &:hover {
        border: solid 1px #daecff;
        background-color: #f3f9ff;
        color: #208cff;
        cursor: pointer;
      }
    `};

  ${(props) =>
    props.isSelectedDay &&
    css`
      border: solid 1px #daecff;
      background-color: #f3f9ff;
      color: #208cff;
    `};
`;
