import { isNull } from "lodash";
import React from "react";

import { Cell, Day, Row } from "./DateRangePickerCalendar.style";
import { getDaysMatrix } from "./utils/getDaysMatrix";

export const DateRangePickerCalendar = (props) => {
  const { year, month, startDay, endDay, onChange } = props;
  const daysMatrix = getDaysMatrix(year, month);

  const isActiveDay = (day) => day >= startDay && day <= endDay;

  const handleDayClick = (day) => {
    if (onChange) {
      onChange(day);
    }
  };

  return daysMatrix.map((week, weekIndex) => (
    <Row key={weekIndex}>
      {week.map((day, dayIndex) => {
        if (isNull(day)) {
          return <Cell key={dayIndex} />;
        }
        const isActive = isActiveDay(day);
        return (
          <Cell key={dayIndex}>
            <Day isActive={isActive} isDisabled={isNull(day)} onClick={() => handleDayClick(day)}>
              {day}
            </Day>
          </Cell>
        );
      })}
    </Row>
  ));
};
