import { getNumberOfWeek, generateNullMatrix } from "helpers/DateUtils";

export function getDaysMatrix(year, month) {
  const date = new Date(year, month, 1);
  const numberOfWeek = getNumberOfWeek(year, month);
  const matrixOfDays = generateNullMatrix(7, numberOfWeek).map((week) =>
    week.map((_, index) => {
      if (index + 1 >= date.getDay() && date.getMonth() === month) {
        const currentDay = date.getDate();
        date.setDate(date.getDate() + 1);
        return currentDay;
      }

      return null;
    })
  );

  return matrixOfDays;
}
