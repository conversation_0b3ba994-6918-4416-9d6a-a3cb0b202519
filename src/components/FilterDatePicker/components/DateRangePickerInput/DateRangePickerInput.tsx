import React from "react";
import { getMonthDetailsById } from "helpers";

import { LeftSection, SelectedContainer, StyledDropIcon, TitleContainer, WrapperCombobox, RightSection } from "./DateRangePickerInput.style";

export const DateRangePickerInput = (props) => {
  const { month, startDay, endDay, startDayPlaceholder = "Дата начала", endDayPlaceholder = "Дата окончания", className, errors = [], disabled, onClick } = props;
  const monthName = getMonthDetailsById(month).value.toLowerCase();

  return (
    <WrapperCombobox className={className} dataName="rangeDatePicker" errors={errors?.length} disabled={disabled} onClick={onClick}>
      <LeftSection>
        <TitleContainer>
          <div style={{ fontSize: "0.75rem" }}>{startDayPlaceholder}</div>
          <div style={{ fontSize: "0.75rem" }}>{endDayPlaceholder}</div>
        </TitleContainer>
        <SelectedContainer>
          <div>
            {startDay} {monthName}
          </div>
          <div>
            {endDay} {monthName}
          </div>
        </SelectedContainer>
      </LeftSection>
      <RightSection>
        <div disabled={disabled}>
          <StyledDropIcon disabled={disabled} />
        </div>
        {/* {errors?.length > 0 && <ErrorTooltip errors={errors} />} */}
      </RightSection>
    </WrapperCombobox>
  );
};
