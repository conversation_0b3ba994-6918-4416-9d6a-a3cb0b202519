import styled, { css } from "styled-components";
import { ReactComponent as CalendarIcon } from "assets/icons/calendar.svg";

export const WrapperCombobox = styled.div`
  cursor: pointer;
  user-select: none;
  min-width: 235px;
  height: 40px;
  margin: 0;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  outline: none;
  border: 1px solid #d4d9dc;
  position: relative;
  border-radius: 5px;
  ${(props) =>
    props.open &&
    css`
      border: 1px solid #007bff;
      box-shadow: 0 0 3px 0 #208cff;
    `}
  ${(props) =>
    props.size === "small" &&
    css`
      width: 235px;
      height: 45px;
    `}

  ${(props) =>
    props.width &&
    css`
      min-width: auto;
      width: ${props.width}px;
    `}

     ${(p) =>
    p.errors &&
    css`
      border: 1px solid red;
    `};

  ${(p) =>
    p.disabled &&
    css`
      background-color: #80808012;
      cursor: default;
    `};

  ${(p) =>
    p.hasChanges &&
    css`
      border: 1px solid green;
    `};
`;

export const LeftSection = styled.div`
  flex: 1;
  padding-left: 15px;
  padding-right: 0;
  position: relative;
  display: flex;
  justify-content: center;
  flex-direction: column;
  height: 100%;
`;
export const RightSection = styled.div`
  width: 55px;
  display: flex;
  justify-content: space-evenly;
  height: 100%;
  align-items: center;
  cursor: pointer;
  ${(p) =>
    p.disabled &&
    css`
      cursor: default;
      &:hover {
        color: #bcc8d1;
      }
    `};
`;

export const StyledDropIcon = styled(CalendarIcon)`
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  ${(props) =>
    props.open &&
    css`
      color: #208cff !important;
    `}

  color: #bcc8d1;
  &:hover {
    color: gray;
  }
  ${(p) =>
    p.disabled &&
    css`
      cursor: default;
      &:hover {
        color: #bcc8d1;
      }
    `};
`;

export const TitleContainer = styled.div`
  display: flex;
  justify-content: space-between;
  text-align: center;
  color: gray;
  ${(props) =>
    props.labelColor &&
    css`
      color: ${props.labelColor};
    `}
`;

export const SelectedContainer = styled.div`
  display: flex;
  justify-content: space-between;
  text-align: center;
`;
