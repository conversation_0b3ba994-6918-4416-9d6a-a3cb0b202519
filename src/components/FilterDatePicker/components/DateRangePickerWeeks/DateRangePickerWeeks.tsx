import React from "react";

import { Week, WeekCell } from "./DateRangePickerWeeks.style";
import { Row } from "../DateRangePickerCalendar/DateRangePickerCalendar.style";
import { WEEK_DAYS } from "../../FilterDatePicker.constants";

export const DateRangePickerWeeks = () => {
  return (
    <Week>
      <Row>
        {WEEK_DAYS.map((day, index) => (
          <WeekCell key={index} isWeekend={day.isWeekend}>
            {day.name}
          </WeekCell>
        ))}
      </Row>
    </Week>
  );
};
