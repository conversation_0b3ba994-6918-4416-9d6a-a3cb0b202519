import styled from "styled-components";

export const Week = styled.div`
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  border-top: 1px solid rgba(212, 217, 220, 0.5);
  border-bottom: 1px solid rgba(212, 217, 220, 0.5);
`;

export const WeekCell = styled.div<{ isWeekend?: boolean }>`
  width: 100%;
  height: 26px;
  font-size: 13px;
  font-weight: normal;
  font-style: normal;
  font-stretch: normal;
  line-height: 1.31;
  display: flex;
  justify-content: center;
  user-select: none;
  align-items: center;
  color: ${(props) => (props.isWeekend ? "#F8969B" : props.theme.textColor)};
`;
