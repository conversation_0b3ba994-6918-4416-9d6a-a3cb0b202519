import React, { FC } from "react";
import { toString } from "lodash";

import { getDetailedMonthList } from "helpers/DateUtils";
import { Combobox } from "components/Combobox";
import styled from "styled-components";

const ComboboxStyled = styled(Combobox)`
  margin: 0 10px;
`;

interface DateRangePickerMonthProps {
  month?: any;
  isDisabled?: boolean;
  onChange?: any;
}

export const DateRangePickerMonth: FC<DateRangePickerMonthProps> = (props) => {
  const { month, isDisabled, onChange } = props;

  const months = getDetailedMonthList().map((month) => ({ value: String(month.id), label: month.value }));

  return <ComboboxStyled placeholder="Месяц" items={months} selectedValue={String(Number(month))} disabled={isDisabled} onChange={onChange} width={120} dataTest="date-picker.month" />;
};
