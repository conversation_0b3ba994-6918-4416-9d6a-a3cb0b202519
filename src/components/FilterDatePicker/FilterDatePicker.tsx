import { FC, useCallback, useEffect, useRef, useState } from "react";

import { Tooltip } from "components/Tooltip/Tooltip";

import {
  DateRangePickerHeader,
  DateRangePickerHeaderActions,
  DateRangePickerContainer,
  DateRangePickerTooltipContent,
  ButtonStyled,
  Container,
  Placeholder,
  SelectedDateContainer,
  Arrow,
  CalendarIcon,
} from "./FilterDatePicker.style";
import { DateRangePickerMonth } from "./components/DateRangePickerMonth";
import { DateRangePickerYear } from "./components/DateRangePickerYear";
import { MonthCalendar } from "components/MonthCalendar";
import { Icon } from "components/Icon";
import { useOnClickOutside } from "hooks/useOnClickOutside";
import { DEFAULT_DAY, DEFAULT_MONTH, DEFAULT_YEAR } from "helpers/DateUtils";
import { prepareVariable } from "./FilterDatePicker.constants";
import { useStores } from "../../stores/useStore";
import { observer } from "mobx-react";

export interface DateRangePickerProps {
  className?: string;
  day?: any;
  month?: any;
  year?: any;
  onLoadDay?: any;
  hiddenChooseButton?: boolean;
  titleButton?: string;
  prevDisabled?: boolean;
  loadDay?: any[];
  onClick?: (values: { day: number; month: number; year: number }) => void;
  disabled?: boolean;
  dataTest?: string;
}

export const FilterDatePicker: FC<DateRangePickerProps> = observer((props) => {
  const { authStore } = useStores();
  const { isCenter } = authStore;
  const {
    className,
    day,
    month,
    year,
    hiddenChooseButton = false,
    titleButton = "Выбрать",
    prevDisabled = false,
    loadDay = [],
    onClick,
    onLoadDay,
    disabled = false,
    dataTest,
  } = props;
  const [isOpenTooltip, setIsOpenTooltip] = useState(false);

  const finalDay = prepareVariable(Number(day), DEFAULT_DAY);
  const finalMonth = prepareVariable(Number(month), DEFAULT_MONTH);
  const finalYear = prepareVariable(Number(year), DEFAULT_YEAR);

  const isYearDisabled = false;
  const isMonthDisabled = false;

  const [selectedYear, setSelectedYear] = useState<number | string | null>(year ?? DEFAULT_YEAR);
  const [selectedMonth, setSelectedMonth] = useState<number | string | null>(month ?? DEFAULT_MONTH);
  const [selectedDay, setSelectedDay] = useState<number | string | null>(day); //?? new Date().getDate()

  useEffect(() => {
    if (selectedYear && onLoadDay) {
      onLoadDay(selectedYear);
    }
  }, [selectedYear,isCenter]);

  const handleChangeDay = (day: number) => {
    setSelectedDay(day);
  };

  const handleChangeMonth = ({ value }: { value: number }): void => {
    setSelectedMonth(value);
    setSelectedDay(null);
  };

  const handleChangeYear = ({ value }: { value: string | number }): void => {
    setSelectedYear(value);
    setSelectedDay(null);
  };

  const refDatePicker = useRef<HTMLDivElement>(null);

  useOnClickOutside(refDatePicker, () => setIsOpenTooltip(false));

  const finalLoadDay = loadDay
    .map((el) => {
      const items = el.split("-");
      if (Number(items[1]) === Number(selectedMonth)) {
        return items[2];
      } else {
        return null;
      }
    })
    .filter((el) => el !== null)
    .map((el) => Number(el));

  const renderDateRangePicker = () => (
    <DateRangePickerContainer ref={refDatePicker}>
      <DateRangePickerHeader>
        <DateRangePickerHeaderActions>
          <DateRangePickerYear year={selectedYear} isDisabled={isYearDisabled} onChange={handleChangeYear} />
          <DateRangePickerMonth month={selectedMonth} isDisabled={isMonthDisabled} onChange={handleChangeMonth} />
          {!hiddenChooseButton && (
            <ButtonStyled
              disabled={selectedDay === null}
              title={titleButton}
              onClick={() => {
                onClick && onClick({ day: Number(selectedDay), month: Number(selectedMonth), year: Number(selectedYear) });
                setIsOpenTooltip(false);
              }}
              dataTest="calendar.accept-button"
            />
          )}
        </DateRangePickerHeaderActions>
      </DateRangePickerHeader>
      <MonthCalendar
        year={Number(selectedYear)}
        month={Number(selectedMonth) - 1}
        selectedDay={Number(selectedDay)}
        prevDisabled={prevDisabled}
        loadDay={finalLoadDay}
        onChange={(day) => {
          handleChangeDay(day);
          if (hiddenChooseButton) {
            onClick && onClick({ day: day, month: Number(selectedMonth), year: Number(selectedYear) });
            setIsOpenTooltip(false);
          }
        }}
      />
    </DateRangePickerContainer>
  );

  return (
    <Tooltip
      visible={isOpenTooltip}
      placement="bottom"
      content={<DateRangePickerTooltipContent hiddenChooseButton={hiddenChooseButton}>{renderDateRangePicker()}</DateRangePickerTooltipContent>}
      interactive
    >
      <Container
        onClick={() => {
          if (!disabled) setIsOpenTooltip((prev) => !prev);
        }}
        className={className}
        disabled={disabled}
      >
        {/*<Placeholder>Дата :</Placeholder>*/}
        <SelectedDateContainer data-test={dataTest}>
          <CalendarIcon width={10} name="calendar" />
          {day && <>{`${finalDay}.${finalMonth}.${finalYear}`}</>}
          <Arrow isOpen={isOpenTooltip}>
            <Icon name="menuArrow" width={6} />
          </Arrow>
        </SelectedDateContainer>
      </Container>
    </Tooltip>
  );
});
