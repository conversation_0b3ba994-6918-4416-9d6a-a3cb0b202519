import styled, { css } from "styled-components";
import { Icon } from "components/Icon";
import { Button } from "../Button";
import { motion } from "framer-motion";

export const DateRangePickerHeader = styled.div<{ hasClose?: boolean }>`
  position: relative;

  ${(p) =>
    p.hasClose &&
    css`
      min-height: 25px;
      padding-right: 30px;
    `}
`;

export const DateRangePickerHeaderActions = styled.div`
  display: flex;
  margin-bottom: 10px;
`;

export const DateRangePickerContainer = styled.div`
  padding: 15px;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  border-radius: 8px;
`;

export const DateRangePickerTooltipContent = styled.div<{ hiddenChooseButton?: boolean }>`
  //width: 400px;
  width: 350px;
  ${(p) =>
    p.hiddenChooseButton &&
    css`
      width: 300px;
    `}
`;

export const CloseButton = styled(Icon)`
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  border: 1px solid #d4d9dc;
  border-radius: 10px;
  width: 24px;
  height: 24px;
  color: gray;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;

  &:hover {
    color: ${(p) => p.theme.primaryColor};
    border: 1px solid ${(p) => p.theme.primaryColor};
  }
`;

export const ButtonStyled = styled(Button)`
  width: 148px;
  //height: 28px;
`;

export const Container = styled.div<{ disabled?: boolean }>`
  width: 150px;
  max-width: 150px;
  min-width: 150px;
  height: 24px; //28
  border: solid 1px ${(p) => p.theme.lightGray};
  position: relative;
  cursor: pointer;
  user-select: none;
  border-radius: 4px;
  ${(p) =>
    p.disabled &&
    css`
      background-color: ${(p) => p.theme.lightGray};
      cursor: not-allowed;
    `}
`;

export const Placeholder = styled.div`
  position: absolute;
  top: -11px;
  left: 10px;
  padding: 0 5px;
  transition: all 0.3s;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  color: ${(p) => p.theme.textColor};
`;

export const SelectedDateContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 5px;
`;

export const Arrow = styled.div<{ isOpen?: boolean }>`
  margin-left: auto;
  margin-right: 5px;
  transform: rotate(90deg);
  color: ${(p) => p.theme.gray};

  ${(p) =>
    p.isOpen &&
    css`
      transform: rotate(270deg);
    `}
`;

export const CalendarIcon = styled(Icon)`
  margin: 0 5px;
  color: ${(p) => p.theme.primaryColor};
`;
