import { FC } from "react";
import {
  Header,
  NotificationContainer,
  NotificationMessage,
  Ring,
  Alert,
  Notification,
  ScrollDiv,
  Up,
  NotificationImage,
  Title,
  Button,
  Down,
  TitleDescriptionAndTimer,
  TimerContainer,
  SpinnerContainer,
  HeaderContainer,
  Description,
  IconContainer,
  BackgroundIcon,
  IconStyled,
  IconStatusContainer,
} from "./Toast.style";
import { Spinner } from "components/Spinner";
import { observer } from "mobx-react";
import { useStores } from "stores/useStore";
import { Icon } from "components/Icon";
import { ToastContainer } from "components/ToastContainer";
import { Tooltip } from "components/Tooltip/Tooltip";
import { AnimatePresence } from "framer-motion";
import { IconNameProps } from "../Icon/Icon.type";

const COLOR_SCHEME: { [index: string]: { backgroundColor: string; colorBorder: string; textColor: string } } = {
  done: { backgroundColor: "#ffffff", colorBorder: "#34B53A", textColor: "#34B53A" },
  error: { backgroundColor: "#ffffff", colorBorder: "#FF3A29", textColor: "#FF3A29" },
  information: { backgroundColor: "#ffffff", colorBorder: "#0071FF", textColor: "#0071FF" },
  warning: { backgroundColor: "#ffffff", colorBorder: "#FFB200", textColor: "#FFB200" },
};

export const Toast: FC = observer(() => {
  const { notificationStore } = useStores();

  const handleToggle = (): void => {
    notificationStore.toggleNotification();
  };

  const stopTimer = (id: number): void => {
    notificationStore.stopTimer(id);
  };

  const playTimer = (id: number): void => {
    notificationStore.playTimer(id);
  };

  return (
    <>
      <NotificationContainer>
        <NotificationMessage>
          <ScrollDiv>
            <>
              {notificationStore.toastList.map(
                (
                  toast: {
                    timer: number;
                    id: number;
                    description: string;
                    icon: IconNameProps;
                    isPaused: boolean;
                    isTimer: boolean;
                    type: "done" | "error" | "information" | "warning";
                    title: string;
                    isWordWrap:boolean
                  },
                  i: number
                ) => {
                  const isWordWrap = toast?.isWordWrap ?? false
                  if (toast.timer <= 0.5) {
                    notificationStore.deleteNotification(toast.id);
                  }
                  const type = toast.type ?? "information";

                  //
                  const valueShort: string = String(toast.description)
                    .split("")
                    .map((item, index) => {
                      if (index < 24) {
                        return item;
                      } else if (index > 24 && index < 28) {
                        return ".";
                      }
                      return null;
                    })
                    .join("");

                  const lengthValue = String(toast.description).split("").length;
                  //

                  const variantsAnimations = {
                    hidden: {
                      x: 100,
                      opacity: 0,
                    },
                    visible: {
                      x: 0,
                      opacity: 1,
                    },
                    exit: {
                      x: 100,
                      opacity: 0,
                    },
                  };

                  return (
                    // @ts-ignore
                    <AnimatePresence key={`Notification-${i}`}>
                      {toast.timer >= 1 && (
                        <Notification
                          onMouseEnter={(): void => {
                            stopTimer(toast.id);
                          }}
                          onMouseLeave={(): void => {
                            playTimer(toast.id);
                          }}
                          key={i}
                          count={Math.ceil(lengthValue / 24)}
                          transition={{ delay: 0.2 }}
                          initial={"hidden"}
                          animate={"visible"}
                          exit={"exit"}
                          variants={variantsAnimations}
                          data-test="notification.container"
                        >
                          <IconStatusContainer>
                            <BackgroundIcon type={type} width={100} name="backgroundToast" />
                            {toast.icon && <IconStyled type={type} width={30} name={toast.icon} />}
                            {toast.isTimer && (
                              <TimerContainer color={COLOR_SCHEME[type].textColor}>
                                {toast.timer}
                                <SpinnerContainer>
                                  <Spinner size={20} color={COLOR_SCHEME[type].textColor} />
                                </SpinnerContainer>
                              </TimerContainer>
                            )}
                          </IconStatusContainer>
                          <IconContainer onClick={(): void => notificationStore.deleteNotification(toast.id)}>
                            <Icon width={15} name="close" dataTest="notification.close-icon" />
                          </IconContainer>
                          <HeaderContainer>
                            <Title isWordWrap={isWordWrap} data-test="notification.title">{toast.title}</Title>
                            <Description isWordWrap={isWordWrap} data-test="notification.message">{toast.description}</Description>
                          </HeaderContainer>
                        </Notification>
                      )}
                    </AnimatePresence>
                  );
                }
              )}
            </>
          </ScrollDiv>
        </NotificationMessage>
      </NotificationContainer>
    </>
  );
});
