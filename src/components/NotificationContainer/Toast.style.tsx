import styled, { css, keyframes } from "styled-components";
import { motion } from "framer-motion";
import { ReactComponent as BellI<PERSON> } from "assets/icons/bell.svg";
import { Icon } from "../Icon";

export const IconStatusContainer = styled.div`
  position: relative;
  margin: auto;
`;

export const Up = styled.div`
  width: 100%;
  height: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
`;
export const Down = styled.div`
  width: 100%;
  height: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
`;

export const toastinright = keyframes`
  from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
`;

export const Notification = styled(motion.div)<{ count: number }>`
  position: relative;
  pointer-events: auto;
  overflow: hidden;
  margin: 0 0 15px;
  //border-radius: 12px 12px 12px 12px;
  //opacity: 0.9;
  background: ${(p) => p.theme.backgroundColorSecondary} no-repeat 15px;
  min-height: 70px;
  //height: 100px;
  width: 550px;
  color: black;
  border: solid 1px ${(p) => p.theme.lightGray};
  display: flex;
  flex-direction: row;
  //padding: 10px 5px 0 10px;
  padding: 10px;

  :hover {
    box-shadow: 0 0 5px #999;
    //opacity: 1;
    // cursor: pointer;
    // min-height: ${(p) => (p.count > 1 ? (p.count - 1) * 25 + 70 : 70)}px;
    // height: ${(p) => (p.count > 1 ? (p.count - 1) * 25 + 70 : 70)}px;
  }
`;

export const Button = styled.button`
  margin-right: 10px;
  font-weight: 700;
  color: #000;
  outline: none;
  text-shadow: 0 1px 0 #fff;
  line-height: 1;
  font-size: 16px;
  padding: 0;
  cursor: pointer;
  background: 0 0;
  border: none;
  img {
    width: 14px;
    height: 14px;
  }
`;

export const Title = styled.div<{isWordWrap:boolean}>`
  font-size: 16px;
  line-height: 19px;
  margin-right: 40px;
    ${p => p.isWordWrap && css`
        word-wrap: break-word;
        word-break: break-all;
    `}
`;

export const Description = styled.div<{isWordWrap:boolean}>`
  font-size: 14px;
  line-height: 19px;
  margin-right: 50px;
    ${p => p.isWordWrap && css`
        word-wrap: break-word;
        word-break: break-all;
    `}
    //white-space: normal;
    //overflow-wrap: break-word;
`;

export const NotificationContainer = styled.div`
  bottom: 0;
  font-size: 14px;
  box-sizing: border-box;
  position: fixed;
  z-index: 200000;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  pointer-events: none;
  transition: transform 0.6s ease-in-out;
`;

export const NotificationImage = styled.div`
  float: left;
  margin-right: 15px;
`;

export const NotificationMessage = styled.div`
  border-radius: 10px;
  pointer-events: auto;
  margin-right: 20px;
  display: flex;
  flex-direction: column-reverse;
  cursor: pointer;
`;

export const ButtonOption = styled.button`
  pointer-events: auto;
  color: #444;
  outline: none;
  width: 20px;
  height: 20px;
  border-radius: 100px;
  cursor: pointer;
  background: 0 0;
  border: solid 0.3px black;
  margin-right: 5px;
  margin-top: auto;
  //opacity: 0.6;
  :hover {
    color: white;
    //opacity: 1;
  }
`;

export const Img = styled.div`
  width: 30px;
  height: 30px;
`;

export const Header = styled.div`
  display: flex;
  justify-content: flex-end;
  z-index: 999;
  left: 0;
  align-items: center;
`;

export const ScrollDiv = styled.div`
  padding: 10px 20px;
`;

export const ButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
`;

export const Ring = styled.div<{ isOpen: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  position: relative;
  transition: all 0.3s;
  color: ${(p) => (p.isOpen ? "#F2994A" : "gray")};
  transform: scale(1);

  &:hover {
    transform: scale(1.1);
  }
`;

export const Alert = styled.div`
  background: #ff7220;
  width: 15px;
  height: 16px;
  color: white;
  position: absolute;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  top: -6px;
  right: -7px;
  border-radius: 3px;
  user-select: none;
`;

export const Bell = styled(BellImage)`
  width: 17px;
  filter: grayscale(1);
  &:hover {
    filter: grayscale(0);
  }
`;

export const TitleHeader = styled.p`
  margin-right: 10px;
  font-weight: 500;
  font-size: 12px;
  margin-top: auto;
  padding: 0 4px;
  color: #708090;
  &:hover + ${Bell} {
    color: red;
  }
`;

export const TimerContainer = styled.div`
  width: 25px;
  height: 25px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  position: absolute;
  top: 13px;
  left: 80px;

  ${(p) =>
    p.color &&
    css`
      color: ${p.color};
    `}
`;

export const Left = styled.div`
  width: 90%;
`;
export const Right = styled.div`
  width: 10%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const TitleDescriptionAndTimer = styled.div`
  display: flex;
`;

export const SpinnerContainer = styled.div`
  position: absolute;
`;

export const HeaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-left: 40px;
  width: 330px;
  color: ${(p) => p.theme.textColor};
`;

export const IconContainer = styled.div`
  position: absolute;
  right: 20px;
  top: 40px;
  color: ${(p) => p.theme.textColor};
`;

export const BackgroundIcon = styled(Icon)<{ type?: "done" | "error" | "information" | "warning" }>`
  position: relative;
  margin-left: 20px;
  ${(p) =>
    p.type === "done" &&
    css`
      color: ${(p) => p.theme.greenLightSupport};
    `}
  ${(p) =>
    p.type === "error" &&
    css`
      color: ${(p) => p.theme.redLightSupport};
    `}
  ${(p) =>
    p.type === "information" &&
    css`
      color: ${(p) => p.theme.blueLightSupport};
    `}
  ${(p) =>
    p.type === "warning" &&
    css`
      color: ${(p) => p.theme.orangeLightSupport};
    `}
`;

export const IconStyled = styled(Icon)<{ type?: "done" | "error" | "information" | "warning" }>`
  position: absolute;
  top: 35px;
  left: 55px;
  ${(p) =>
    p.type === "done" &&
    css`
      color: ${(p) => p.theme.greenActiveSupport};
    `}
  ${(p) =>
    p.type === "error" &&
    css`
      color: ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.type === "information" &&
    css`
      color: ${(p) => p.theme.blueActiveSupport};
    `}
  ${(p) =>
    p.type === "warning" &&
    css`
      color: ${(p) => p.theme.orangeActiveSupport};
    `}
`;
