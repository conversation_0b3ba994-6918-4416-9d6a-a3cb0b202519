import styled, { css } from "styled-components";

const ButtonComponent = styled.button<{ isError?: boolean; disabled?: boolean }>`
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  border: none;
  width: 175px;
  cursor: pointer;
  transition: all 0.2s;
  user-select: none;
  min-height: 24px;

  ${(p) =>
    p.disabled &&
    css`
      background-color: ${(p) => p.theme.lightGray} !important;
      color: ${(p) => p.theme.textColor} !important;
      cursor: no-drop !important;
    `}

  ${(p) =>
    p.isError &&
    css`
      background-color: ${(p) => p.theme.redActiveSupport} !important;
      color: ${(p) => p.theme.white} !important;
    `}
`;

export const PrimaryButton = styled(ButtonComponent)`
  background-color: ${(props) => props.theme.primaryColor};
  color: ${(p) => p.theme.white} !important;

  &:hover {
    background-color: ${(props) => props.theme.primaryColorHover};
  }

  &:active {
    background-color: ${(props) => props.theme.primaryColorActive};
  }
  &:disabled {
    color: ${(p) => p.theme.black} !important;
  }
`;

export const SecondaryButton = styled(ButtonComponent)`
  background: transparent;
  color: ${(props) => props.theme.buttonSecondaryColor};

  &:hover {
    background-color: ${(props) => props.theme.lightGray};
    border-color: ${(props) => props.theme.primaryColorHover};
  }

  &:active {
    color: ${(props) => props.theme.buttonSecondaryColorActive};
    border-color: ${(props) => props.theme.buttonSecondaryColorActive};
  }
`;

export const TertiaryButton = styled(ButtonComponent)`
  background: ${(props) => props.theme.white};
  border: 1px solid ${(props) => props.theme.lightGray};

  &:hover {
    border-color: ${(props) => props.theme.black};
  }

  &:active {
    background-color: ${(props) => props.theme.black};
    color: ${(props) => props.theme.white};
  }
`;

export const IconContainer = styled.div<{ width?: number; name?: any; padding?: any }>`
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 4px;

  ${(p) =>
    p.padding &&
    css`
      padding: ${p.padding};
    `}

  ${(p) =>
    p.width &&
    css`
      width: ${p.width}px;
      height: ${p.width}px;
    `}

  ${(p) =>
    p.name === "error" &&
    css`
      color: ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.name === "success" &&
    css`
      color: ${(p) => p.theme.greenActiveSupport};
      margin-top: -4px;
    `}
  ${(p) =>
    p.name === "wait" &&
    css`
      color: ${(p) => p.theme.orangeActiveSupport};
    `}
`;

export const TitleLabel = styled.div<{ title?: string; isError?: boolean }>`
  ${(p) =>
    p.title &&
    css`
      margin-left: 4px;
    `}
  ${(p) =>
    p.isError &&
    css`
      color: ${(p) => p.theme.white} !important;
    `}
`;
