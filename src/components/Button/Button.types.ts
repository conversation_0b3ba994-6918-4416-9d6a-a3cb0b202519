import { IconNameProps } from "components/Icon/Icon.type";

export interface IButton {
  type?: "primary" | "secondary" | "tertiary";
  onClick?: () => void;
  title?: any;
  icon?: IconNameProps | null;
  className?: string;
  widthIcon?: number;
  disabled?: boolean;
  isError?: boolean;
  ref?: any;
  message?: any;
  onMouseEnter?: any;
  onMouseLeave?: any;
  padding?: any;
  dataTest?: string;
  dataTestIcon?: string;
}
