import React, { <PERSON> } from "react";
import { PrimaryButton, SecondaryButton, TertiaryButton, IconContainer, TitleLabel } from "./Button.style";
import { Icon } from "components/Icon";
import { IButton } from "./Button.types";

export const Button: FC<IButton> = (buttonProps) => {
  const {
    type = "primary",
    title,
    icon,
    className,
    widthIcon = 16,
    disabled = false,
    onClick,
    isError = false,
    message = null,
    onMouseLeave,
    onMouseEnter,
    padding,
    dataTest,
    dataTestIcon,
    ...props
  } = buttonProps;
  if (type === "primary") {
    return (
      <PrimaryButton
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          if (!disabled && onClick) {
            onClick();
          }
        }}
        className={className}
        {...props}
        disabled={disabled}
        isError={isError}
        title={message}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        data-test={dataTest}
      >
        {icon && (
          <IconContainer width={widthIcon} name={icon} padding={padding}>
            <Icon name={icon} width={widthIcon} />
          </IconContainer>
        )}
        <TitleLabel isError={isError} title={message ? message : title}>
          {title}
        </TitleLabel>
      </PrimaryButton>
    );
  }
  if (type === "secondary") {
    return (
      <SecondaryButton
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          if (!disabled && onClick) {
            onClick();
          }
        }}
        className={className}
        {...props}
        disabled={disabled}
        isError={isError}
        title={message}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        data-test={dataTest}
      >
        {icon && (
          <IconContainer width={widthIcon} name={icon} padding={padding} data-test={dataTestIcon}>
            <Icon name={icon} width={widthIcon} />
          </IconContainer>
        )}
        <TitleLabel title={message ? message : title}>{title}</TitleLabel>
      </SecondaryButton>
    );
  }
  if (type === "tertiary") {
    return (
      <TertiaryButton
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          if (!disabled && onClick) {
            onClick();
          }
        }}
        className={className}
        {...props}
        disabled={disabled}
        isError={isError}
        title={message}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      >
        {icon && (
          <IconContainer width={widthIcon} name={icon} padding={padding}>
            <Icon name={icon} width={widthIcon} />
          </IconContainer>
        )}
        <TitleLabel isError={isError} title={message ? message : title}>
          {title}
        </TitleLabel>
      </TertiaryButton>
    );
  }
  return (
    <PrimaryButton
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        if (!disabled && onClick) {
          onClick();
        }
      }}
      className={className}
      {...props}
      disabled={disabled}
      isError={isError}
      title={message}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      {icon && <Icon name={icon} width={widthIcon} />}
      <TitleLabel isError={isError} title={message ? message : title}>
        {title}
      </TitleLabel>
    </PrimaryButton>
  );
};
