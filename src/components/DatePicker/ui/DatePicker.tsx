import { DatePicker as DatePickerMui, LocalizationProvider } from "@mui/x-date-pickers";
import { ru } from "date-fns/locale";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
// import { useTheme } from "app/providers/ThemeProvider";
import { useState } from "react";
import styled from "styled-components";

const DatePickerMuiLocal = styled(DatePickerMui)`
  min-width: unset !important;
  & [class$="MuiInputBase-root-MuiOutlinedInput-root"],
  & .MuiOutlinedInput-root {
    padding-right: 11px;
    border-radius: 6px;
    //font-size: 0.75rem !important;
    font-weight: 500;

    & input {
      padding: 0.25em 1em;
    }
  }

  [class*="MuiInputAdornment-outlined"] {
    width: 20px;
    margin: 0;

    button {
      padding: 0;
      margin: 0;
    }
  }
`;

interface DatePickerProps {
  className?: string;
  label?: string;
  value?: any;
  setValue?: any;
  isOpen?: any;
  setIsOpen?: any;
  disablePast?: boolean;
}

export const DatePicker = (props: DatePickerProps) => {
  const { className, label, value, setValue, isOpen, setIsOpen, disablePast = false } = props;

  const [isOpenLocal, setIsOpenLocal] = useState(false);

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ru}>
      <div>
        <DatePickerMuiLocal
          disablePast={disablePast}
          value={value}
          onChange={(newValue) => {
            // if(disablePast){
            // 	const date = new Date(newValue)
            // 	if(date < new Date()){
            // 		setValue(new Date())
            // 	}else{
            // 		setValue(newValue)
            // 	}
            // }else{
            // 	setValue(newValue)
            // }
            setValue(newValue);
          }}
          open={isOpen || isOpenLocal}
          label={label}
          className={className}
          onOpen={() => {
            if (setIsOpen) {
              setIsOpen(true);
            } else {
              setIsOpenLocal(true);
            }
            setTimeout(() => {
              // const root = document.querySelector(".MuiPickersPopper-root");
              // if (root) {
              //   root.classList.add(theme);
              // }
            }, 100);
          }}
          onClose={() => {
            if (setIsOpen) {
              setIsOpen(false);
            } else {
              setIsOpenLocal(false);
            }
          }}
        />
      </div>
    </LocalizationProvider>
  );
};
