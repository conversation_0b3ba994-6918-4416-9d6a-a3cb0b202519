import React from "react";
import styled from "styled-components";

const Label = styled.label`
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
`;

const Switcher = styled.div`
  position: relative;
  width: 36px;
  height: 18px;
  background: ${(p) => p.theme.lightGray};
  border-radius: 32px;
  padding: 4px;
  transition: 300ms all;

  &:before {
    transition: 300ms all;
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    border-radius: 35px;
    top: 50%;
    left: 1px;
    background: white;
    transform: translate(0, -50%);
  }
`;

const Input = styled.input`
  opacity: 0;
  position: absolute;

  &:checked + ${Switcher} {
    background: ${(p) => p.theme.primaryColor};

    &:before {
      transform: translate(18px, -50%);
    }
  }
`;

export const Switch = ({ ...rest }) => {
  return (
    <Label>
      <Input type="checkbox" {...rest} />
      <Switcher></Switcher>
    </Label>
  );
};
