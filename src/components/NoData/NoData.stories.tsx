import { ComponentStory, ComponentMeta } from "@storybook/react";

import { NoData, NoDataProps } from "./NoData";

export default {
  title: "Design System/NoData",
  component: NoData,
} as ComponentMeta<typeof NoData>;

const Template: ComponentStory<typeof NoData> = (args: NoDataProps) => (
  <div style={{ width: "100%", height: "90vh" }}>
    <NoData {...args} />
  </div>
);

export const MonthCalendarComponent = Template.bind({});
MonthCalendarComponent.args = {};
