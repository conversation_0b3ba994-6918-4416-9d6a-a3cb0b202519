import { FC, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { Icon } from "components/Icon";
import { observer } from "mobx-react";
import { ErrorTooltip } from "components/Tooltip/ErrorTooltip";
import {
  DropZoneContainer,
  MessageSection,
  MessageInput,
  MessageLabel,
  MessageContainer,
  FileSection,
  FileContainer,
  FileCaption,
  FileList,
  File,
  FileActions,
} from "./DropZone.style";

export interface DropZoneProps {
  label?: string | JSX.Element;
  accept?: string;
  files?: any | null[];
  setFiles: any;
  disabled?: boolean;
  errors?: [string];
  name?: string;
  onBlur?: () => void;
  readOnly?: boolean;
  dataTest?: string;
}

export const DropZone: FC<DropZoneProps> = observer((props) => {
  const { label, accept, files = [], setFiles, disabled, errors = [], name, onBlur, dataTest } = props;
  const onDrop = useCallback((acceptedFiles: any) => setFiles([...files, ...acceptedFiles]), [files, setFiles]);
  const removeAll = (): [any] => setFiles([]);

  const { getRootProps, getInputProps, isDragActive, isDragAccept, isDragReject } = useDropzone({
    onDrop,
    accept: accept,
    disabled,
  });

  const hasFiles = !!files.length;
  const hasErrors = !!errors.length;

  return (
    <DropZoneContainer>
      {!hasFiles && (
        <MessageSection>
          <MessageContainer disabled={disabled} {...getRootProps({ isDragActive, isDragAccept, isDragReject, errors: hasErrors })} onBlur={onBlur}>
            <MessageInput {...getInputProps()} name={name} data-test={dataTest} />
            <MessageLabel>{label}</MessageLabel>
          </MessageContainer>
          {hasErrors && <ErrorTooltip errors={errors} />}
        </MessageSection>
      )}

      {hasFiles && (
        <FileSection>
          {/*<FileCaption>Файл :</FileCaption>*/}
          <FileContainer>
            <FileList>
              {files.map((file: any) => {
                return (
                  <File key={file.path}>
                    {file.path} - {file.size} bytes
                  </File>
                );
              })}
            </FileList>
            {!disabled && (
              <FileActions onClick={removeAll}>
                <Icon width={20} height={20} name="close" />
              </FileActions>
            )}
          </FileContainer>
        </FileSection>
      )}
    </DropZoneContainer>
  );
});
