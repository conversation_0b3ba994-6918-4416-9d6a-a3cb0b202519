import { ComponentStory, ComponentMeta } from "@storybook/react";

import { DropZone, DropZoneProps } from "./DropZone";

export default {
  title: "Design System/DropZone",
  component: DropZone,
} as ComponentMeta<typeof DropZone>;

const Template: ComponentStory<typeof DropZone> = (args: DropZoneProps) => (
  <div style={{ width: "200px" }}>
    <DropZone {...args} />
  </div>
);

export const DropZoneComponent = Template.bind({});
DropZoneComponent.args = {
  label: "Кликните или перетащите сюда файлы.(Расширения .xls .xlsx)",
  accept: ".xls",
};
