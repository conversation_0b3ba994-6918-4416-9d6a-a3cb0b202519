import styled from "styled-components";

const getColor = (props: any) => {
  if (props.errors) {
    return "red";
  }
  if (props.isDragAccept) {
    return "#2196f3";
  }
  if (props.isDragReject) {
    return "#2196f3";
  }
  if (props.isDragActive) {
    return "#2196f3";
  }

  return "#eeeeee";
};

export const DropZoneContainer = styled.section`
  width: 100%;
`;
export const MessageSection = styled.div`
  position: relative;
`;
export const MessageInput = styled.input``;
export const MessageLabel = styled.p`
  position: relative;
  text-align: center;
`;
export const MessageContainer = styled.div<{ disabled?: boolean }>`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-width: 2px;
  border-radius: 2px;
  border-color: ${(props) => getColor(props)};
  border-style: dashed;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  color: ${(props) => (props.disabled ? "gray" : props.theme.textColor)};
  outline: none;
  transition: border 0.24s ease-in-out;
  margin: 5px 0;
  cursor: pointer;
`;
export const FileSection = styled.div`
  padding: 20px;
  margin: 5px 0;
`;
export const FileContainer = styled.div`
  display: flex;
`;
export const FileCaption = styled.h4``;
export const FileList = styled.ul`
  padding: 10px 20px;
  width: 100%;
`;
export const File = styled.li``;
export const FileActions = styled.div`
  align-items: center;
  display: flex;
  cursor: pointer;
  opacity: 0.3;
  transition: all 0.2s;
  &:hover {
    opacity: 1;
  }
`;
