import { FC } from "react";
import * as Icons from "./IconsList";
import { Container } from "./Icon.style";
import { IconProps } from "./Icon.type";

export const Icon: FC<IconProps> = (props): JSX.Element => {
  const { name, width, height, title, onClick, className, styleIcon, dataTest, ...rest } = props;
  const Svg = name ? Icons[name] : null;

  return (
    <Container title={title} {...rest} onClick={onClick} className={className} style={styleIcon} data-test={dataTest}>
      {Svg && <Svg width={width} height={height} />}
    </Container>
  );
};
