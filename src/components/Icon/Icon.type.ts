import { groups } from "./IconsList";

export interface IconProps {
  name: IconNameProps;
  width?: number | string;
  height?: number | string;
  title?: string;
  onClick?: () => void;
  className?: string;
  styleIcon?: any;
  dataTest?: string;
}

export type IconNameProps =
  | "logo"
  | "key"
  | "profile"
  | "settings"
  | "menuArrow"
  | "logout"
  | "fillMoon"
  | "check"
  | "directory"
  | "data"
  | "calendar"
  | "close"
  | "download"
  | "comparison"
  | "trash"
  | "folderOpen"
  | "folderClose"
  | "file"
  | "start"
  | "save"
  | "create"
  | "error"
  | "success"
  | "edit"
  | "copy"
  | "information"
  | "search"
  | "menu"
  | "backgroundToast"
  | "warning"
  | "post"
  | "publication"
  | "done"
  | "wait"
  | "reset"
  | "arrow"
  | "xml"
  | "unavailable"
  | "sync"
  | "user"
  | "moon"
  | "filter"
  | "pencil"
  | "groups"
  | "role"
  | "plus"
  | "minus"
  | "triangle"
  | "goToLink"
  | "logoWhite"
  | "system"
  | "add"
  | "help"
  | "viewPassword"
  | "notViewPassword"
  | "excel"
  | "csv"
  | "notification"
  | "notNotification"
  | "selectAll"
  | "unSelectAll"
  | "types"
  | "back"
  | "list"
  | "openAll"
  | "tableSave"
  | "tableReset"
  | "arrowSorting"
  | "view"
  | "gou"
  | "rge"
  | "ego"
  | "vetv"
  | "sech"
  | "ter"
  | "electric"
  | "integral"
  | "dist"
  | "ng"
  | "gen"
  | "sumConsumption"
  | "saldo"
  | "ps"
  | "consumption"
  | "adderPotr";
