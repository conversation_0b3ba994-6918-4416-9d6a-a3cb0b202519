import React from "react";
export { ReactComponent as logo } from "assets/icons/logo.svg";
export { ReactComponent as key } from "assets/icons/key.svg";
export { ReactComponent as profile } from "assets/icons/profile.svg";
export { ReactComponent as user } from "assets/icons/user.svg";
export { ReactComponent as settings } from "assets/icons/settings.svg";
export { ReactComponent as moon } from "assets/icons/moon.svg";
export { ReactComponent as fillMoon } from "assets/icons/fillMoon.svg";
export { ReactComponent as menuArrow } from "assets/icons/menu-arrow.svg";
export { ReactComponent as logout } from "assets/icons/logout.svg";
export { ReactComponent as check } from "assets/icons/check.svg";
export { ReactComponent as directory } from "assets/icons/directory.svg";
export { ReactComponent as data } from "assets/icons/data.svg";
export { ReactComponent as close } from "assets/icons/close.svg";
export { ReactComponent as calendar } from "assets/icons/calendar.svg";
export { ReactComponent as download } from "assets/icons/download.svg";
export { ReactComponent as comparison } from "assets/icons/comparison.svg";
export { ReactComponent as trash } from "assets/icons/trash.svg";
export { ReactComponent as folderOpen } from "assets/icons/open-folder.svg";
export { ReactComponent as folderClose } from "assets/icons/close-folder.svg";
export { ReactComponent as file } from "assets/icons/file.svg";
export { ReactComponent as start } from "assets/icons/start.svg";
export { ReactComponent as save } from "assets/icons/save.svg";
export { ReactComponent as create } from "assets/icons/create.svg";
export { ReactComponent as error } from "assets/icons/error.svg";
export { ReactComponent as success } from "assets/icons/success.svg";
export { ReactComponent as edit } from "assets/icons/edit.svg";
export { ReactComponent as copy } from "assets/icons/copy.svg";
export { ReactComponent as information } from "assets/icons/information.svg";
export { ReactComponent as search } from "assets/icons/search.svg";
export { ReactComponent as menu } from "assets/icons/menu.svg";
export { ReactComponent as backgroundToast } from "assets/icons/backgroundToast.svg";
export { ReactComponent as warning } from "assets/icons/warning.svg";
export { ReactComponent as publication } from "assets/icons/publication.svg";
export { ReactComponent as post } from "assets/icons/post.svg";
export { ReactComponent as done } from "assets/icons/done.svg";
export { ReactComponent as wait } from "assets/icons/wait.svg";
export { ReactComponent as reset } from "assets/icons/reset.svg";
export { ReactComponent as arrow } from "assets/icons/arrow.svg";
export { ReactComponent as system } from "assets/icons/system.svg";
export { ReactComponent as xml } from "assets/icons/xml.svg";
export { ReactComponent as unavailable } from "assets/icons/unavailable.svg";
export { ReactComponent as sync } from "assets/icons/sync.svg";
export { ReactComponent as filter } from "assets/icons/filter.svg";
export { ReactComponent as pencil } from "assets/icons/pencil.svg";
export { ReactComponent as upload } from "assets/icons/upload.svg";
export { ReactComponent as groups } from "assets/icons/groups.svg";
export { ReactComponent as role } from "assets/icons/role.svg";
export { ReactComponent as plus } from "assets/icons/plus.svg";
export { ReactComponent as minus } from "assets/icons/minus.svg";
export { ReactComponent as triangle } from "assets/icons/triangle.svg";
export { ReactComponent as goToLink } from "assets/icons/goToLink.svg";
export { ReactComponent as logoWhite } from "assets/icons/logoWhite.svg";
export { ReactComponent as add } from "assets/icons/add.svg";
export { ReactComponent as help } from "assets/icons/help.svg";
export { ReactComponent as viewPassword } from "assets/icons/viewPassword.svg";
export { ReactComponent as notViewPassword } from "assets/icons/notViewPassword.svg";
export { ReactComponent as excel } from "assets/icons/excel.svg";
export { ReactComponent as csv } from "assets/icons/csv.svg";
export { ReactComponent as notNotification } from "assets/icons/not-notification.svg";
export { ReactComponent as notification } from "assets/icons/notification.svg";
export { ReactComponent as selectAll } from "assets/icons/selectAll.svg";
export { ReactComponent as unSelectAll } from "assets/icons/unSelectAll.svg";
export { ReactComponent as types } from "assets/icons/types.svg";
export { ReactComponent as back } from "assets/icons/back.svg";
export { ReactComponent as list } from "assets/icons/list.svg";
export { ReactComponent as openAll } from "assets/icons/openAll.svg";
export { ReactComponent as tableSave } from "assets/icons/tableSave.svg";
export { ReactComponent as tableReset } from "assets/icons/tableReset.svg";
export { ReactComponent as arrowSorting } from "assets/icons/arrowSorting.svg";
export { ReactComponent as view } from "assets/icons/view.svg";

export { ReactComponent as gou } from "assets/types/gou.svg";
export { ReactComponent as rge } from "assets/types/rge.svg";
export { ReactComponent as ego } from "assets/types/ego.svg";
export { ReactComponent as vetv } from "assets/types/vetv.svg";
export { ReactComponent as sech } from "assets/types/sech.svg";
export { ReactComponent as consumption } from "assets/types/consumption.svg";
export { ReactComponent as ter } from "assets/types/ter.svg";
export { ReactComponent as electric } from "assets/types/electric.svg";
export { ReactComponent as integral } from "assets/types/integral.svg";
export { ReactComponent as dist } from "assets/types/dist.svg";
export { ReactComponent as ng } from "assets/types/ng.svg";
export { ReactComponent as gen } from "assets/types/gen.svg";
export { ReactComponent as sumConsumption } from "assets/types/sumConsumption.svg";
export { ReactComponent as saldo } from "assets/types/saldo.svg";
export { ReactComponent as ps } from "assets/types/ps.svg";
export { ReactComponent as adderPotr } from "assets/types/adder_potr.svg";
