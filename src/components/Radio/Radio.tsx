import React from "react";
import styled from "styled-components";

const Label = styled.label`
  padding: 6px;
  border-radius: 50px;
  display: inline-flex;
  cursor: pointer;
  transition: background 0.2s ease;
  margin: 4px 0; //8 0
  -webkit-tap-highlight-color: transparent;
`;

const Input = styled.input`
  vertical-align: middle;
  width: 14px;
  height: 14px;
  border-radius: 10px;
  background: none;
  border: 0;
  box-shadow: inset 0 0 0 1.5px #9f9f9f;
  appearance: none;
  padding: 0;
  margin: 0;
  transition: box-shadow 150ms cubic-bezier(0.95, 0.15, 0.5, 1.25);
  pointer-events: none;

  &:focus {
    outline: none;
  }
  &:checked {
    box-shadow: inset 0 0 0 4px ${(p) => p.theme.primaryColor};
  }
`;

export const Radio = ({ ...rest }) => {
  return (
    <Label>
      <Input type="radio" {...rest} />
    </Label>
  );
};
