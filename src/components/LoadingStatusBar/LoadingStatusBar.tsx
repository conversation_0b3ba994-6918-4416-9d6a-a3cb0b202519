import React from "react";
import styled, { css } from "styled-components";

export const Box = styled.div`
  //background: #fff;
  //width: 300px;
  //height: 400px;
  display: flex;
  //justify-content: center;
  //align-items: center;
  position: relative;
  flex-direction: column;
  //box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4);
  transition: transform 0.2s;
`;

export const Percent = styled.div`
  width: 40px;
  height: 40px;
  position: relative;
`;

export const Num = styled.div`
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  color: #111;
`;

export const Svg = styled.svg`
  //width: 150px;
  width: 40px;
  //height: 150px;
  height: 40px;
  position: relative;
`;

export const Circle = styled.circle<{ value?: number }>`
  //width: 150px;
  width: 40px;
  //height: 150px;
  height: 40px;
  fill: none;
  stroke-width: 4;
  stroke: #000;
  transform: translate(5px, 5px);
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
  stroke-linecap: round;

  &:nth-child(1) {
    stroke-dashoffset: 0;
    stroke: #f3f3f3;
  }
  &:nth-child(2) {
    stroke-dashoffset: calc(100 - (100 * ${(p) => p.value}) / 100);
    stroke: ${(p) => p.theme.blueActiveSupport};
  }
`;

export const H2 = styled.h2`
  font-size: 10px;
`;

export const Span = styled.span`
  font-size: 8px;
`;

export const LoadingStatusBar = ({ value }: { value: number }) => {
  return (
    <Box>
      <Percent>
        <Svg>
          <Circle cx="16" cy="16" r="16" />
          <Circle cx="16" cy="16" r="16" value={value} />
        </Svg>
        <Num>
          <H2>
            {value}
            <Span>%</Span>
          </H2>
        </Num>
      </Percent>
    </Box>
  );
};
