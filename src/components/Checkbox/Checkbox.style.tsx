import styled, { css } from "styled-components";
import { Icon } from "../Icon";

export const CheckIconStyled = styled(Icon)`
  color: ${(p) => p.theme.white};
`;

export const Container = styled.div<{ isChecked?: boolean; backgroundColor?: string; disabled?: boolean; cursor: string }>`
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: ${(p) => p.cursor};
  min-width: 16px;
  width: 16px;
  min-height: 16px;
  height: 16px;
  border-radius: 4px;
  border: solid 1px rgb(212, 217, 220);
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  z-index: 0;

  ${(props) =>
    props.isChecked &&
    css`
      border: solid 1px ${(theme) => theme?.theme?.primaryColor};
      background-color: ${(theme) => theme?.theme?.primaryColor};
    `};

  ${(props) =>
    props.disabled &&
    css`
      background-color: ${(p) => p.theme.gray};
      border: 1px solid #80808057;
    `};

  ${(props) =>
    props.disabled &&
    !props.isChecked &&
    css`
      background-color: #80808038;
      border: 1px solid #80808057;
    `};
`;

export const Wrapper = styled.div<{ cursor: string }>`
  display: flex;
  align-items: center;
  cursor: ${(p) => p.cursor};
`;

export const Label = styled.div`
  margin-left: 10px;
  user-select: none;
  color: ${(p) => p.theme.textColor};
`;
