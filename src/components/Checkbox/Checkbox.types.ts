export interface ListItemProps {
  value?: string | number;
  label?: string;
  checked?: boolean;
  status?: boolean;
  group?: string;
}

export interface CheckboxProps {
  status?: boolean;
  value?: string | number;
  disabled?: boolean;
  label?: string;
  title?: string;
  backgroundColor?: string;
  flagColor?: string;
  group?: string;
  className?: string;
  onChange?: (_: ListItemProps) => void;
  readonly?: boolean;
  cursor?: "pointer" | "default";
  dataTest?: string;
}
