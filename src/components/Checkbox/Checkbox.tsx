import { FC, MouseEvent } from "react";
import { CheckIconStyled, Container, Wrapper, Label } from "./Checkbox.style";
import { CheckboxProps } from "./Checkbox.types";

export const Checkbox: FC<CheckboxProps> = (props) => {
  const { status, value, disabled, label, title, flagColor = "#ffffff", group, className, onChange, cursor = "pointer", dataTest } = props;

  const handleCheckboxClick = (e: MouseEvent<HTMLDivElement>): void => {
    e.stopPropagation();
    if (onChange && !disabled) {
      onChange({ status: !status, value, group });
    }
  };

  const styleWrapper = { color: flagColor };

  return (
    <Wrapper
      title={title}
      onClick={(e: MouseEvent<HTMLDivElement>): void => handleCheckboxClick(e)}
      data-name="checkbox"
      data-status={!!status}
      className={className}
      style={styleWrapper}
      cursor={cursor}
      data-test={dataTest}
    >
      <Container cursor={cursor} onClick={(e: MouseEvent<HTMLDivElement>): void => handleCheckboxClick(e)} isChecked={status} disabled={disabled}>
        {status && <CheckIconStyled width={12} name="check" />}
      </Container>
      {label && <Label>{label}</Label>}
    </Wrapper>
  );
};
