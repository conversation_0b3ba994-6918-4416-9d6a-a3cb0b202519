import { ComponentStory, ComponentMeta } from "@storybook/react";

import { Spinner, SpinnerProps } from "./index";

export default {
  title: "Design System/Spinner",
  component: Spinner,
} as ComponentMeta<typeof Spinner>;

const Template: ComponentStory<typeof Spinner> = (args: SpinnerProps) => (
  <div style={{ width: "200px" }}>
    <Spinner {...args} />
  </div>
);

export const SpinnerComponent = Template.bind({});
SpinnerComponent.args = {
  size: 100,
};
