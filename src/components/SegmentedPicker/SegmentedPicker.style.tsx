import styled, { css } from "styled-components";

export const ActiveSegmented = styled.div<{ indexSelectedItems: number; count: number }>`
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  color: ${(p) => p.theme.textColor};
  z-index: 0;
  width: calc(${(p) => 100 / p.count}% - 5px);
  left: ${(p) => (p.indexSelectedItems * 100) / p.count}%;
  height: 20px; //15
  top: 2px;
  position: absolute;
  border-radius: 10px;
  transition: 0.3s;
  box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.12), 0px 3px 1px rgba(0, 0, 0, 0.04);
  border: 0.5px solid rgba(0, 0, 0, 0.04);

  ${(p) =>
    p.indexSelectedItems === 0 &&
    css`
      left: calc(${(p.indexSelectedItems * 100) / p.count}% + 5px) !important;
    `}
`;

export const SegmentedButton = styled.div<{ isSelected?: boolean }>`
  width: 100%;
  height: 24px; //28px
  border-radius: 6.93px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1;
  ${(p) =>
    p.isSelected &&
    css`
      background-color: ${(p) => p.theme.backgroundColorSecondary};
      color: ${(p) => p.theme.textColor};
      height: 20px;
      border-radius: 10px;
      transition: 0.3s;
    `}
`;

export const Container = styled.div`
  background-color: ${(p) => p.theme.lightGray};
  width: 343px;
  height: 24px; //20
  border-radius: 10px;
  display: flex;
  align-items: center;
  padding: 5px;
  font-size: 10px;
  font-weight: 600;
  user-select: none;
  position: relative;
  color: ${(p) => p.theme.textColor};
`;
