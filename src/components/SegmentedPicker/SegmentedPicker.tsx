import { FC } from "react";
import { ActiveSegmented, SegmentedButton, Container } from "./SegmentedPicker.style";
import { SegmentedPickerProps } from "./SegmentedPicker.types";

export const SegmentedPicker: FC<SegmentedPickerProps> = (props) => {
  const { items = [], selectedItems, onChange, className, dataTest } = props;
  const handleConfirmItem = (value: number, label: string): void => {
    if (onChange) {
      onChange({ value, label });
    }
  };
  const indexSelectedItems =
    items.length > 0 && selectedItems
      ? items
          .map((item, index) => {
            if (item.value === selectedItems) {
              return index;
            }
            return null;
          })
          .filter((item) => item !== null)[0]
      : 0;

  return (
    <Container className={className}>
      {items.map((item, index) => {
        return (
          <SegmentedButton isSelected={indexSelectedItems === index} key={`segmented-${index}`} onClick={() => handleConfirmItem(item.value, item.label)} data-test={dataTest}>
            {item.label}
          </SegmentedButton>
        );
      })}
      {/*<ActiveSegmented count={items.length === 0 ? 1 : items.length} indexSelectedItems={indexSelectedItems ? indexSelectedItems : 0} />*/}
    </Container>
  );
};
