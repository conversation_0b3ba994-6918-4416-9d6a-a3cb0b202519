import React, { <PERSON> } from "react";
import { Icon<PERSON>ontainer, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontaine<PERSON>, Left, Right } from "./Status.style";
import { Icon } from "components/Icon";
import { Loader } from "../Loader";
import { StatusProps } from "./Status.types";

export const Status: FC<StatusProps> = ({ value, isHiddenText = false, className }) => {
  if (value === "CONFIRMED" || value === "DONE") {
    return (
      <IconContainer status="CONFIRMED" className={className}>
        <Left>
          <Icon width={20} name="done" />
        </Left>
        <Right>{!isHiddenText && <IconLabel>Готово</IconLabel>}</Right>
      </IconContainer>
    );
  }
  if (value === "CREATED") {
    return (
      <IconContainer status="CREATED" className={className}>
        <Left>
          <LoaderContainer>
            <Loader spinnerSize={14} />
          </LoaderContainer>
        </Left>
        <Right>{!isHiddenText && <IconLabel>Идет загрузка</IconLabel>}</Right>
      </IconContainer>
    );
  }
  if (value === "FAILED") {
    return (
      <IconContainer status="FAILED" className={className}>
        <Left>
          <Icon width={20} name="error" />
        </Left>
        <Right>{!isHiddenText && <IconLabel>Ошибка</IconLabel>}</Right>
      </IconContainer>
    );
  }
  return (
    <IconContainer status="UNAVAILABLE" className={className}>
      <Left>
        <Icon width={16} name="unavailable" />
      </Left>
      <Right>{!isHiddenText && <IconLabel>Недоступен</IconLabel>}</Right>
    </IconContainer>
  );
};
