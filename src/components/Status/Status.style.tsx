import styled, { css } from "styled-components";

export const Left = styled.div`
  //border: solid 1px blue;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  padding-top: 3px;
`;

export const Right = styled.div`
  //border: solid 1px green;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 2px;
`;

export const IconContainer = styled.div<{ status?: string }>`
  width: 100%;
  //height: 100%;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  //border: solid 1px red;

  ${(p) =>
    p.status &&
    p.status === "FAILED" &&
    css`
      color: ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.status &&
    p.status === "CREATED" &&
    css`
      color: ${(p) => p.theme.blueActiveSupport};
    `}
  ${(p) =>
    p.status &&
    p.status === "CONFIRMED" &&
    css`
      color: ${(p) => p.theme.greenActiveSupport};
    `}
  ${(p) =>
    p.status &&
    p.status === "UNAVAILABLE" &&
    css`
      color: ${(p) => p.theme.gray};
    `}
`;

export const IconLabel = styled.div`
  display: flex;
  align-items: center;
  //margin-bottom: 5px;
  //margin-left: 10px;
`;

export const LoaderContainer = styled.div`
  width: 20px;
  height: 32px;
  margin-top: 8px;
`;
