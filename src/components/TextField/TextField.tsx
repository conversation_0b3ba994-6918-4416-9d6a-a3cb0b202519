import React, { useEffect } from "react";
import { Container, Label, LeftSection, RightSection, IconWrapper, Input, LengthContainer } from "./TextField.style";

import { CSSProperties, FC, useState, ChangeEvent } from "react";
import { Icon } from "components/Icon";
import { TextFieldProps } from "./TextField.types";
import { ErrorTooltip } from "components/Tooltip/ErrorTooltip";

export const TextField: FC<TextFieldProps> = (props): JSX.Element => {
  const {
    label,
    hint,
    value,
    type = "text",
    icon,
    style,
    name,
    className,
    placeholder,
    hasError,
    hasChanges,
    focus,
    readOnly,
    onBlur,
    onFocus,
    onChange,
    onKeyDown,
    errors,
    height,
    margin,
    maxLength,
    disabled,
    isChange,
    dataTest,
    ...rest
  } = props;

  const [isFocused, setIsFocused] = useState(false);
  const isMovedLabel = isFocused || !!value;
  const [hasErrors, setHasErrors] = useState(false);

  useEffect(() => {
    const tempErrors = errors ? errors.filter((el: any) => el !== null) : [];
    setHasErrors(tempErrors.length > 0);
  }, [errors]);

  const handleBlur = (e: ChangeEvent<HTMLInputElement>): void => {
    setIsFocused(false);
    if (onBlur && !disabled) {
      onBlur(e);
    }
  };

  const handleFocus = (e: ChangeEvent<HTMLInputElement>): void => {
    setIsFocused(true);
    if (onFocus && !disabled) {
      onFocus(e);
    }
  };

  const lengthValue = maxLength ? String(value)?.split("")?.length : 0;

  const [typePassword, setTypePassword] = useState(type);

  const changeType = (): void => {
    if (typePassword === "password") {
      setTypePassword("text");
    }
    if (typePassword === "text") {
      setTypePassword("password");
    }
  };

  return (
    <Container focused={isFocused && !readOnly} errors={hasErrors} className={className} disabled={disabled} isChange={isChange}>
      {icon && (
        <LeftSection>
          <IconWrapper focused={isFocused && !readOnly}>{icon && <Icon name={icon} width={16} />}</IconWrapper>
        </LeftSection>
      )}
      <RightSection>
        <Input
          name={name}
          // type={type === "text" ? "text" : typePassword}
          type={type}
          value={value}
          readOnly={readOnly}
          onKeyDown={(e: any) => {
            if (!disabled) {
              onKeyDown && onKeyDown(e);
            }
          }}
          onChange={(e: ChangeEvent<HTMLInputElement>) => {
            if (!disabled) {
              onChange && onChange(e);
            }
          }}
          onBlur={handleBlur}
          onFocus={handleFocus}
          autoFocus={focus}
          // placeholder={hint ? hint : placeholder}
          placeholder={placeholder}
          errors={hasErrors}
          disabled={disabled}
          isChange={isChange}
          data-test={dataTest}
          {...rest}
        />
        {label && <Label moved={isMovedLabel}>{label}</Label>}
      </RightSection>
      {maxLength && (
        <LengthContainer isErrors={lengthValue > maxLength} isChange={isChange}>
          {lengthValue} / {maxLength}
        </LengthContainer>
      )}
      {/*{type === "password" && (*/}
      {/*  <ViewPassword onClick={(): void => changeType()}>*/}
      {/*    <Icon width={17} name={typePassword === "text" ? namesIcons.notViewPassword : namesIcons.viewPassword} />*/}
      {/*  </ViewPassword>*/}
      {/*)}*/}

      {hasErrors && <ErrorTooltip errors={errors} />}
    </Container>
  );
};
