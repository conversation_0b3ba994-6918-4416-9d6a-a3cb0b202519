import styled, { css } from "styled-components";

export const ViewPassword = styled.div`
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 20px;
  color: #777777;
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.3s;

  &:hover {
    opacity: 1;
  }
`;

export const Container = styled.div<{
  readOnly?: boolean;
  focused?: boolean;
  changes?: boolean;
  errors?: boolean;
  height?: string | number;
  margin?: string;
  disabled?: boolean;
  isChange?: boolean;
}>`
  border-radius: 5px;
  position: relative;
  width: 100%;
  height: 24px; //40px
  font-size: 14px;
  background: ${(p) => p.theme.backgroundColorSecondary};
  outline: none;
  display: flex;
  align-items: center;
  border: 1px solid ${(p) => (p.focused ? p.theme.primaryColor : p.theme.lightGray)};
  ${(p) =>
    p.errors &&
    css`
      border: solid 1px ${(p) => p.theme.redActiveSupport} !important;
      background: ${(p) => p.theme.redLightSupport} !important;
    `}
  ${(p) =>
    p.disabled &&
    css`
      background-color: ${(p) => p.theme.inputDisabledBackground};
      cursor: no-drop;
    `}
  ${(p) =>
    p.isChange &&
    css`
      border: solid 1px ${(p) => p.theme.greenActiveSupport};
      //background: ${(p) => p.theme.greenLightSupport};
    `}
`;

export const Label = styled.div<{ moved: boolean }>`
  position: absolute;
  pointer-events: none;
  transition: all 200ms;
  display: flex;
  align-items: center;
  color: ${(p) => p.theme.textColor};

  ${(p) =>
    p.moved &&
    css`
      top: 0;
      transform: translate3d(-10px, -100%, 0px);
    `}
`;
export const Placeholder = styled.div<{ moved: boolean; errors: boolean }>`
  pointer-events: none;
  color: ${(p) => p.theme.textColor};
  transition: all 200ms;
  display: flex;
  align-items: center;
  user-select: none;
  position: absolute;
  top: 10px;

  ${(p) =>
    p.moved &&
    css`
      height: 30px;
      top: -15px;
      background: ${(p) => p.theme.backgroundColorSecondary};
      padding: 0 2px;
      bottom: 0;
      color: ${(p) => p.theme.primaryColor};
    `}

  ${(p) =>
    p.errors &&
    css`
      color: ${(p) => p.theme.redActiveSupport};
    `};
`;

export const LeftSection = styled.div<{ focused?: boolean }>`
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 16px;
  ${(p) =>
    p.focused &&
    css`
      color: ${(p) => p.theme.primaryColor};
    `}
`;
export const RightSection = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  position: relative;
  padding: 0 10px;
`;
export const IconWrapper = styled.div<{ focused: boolean }>`
  width: 16px;
  height: 16px;
  color: #777777;
  overflow: hidden;
  opacity: ${(p) => (p.focused ? "1" : "0.5")};
  ${(p) =>
    p.focused &&
    css`
      color: ${(p) => p.theme.primaryColor};
    `}
`;
export const Input = styled.input<{ errors?: boolean; readOnly?: boolean; isChange?: boolean }>`
  width: 100%;
  height: 100%;
  outline: none;
  border: none;
  //font-size: 14px; //16
  font-size: 0.9rem; //16
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  color: ${(p) => p.theme.textColor};

  ${(p) =>
    p.disabled &&
    css`
      background-color: ${(p) => p.theme.inputDisabledBackground};
      color: ${(p) => p.theme.inputDisabledText};
      cursor: no-drop;
    `}

  ${(p) =>
    p.errors &&
    css`
      color: ${(p) => p.theme.redActiveSupport} !important;
      background-color: ${(p) => p.theme.redLightSupport} !important;
    `};
  ${(p) =>
    p.isChange &&
    css`
      color: ${(p) => p.theme.greenActiveSupport};
      //background-color: ${(p) => p.theme.greenLightSupport};
    `};

  ::-webkit-input-placeholder {
    color: ${(p) => p.theme.gray};
  }
`;

//${(p) =>
//   p.readOnly &&
//   css`
//     cursor: no-drop;
//     background-color: #80808012;
//   `};
//
// &:read-only {
//   color: #808080;
//   background-color: #f6f6f6;
// }

export const LengthContainer = styled.div<{ isErrors: boolean; isChange?: boolean }>`
  color: gray;
  font-size: 10px;
  //padding: 0 10px;
  padding-right: 20px;
  //padding-right: 2px;

  ${(p) =>
    p.isErrors &&
    css`
      color: ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.isChange &&
    css`
      color: ${(p) => p.theme.greenActiveSupport};
    `};
`;
