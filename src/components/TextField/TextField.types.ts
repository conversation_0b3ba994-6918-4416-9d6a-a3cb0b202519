import { ChangeEvent, CSSProperties } from "react";
import { IconNameProps } from "../Icon/Icon.type";

export interface TextFieldProps {
  label?: string;
  hint?: string;
  value?: string;
  type?: string;
  icon?: IconNameProps;
  style?: CSSProperties;
  name?: string;
  className?: string;
  placeholder?: string;
  hasError?: boolean;
  hasChanges?: boolean;
  focus?: boolean;
  readOnly?: boolean;
  onBlur?: (event: ChangeEvent<HTMLInputElement>) => void;
  onFocus?: (event: ChangeEvent<HTMLInputElement>) => void;
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
  onKeyDown?: (event: ChangeEvent<HTMLInputElement>) => void;
  errors?: any;
  height?: number | string;
  margin?: string;
  maxLength?: number;
  disabled?: boolean;
  isChange?: boolean;
  dataTest?: string;
}
