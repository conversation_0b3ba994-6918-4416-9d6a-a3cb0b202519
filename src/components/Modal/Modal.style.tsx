import styled, { css } from "styled-components";
import { Icon } from "components/Icon";
import { Button } from "components/Button";

export const CloseIcon = styled(Icon)`
  //position: absolute;
  //top: 12px;
  //right: 16px;
  cursor: pointer;
  opacity: 0.6;
  transition: all 0.3s;

  &:hover {
    opacity: 1;
  }
`;

export const Container = styled.div<{ isOverLay?: boolean }>`
  //position: fixed;
  //top: 0;
  //right: 0;
  //left: 0;
  //bottom: 0;
  //z-index: 0; //
  //display: flex;
  //align-items: center;
  //justify-content: center;

  ${(p) =>
    p.isOverLay &&
    css`
      background-color: #0000001f;
    `}
`;

export const ModalContainer = styled.div<{ isLoadContainer?: boolean; x?: any; y?: any; width?: any; height?: any; scrollableContent?: boolean }>`
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  //width: 520px;
  //height: 212px;
  //box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.1);
  //box-shadow: 0 3px 7px rgba(0, 0, 0, 0.3);
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  border-radius: 12px;
  user-select: none;
  //position: relative;
  position: fixed;
  z-index: 1000;
  resize: block;

  &::after {
    content: "";
    color: blue;
  }

  ${(p) =>
    p.width &&
    css`
      width: ${p.width}px;
    `}
  ${(p) =>
    p.height &&
    css`
      height: ${p.height}px;
    `}
  ${(p) =>
    p.isLoadContainer &&
    css`
      display: flex;
      align-items: center;
      justify-content: center;
    `}
  ${(p) =>
    p.scrollableContent &&
    css`
      display: flex;
      flex-direction: column;
    `}
`;

export const Body = styled.div<{ scrollableContent?: boolean }>`
  padding: 0 40px;
  position: relative;

  ${(p) =>
    p.scrollableContent &&
    css`
      flex-grow: 1;
      overflow: hidden;
      min-height: 0;
      padding-bottom: 20px;
    `}
`;

export const Title = styled.div`
  font-weight: 700;
  font-size: 14px;
  line-height: 14px;
  display: flex;
  align-items: center;
`;

export const Description = styled.div`
  font-size: 12px;
  line-height: 12px;
  margin-top: 4px;
`;

export const CancelButton = styled(Button)<{ colorScheme?: string; width?: number }>`
  max-width: 160px;
  max-height: 24px;
  //margin-right: 24px;

  ${(p) =>
    p.colorScheme === "red" &&
    css`
      color: ${(props) => props.theme.red};
      &:hover {
        color: ${(props) => props.theme.redHover};
      }
      &:active {
        color: ${(props) => props.theme.redActive};
      }
    `}
  ${(p) =>
    p.width &&
    css`
      max-width: ${p.width}px;
    `}
`;

export const ConfirmButton = styled(Button)<{ colorScheme?: string; width?: number }>`
  max-width: 160px;
  max-height: 24px;
  margin-right: 10px;

  ${(p) =>
    p.colorScheme === "red" &&
    css`
      background-color: ${(props) => props.theme.red};
      &:hover {
        background-color: ${(props) => props.theme.redHover};
      }
      &:active {
        background-color: ${(props) => props.theme.redActive};
      }
    `}
  ${(p) =>
    p.width &&
    css`
      max-width: ${p.width}px;
    `}
`;

export const HintContainer = styled.div<{ colorScheme?: string }>`
  color: ${(p) => p.theme.primaryColor};
  ${(p) =>
    p.colorScheme === "red" &&
    css`
      color: ${(props) => props.theme.red};
    `}
`;

export const IconContainer = styled.div<{ colorScheme?: string }>`
  width: 40px;
  height: 40px;
  background-color: ${(p) => p.theme.backgroundColor};
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  ${(p) =>
    p.colorScheme === "red" &&
    css`
      color: ${(p) => p.theme.redBackground};
    `}
`;

export const TrashIcon = styled(Icon)<{ colorScheme?: string }>`
  color: ${(p) => p.theme.primaryColor};

  ${(p) =>
    p.colorScheme === "red" &&
    css`
      color: ${(p) => p.theme.red};
    `}
`;
