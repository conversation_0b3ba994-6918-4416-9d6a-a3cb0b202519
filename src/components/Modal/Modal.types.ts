import { IconNameProps } from "../Icon/Icon.type";
import { ReactFragment } from "react";

export interface ModalProps {
  isOverLay?: boolean;
  children?: any;
  cancelText?: string;
  confirmText?: string;
  title?: string;
  description?: string;
  hint?: string;
  colorScheme?: "default" | "red";
  icon?: IconNameProps;
  onCancel?: () => void;
  onConfirm?: () => void;
  className?: string;
  isDisabledConfirm?: boolean;
  isDisabledCancel?: boolean;
  isLoading?: boolean;
  styleBody?: any;
  widthButtons?: any;
  width?: any;
  height?: any;
  setModalHeight?: any;
  scrollableContent?: boolean;
  dataTestContainer?: string;
  dataTestConfirmButton?: string;
  hideCloseIcon?: boolean; // Скрывает иконку закрытия модалки
}
