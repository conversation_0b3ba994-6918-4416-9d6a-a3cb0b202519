import { isNull, isUndefined } from "lodash";
import { FC } from "react";

import { CalendarWeeksWrapper, CalendarContainer, Cell, Row } from "./MonthCalendar.style";
import { getDaysMatrix } from "./utils/getDaysMatrix";
import { CalendarDay } from "./components/CalendarDay";
import { DateRangePickerWeeks } from "components/FilterDatePicker/components/DateRangePickerWeeks";
import { MonthCalendarProps } from "./MonthCalendar.types";
import { DEFAULT_DAY, DEFAULT_MONTH, DEFAULT_YEAR } from "../../helpers/DateUtils";

const checkDay = ({ day, prevDisabled }: { day: any; prevDisabled: any }) => {
  return prevDisabled ? day <= DEFAULT_DAY : false;
};

const checkMonth = ({ month, day, prevDisabled }: { month: any; day: any; prevDisabled: any }) => {
  if (month + 1 === DEFAULT_MONTH) {
    return checkDay({ day, prevDisabled });
  }
  if (month + 1 < DEFAULT_MONTH) {
    return true;
  }
  if (month + 1 > DEFAULT_MONTH) {
    return false;
  }
};

const checkYear = ({ year, month, day, prevDisabled }: { year: any; month: any; day: any; prevDisabled: any }) => {
  if (year === DEFAULT_YEAR) {
    return checkMonth({ month, day, prevDisabled });
  }
  if (year < DEFAULT_YEAR) {
    return true;
  }
  if (year > DEFAULT_YEAR) {
    return false;
  }
};

// eslint-disable-next-line react/display-name
export const MonthCalendar: FC<MonthCalendarProps> = (props) => {
  const { year, month, selectedDay, size = "medium", onChange, renderDay, prevDisabled, loadDay } = props;
  const daysMatrix = getDaysMatrix(year, month);

  const handleDayClick = (day: number | string): void => {
    if (onChange) {
      onChange(day);
    }
  };

  return (
    <CalendarContainer>
      <CalendarWeeksWrapper>
        <DateRangePickerWeeks />
      </CalendarWeeksWrapper>
      {daysMatrix.map((week: any, weekIndex: number) => (
        <Row key={weekIndex}>
          {week.map((day: number | null, dayIndex: number) => {
            if (isNull(day)) {
              return <Cell key={dayIndex} size={size} />;
            }
            const isActive = day === Number(selectedDay);

            const isLoadDay = loadDay?.some((el) => Number(el) === Number(day));

            return (
              <Cell key={dayIndex} size={size}>
                <CalendarDay
                  size={size}
                  isActive={isActive}
                  prevDisabled={prevDisabled}
                  day={day}
                  month={month}
                  year={year}
                  isLoadDay={isLoadDay}
                  onClick={
                    !isUndefined(onChange)
                      ? () => {
                          if (prevDisabled) {
                            if (!checkYear({ year, month, day, prevDisabled })) {
                              handleDayClick(day);
                            }
                          } else {
                            handleDayClick(day);
                          }
                        }
                      : undefined
                  }
                  render={renderDay}
                >
                  <>{day}</>
                </CalendarDay>
              </Cell>
            );
          })}
        </Row>
      ))}
    </CalendarContainer>
  );
};
