import styled, { css } from "styled-components";

enum DAY_SIZE {
  small = 18,
  medium = 36, //23
}

enum DAY_FONT_SIZE {
  small = 11,
  medium = 12, //14
}

export const Day = styled.div<{ size?: any; isActive?: boolean; isView?: boolean; hasError?: boolean; isDisabled?: boolean; isLoadDay?: boolean }>`
  font-weight: normal;
  display: flex;
  justify-content: center;
  align-items: center;
  color: ${(p) => p.theme.textColor};
  cursor: ${(p) => (p.onClick ? "pointer" : "default")};
  user-select: none;
  width: ${(p) => DAY_SIZE[p.size]}px;
  height: ${(p) => DAY_SIZE[p.size]}px;
  font-size: ${(p) => DAY_FONT_SIZE[p.size]}px;
  flex-shrink: 0;
  // border: solid 1px ${(p) => p.theme.lightGray};
  //border-radius: 3px;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  border-radius: 50%;

  ${(props) =>
    props.isActive &&
    !props.isView &&
    css`
      color: #f3f9ff;
      background-color: ${(p) => p.theme.primaryColor};
      border: solid 1px ${(p) => p.theme.lightGray};
    `};

  &:hover {
    border: ${(p) => (p.onClick ? "solid 1px #daecff" : "none")};
    background-color: ${(p) => (p.onClick ? "#f3f9ff" : "none")};
    color: ${(p) => (p.onClick ? p.theme.primaryColor : "none")};
    cursor: pointer;
  }

  ${(p) =>
    p.hasError &&
    css`
      background-color: #ff2020;

      &:hover {
        border: solid 1px #ffdada;
        background-color: #fff3f3;
        color: #ff2020;
      }
    `}

  ${(props) =>
    props.isDisabled &&
    css`
      color: gray;
      background-color: #80808012;
      &:hover {
        border: solid 1px #80808012;
        background-color: #80808012;
        color: gray;
        cursor: no-drop;
      }
    `};

  ${(p) =>
    p.isLoadDay &&
    css`
      background-color: ${(p) => p.theme.greenLightSupport}; //greenActiveSupport
      color: ${(p) => p.theme.greenActiveSupport}; //white
      &:hover {
        background-color: ${(p) => p.theme.greenLightSupport}; //greenActiveSupport
        color: ${(p) => p.theme.greenActiveSupport}; //white
      }
    `}
  ${(p) =>
    p.isLoadDay &&
    p.isActive &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport}; //greenActiveSupport
      color: ${(p) => p.theme.white}; //white
      &:hover {
        background-color: ${(p) => p.theme.greenActiveSupport}; //greenActiveSupport
        color: ${(p) => p.theme.white}; //white
      }
    `}
`;

export const Cell = styled.div<{ isWeek?: boolean; isCurrentMonth?: boolean; isSelectedDay?: boolean }>`
  width: 100%;
  height: 26px;
  border-radius: 2px;
  display: flex;
  justify-content: center;
  align-items: center;

  color: ${(props) => (props.isWeek ? "#F8969B" : "#6e7478")};

  ${(props) =>
    props.isCurrentMonth &&
    css`
      &:hover {
        border: solid 1px #daecff;
        background-color: #f3f9ff;
        color: #208cff;
        cursor: pointer;
      }
    `};

  ${(props) =>
    props.isSelectedDay &&
    css`
      border: solid 1px #daecff;
      background-color: #f3f9ff;
      color: #208cff;
    `};
`;
