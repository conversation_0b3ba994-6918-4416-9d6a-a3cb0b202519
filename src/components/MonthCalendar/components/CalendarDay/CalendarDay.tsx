import { DEFAULT_DAY, DEFAULT_MONTH, DEFAULT_YEAR } from "helpers/DateUtils";
import { FC } from "react";

import { Day } from "./CalendarDay.style";

interface CalendarDayProps {
  children?: string | JSX.Element;
  size?: string | number;
  isActive?: boolean;
  hasError?: boolean;
  render?: (_: any) => JSX.Element;
  onClick?: (_: any) => void;
  prevDisabled?: boolean;
  day?: any;
  month?: any;
  year?: any;
  isLoadDay?: boolean;
}

export const CalendarDay: FC<CalendarDayProps> = (props) => {
  const { children, size, isActive, hasError, render, onClick, prevDisabled, day, month, year, isLoadDay } = props;

  const checkDay = () => {
    return prevDisabled ? day <= DEFAULT_DAY : false;
  };

  const checkMonth = () => {
    if (month + 1 === DEFAULT_MONTH) {
      return checkDay();
    }
    if (month + 1 < DEFAULT_MONTH) {
      return true;
    }
    if (month + 1 > DEFAULT_MONTH) {
      return false;
    }
  };

  const checkYear = () => {
    if (year === DEFAULT_YEAR) {
      return checkMonth();
    }
    if (year < DEFAULT_YEAR) {
      return true;
    }
    if (year > DEFAULT_YEAR) {
      return false;
    }
  };

  // const isDisabled = isLoadDay && isActive; // isDisabled={prevDisabled && checkYear()}

  return render ? (
    render(props)
  ) : (
    <Day size={size} isActive={isActive} hasError={hasError} isDisabled={prevDisabled && checkYear()} onClick={onClick} isLoadDay={isLoadDay} data-test="calendar.day">
      {children}
    </Day>
  );
};
