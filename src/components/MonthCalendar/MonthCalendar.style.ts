import styled, { css } from "styled-components";

export const CalendarContainer = styled.div`
  width: 100%;
  height: 100%;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
`;

export const CalendarWeeksWrapper = styled.div`
  margin-bottom: 5px;
`;

export const Row = styled.div`
  flex-direction: row;
  display: flex;
  justify-content: space-around;
  margin: 12px 0;
`;

export const Day = styled.div<{ isActive?: boolean; hasError?: boolean; isDisabled?: boolean }>`
  font-weight: normal;
  display: flex;
  justify-content: center;
  align-items: center;
  color: ${(p) => p.theme.textColor};
  cursor: pointer;
  user-select: none;
  width: 23px;
  height: 23px;
  border: solid 1px ${(p) => p.theme.backgroundColorSecondary};
  border-radius: 3px;

  &:hover {
    border: solid 1px ${(p) => p.theme.backgroundColorSecondary};
    background-color: ${(p) => p.theme.backgroundColorSecondary};
    color: ${(p) => p.theme.primaryColor};
  }

  ${(props) =>
    props.isActive &&
    css`
      color: ${(p) => p.theme.textColor};
      background-color: ${(p) => p.theme.primaryColor};
    `};

  ${(p) =>
    p.hasError &&
    css`
      background-color: red;
    `}

  ${(props) =>
    props.isDisabled &&
    css`
      color: gray;
      background-color: #80808012;
      &:hover {
        border: solid 1px #80808012;
        background-color: #80808012;
        color: gray;
        cursor: no-drop;
      }
    `};
`;

enum CELL_SIZE_HEIGHT {
  small = 19,
  medium = 26,
}

export const Cell = styled.div<{ size: any; isWeek?: boolean; isCurrentMonth?: boolean; isSelectedDay?: boolean }>`
  width: 100%;
  height: ${(p) => (p.size ? CELL_SIZE_HEIGHT[p.size] : 100)}px;
  border-radius: 2px;
  display: flex;
  justify-content: center;
  align-items: center;

  color: ${(props) => (props.isWeek ? "#F8969B" : props.theme.textColor)};

  ${(props) =>
    props.isCurrentMonth &&
    css`
      &:hover {
        border: solid 1px ${(p) => p.theme.backgroundColorSecondary};
        background-color: ${(p) => p.theme.backgroundColorSecondary};
        color: ${(p) => p.theme.primaryColor};
        cursor: pointer;
      }
    `};

  ${(props) =>
    props.isSelectedDay &&
    css`
      border: solid 1px ${(p) => p.theme.backgroundColorSecondary};
      background-color: ${(p) => p.theme.backgroundColorSecondary};
      color: ${(p) => p.theme.primaryColor};
    `};
`;
