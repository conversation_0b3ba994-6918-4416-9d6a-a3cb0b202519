import { getNumberOfWeek, generateNullMatrix } from "helpers/DateUtils";

export function getDaysMatrix(year: any, month: any) {
  const date = new Date(year, month, 1);
  const numberOfWeek = getNumberOfWeek(year, month);
  const theFirstDate = new Date(year, month, 1).getDay();

  let matrixOfDays: any = [];

  if (theFirstDate === 0) {
    matrixOfDays = generateNullMatrix(7, numberOfWeek);
    matrixOfDays[0][6] = 1;
    let dayIndex = 2;
    const maxDay = new Date(year, month + 1, 0).getDate();
    for (let i = 1; i < numberOfWeek; i++) {
      for (let day = 0; day < 7; day++) {
        if (dayIndex > maxDay) {
          matrixOfDays[i][day] = null;
        } else {
          matrixOfDays[i][day] = dayIndex;
          dayIndex++;
        }
      }
    }
  } else {
    matrixOfDays = generateNullMatrix(7, numberOfWeek).map((week) =>
      week.map((_: any, index: number) => {
        if (index + 1 >= date.getDay() && date.getMonth() === month) {
          const currentDay = date.getDate();
          date.setDate(date.getDate() + 1);
          return currentDay;
        }

        return null;
      })
    );
  }

  return matrixOfDays;
}
