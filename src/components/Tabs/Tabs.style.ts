import styled, { css } from "styled-components";

export const TabsContainer = styled.div<{ isTopMenu?: boolean }>`
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  color: ${(p) => p.theme.textColor};
  user-select: none;
  ${(p) =>
    p.isTopMenu &&
    css`
      color: ${(p) => p.theme.topMenuTextColor};
    `}
`;

export const CountContainer = styled.div<{ color?: "default" | "red" | "green" | "purple" }>`
  position: absolute;
  right: -7px;
  top: 0;
  font-size: 10px;
  color: ${(p) => p.theme.white};
  padding: 4px;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${(p) => p.theme.blueActiveSupport};
  ${(p) =>
    p.color === "default" &&
    css`
      background-color: ${(p) => p.theme.blueActiveSupport};
    `}
  ${(p) =>
    p.color === "green" &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport};
    `}
  ${(p) =>
    p.color === "red" &&
    css`
      background-color: ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.color === "purple" &&
    css`
      background-color: ${(p) => p.theme.purpleActiveSupport};
    `}
`;

export const Tab = styled.div<{ isActive?: boolean; type?: string; colorScheme?: "green" | "red" | "purple" | "default"; isTopMenu?: boolean; isUserSelected?: boolean }>`
  margin: 0 20px;
  cursor: pointer;
  border-bottom: solid 2px transparent;
  height: 100%;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  opacity: 0.7;
  position: relative;
  text-align: center;

  &:hover {
    border-bottom: solid 2px ${(p) => p.theme.primaryColor};
    opacity: 1;
  }

  ${(p) =>
    p.isActive &&
    css`
      border-bottom: solid 2px ${(p) => p.theme.primaryColor};
      color: ${(p) => p.theme.primaryColor};
      opacity: 1;
    `}

  ${(p) =>
    p.colorScheme === "default" &&
    css`
      color: ${(p) => p.theme.primaryColor};
      &:hover {
        border-bottom: solid 2px ${(p) => p.theme.primaryColor};
      }
    `}

  ${(p) =>
    p.isActive &&
    p.colorScheme === "default" &&
    css`
      border-bottom: solid 2px ${(p) => p.theme.primaryColor};
      color: ${(p) => p.theme.primaryColor};
      opacity: 1;
    `}

  ${(p) =>
    p.type === "secondary" &&
    css`
      padding: 0 20px;
      opacity: 1;
    `}
  
  ${(p) =>
    p.colorScheme === "green" &&
    css`
      color: ${(p) => p.theme.greenActiveSupport};
      &:hover {
        border-bottom: solid 2px ${(p) => p.theme.greenActiveSupport};
      }
    `}

  ${(p) =>
    p.isActive &&
    p.colorScheme === "green" &&
    css`
      border-bottom: solid 2px ${(p) => p.theme.greenActiveSupport};
      color: ${(p) => p.theme.greenActiveSupport};
      opacity: 1;
    `}
  
  ${(p) =>
    p.colorScheme === "red" &&
    css`
      color: ${(p) => p.theme.redActiveSupport};
      &:hover {
        border-bottom: solid 2px ${(p) => p.theme.redActiveSupport};
      }
    `}

  ${(p) =>
    p.isActive &&
    p.colorScheme === "red" &&
    css`
      border-bottom: solid 2px ${(p) => p.theme.redActiveSupport};
      color: ${(p) => p.theme.redActiveSupport};
      opacity: 1;
    `}
  
  ${(p) =>
    p.colorScheme === "purple" &&
    css`
      color: ${(p) => p.theme.purpleActiveSupport};
      &:hover {
        border-bottom: solid 2px ${(p) => p.theme.purpleActiveSupport};
      }
    `}

  ${(p) =>
    p.isActive &&
    p.colorScheme === "purple" &&
    css`
      border-bottom: solid 2px ${(p) => p.theme.purpleActiveSupport};
      color: ${(p) => p.theme.purpleActiveSupport};
      opacity: 1;
    `}
  ${(p) =>
    p.isTopMenu &&
    p.isActive &&
    css`
      border-bottom: solid 2px transparent;
      color: ${(p) => p.theme.topMenuActiveColor};
      opacity: 1 !important;
    `}
  ${(p) =>
    p.isTopMenu &&
    css`
      opacity: 0.6;
      color: ${(p) => p.theme.topMenuTextColor} !important;
      &:hover {
        border-bottom: solid 2px transparent;
      }
    `}
  ${(p) =>
    p.isUserSelected &&
    css`
      user-select: auto;
      text-decoration: none;
      color: ${(p) => p.theme.textColor};
      &:active {
        color: ${(p) => p.theme.textColor};
      }
      &:visited {
        color: ${(p) => p.theme.textColor};
      }
    `}
`;
