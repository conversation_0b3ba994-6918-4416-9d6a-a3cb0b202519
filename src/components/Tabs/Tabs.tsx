import React, { <PERSON> } from "react";
import { Tabs<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tab } from "./Tabs.style";

export const Tabs: FC<ITabs> = ({ tabs, onClick, selectedValue, type = "primary", isTopMenu = false, isUserSelected = false }) => {
  return (
    <TabsContainer isTopMenu={isTopMenu}>
      {tabs &&
        tabs.map(({ value, label, count, colorScheme }) => {
          return (
            <Tab
              onClick={(e: any) => {
                e.preventDefault();
                if (onClick) {
                  onClick({ value, label });
                }
              }}
              type={type}
              key={`tab-${value}`}
              isActive={value === selectedValue}
              colorScheme={colorScheme}
              isTopMenu={isTopMenu}
              as={isUserSelected ? "a" : "div"}
              href={String(value)}
              isUserSelected={isUserSelected}
              data-test={`tab-title.${value}`}
            >
              {(count || count === 0 || (count && count > 0)) && <CountContainer color={colorScheme}>{count}</CountContainer>}
              {label}
            </Tab>
          );
        })}
    </TabsContainer>
  );
};
