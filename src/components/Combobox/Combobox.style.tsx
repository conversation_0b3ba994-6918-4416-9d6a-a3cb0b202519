import styled, { css } from "styled-components";
import { LoaderContainer } from "./components/List/List.style";
import { Loader } from "../Loader";
import React from "react";
import { Icon } from "../Icon";

export const IconStyled = styled(Icon)<{ color?: string; disabled?: boolean }>`
  margin-top: 3px;
  ${(p) =>
    p.color &&
    css`
      color: ${(props) => (p.color === "default" ? props.theme.primaryColor : p.color)};
    `}
  ${(p) =>
    p.disabled &&
    css`
      color: ${(p) => p.theme.gray};
    `}
`;

export const getIcon = (name: any, color: any, disabled: any) => {
  if (name === "load") {
    return (
      <LoaderContainer>
        <Loader spinnerSize={16} />
      </LoaderContainer>
    );
  }
  if (name === "empty") {
    return <></>;
  }
  return <IconStyled name={name} width={20} color={color} disabled={disabled} />;
};

export const IconLabelContainer = styled.div`
  height: 100%;
`;

export const LabelContainer = styled.div<{ color?: any; icon?: boolean }>`
  ${(p) =>
    p.color &&
    css`
      color: ${p.color};
    `}
  ${(p) =>
    p.icon &&
    css`
      margin-top: 2px;
      margin-left: 4px;
    `}
`;

export const PlaceholderLabel = styled.div`
  margin-top: 3px;
`;

export const Placeholder = styled.div<{
  isEmpty?: boolean;
  disabled?: boolean;
  comboboxStyle?: string;
  width?: any;
  isTextColorPlaceHolder?: boolean;
  isNull?: boolean;
  isDefault?: boolean;
}>`
  //position: absolute;
  //top: -11px;
  //left: 10px;
  padding: 0 5px;
  white-space: nowrap;
  transition: all 0.3s;
  display: flex;
  ${(p) =>
    p.isEmpty &&
    css`
      top: 0; //8px
      background-color: transparent !important;
    `};
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  color: ${(p) => p.theme.textColor}
    ${(p) =>
      p.disabled &&
      css`
        background-color: ${(p) => p.theme.lightGray};
      `}
    ${(p) =>
      p.width &&
      css`
        width: ${p.width};
      `}
    ${(p) =>
      p.isTextColorPlaceHolder &&
      css`
        color: ${(p) => p.theme.textColor};
      `}
    ${(p) =>
      p.isNull &&
      css`
        padding: 0 2px;
      `}
    ${(p) =>
      p.comboboxStyle === "secondary" &&
      css`
        color: ${(p) => p.theme.primaryColor} !important;
      `};
`;

export const SelectedValueContainer = styled.div<{ comboboxStyle?: string }>`
  margin-left: 5px;
  display: flex;
  flex-direction: row;
  width: 100%;
  color: ${(p) => p.theme.textColor};
  ${(p) =>
    p.comboboxStyle === "secondary" &&
    css`
      color: ${(p) => p.theme.primaryColor};
    `}
`;

export const Container = styled.div<{ width?: number; disabled?: boolean; comboboxStyle?: string }>`
  border: ${(p) => (p.comboboxStyle === "default" ? `solid 1px ${p.theme.lightGray}` : `solid 1px ${p.theme.primaryColor}`)};
  width: 245px;
  border-radius: 4px;
  padding: 0 10px;
  position: relative;
  height: 24px; //28px
  display: flex;
  align-items: center;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  cursor: pointer;
  transition: all 0.3s;
  user-select: none;
  //z-index: 999;

  ${(p) =>
    p.width &&
    css`
      width: ${p.width}px;
    `}
  ${(p) =>
    p.disabled &&
    css`
      background-color: ${(p) => p.theme.lightGray};
      cursor: not-allowed;
    `}
`;

export const Arrow = styled.div<{ isOpen?: boolean; comboboxStyle?: string }>`
  transform: rotate(90deg);
  margin-left: auto;
  color: ${(p) => p.theme.gray};

  min-width: 20px;
  min-height: 20px;

  ${(p) =>
    p.isOpen &&
    css`
      transform: rotate(270deg);
    `}
  ${(p) =>
    p.comboboxStyle === "secondary" &&
    css`
      color: ${(p) => p.theme.primaryColor};
    `}
`;

export const IconContainer = styled.div<{ color: string }>`
  margin-right: 10px;
  color: ${(p) => (p.color === "default" ? p.theme.primaryColor : p.color)};
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 4px;
`;

export const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
`;
