import React, { FC, useRef, useState } from "react";
import {
  Placeholder,
  SelectedValueContainer,
  Container,
  Arrow,
  IconContainer,
  PlaceholderLabel,
  LabelContainer,
  getIcon,
  IconLabelContainer,
  LoadingContainer,
} from "./Combobox.style";
import { Icon } from "components/Icon";
import { List } from "./components/List";
import { useOnClickOutside } from "hooks/useOnClickOutside";
import { ComboboxProps } from "./Combobox.types";
import { ItemsProps } from "./components/List/List.types";
import { observer } from "mobx-react";
import { useStores } from "../../stores/useStore";
import { Loader } from "../Loader";

export const Combobox: FC<ComboboxProps> = observer((props) => {
  const { authStore } = useStores();
  const { comboboxStyle } = authStore;
  const {
    items,
    selectedValue = null,
    onChange,
    placeholder,
    className,
    width = 245,
    icon,
    iconColor = "default",
    position = "bottom",
    disabled = false,
    maxHeightList = 145,
    isTextColorPlaceHolder = false,
    dataTest,
    isLoading = false,
  } = props;
  const [isOpen, setIsOpen] = useState(false);

  const selectedItem: ItemsProps | undefined | null = selectedValue && items && items.length > 0 ? items.find(({ value }: any) => value === selectedValue) : null;

  const selectedLabel: string = selectedItem ? selectedItem?.label ?? "" : "";
  const selectedIcon: ItemsProps | undefined | null = selectedItem ? { icon: selectedItem.icon, color: selectedItem.color } : null;

  const refCombobox = useRef<HTMLDivElement>(null);

  useOnClickOutside(refCombobox, () => setIsOpen(false));

  const isNull = selectedLabel === "";

  return (
    <Container
      disabled={disabled || isLoading}
      className={className}
      ref={refCombobox}
      onClick={() => {
        if (!disabled && !isLoading) setIsOpen((prev) => !prev);
      }}
      width={width}
      comboboxStyle={comboboxStyle}
      data-test={dataTest}
    >
      {isLoading ? (
        <LoadingContainer>
          <Loader spinnerSize={16} />
        </LoadingContainer>
      ) : (
        <>
          {!selectedValue && (
            <Placeholder
              comboboxStyle={comboboxStyle}
              isEmpty={selectedValue === null}
              className={className}
              disabled={disabled}
              width={width}
              isTextColorPlaceHolder={isTextColorPlaceHolder}
              isNull={isNull}
            >
              {icon && (
                <IconContainer color={iconColor}>
                  <Icon name={icon} width={16} />
                </IconContainer>
              )}
              <PlaceholderLabel>{placeholder}</PlaceholderLabel>
            </Placeholder>
          )}
          <SelectedValueContainer comboboxStyle={comboboxStyle}>
            {selectedIcon && <IconLabelContainer>{getIcon(selectedIcon.icon, selectedIcon.color, disabled)}</IconLabelContainer>}
            {selectedValue && (
              <LabelContainer color={selectedItem?.color} icon={!!selectedIcon}>
                <>{selectedLabel}</>
              </LabelContainer>
            )}
            <Arrow isOpen={isOpen} comboboxStyle={comboboxStyle}>
              <Icon name="menuArrow" width={6} />
            </Arrow>
          </SelectedValueContainer>
          {isOpen && (
            <List
              items={items}
              onChange={onChange}
              width={width}
              position={position}
              selectedValue={selectedValue}
              maxHeightList={maxHeightList}
              comboboxStyle={comboboxStyle}
            />
          )}
        </>
      )}
    </Container>
  );
});
