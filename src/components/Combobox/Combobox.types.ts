import { IconNameProps } from "components/Icon/Icon.type";
import { ItemsProps } from "./components/List/List.types";

export interface ItemProps {
  icon?: IconNameProps;
  iconColor?: string;
}

export interface SelectedItemProps extends ItemProps {
  label: string;
}

export interface ComboboxProps {
  items?: any;
  selectedValue?: any;
  onChange?: (_: any) => void;
  placeholder?: string;
  width?: number;
  icon?: IconNameProps;
  iconColor?: string;
  className?: string;
  position?: string;
  disabled?: boolean;
  maxHeightList?: number;
  isTextColorPlaceHolder?: boolean;
  dataTest?: string;
  isLoading?: boolean;
}
