import styled, { css } from "styled-components";
import { Icon } from "components/Icon";

export const LoaderContainer = styled.div`
  width: 20px;
  height: 20px;
`;

export const Container = styled.div<{ width?: number; position?: string; maxHeightList?: number }>`
  position: absolute;
  width: 245px;
  min-height: 24px; //32px
  //max-height: 145px;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  top: 24px; //30
  left: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3), 0 2px 6px 2px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  overflow: auto; // Для появления скролла в случае, если элементы селектора не входят в размеры контейнера maxHeightList
  z-index: 1000;

  ${(p) =>
    p.width &&
    css`
      width: ${p.width}px !important;
    `}
  ${(p) =>
    p.position === "top" &&
    css`
      bottom: 18px; //30
      top: auto;
    `}
  ${(p) =>
    p.maxHeightList &&
    css`
      max-height: ${p.maxHeightList}px;
    `}
`;

export const Item = styled.div<{ disabled?: boolean; isSelected: boolean; isNull?: boolean; color?: any }>`
  width: 100%;
  height: 24px; //28
  //padding: 0 15px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  color: ${(p) => p.theme.textColor};
  border-bottom: solid 1px ${(p) => p.theme.lightGray};

  &:hover {
    background-color: ${(p) => p.theme.hover};
  }

  ${(p) =>
    p.disabled &&
    css`
      cursor: no-drop;
      background-color: ${(p) => p.theme.lightGray};
      &:hover {
        background-color: ${(p) => p.theme.lightGray};
      }
    `}

  ${(p) =>
    p.isSelected &&
    css`
      background-color: ${(p) => p.theme.hover};
    `}
  ${(p) =>
    p.isNull &&
    css`
      padding: 0 19px;
    `}
  ${(p) =>
    p.color &&
    css`
      color: ${p.color};
    `}
`;

export const Label = styled.div<{ comboboxStyle?: string }>`
  margin-left: 4px;
  color: ${(p) => p.theme.textColor};
  ${(p) =>
    p.comboboxStyle === "secondary" &&
    css`
      color: ${(p) => p.theme.primaryColor} !important;
    `};
`;

export const IconStyled = styled(Icon)<{ color?: string; disabled?: boolean }>`
  padding-top: 3px;
  ${(p) =>
    p.color &&
    css`
      color: ${(props) => (p.color === "default" ? props.theme.primaryColor : p.color)};
    `}
  ${(p) =>
    p.disabled &&
    css`
      color: ${(p) => p.theme.gray};
    `}
`;
