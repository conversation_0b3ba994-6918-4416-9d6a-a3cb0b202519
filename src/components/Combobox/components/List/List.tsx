import React, { FC } from "react";
import { Container, Item, Label, IconStyled, LoaderContainer } from "./List.style";
import { ListProps } from "./List.types";
import { Loader } from "../../../Loader";

const getIcon = (name: any, color: any, disabled: any) => {
  if (name === "load") {
    return (
      <LoaderContainer>
        <Loader spinnerSize={16} />
      </LoaderContainer>
    );
  }
  if (name === "empty") {
    return <></>;
  }
  return <IconStyled name={name} width={20} color={color} disabled={disabled} />;
};

export const List: FC<ListProps> = (props) => {
  const { items, onChange, width, position, selectedValue, maxHeightList, comboboxStyle } = props;
  const chooseItem = ({ value, label }: { value?: number | string; label?: string }) => {
    onChange && onChange({ value, label });
  };

  return (
    <Container width={width} position={position} maxHeightList={maxHeightList} data-test="list.container">
      {items &&
        items.map(({ value, label, icon, iconColor, disabled, color }, index) => {
          return (
            <Item
              key={`list-combobox-${index}`}
              onClick={() => {
                if (!disabled) {
                  chooseItem({ value, label });
                }
              }}
              disabled={disabled}
              isSelected={selectedValue === value}
              isNull={label === "0"}
              color={color}
            >
              {icon && getIcon(icon, color, disabled)}
              <Label comboboxStyle={comboboxStyle} data-test="list.item">{label}</Label>
            </Item>
          );
        })}
    </Container>
  );
};
