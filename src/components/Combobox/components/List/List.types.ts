import { IconNameProps } from "../../../Icon/Icon.type";

export interface ItemsProps {
  value?: number | string;
  label?: string;
  icon?: IconNameProps;
  iconColor?: string;
  disabled?: boolean;
  color?: any;
}

export interface ListProps {
  items?: ItemsProps[];
  onChange?: (_: any) => void;
  width?: number;
  position?: string;
  selectedValue?: any;
  isLoading?: boolean;
  comboboxStyle?: string;
  maxHeightList?: number;
}
