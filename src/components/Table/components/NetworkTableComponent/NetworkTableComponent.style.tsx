import styled, { css } from "styled-components";
import { TextField } from "components/TextField";
import { Checkbox } from "components/Checkbox";

export const OpenAllIcon = styled.div<{ level: any }>`
  position: absolute;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  //transition: all 0.3s;
  border-radius: 50%;
  background-color: ${(p) => p.theme.orangeActiveSupport};

  //
  width: 18px;
  height: 18px;
  max-width: 18px;
  max-height: 18px;
  min-width: 18px;
  min-height: 18px;

  ${(p) =>
    p.level &&
    css`
      left: ${p.level * 24}px;
    `}
`;

export const TextMenu = styled.div`
  margin-right: 15px;
`;

export const IconMenu = styled.div`
  margin-left: 15px;
`;

export const CellName = styled.div`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

export const SearchInput = styled(TextField)`
  height: 24px; //24
`;

export const SupportMenuElement = styled.div<{ isSelectMode?: boolean; isDisabled?: boolean }>`
  width: 100%;
  height: 25%;
  display: flex;
  align-items: center;
  //justify-content: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 5px 0;
  &:hover {
    background-color: ${(p) => p.theme.backgroundColor};
  }
  ${(p) =>
    !p.isSelectMode &&
    css`
      height: 50%;
    `}
  ${(p) =>
    p.isDisabled &&
    css`
      background-color: ${(p) => p.theme.gray};
      color: ${(p) => p.theme.black};
      cursor: no-drop;
      &:hover {
        background-color: ${(p) => p.theme.gray};
        color: ${(p) => p.theme.black};
      }
    `}
`;

export const SupportMenuLeft = styled.div<{ isSelectMode?: boolean }>`
  width: 200px;
  //height: 120px;
  box-shadow: 0 1px 2px rgb(0 0 0 / 30%), 0 2px 6px 2px rgb(0 0 0 / 15%);
  border-radius: 4px;
  top: 20px; //40px 28
  z-index: 5;
  position: absolute;
  background-color: ${(p) => p.theme.backgroundColorSecondary};

  ${(p) =>
    !p.isSelectMode &&
    css`
      height: 60px;
    `}
`;

export const MenuArrowIcon = styled.div<{ level?: number; isOpen?: boolean }>`
  margin-right: 5px;
  margin-left: ${(p) => (p.level ? p.level * 15 : 0)}px;
  position: relative;
  transform: rotate(90deg);

  ${(p) =>
    p.isOpen &&
    css`
      transform: rotate(180deg);
    `}
`;

export const Container = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  color: ${(p) => p.theme.textColor};
  user-select: none;
`;

export const TableHeader = styled.table`
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 999;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
`;

export const TD = styled.td<{ fixed?: boolean; width?: number; left?: string; isLastFixed?: boolean; minWidth?: number; hoverRow?: any; isHoverRow?: boolean }>`
  border-bottom: 1px solid rgba(224, 224, 224, 1);
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  border-left: none;
  border-right: none;

  ${(p) =>
    p.fixed &&
    css`
      position: sticky;
    `}
  ${(p) =>
    p.width &&
    css`
      width: ${p.width}px;
    `}
  ${(p) =>
    p.minWidth &&
    css`
      min-width: ${p.minWidth}px;
    `}
  ${(p) =>
    p.left &&
    css`
      left: ${p.left}px;
    `}
  ${(p) =>
    p.isLastFixed &&
    css`
      box-shadow: 6px 2px 6px -1px rgba(34, 60, 80, 0.2);
    `}

  ${(p) =>
    p.hoverRow &&
    css`
      opacity: 0.3;
    `}

  ${(p) =>
    p.isHoverRow &&
    css`
      opacity: 1;
    `}
`;

export const TRBody = styled.tr<{ selectedMode?: any }>`
  display: inline-flex;
  &:hover ${TD} {
    background-color: ${(p) => p.theme.backgroundColor} !important;
  }
  ${(p) =>
    p.selectedMode &&
    css`
      cursor: pointer;
    `}
`;

export const Cell = styled.div<{ isFirstIndex?: any }>`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  //height: 38px;
  height: 20px; //20
  position: relative;

  ${(p) =>
    p.isFirstIndex &&
    css`
      justify-content: flex-start;
    `}
`;

export const Column = styled.div``;

export const ColResizer = styled.div`
  width: 4px;
  height: 20px;
  position: absolute;
  top: 4px; //6px
  right: -3px;
  cursor: col-resize;
  z-index: 9;
  background-color: transparent;
  transition: all 0.3s;

  &:hover {
    background-color: ${(p) => p.theme.blueActiveSupport};
  }
  &:active {
    top: 4px;
    height: 20px; //30
  }
`;

export const TH = styled.th<{ fixed?: boolean; width?: number; left?: number; isLastFixed?: boolean; minWidth?: number }>`
  border-bottom: 3px solid #e0e2e7;
  border-right: 1px solid #e0e2e7;
  padding: 8px;
  font-size: 13px; //0.875rem;
  position: relative;
  height: 32px; //20px
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  // &:hover ${ColResizer} {
  //   background-color: ${(p) => p.theme.blueActiveSupport};
  // В случае нужды расомментирова (при наведении на шапку таблицы подсвечивает ресайз элемента)
  // }

  ${(p) =>
    p.fixed &&
    css`
      position: sticky;
      z-index: 9;
      &:first-child {
        z-index: 10;
      }
    `}
  ${(p) =>
    p.width &&
    css`
      width: ${p.width}px;
    `}
  ${(p) =>
    p.minWidth &&
    css`
      min-width: ${p.minWidth}px;
    `}
  ${(p) =>
    p.left &&
    css`
      left: ${p.left}px;
    `}
  ${(p) =>
    p.isLastFixed &&
    css`
      box-shadow: 6px 2px 6px -1px rgba(34, 60, 80, 0.2);
    `}
`;

export const THead = styled.thead`
  background-color: ${(p) => p.theme.backgroundColorSecondary};
`;

export const TableBody = styled.table`
  width: 100%;
  border: none;
`;

export const IconContainer = styled.div<{ isOpen?: boolean; level?: number; isChild?: boolean; isFullOpenChilds?: boolean; isEntry?: boolean }>`
  width: 16px; //28px
  height: 16px;
  min-width: 16px;
  min-height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(90deg);
  margin-right: 10px;
  //margin-left: 20px;
  //transition: all 0.3s;

  ${(p) =>
    p.isChild &&
    !p.isFullOpenChilds &&
    css`
      &:hover {
        background-color: ${(p) => p.theme.lightGray} !important;
        cursor: pointer;
      }
      &:active {
        background-color: ${(p) => p.theme.gray} !important;
        cursor: pointer;
      }
    `}

  ${(p) =>
    p.isOpen &&
    css`
      transform: rotate(180deg) !important;
    `}

  ${(p) =>
    p.level &&
    css`
      margin-left: ${p.level * 24}px; //40
    `}
`;

//  // ${(p) =>
//   //   p.isEntry &&
//   //   css`
//   //     //margin-left: 10px !important;
//   //     margin-left: ${p.level * 40}px;
//   //   `}

export const SearchContainer = styled.div<{ isCustomSelect?: boolean }>`
  width: 16px; //36px 24
  height: 16px; //36px 24
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  margin-left: 20px;
  transition: all 0.3s;
  position: relative;

  &:hover {
    background-color: ${(p) => p.theme.lightGray} !important;
    cursor: pointer;
  }
  &:active {
    background-color: ${(p) => p.theme.gray} !important;
    cursor: pointer;
  }

  ${(p) =>
    p.isCustomSelect &&
    css`
      background-color: ${(p) => p.theme.blueActiveSupport};
      color: ${(p) => p.theme.white};
      &:hover {
        background-color: ${(p) => p.theme.blueActiveSupport} !important;
      }
    `}
`;

export const CloseButton = styled.div`
  width: 16px; //24
  height: 16px; //24
  min-width: 16px; //36px
  min-height: 16px; //36px
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  margin-left: 20px;
  transition: all 0.3s;

  &:hover {
    background-color: ${(p) => p.theme.lightGray} !important;
    cursor: pointer;
  }
  &:active {
    background-color: ${(p) => p.theme.gray} !important;
    cursor: pointer;
  }
`;

export const MenuContainer = styled.div`
  width: 18px; //36px 28
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0;
  margin-left: 10px;
  transition: all 0.3s;

  &:hover {
    background-color: ${(p) => p.theme.lightGray} !important;
    cursor: pointer;
  }
  &:active {
    background-color: ${(p) => p.theme.gray} !important;
    cursor: pointer;
  }
`;

export const CheckboxStyled = styled(Checkbox)<{ level?: number }>`
  margin-left: 20px;
  ${(p) =>
    p.level &&
    css`
      margin-left: ${p.level * 20 + 20}px;
    `}
`;
