import React, { FC, Fragment, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Icon } from "components/Icon";
import { useOnClickOutside } from "hooks/useOnClickOutside";
import { filterTree, filterTreeManyKeys } from "helpers/SearchUtils";
import { Radio } from "components/Radio";
import {
  SearchInput,
  SupportMenuElement,
  SupportMenuLeft,
  SearchContainer,
  ColResizer,
  CloseButton,
  MenuContainer,
  MenuArrowIcon,
  Column,
  TD,
  TRBody,
  TableBody,
  TableHeader,
  THead,
  TH,
  Container,
  IconContainer,
  Cell,
  CellName,
  CheckboxStyled,
  TextMenu,
  IconMenu,
  OpenAllIcon,
} from "./NetworkTableComponent.style";
import { Checkbox } from "../../../Checkbox";
import styled, { css } from "styled-components";
import { NoData } from "components/NoData";
import { useLocation } from "react-router-dom";
import { Loader } from "../../../Loader";

const CheckBoxNotAll = styled.div<{ level?: number }>`
  color: white;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background-color: ${(p) => p.theme.blueActiveSupport};
  font-size: 38px;
  padding-bottom: 4px;
  margin-left: 20px;
  ${(p) =>
    p.level &&
    css`
      margin-left: ${p.level * 20 + 20}px;
    `}
`;

interface ITableComponentProps {
  columns: any[];
  tableData: any[];
  customCells?: any[];
  tableKey?: number;
  setColumns?: any;
  defaultColumns: any[];
  selectedMode?: null | "one" | "many";
  selected?: any;
  setSelected?: any;
  opacityMode?: boolean;
  childrenKey?: any;
  searchCustomFilter?: any[];
  isFullOpenChilds?: boolean;
  setSearchParams: any;
  onClickChildNetwork: any;
  openChildsNetwork: any;
  objectLoaderId: any;
  searchParams: any;
}

const checkedChilds = (array: any) => {
  let result: any[] = [];
  array.forEach((el: any) => {
    result.push(el.tabId);
    if (el.childs && el.childs.length > 0) {
      result = [...result, ...checkedChilds(el.childs)];
    }
  });
  return result;
};

export const FilterCustomCell = styled.div`
  //width: 200px;
  //height: 120px;
  width: auto;
  height: auto;
  box-shadow: 0 1px 2px rgb(0 0 0 / 30%), 0 2px 6px 2px rgb(0 0 0 / 15%);
  border-radius: 4px;
  top: 28px; //40px
  z-index: 5;
  position: absolute;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
`;

export const NoDataContainer = styled.div`
  position: absolute;
  top: 38px;
  bottom: 0;
  right: 0;
  left: 0;
`;

export const EmptyCheck = styled.div<{ selectedMode?: string }>`
  min-width: 30px;
  min-height: 28px;
  ${(p) =>
    p.selectedMode === "many" &&
    css`
      min-width: 40px;
    `}
`;

// @ts-ignore
const renderChildren = (
  array: any[],
  columns: any[],
  childrenKey?: string,
  // @ts-ignore
  onClickOpenAndClose: (_: any) => void,
  indexLastFixed?: number | undefined,
  customCells?: any[] | undefined,
  tableOpened?: any[],
  tableKey?: string | number | undefined,
  defaultColumns: any[],
  selectedMode?: null | "one" | "many",
  selected: any[],
  setSelected: any,
  parent: any,
  opacityMode?: boolean,
  isFullOpenChilds?: boolean,
  onClickChildNetwork?: any,
  openChildsNetwork?: any,
  objectLoaderId?: any,
  path: any,
  searchesButton: any,
  level: number
) => {
  const isCustomCells = customCells && customCells.length > 0;

  const newPath = parent.tabId ? path + "/" + parent.tabId : "";

  return array?.map((item: any, index: number) => {
    // const isOpen = tableOpened ? tableOpened.some((tabId) => tabId === item.tabId) : false; // ??
    // const isOpen = openChildsNetwork ? openChildsNetwork.some(({ tabId }: any) => tabId === item.tabId) : false; // ??
    const isChecked = selected ? selected.some((check) => check === item.tabId) : false;

    const onClick = () => {
      if (selectedMode === "one") {
        setSelected(() => {
          let result: any;
          const isSelected = selected.some((sel) => sel === item.tabId);
          if (isSelected) {
            result = [];
          } else {
            result = [item?.tabId];
          }
          if (result.length === 0) {
            localStorage.removeItem(`table-checked-items-${tableKey}`);
          } else {
            localStorage.setItem(`table-checked-items-${tableKey}`, JSON.stringify(result));
          }
          return result;
        });
      }
      if (selectedMode === "many") {
        setSelected((prev: any) => {
          let result: any;
          const isSelected = selected.some((sel) => sel === item.tabId);
          const childsArray = item?.childs ? checkedChilds(item?.childs) : [];
          if (isSelected) {
            result = prev
              .map((sel: any) => {
                if (sel === item.tabId) {
                  return null;
                }
                if (childsArray.some((child) => child === sel)) {
                  return null;
                }
                return sel;
              })
              .filter((sel: any) => sel !== null);
          } else {
            result = [...prev, item?.tabId, ...childsArray];
          }

          const resultAllSelected =
            parent?.childs && parent?.childs?.length > 0
              ? parent.childs.some((el: any) => {
                  const isSelected = result.some((sel: any) => sel === el.tabId);
                  return !isSelected;
                })
              : true;
          const isAllSelected = !resultAllSelected;

          if (isAllSelected) {
            result = [...result, parent?.tabId];
          } else {
            result = result
              .map((sel: any) => {
                if (sel === parent?.tabId) {
                  return null;
                }
                return sel;
              })
              .filter((sel: any) => sel !== null);
          }

          if (result.length === 0) {
            localStorage.removeItem(`table-checked-items-${tableKey}`);
          } else {
            localStorage.setItem(`table-checked-items-${tableKey}`, JSON.stringify(result));
          }

          return result;
        });
      }
    };

    const getSelectedChilds = (array: any, mode: boolean) => {
      return (
        array &&
        array?.length > 0 &&
        array?.some((el: any) => {
          let isSelectedChilds = false;
          if (el.childs && el.childs.length > 0) {
            isSelectedChilds = getSelectedChilds(el.childs, mode);
          }
          if (mode) {
            return (selected && selected?.length > 0 && selected?.some((sel: any) => sel === el.tabId)) || isSelectedChilds;
          } else {
            return (selected && selected?.length > 0 && !selected?.some((sel: any) => sel === el.tabId)) || isSelectedChilds;
          }
        })
      );
    };

    const isAllSelectedChilds = getSelectedChilds(item?.childs, true);
    const isNotAllSelectedChilds = getSelectedChilds(item?.childs, false);

    const isLoader = objectLoaderId === item.tabId;

    const getCellName = (value: any) => {
      const isFind = searchesButton.some((el: any) => el.searchName === value);
      if (isFind) {
        return (
          <CellName>
            <mark>{value}</mark>
          </CellName>
        );
      }
      return <CellName>{value}</CellName>;
    };

    return (
      <Fragment key={`tr-${level}-${index}`}>
        <TRBody selectedMode={selectedMode}>
          {columns.map((col, indexEl) => {
            return (
              <TD
                data-col={`${col?.name}-${tableKey}`}
                key={`td-${level}-${index}-${indexEl}`}
                fixed={col?.fixedColumn}
                width={col?.width}
                minWidth={defaultColumns[indexEl]?.width}
                left={col?.left}
                isLastFixed={indexLastFixed === indexEl}
                onClick={() => {
                  if (!item?.isDisableChecked) onClick();
                }}
                // onMouseEnter={() => setHoverRow(item?.tabId)}
                // onMouseLeave={() => setHoverRow(null)}
                // hoverRow={hoverRow && opacityMode}
                // isHoverRow={hoverRow === item?.tabId || parent?.tabId === hoverRow}
              >
                {/*<>{isAllSelected && <div>isAllSelected</div>}</>*/}
                <Cell isFirstIndex={indexEl === 0 && childrenKey} title={item[col?.name]}>
                  {/*Поправить отображение*/}
                  {indexEl === 0 && selectedMode && (
                    <>
                      {selectedMode === "one" && !item?.isDisableChecked && <Radio onClick={onClick} checked={isChecked} readOnly />}
                      {selectedMode === "many" && !item?.isDisableChecked && (
                        <>
                          {isAllSelectedChilds && isNotAllSelectedChilds ? (
                            <CheckBoxNotAll level={level} onClick={onClick}>
                              -
                            </CheckBoxNotAll>
                          ) : (
                            <CheckboxStyled level={level} onChange={onClick} status={isChecked} />
                          )}
                        </>
                      )}
                      {(!selectedMode || item?.isDisableChecked) && <EmptyCheck selectedMode={selectedMode} />}
                    </>
                  )}
                  {item?.isEntry && item?.hasChildren && childrenKey === col.name && (
                    <OpenAllIcon
                      title="Имеются дочерние объекты"
                      level={level}
                      onClick={() => {
                        const finalPath = newPath + "/" + item.tabId;
                        onClickChildNetwork(item.tabId, level, finalPath, true);
                      }}
                    >
                      {<Icon name="openAll" width={12} />}
                    </OpenAllIcon>
                  )}
                  {childrenKey === col.name && (
                    <IconContainer
                      isChild={item?.hasChildren}
                      isOpen={!(item?.hasChildren && item?.childs?.length === 0)}
                      isFullOpenChilds={isFullOpenChilds}
                      isEntry={item?.isEntry ?? false}
                      onClick={(e) => {
                        const finalPath = newPath + "/" + item.tabId;
                        onClickChildNetwork(item.tabId, level, finalPath, false);
                      }}
                      level={level + 1}
                    >
                      {isLoader ? (
                        <>
                          <Loader spinnerSize={20} />
                        </>
                      ) : (
                        <>{item?.hasChildren && <Icon name="arrow" width={10} />}</>
                      )}
                    </IconContainer>
                  )}
                  {isCustomCells && customCells.some((cus) => cus?.name === col?.name) ? (
                    <>{customCells.filter((cus) => cus.name === col.name)[0].render(item[col?.name], item, level, searchesButton) ?? <></>}</>
                  ) : (
                    // <CellName>{item[col.name]}</CellName>
                    <>{getCellName(String(item[col?.name]))}</>
                  )}
                </Cell>
              </TD>
            );
          })}
        </TRBody>
        {
          //isOpen &&
          item?.childs &&
            item?.childs?.length > 0 &&
            renderChildren(
              item?.childs,
              columns,
              childrenKey,
              onClickOpenAndClose,
              indexLastFixed,
              customCells,
              tableOpened,
              tableKey,
              defaultColumns,
              selectedMode,
              selected,
              setSelected,
              item,
              // hoverRow,
              // setHoverRow,
              opacityMode,
              isFullOpenChilds,
              onClickChildNetwork,
              openChildsNetwork,
              objectLoaderId,
              newPath,
              searchesButton,
              level + 1
            )
        }
      </Fragment>
    );
  });
};

export const NetworkTableComponent: FC<ITableComponentProps> = (props) => {
  const {
    columns = [],
    setColumns,
    tableData,
    customCells,
    tableKey,
    defaultColumns,
    selectedMode,
    selected,
    setSelected,
    opacityMode,
    childrenKey,
    searchCustomFilter,
    isFullOpenChilds,
    setSearchParams,
    onClickChildNetwork,
    openChildsNetwork,
    objectLoaderId,
    searchParams,
  } = props;

  const searchArrayColumn = columns.filter((item) => item.isSearch).map((item) => item.name);

  useEffect(() => {
    if (Object.keys(searchParams).length > 0) {
      setSearch(searchParams);
    }
  }, [searchParams]);

  let initSearch: any = [];
  if (searchArrayColumn.length > 0) {
    initSearch = searchArrayColumn.map((item) => {
      return { isOpen: false, searchName: "", key: item };
    });
  }
  const [search, setSearch] = useState<any[]>(initSearch);

  const [searchesButton, setSearchesButton] = useState<any>([]);

  // const searches: any = [];
  // for (let [key, value] of Object.entries(search)) {
  //   searches.push({ key, searchName: value });
  // }

  const clearSearch = () => {
    // setSearch([]);
    // setSearchesButton([]);
    setSearch((prev) => {
      return prev.map((el) => {
        return { ...el, isOpen: false, searchName: "" };
      });
    });
    setSearchesButton((prev: any) => {
      return prev.map((el: any) => {
        return { ...el, isOpen: false, searchName: "" };
      });
    });
  };

  useEffect(() => {
    if (Object.keys(searchParams).length > 0) {
      setSearch(searchParams);
    }
  }, [searchParams]);

  const initTableOpened = JSON.parse(localStorage.getItem(`tableOpened-${tableKey}`) as string) ?? [];
  const [tableOpened, setTableOpened] = useState<any[]>(initTableOpened);

  useEffect(() => {
    if (isFullOpenChilds) {
      openAllChild(tableData, true);
    }
    if (tableOpened.length > 0) {
      localStorage.setItem(`tableOpened-${tableKey}`, JSON.stringify(tableOpened));
    }
    return () => {
      // localStorage.removeItem(`tableOpened-${tableKey}`);
      localStorage.removeItem(`table-columns-${tableKey}`);
      localStorage.removeItem(`table-checked-items-${tableKey}`);
    };
  }, []);

  // const isEmptyFilter = search?.some((item) => item.searchName !== "") ?? false;
  //
  // const searches: any = [];
  // for (let [key, value] of Object.entries(search)) {
  //   searches.push({ key, searchName: value });
  // }

  // const filterTableData = searchArrayColumn.length === 0 || !isEmptyFilter ? tableData : !!setSearchParams ? tableData : filterTreeManyKeys(tableData, search, false);
  const filterTableData =
    // searchArrayColumn.length === 0 ? tableData : !!setSearchParams || searchesButton.length === 0 ? tableData : filterTreeManyKeys(tableData, searchesButton);
    tableData;

  useEffect(() => {
    if (!!setSearchParams) {
      setSearchParams(searchesButton);
    }
  }, [searchesButton]);

  const openAllChild = (array: any[], isSearchMode: boolean) => {
    array.forEach((item) => {
      if (item.childs && item.childs.length > 0) {
        openAllChild(item.childs, isSearchMode);
      }
      onClickOpenAndClose({ tabId: item.tabId, isSearchMode: isSearchMode });
    });
  };

  const closeAllChild = () => {
    setTableOpened([]);
    localStorage.removeItem(`tableOpened-${tableKey}`);
  };

  const onClickOpenAndClose = ({ tabId, isSearchMode }: { tabId: string | number; isSearchMode: boolean }) => {
    setTableOpened((prev) => {
      const isValue = prev.some((item) => item === tabId);
      let result;
      if (isValue) {
        if (isSearchMode) {
          result = prev;
        } else {
          result = prev
            .map((item) => {
              if (item === tabId) {
                return null;
              }
              return item;
            })
            .filter((item) => item !== null);
        }
      } else {
        result = [...prev, tabId];
      }
      if (result.length > 0) {
        localStorage.setItem(`tableOpened-${tableKey}`, JSON.stringify(result));
      } else {
        localStorage.removeItem(`tableOpened-${tableKey}`);
      }
      return result;
    });
  };

  const tableWidth = columns.reduce((sum, cur) => {
    sum += cur.width;
    return sum;
  }, 0);

  let indexLastFixed = columns.some((item) => item.fixedColumn) ? 0 : undefined;

  for (let i = 0; i < columns.length - 1; i++) {
    if (columns[i].fixedColumn === true) {
      indexLastFixed = i;
    }
  }

  const isChild = tableData?.some((item) => item?.childs && item?.childs?.length > 0) ?? false;

  const checkAllElement = () => {
    setSelected(() => {
      let result: any[] = [];
      tableData.map((item) => {
        const childs = item.childs ? checkedChilds(item.childs) : [];
        result.push(item.tabId);
        result.push(...childs);
      });
      localStorage.setItem(`table-checked-items-${tableKey}`, JSON.stringify(result));
      return result;
    });
  };

  const uncheckAllElement = () => {
    setSelected([]);
    localStorage.removeItem(`table-checked-items-${tableKey}`);
  };

  return (
    <Container
      onMouseDown={(e) => {
        // @ts-ignore
        if (e.target.dataset.resize) {
          const resizer = e.target;
          // @ts-ignore
          const parent = resizer.closest('[data-type="resizable"]');
          const coords = parent.getBoundingClientRect();
          const keyParent = parent.dataset.col.split("-")[0];
          let deltaKey: number | null = null;

          document.onmousemove = (event) => {
            const delta = event.pageX - coords.right;
            const value = coords.width + delta + "px";
            deltaKey = coords.width + delta;
            parent.style.width = value;

            const childs = document.querySelectorAll(`[data-col="${parent.dataset.col}"]`);
            childs.forEach((el) => {
              // @ts-ignore
              el.style.width = value;
            });
          };

          document.onmouseup = () => {
            document.onmousemove = null;
            setColumns((prev: any) => {
              const prevFinal = prev.map((item: any) => {
                if (item.name === keyParent && deltaKey) {
                  const widthDefault = defaultColumns.find((item) => item.name === keyParent)?.width;
                  return { ...item, width: deltaKey < widthDefault ? widthDefault : deltaKey };
                }
                return item;
              });
              localStorage.setItem(`table-columns-${tableKey}`, JSON.stringify(prevFinal));
              return prevFinal;
            });
          };
        }
      }}
    >
      <TableHeader cellSpacing="0" cellPadding="0" width={tableWidth}>
        <colgroup>
          {columns.map((col, index) => {
            return <col key={`colgroup-${index}`} style={{ width: col.width }} />;
          })}
        </colgroup>
        <THead>
          <tr style={{ display: "inline-flex" }}>
            {columns.map((col, index) => {
              // eslint-disable-next-line react-hooks/rules-of-hooks
              const [isOpenMenu, setIsOpenMenu] = useState(false);
              // eslint-disable-next-line react-hooks/rules-of-hooks
              const [isOpenMenuFilter, setIsOpenMenuFilter] = useState<boolean>(false);

              // eslint-disable-next-line react-hooks/rules-of-hooks
              const refMenu = useRef<HTMLDivElement>(null);
              // eslint-disable-next-line react-hooks/rules-of-hooks
              useOnClickOutside(refMenu, () => setIsOpenMenu(false));

              const isSearchColumn = search.find((item: any) => item.key === col.name).isOpen ?? false;

              const valueSearch = search.find((el: any) => el.key === col.name)?.searchName ?? "";

              const isCustomFilter = searchCustomFilter && searchCustomFilter.some((el) => el.name === col.name);

              // eslint-disable-next-line react-hooks/rules-of-hooks
              const refFilterCell = useRef<HTMLDivElement>(null);

              // eslint-disable-next-line react-hooks/rules-of-hooks
              useOnClickOutside(refFilterCell, () => {
                setIsOpenMenuFilter(false);
                // ??
                // setSearch({});
                // setSearch((prev) => {
                //   return prev.map((el) => {
                //     if (el.key === col.name) {
                //       return { ...el, isOpen: !el.isOpen };
                //     }
                //     return el;
                //   });
                // });
                // ??
                // if (isChild) {
                //   closeAllChild();
                // }
              });
              // const isSearchColumn = Object.prototype.hasOwnProperty.call(search, col.name);

              return (
                <TH
                  data-type="resizable"
                  data-col={`${col.name}-${tableKey}`}
                  key={`th-${index}`}
                  style={{ width: col.width, zIndex: columns.length - 1 - index }}
                  fixed={col.fixedColumn}
                  width={col.width}
                  minWidth={defaultColumns[index]?.width}
                  left={col.left}
                  isLastFixed={indexLastFixed === index}
                >
                  {isSearchColumn && !isCustomFilter ? (
                    <>
                      <SearchInput
                        placeholder="Введите текст и нажмите ENTER"
                        value={valueSearch} //search[col.name]
                        onChange={(e) => {
                          // setSearch((prev) => {
                          //   return prev.map((el) => {
                          //     if (el.key === col.name) {
                          //       return { ...el, searchName: e.target.value };
                          //     }
                          //     return el;
                          //   });
                          // });
                          setSearch((prev: any) => {
                            return prev.map((el: any) => {
                              if (col.name === el.key) {
                                return { ...el, searchName: e.target.value };
                              }
                              return el;
                            });
                          });
                        }}
                        focus
                        // @ts-ignore
                        onKeyPress={(e: any) => {
                          if (e.code === "Enter" || e.code === "NumpadEnter") {
                            e.preventDefault();
                            setSearchesButton(search);
                            // openAllChild();
                          }
                        }}
                      />
                      <CloseButton
                        onClick={() => {
                          setSearchesButton(search);
                          // openAllChild();
                        }}
                      >
                        <Icon name={"search"} width={14} />
                      </CloseButton>
                      <CloseButton
                        onClick={() => {
                          setSearch((prev: any) => {
                            const res = prev.map((el: any) => {
                              if (col.name === el.key) {
                                return { ...el, isOpen: false, searchName: "" };
                              }
                              return el;
                            });
                            const isCloseAll = res.every((el: any) => !el.isOpen);
                            if (isCloseAll) {
                              closeAllChild();
                            }
                            return res;
                          });

                          setSearchesButton((prev: any) => {
                            return prev.map((el: any) => {
                              if (col.name === el.key) {
                                return { ...el, isOpen: false, searchName: "" };
                              }
                              return el;
                            });
                            // return prev.filter((el: any) => el.key !== col.name);
                          });
                        }}
                      >
                        <Icon name="close" width={10} />
                      </CloseButton>
                      {!col.fixedColumn && <ColResizer data-resize="col" />}
                    </>
                  ) : (
                    <>
                      {col.isSearch && (
                        <SearchContainer
                          isCustomSelect={isCustomFilter && search.find((el) => el.key === col.name)?.searchName?.trim() !== ""}
                          onClick={() => {
                            if (isCustomFilter) {
                              setIsOpenMenuFilter(true);
                            } else {
                              // setSearch((prev: any) => {
                              //   const res: any = {};
                              //   res[col.name] = "";
                              //   return { ...prev, ...res };
                              // });
                              setSearch((prev: any) => {
                                return prev.map((el: any) => {
                                  if (col.name === el.key) {
                                    return { ...el, isOpen: true };
                                  }
                                  return el;
                                });
                              });
                            }
                            // setSearch((prev) => {
                            //   return prev.map((el) => {
                            //     if (el.key === col.name) {
                            //       return { ...el, isOpen: !el.isOpen };
                            //     }
                            //     return el;
                            //   });
                            // });
                            // if (isChild) {
                            //   openAllChild(tableData, true);
                            // }
                          }}
                        >
                          <Icon name={isCustomFilter ? "filter" : "search"} width={isCustomFilter ? 16 : 14} />
                        </SearchContainer>
                      )}
                      {isCustomFilter && isSearchColumn && (
                        <FilterCustomCell ref={refFilterCell}>{searchCustomFilter.find((el) => el.name === col.name)?.render(setSearch, col.name)}</FilterCustomCell>
                      )}
                      <Column>{col.title}</Column>
                      {!col.fixedColumn && <ColResizer data-resize="col" />}
                      {(isFullOpenChilds || selectedMode === "many") &&
                        childrenKey === col.name && ( //(isChild && childrenKey === col.name)
                          <MenuContainer
                            onClick={() => {
                              setIsOpenMenu(true);
                            }}
                          >
                            <Icon name="menu" width={16} />
                          </MenuContainer>
                        )}
                      {isOpenMenu && (
                        <SupportMenuLeft ref={refMenu} isSelectMode={selectedMode === "many"}>
                          {isFullOpenChilds && (
                            <>
                              <SupportMenuElement
                                onClick={() => {
                                  if (!isFullOpenChilds) {
                                    setIsOpenMenu(false);
                                    openAllChild(tableData, true);
                                  }
                                }}
                                isSelectMode={selectedMode === "many"}
                              >
                                <IconMenu>
                                  <MenuArrowIcon level={0} isOpen={true}>
                                    <Icon name="arrow" width={10} />
                                  </MenuArrowIcon>
                                </IconMenu>
                                <TextMenu>Раскрыть все уровни</TextMenu>
                              </SupportMenuElement>
                              <SupportMenuElement
                                isSelectMode={selectedMode === "many"}
                                onClick={() => {
                                  if (!isFullOpenChilds) {
                                    setIsOpenMenu(false);
                                    closeAllChild();
                                  }
                                }}
                              >
                                <IconMenu>
                                  <MenuArrowIcon level={0} isOpen={false}>
                                    <Icon name="arrow" width={10} />
                                  </MenuArrowIcon>
                                </IconMenu>
                                <TextMenu>Свернуть все уровни</TextMenu>
                              </SupportMenuElement>
                            </>
                          )}
                          {selectedMode === "many" && (
                            <>
                              <SupportMenuElement isSelectMode={selectedMode === "many"} onClick={() => checkAllElement()}>
                                <IconMenu>
                                  <Checkbox status={true} readonly />
                                </IconMenu>
                                <TextMenu>Выделить все</TextMenu>
                              </SupportMenuElement>
                              <SupportMenuElement isSelectMode={selectedMode === "many"} onClick={() => uncheckAllElement()}>
                                <IconMenu>
                                  <Checkbox status={false} readonly />
                                </IconMenu>
                                <TextMenu>Снять выделения</TextMenu>
                              </SupportMenuElement>
                            </>
                          )}
                        </SupportMenuLeft>
                      )}
                    </>
                  )}
                </TH>
              );
            })}
          </tr>
        </THead>
      </TableHeader>
      <TableBody width={tableWidth} cellSpacing="0" cellPadding="0">
        <colgroup>
          {columns.map((col, index) => {
            return <col key={`colgroup-${index}`} style={{ width: col?.width ?? 100 }} />;
          })}
        </colgroup>
        <tbody>
          {renderChildren(
            filterTableData,
            columns,
            childrenKey,
            onClickOpenAndClose,
            indexLastFixed,
            customCells,
            tableOpened,
            tableKey,
            defaultColumns,
            selectedMode,
            selected,
            setSelected,
            { childs: [], tabId: null },
            opacityMode,
            isFullOpenChilds,
            onClickChildNetwork,
            openChildsNetwork,
            objectLoaderId,
            "",
            searchesButton,
            0
          )}
          {filterTableData.length === 0 && (
            <NoDataContainer>
              <NoData />
            </NoDataContainer>
          )}
        </tbody>
      </TableBody>
    </Container>
  );
};
