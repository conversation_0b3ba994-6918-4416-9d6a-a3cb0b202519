export interface TableCellProps {
  cellProps?: any;
  propsTable?: any;
  filters?: any;
  dataTest?: string;
}

export const getRowId = (row: any) => row.tabId;

export const ROOT_ID = "";

export interface TableRowProps {
  row?: any;
  setSelected?: any;
  selectedMode?: any;
  selected?: any[];
  restProps?: any;
  tableKey?: any;
  dataTestRows?: string;
}

export const getChildRows = (row: any, rootRows: any) => {
  const childRows = rootRows.filter((r: any) => r.parentId === (row ? row.tabId : ROOT_ID));
  if (childRows.length) {
    return childRows;
  }
  return row && row.hasChildren ? [] : null;
};

export interface TableComponentV2Props {
  columns?: any;
  tableData?: any;
  selectedMode?: any;
  selected?: any;
  setSelected?: any;
  childrenKey?: any;
  height?: any;
  tableKey?: any;
  setColumns?: any;
  customCells?: any;
  disabledSearches?: any;
  isLoading?: any;
  onClickChildNetwork?: any;
  setSearchParams?: any;
}
