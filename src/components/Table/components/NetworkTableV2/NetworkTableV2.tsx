import React, { FC, useCallback, useEffect, useState } from "react";
import {
  SortingState,
  SelectionState,
  FilteringState,
  GroupingState,
  SearchState,
  IntegratedFiltering,
  IntegratedGrouping,
  IntegratedSorting,
  IntegratedSelection,
  TreeDataState,
  CustomTreeData,
} from "@devexpress/dx-react-grid";
import {
  Grid,
  VirtualTable,
  TableHeaderRow,
  TableFilterRow,
  TableSelection,
  TableGroupRow,
  GroupingPanel,
  DragDropProvider,
  TableColumnReordering,
  Toolbar,
  SearchPanel,
  TableTreeColumn,
  ColumnChooser,
  TableColumnVisibility,
  TableColumnResizing,
} from "@devexpress/dx-react-grid-material-ui";
import { observer } from "mobx-react";
import {
  CellStyled,
  ContainerLoading,
  CircularProgressStyled,
  Loading,
  RowStyled,
  ContentCell,
  CellName,
  getCellName,
  PaperStyled,
  Container,
} from "./NetworkTableV2.style";
import { TableCellProps, getRowId, TableRowProps, getChildRows, TableComponentV2Props } from "./NetworkTableV2.constant";
import { TextField } from "../../../TextField";
import { Icon } from "../../../Icon";
import styled, { css } from "styled-components";
import { Checkbox } from "../../../Checkbox";
import { Radio } from "../../../Radio";
import { CheckBoxContainer } from "../TableComponentV2";
import { useStores } from "../../../../stores/useStore";

const TableCell: FC<TableCellProps> = (props) => {
  const { cellProps, propsTable, filters, dataTest } = props;
  const { value, column, row } = cellProps;
  const { customCells = [], columns }: { customCells: any[]; columns: any[] } = propsTable;

  const customCell = customCells.find((el) => el.name === column.name);

  const tooltip = customCells.find((el: any) => el.name === column.name)?.tooltip;
  const tooltipCell = tooltip ? tooltip(row[column?.name], row) : row[column.name];

  const position = columns.find((el) => el.name === column.name)?.position ?? "center";

  return (
    <CellStyled>
      <ContentCell title={tooltipCell} position={position} data-test={dataTest}>
        {customCell ? <>{customCell?.render(row[column?.name], row, row?.level ?? 0, filters) ?? <></>}</> : <> {getCellName(String(value), filters)}</>}
      </ContentCell>
    </CellStyled>
  );
};

const TableRow: FC<TableRowProps> = ({ row, setSelected, selectedMode, selected, tableKey, dataTestRows, ...restProps }) => {
  const isSelected = selected?.some((el) => el === row.tabId) ?? false;
  return (
    <RowStyled
      {...restProps}
      // eslint-disable-next-line no-alert
      onClick={() =>
        !row.isDisableChecked &&
        selectedMode &&
        setSelected &&
        setSelected((prev: any[]) => {
          let result: any[] = [];
          const isFind = prev.some((item) => item === row.tabId);
          if (isFind) {
            result = prev.filter((el) => el !== row.tabId);
          } else {
            if (selectedMode === "many") {
              result = [...prev, row.tabId];
            } else {
              result = [row.tabId];
            }
          }
          if (result.length === 0) {
            localStorage.removeItem(`table-checked-items-${tableKey}`);
          } else {
            localStorage.setItem(`table-checked-items-${tableKey}`, JSON.stringify(result));
          }
          return result;
        })
      }
      isselected={isSelected && selectedMode && selectedMode !== "many"}
      ishover={!!setSelected && !row.isDisableChecked}
      data-test={dataTestRows}
    />
  );
};

export const TitleContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  max-height: 20px;
  min-height: 20px;
  width: 100%;
  z-index: 1000000;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

export const SearchContainer = styled.div<{ isCustomSelect?: boolean }>`
  width: 20px;
  height: 20px;
  max-width: 20px;
  max-height: 20px;
  min-width: 20px;
  min-height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  margin-right: 2px;

  &:hover {
    background-color: ${(p) => p.theme.lightGray} !important;
    cursor: pointer;
  }
  &:active {
    background-color: ${(p) => p.theme.gray} !important;
    cursor: pointer;
  }
`;

export const EmptySearchContainer = styled.div`
  width: 20px;
  height: 20px;
  max-width: 20px;
  max-height: 20px;
  min-width: 20px;
  min-height: 20px;
  margin-right: 10px;
`;

export const LabelTitle = styled.div`
  cursor: pointer;
`;

const SortContainer = styled.div`
  width: 20px;
  height: 20px;
  min-width: 20px;
  min-height: 20px;
  max-height: 20px;
  max-width: 20px;
  display: flex;
  flex-direction: column;
  margin-left: 6px;
`;

const SortUpContainer = styled.div<{ isActive?: boolean }>`
  height: 50%;
  width: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  opacity: 0.4;
  cursor: pointer;
  transition: all 0.3s;
  ${(p) =>
    p.isActive &&
    css`
      opacity: 1;
      color: ${(p) => p.theme.primaryColor};
    `}
`;

const SortDownContainer = styled.div<{ isActive?: boolean }>`
  cursor: pointer;
  height: 50%;
  width: 20px;
  transform: rotate(180deg);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  opacity: 0.4;
  transition: all 0.3s;
  ${(p) =>
    p.isActive &&
    css`
      opacity: 1;
      color: ${(p) => p.theme.primaryColor};
    `}
`;

interface ColumnsHeaderProps {
  name: any;
  title: any;
}

interface HeaderProps {
  align?: any;
  children?: any;
  column: ColumnsHeaderProps;
}

export const TextFieldStyled = styled(TextField)`
  height: 20px;
`;

export const TableHeaderContent: FC<any> = observer((restProps) => {
  const props: any = restProps[0];
  const {
    filters,
    setFilters,
    setFiltersPrev,
    disabledSearches,
    sorting,
    setSorting,
    setSearchParams,
    isEnter,
    tableKey,
    handleNetworkSort,
    dataTestSearch,
    focusedFilter,
    setFocusedFilter,
  }: any = restProps;
  /*
    Ввиду того, что компонент повторно рендерится при каждом рендере родительского компонента,
    что приводило к сбросу значений (value) в TextFieldStyled,
    а попытка вынести состояние из компонента по иерерахии выше не увенчалась успехом,
    было решено вынести хранение значений из локального состояния в глобальное состояние inputFilter в nsiStore
  */
  const { nsiStore } = useStores();
  const { column = { name: "", title: "" } }: HeaderProps = props;
  const currentFilter = filters?.find((el: any) => el.columnName === column.name) ?? null;

  const accessSearch = !disabledSearches.some((el: any) => el === column.name);

  const findSorting = sorting.find((el: any) => el.columnName === column.name) ?? null;

  return (
    <TitleContainer>
      {currentFilter ? (
        <>
          <TextFieldStyled
            value={nsiStore.inputFilter[`${tableKey}-${column.name}`] ?? ""}
            placeholder={isEnter ? "Введите текст нажмите ENTER" : "Поиск"}
            onKeyDown={(e: any) => {
              if (e.keyCode === 27) {
                setFilters((prev: any) => {
                  const res = prev.filter((el: any) => el.columnName !== column.name);
                  setSearchParams(res);
                  return res;
                });
                e.target.blur();
                setFocusedFilter(null);
              }
              if (e.keyCode === 13) {
                setFilters((prev: any) => {
                  return prev.map((item: any) => {
                    if (item.columnName === column.name) {
                      return { ...item, value: e?.target?.value };
                    }
                    return item;
                  });
                });
              }
            }}
            focus={focusedFilter === `${tableKey}-${column.name}`}
            onFocus={() => {
              setFocusedFilter(`${tableKey}-${column.name}`);
            }}
            onBlur={() => {
              // Небольшая задержка для обработки кликов на кнопки
              setTimeout(() => {
                setFocusedFilter(null);
              }, 150);
            }}
            onChange={(e) => {
              nsiStore.changeInputFilter(tableKey, column.name, e.target.value);
            }}
            dataTest="table.search-input"
          />
          <SearchContainer
            onMouseDown={(e) => {
              // Предотвращаем потерю фокуса при клике на кнопку
              e.preventDefault();
            }}
            onClick={() => {
              setSearchParams(
                filters.map((el: any) => {
                  if (el.columnName === column.name) {
                    return { ...el, value: nsiStore.inputFilter[`${tableKey}-${column.name}`] };
                  }
                  return el;
                })
              );
              setFilters((prev: any) =>
                prev.map((el: any) => {
                  if (el.columnName === column.name) {
                    return { ...el, value: nsiStore.inputFilter[`${tableKey}-${column.name}`] };
                  }
                  return el;
                })
              );
              // Сохраняем фокус в текущем инпуте
              setTimeout(() => {
                setFocusedFilter(`${tableKey}-${column.name}`);
              }, 0);
            }}
          >
            <Icon name="search" width={14} dataTest="table.search-button" />
          </SearchContainer>
          <SearchContainer
            onMouseDown={(e) => {
              // Предотвращаем потерю фокуса при клике на кнопку
              e.preventDefault();
            }}
            onClick={() => {
              nsiStore.changeInputFilter(tableKey, column.name, "");
              setFilters((prev: any) => {
                const res = prev.filter((el: any) => el.columnName !== column.name);
                setSearchParams(res);
                return res;
              });
              setFiltersPrev((prev: any) => {
                const res = prev.filter((el: any) => el.columnName !== column.name);
                setSearchParams(res);
                return res;
              });
              setFocusedFilter(null);
            }}
          >
            <Icon name="close" width={12} />
          </SearchContainer>
        </>
      ) : (
        <>
          {accessSearch && (
            <SearchContainer
              onClick={() => {
                setFilters((prev: any) => {
                  return [...prev, { columnName: column.name, operation: "contains", value: "" }];
                });
                // Устанавливаем фокус на новое поле после создания фильтра
                setTimeout(() => {
                  setFocusedFilter(`${tableKey}-${column.name}`);
                }, 0);
              }}
            >
              <Icon name="search" width={14} dataTest={dataTestSearch} />
            </SearchContainer>
          )}
          <LabelTitle>{column?.title}</LabelTitle>
          {accessSearch && (
            <SortContainer>
              <SortUpContainer
                onClick={() => {
                  const isCurrentlyActive = column.name === findSorting?.columnName && findSorting.direction === "asc";
                  // Если уже активна сортировка по возрастанию, сбрасываем её. Иначе - устанавливаем.
                  const res = isCurrentlyActive ? [] : [{ columnName: column.name, direction: "asc" }];
                  setSorting(res);
                  handleNetworkSort(res);
                }}
                isActive={column.name === findSorting?.columnName && findSorting.direction === "asc"}
              >
                <Icon width={10} name="arrow" />
              </SortUpContainer>
              <SortDownContainer
                onClick={() => {
                  const isCurrentlyActive = column.name === findSorting?.columnName && findSorting.direction === "desc";
                  // Если уже активна сортировка по убыванию, сбрасываем её. Иначе - устанавливаем.
                  const res = isCurrentlyActive ? [] : [{ columnName: column.name, direction: "desc" }];
                  setSorting(res);
                  handleNetworkSort(res);
                }}
                isActive={column.name === findSorting?.columnName && findSorting.direction === "desc"}
              >
                <Icon width={10} name="arrow" />
              </SortDownContainer>
            </SortContainer>
          )}
        </>
      )}
    </TitleContainer>
  );
});

export const CheckboxCell = (restProps: any) => {
  const { rowHeight, selectedMode }: any = restProps;
  const props: any = restProps[0];
  // eslint-disable-next-line react/prop-types
  const { selected, onToggle, row } = props;
  // eslint-disable-next-line react/prop-types
  if (!!selectedMode && !row?.isDisableChecked) {
    return (
      <CheckBoxContainer height={rowHeight}>
        {selectedMode === "many" ? (
          <Checkbox
            status={selected}
            onChange={onToggle}
            disabled={
              // eslint-disable-next-line react/prop-types
              !!!selectedMode && row?.isDisableChecked
            }
          />
        ) : (
          <Radio
            checked={selected}
            // onClick={onToggle}
            onChange={onToggle}
            disabled={
              // eslint-disable-next-line react/prop-types
              !!!selectedMode && row?.isDisableChecked
            }
          />
        )}
      </CheckBoxContainer>
    );
  } else {
    return <CheckBoxContainer height={rowHeight + 5}></CheckBoxContainer>;
  }
};

export const NetworkTableV2 = observer((props: any) => {
  const {
    columns,
    tableData,
    selectedMode,
    selected,
    setSelected,
    childrenKey,
    height,
    isLoading,
    tableKey,
    setSearchParams,
    expandedRowIds,
    setExpandedRowIds,
    filtersExternal,
    setFiltersExternal,
    disabledSearches = [],
    initSorting,
    isEnter,
    isOverFlowHidden,
    hiddenColumnNames,
    columnOrder,
    setColumnOrder,
    dataTest,
    dataTestDefaultCells,
    dataTestRows,
    dataTestSearch,
  } = props;
  const { tableStore } = useStores();

  const [filters, setFilters] = useState<any>([]);
  const [filtersPrev, setFiltersPrev] = useState<any>([]);
  const [focusedFilter, setFocusedFilter] = useState<string | null>(null);

  useEffect(() => {
    if (filtersExternal && filtersExternal.length === 0) {
      setFilters(filtersExternal);
      setFiltersPrev(filtersExternal);
    } else {
      setFilters(filtersExternal);
    }
  }, [filtersExternal]);

  const [columnWidths, setColumnWidths] = useState<any>([]);
  const [columnOrderLocal, setColumnOrderLocal] = useState<any>([]);

  useEffect(() => {
    const initColumnWidth = columns.map((el: any) => ({ columnName: el.name, width: el.width }));
    const initColumnOrder = columns.map((el: any) => el.name);
    setColumnWidths(initColumnWidth);
    if (columnOrder) {
      setColumnOrder(initColumnOrder);
    } else {
      setColumnOrderLocal(initColumnOrder);
    }
  }, [columns]);

  const handleUserKeyPress = useCallback(
    (event: any) => {
      const { keyCode } = event;
      if (keyCode == 13) {
        const is_same =
          filters.length == filtersPrev.length &&
          filters.every(function (element: any, index: any) {
            return element.value === filtersPrev[index].value;
          });
        if (!is_same) {
          setSearchParams(filters);
          setFiltersPrev(filters);
        }
      }
    },
    [filters]
  );

  useEffect(() => {
    if (isEnter) {
      window.addEventListener("keydown", handleUserKeyPress);
      return () => {
        window.removeEventListener("keydown", handleUserKeyPress);
      };
    }
  }, [handleUserKeyPress]);

  const [sorting, setSorting] = useState<any>([]);

  useEffect(() => {
    if (initSorting) {
      setSorting(initSorting);
    }
  }, [initSorting]);

  const isSearchMode = filters?.length > 0;

  const rowHeight = 21;

  useEffect(() => {
    if (columnOrder) {
      const data = columnOrder.map((el: any) => {
        const select = columnWidths.find((item: any) => item.columnName === el);
        const selectInit = columns.find((item: any) => item.name === el);
        return { ...select, position: selectInit?.position, name: select?.columnName, title: selectInit?.title };
      });
      if (data && data?.length > 0) {
        tableStore.setTableParams(data, tableKey, true);
      }
    } else {
      const data = columnOrderLocal.map((el: any) => {
        const select = columnWidths.find((item: any) => item.columnName === el);
        const selectInit = columns.find((item: any) => item.name === el);
        return { ...select, position: selectInit?.position, name: select?.columnName, title: selectInit?.title };
      });
      if (data && data?.length > 0) {
        tableStore.setTableParams(data, tableKey, true);
      }
    }
  }, [columnWidths, columnOrder, columnOrderLocal]);

  const handleNetworkSort = (res: any) => {
    tableStore.setSortParams(tableKey, res);
  };

  const columnsFinal = columnOrder
    ? columnOrder
        .map((el: any) => {
          return columns.find((item: any) => item.name === el);
        })
        .map((el: any) => ({ title: el.title, name: el.name }))
    : columnOrderLocal
        .map((el: any) => {
          return columns.find((item: any) => item.name === el);
        })
        .map((el: any) => ({ title: el.title, name: el.name }));

  return (
    <Container isOverFlowHidden={isOverFlowHidden} data-test={dataTest}>
      <PaperStyled>
        <Grid rows={tableData} columns={columnsFinal} getRowId={getRowId}>
          <SortingState sorting={sorting} onSortingChange={setSorting} />
          <SelectionState
            selection={selected}
            onSelectionChange={(e) => {
              if (selectedMode && selectedMode === "many") {
                setSelected(e);
              } else if (selectedMode && selectedMode === "one") {
                setSelected([e[e.length - 1]]);
              }
            }}
          />
          <IntegratedSorting />
          {selectedMode && <IntegratedSelection />}
          <TreeDataState
            expandedRowIds={expandedRowIds}
            onExpandedRowIdsChange={(prev: any) => {
              setExpandedRowIds(prev);
            }}
          />
          <CustomTreeData getChildRows={getChildRows} />
          {!isSearchMode && <DragDropProvider />}
          <VirtualTable
            rowComponent={({ row, ...restProps }) => TableRow({ row, setSelected, selectedMode, selected, tableKey, dataTestRows, ...restProps })}
            cellComponent={(cellProps) => <TableCell cellProps={cellProps} propsTable={props} filters={filters} dataTest={dataTestDefaultCells} />}
            messages={{ noData: "Нет данных" }}
            height={height ?? 800}
            columnExtensions={columns.map((el: any) => ({ width: el.width, columnName: el.name }))}
            estimatedRowHeight={20}
          />
          {!isSearchMode && (
            <TableColumnReordering
              order={columnOrder ? columnOrder : columnOrderLocal}
              onOrderChange={(e) => {
                if (columnOrder) {
                  setColumnOrder(e);
                } else {
                  setColumnOrderLocal(e);
                }
              }}
            />
          )}
          <TableColumnResizing columnWidths={columnWidths} onColumnWidthsChange={setColumnWidths} defaultColumnWidths={[]} />
          <TableHeaderRow
            contentComponent={(...props) => (
              <TableHeaderContent
                filters={filters}
                setFilters={setFilters}
                setFiltersPrev={setFiltersPrev}
                disabledSearches={disabledSearches}
                sorting={sorting}
                setSorting={setSorting}
                setSearchParams={setSearchParams}
                isEnter={isEnter}
                tableKey={tableKey}
                handleNetworkSort={handleNetworkSort}
                dataTestSearch={dataTestSearch}
                focusedFilter={focusedFilter}
                setFocusedFilter={setFocusedFilter}
                {...props}
              />
            )}
          />
          {selectedMode && (
            <TableSelection
              showSelectionColumn={selectedMode}
              showSelectAll={selectedMode && selectedMode === "many"}
              cellComponent={(...resProps) => (
                <CheckboxCell {...resProps} selected={selected} setSelected={setSelected} rowHeight={rowHeight} selectedMode={selectedMode} />
              )}
            />
          )}
          {hiddenColumnNames && <TableColumnVisibility defaultHiddenColumnNames={hiddenColumnNames} messages={{ noColumns: "Нет данных" }} />}
          <TableTreeColumn for={childrenKey} />
        </Grid>
        {(isLoading || columns.length === 0 || initSorting === null) && <Loading />}
      </PaperStyled>
    </Container>
  );
});
