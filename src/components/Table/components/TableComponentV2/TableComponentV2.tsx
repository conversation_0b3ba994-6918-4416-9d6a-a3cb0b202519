import React, { FC, useEffect, useCallback, useRef, useState } from "react";
import { observer } from "mobx-react";
import {
  SortingState,
  SelectionState,
  FilteringState,
  GroupingState,
  SearchState,
  IntegratedFiltering,
  IntegratedGrouping,
  IntegratedSorting,
  IntegratedSelection,
  TreeDataState,
  CustomTreeData,
} from "@devexpress/dx-react-grid";
import {
  Grid,
  VirtualTable,
  Table,
  TableHeaderRow,
  TableFilterRow,
  TableSelection,
  TableGroupRow,
  GroupingPanel,
  DragDropProvider,
  TableColumnReordering,
  Toolbar,
  ExportPanel,
  SearchPanel,
  TableTreeColumn,
  ColumnChooser,
  TableColumnVisibility,
  TableColumnResizing,
} from "@devexpress/dx-react-grid-material-ui";
import { filterTreeManyKeys } from "../../../../helpers/SearchUtils";
import { PaperStyled, Container, CellStyled, RowStyled, ContentCell, getCellName, ContainerLoading, CircularProgressStyled, Loading } from "./TableComponentV2.style";
import { TableCellProps, TableRowProps, TableComponentV2Props, getRowId, getChildRows, prepareRows } from "./TableComponentV2.contant";
import styled, { css } from "styled-components";
import { Icon } from "../../../Icon";
import { TextField } from "components/TextField";
import { Checkbox } from "components/Checkbox";
import { Radio } from "components/Radio";
import { useStores } from "../../../../stores/useStore";
import { CheckboxIconContainer } from "../TableComponent";
import { GridExporter } from "@devexpress/dx-react-grid-export";
import { saveAs } from "file-saver";

const InputContainer = styled.div`
  width: 100%;
  display: flex;
`;

const TableCell: FC<TableCellProps> = (props) => {
  const { cellProps, propsTable, filters, dataTest, sortingValue, findInCell } = props;
  const { value, column, row } = cellProps;
  const { customCells = [], columns }: { customCells: any[]; columns: any[] } = propsTable;

  const customCell = customCells.find((el) => el.name === column.name);

  const tooltip = customCells.find((el: any) => el.name === column.name)?.tooltip;
  const tooltipCell = tooltip ? tooltip(row[column?.name], row) : row[column.name];

  const position = columns.find((el) => el.name === column.name)?.position ?? "center";

  const custoRow =
    findInCell != null && row[findInCell]?.length > 1
      ? row[findInCell].sort((a: string, b: string) => (sortingValue === "desc" ? a.localeCompare(b) : b.localeCompare(a)))
      : row[column?.name];

  return (
    <CellStyled>
      <ContentCell title={tooltipCell} position={position} data-test={dataTest}>
        {customCell ? <>{customCell?.render(custoRow, row, row?.level ?? 0, filters) ?? <></>}</> : <> {getCellName(String(value), filters)}</>}
      </ContentCell>
    </CellStyled>
  );
};

const TableRow: FC<TableRowProps> = ({ row, setSelected, selectedMode, selected, tableKey, dataTestRows, ...restProps }) => {
  const isSelected = selected?.some((el) => el === row.tabId) ?? false;
  const rowColor = row.rowColor ? row.rowColor : "white";
  return (
    <RowStyled
      data-test={dataTestRows}
      onClick={() => {
        if (!row.isDisableChecked && selectedMode && setSelected) {
          let res = [];
          // @ts-ignore
          const find = selected.find((el) => el === row.tabId);
          if (find) {
            // @ts-ignore
            res = selected.filter((el) => el !== row.tabId);
          } else {
            // setSelected([...selected, row.tabId]);
            if (selectedMode === "many") {
              // @ts-ignore
              res = [...selected, row.tabId];
            } else {
              res = [row.tabId];
            }
          }
          setSelected(res);

          if (res.length === 0) {
            localStorage.removeItem(`table-checked-items-${tableKey}`);
          } else {
            localStorage.setItem(`table-checked-items-${tableKey}`, JSON.stringify(res));
          }

          // setSelected((prev: any[]) => {
          //   let result: any[] = [];
          //   const isFind = prev.some((item) => item === row.tabId);
          //   if (isFind) {
          //     result = prev.filter((el) => el !== row.tabId);
          //   } else {
          //     if (selectedMode === "many") {
          //       result = [...prev, row.tabId];
          //     } else {
          //       result = [row.tabId];
          //     }
          //   }
          //   if (result.length === 0) {
          //     localStorage.removeItem(`table-checked-items-${tableKey}`);
          //   } else {
          //     localStorage.setItem(`table-checked-items-${tableKey}`, JSON.stringify(result));
          //   }
          //   return result;
          // });
        }
      }}
      isselected={isSelected && selectedMode && selectedMode !== "many"}
      ishover={!!setSelected && !row.isDisableChecked}
      rowColor={rowColor}
      // eslint-disable-next-line no-alert
      {...restProps}
    />
  );
};

export const TitleContainer = styled.div<{ isVisible?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  max-height: 20px;
  min-height: 20px;
  width: 100%;
  z-index: 1000000;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  ${(p) =>
    p.isVisible &&
    css`
      overflow: visible;
    `}
`;

export const SearchContainer = styled.div<{ isCustomSelect?: boolean }>`
  width: 20px;
  height: 20px;
  max-width: 20px;
  max-height: 20px;
  min-width: 20px;
  min-height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  margin-right: 2px;

  &:hover {
    background-color: ${(p) => p.theme.lightGray} !important;
    cursor: pointer;
  }
  &:active {
    background-color: ${(p) => p.theme.gray} !important;
    cursor: pointer;
  }
`;

export const EmptySearchContainer = styled.div`
  width: 20px;
  height: 20px;
  max-width: 20px;
  max-height: 20px;
  min-width: 20px;
  min-height: 20px;
  margin-right: 10px;
`;

export const LabelTitle = styled.div`
  cursor: pointer;
  color: ${(p) => p.theme.color};
`;

const SortContainer = styled.div`
  width: 20px;
  height: 20px;
  min-width: 20px;
  min-height: 20px;
  max-height: 20px;
  max-width: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-left: 6px;
`;

const SortUpContainer = styled.div<{ isActive?: boolean }>`
  //height: 10px;
  height: 50%;
  width: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  opacity: 0.4;
  cursor: pointer;
  transition: all 0.3s;
  ${(p) =>
    p.isActive &&
    css`
      opacity: 1;
      color: ${(p) => p.theme.primaryColor};
    `}
`;

const SortDownContainer = styled.div<{ isActive?: boolean }>`
  cursor: pointer;
  //height: 10px;
  height: 50%;
  width: 20px;
  transform: rotate(180deg);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  opacity: 0.4;
  transition: all 0.3s;
  ${(p) =>
    p.isActive &&
    css`
      opacity: 1;
      color: ${(p) => p.theme.primaryColor};
    `}
`;

export const TextFieldStyled = styled(TextField)`
  height: 20px;
`;

interface ColumnsHeaderProps {
  name: any;
  title: any;
}

interface HeaderProps {
  align?: any;
  children?: any;
  column: ColumnsHeaderProps;
}

const TableShell = (propsTable: any) => {
  const { isVirtualTable } = propsTable;
  if (isVirtualTable) {
    return <VirtualTable {...propsTable} />;
  } else {
    return <Table {...propsTable} />;
  }
};

export const TableHeaderContent: FC<any> = (restProps) => {
  const props: any = restProps[0];
  const {
    filters,
    setFilters,
    isEnter,
    setFiltersLocal,
    setFiltersPrev,
    filtersLocal,
    disabledSearches,
    sorting,
    setSorting,
    tableKey,
    focus,
    setFocus,
    searchCustomFilter,
    handleNetworkSort,
    findInCell,
    setInCellFilterValue,
  }: any = restProps;
  const { column = { name: "", title: "" } }: HeaderProps = props;
  const currentFilter = isEnter
    ? filtersLocal?.find((el: any) => el.columnName === column?.name) ?? null
    : filters?.find((el: any) => el.columnName === column?.name) ?? null;

  const [currentValue, setCurrentValue] = useState(""); //currentFilter.value ??

  useEffect(() => {
    setCurrentValue(currentFilter?.value);
  }, []);

  const accessSearch = !disabledSearches.some((el: any) => el === column.name);

  const findSorting = sorting.find((el: any) => el.columnName === column.name) ?? null;

  const searchFilter = searchCustomFilter.find((el: any) => el.name === column.name);
  const selectedCustomFilter = filters.find((el: any) => el.columnName === column.name)?.value ?? "";

  const isVisible =
    selectedCustomFilter === "none" ||
    selectedCustomFilter === "Готово" ||
    selectedCustomFilter === "В процессе" ||
    selectedCustomFilter === "Ошибка" ||
    selectedCustomFilter === "SENDING_TO_DC" ||
    selectedCustomFilter === "SENDING_TO_DC_1" ||
    selectedCustomFilter === "WAITING_FOR_ACCEPT" ||
    selectedCustomFilter === "WAITING_FOR_GLOBAL_ACCEPT" ||
    selectedCustomFilter === "SENDING_GLOBAL_ACCEPT" ||
    selectedCustomFilter === "SENDING_TO_OIK" ||
    selectedCustomFilter === "ERROR_SENDING_TO_DC" ||
    selectedCustomFilter === "ERROR_SENDING_GLOBAL_ACCEPT" ||
    selectedCustomFilter === "ERROR_SENDING_TO_OIK" ||
    selectedCustomFilter === "DONE" ||
    selectedCustomFilter === "UNSENT_TO_DC" ||
    selectedCustomFilter === "SAVING_PG" ||
    selectedCustomFilter === "SAVING_PRE_PG" ||
    selectedCustomFilter === "SAVED_PRE_PG" ||
    selectedCustomFilter === "ERROR_SAVING_PRE_PG" ||
    selectedCustomFilter === "ERROR_SAVING_PG" ||
    selectedCustomFilter === "CONFIRMED" ||
    selectedCustomFilter === "FAILED" ||
    selectedCustomFilter === "RESEND_REQUIRED" ||
    selectedCustomFilter === "RESEND_REQUIRED_ACCEPT" ||
    selectedCustomFilter === "CREATED" ||
    selectedCustomFilter === "NO_DATA";

  return (
    <TitleContainer isVisible={isVisible}>
      {searchFilter ? (
        <>
          {currentFilter ? (
            <>
              <>{searchFilter.render(selectedCustomFilter, setFilters)}</>
              <SearchContainer
                onClick={() => {
                  // setFocus(null);
                  if (isEnter) {
                    setFiltersLocal((prev: any) => {
                      return prev.filter((el: any) => el.columnName !== column.name);
                    });
                    setFilters((prev: any) => {
                      return prev.filter((el: any) => el.columnName !== column.name);
                    });
                    setFiltersPrev((prev: any) => {
                      return prev.filter((el: any) => el.columnName !== column.name);
                    });
                  } else {
                    setFilters((prev: any) => {
                      return prev.filter((el: any) => el.columnName !== column.name);
                    });
                  }
                }}
              >
                <Icon name="close" width={12} />
              </SearchContainer>
            </>
          ) : (
            <>
              {accessSearch && (
                <SearchContainer
                  onClick={() => {
                    if (isEnter) {
                      setFiltersLocal((prev: any) => {
                        return [...prev, { columnName: column?.name, operation: "contains", value: "none" }];
                      });
                      setFilters((prev: any) => {
                        return [...prev, { columnName: column?.name, operation: "contains", value: "none" }];
                      });
                      setFocus(column.name);
                    } else {
                      setFilters((prev: any) => {
                        return [...prev, { columnName: column?.name, operation: "contains", value: "none" }];
                      });
                      setFocus(column.name);
                    }
                  }}
                >
                  <Icon name="search" width={14} />
                </SearchContainer>
              )}
              <LabelTitle>{column?.title}</LabelTitle>
              {accessSearch && (
                <SortContainer>
                  <SortUpContainer
                    onClick={() => {
                      const isCurrentlyActive = column.name === findSorting?.columnName && findSorting.direction === "asc";
                      // Если уже активна сортировка по возрастанию, сбрасываем её. Иначе - устанавливаем.
                      const res = isCurrentlyActive ? [] : [{ columnName: column.name, direction: "asc" }];
                      setSorting(res);
                      handleNetworkSort(res);
                    }}
                    isActive={column.name === findSorting?.columnName && findSorting.direction === "asc"}
                  >
                    <Icon width={10} name="arrow" />
                  </SortUpContainer>
                  <SortDownContainer
                    onClick={() => {
                      const isCurrentlyActive = column.name === findSorting?.columnName && findSorting.direction === "desc";
                      // Если уже активна сортировка по убыванию, сбрасываем её. Иначе - устанавливаем.
                      const res = isCurrentlyActive ? [] : [{ columnName: column.name, direction: "desc" }];
                      setSorting(res);
                      handleNetworkSort(res);
                    }}
                    isActive={column.name === findSorting?.columnName && findSorting.direction === "desc"}
                  >
                    <Icon width={10} name="arrow" />
                  </SortDownContainer>
                </SortContainer>
              )}
            </>
          )}
        </>
      ) : (
        <>
          {currentFilter ? (
            <InputContainer
              onClick={() => {
                setFocus(column.name);
              }}
              onBlur={(e) => {
                setTimeout(() => {
                  if (focus !== null) {
                    setFocus(null);
                  }
                }, 200);
              }}
            >
              <TextFieldStyled
                value={currentValue}
                placeholder={isEnter ? "Введите текст нажмите ENTER" : "Поиск"}
                onKeyDown={(e: any) => {
                  if (e.keyCode === 27) {
                    if (isEnter) {
                      setFiltersLocal((prev: any) => {
                        return prev.filter((el: any) => el.columnName !== column.name);
                      });
                      setFilters((prev: any) => {
                        return prev.filter((el: any) => el.columnName !== column.name);
                      });
                    } else {
                      setFilters((prev: any) => {
                        return prev.filter((el: any) => el.columnName !== column.name);
                      });
                    }
                    e.target.blur();
                  }
                  if (e.keyCode === 13) {
                    setFiltersLocal((prev: any) => {
                      return prev.map((item: any) => {
                        if (item.columnName === column.name) {
                          return { ...item, value: e?.target?.value };
                        }
                        return item;
                      });
                    });
                  }
                }}
                focus={focus === column.name}
                onChange={(e) => {
                  if (isEnter) {
                    setCurrentValue(e.target.value);
                  } else {
                    if (findInCell != null && column.name === findInCell) {
                      setInCellFilterValue(e.target.value);
                      setCurrentValue(e.target.value);
                    }
                    setFilters((prev: any) => {
                      return prev.map((item: any) => {
                        if (item.columnName === column.name) {
                          return { ...item, value: e?.target?.value };
                        }
                        return item;
                      });
                    });
                  }
                }}
              />
              {isEnter && (
                <SearchContainer
                  onClick={() => {
                    setFilters((prev: any) => {
                      return prev.map((el: any) => {
                        if (el.columnName === column.name) {
                          return { ...el, value: currentValue };
                        }
                        return el;
                      });
                    });
                    setFiltersLocal((prev: any) => {
                      return prev.map((el: any) => {
                        if (el.columnName === column.name) {
                          return { ...el, value: currentValue };
                        }
                        return el;
                      });
                    });
                  }}
                >
                  <Icon name="search" width={14} />
                </SearchContainer>
              )}
              <SearchContainer
                onClick={() => {
                  setFocus(null);
                  if (findInCell != null) {
                    setInCellFilterValue("");
                  }
                  if (isEnter) {
                    setFiltersLocal((prev: any) => {
                      return prev.filter((el: any) => el.columnName !== column.name);
                    });
                    setFilters((prev: any) => {
                      return prev.filter((el: any) => el.columnName !== column.name);
                    });
                    setFiltersPrev((prev: any) => {
                      return prev.filter((el: any) => el.columnName !== column.name);
                    });
                  } else {
                    setFilters((prev: any) => {
                      return prev.filter((el: any) => el.columnName !== column.name);
                    });
                  }
                }}
              >
                <Icon name="close" width={12} />
              </SearchContainer>
            </InputContainer>
          ) : (
            <>
              {accessSearch && (
                <SearchContainer
                  onClick={() => {
                    if (isEnter) {
                      setFiltersLocal((prev: any) => {
                        return [...prev, { columnName: column?.name, operation: "contains", value: "" }];
                      });
                      setFilters((prev: any) => {
                        return [...prev, { columnName: column?.name, operation: "contains", value: "" }];
                      });
                      setFocus(column.name);
                    } else {
                      setFilters((prev: any) => {
                        return [...prev, { columnName: column?.name, operation: "contains", value: "" }];
                      });
                      setFocus(column.name);
                    }
                  }}
                >
                  <Icon name="search" width={14} />
                </SearchContainer>
              )}
              <LabelTitle>{column?.title}</LabelTitle>
              {accessSearch && (
                <SortContainer>
                  <SortUpContainer
                    onClick={() => {
                      const isCurrentlyActive = column.name === findSorting?.columnName && findSorting.direction === "asc";
                      // Если уже активна сортировка по возрастанию, сбрасываем её. Иначе - устанавливаем.
                      const res = isCurrentlyActive ? [] : [{ columnName: column.name, direction: "asc" }];
                      setSorting(res);
                      handleNetworkSort(res);
                    }}
                    isActive={column.name === findSorting?.columnName && findSorting?.direction === "asc"}
                  >
                    <Icon width={10} name="arrow" />
                  </SortUpContainer>
                  <SortDownContainer
                    onClick={() => {
                      const isCurrentlyActive = column.name === findSorting?.columnName && findSorting.direction === "desc";
                      // Если уже активна сортировка по убыванию, сбрасываем её. Иначе - устанавливаем.
                      const res = isCurrentlyActive ? [] : [{ columnName: column.name, direction: "desc" }];
                      setSorting(res);
                      handleNetworkSort(res);
                    }}
                    isActive={column.name === findSorting?.columnName && findSorting?.direction === "desc"}
                  >
                    <Icon width={10} name="arrow" />
                  </SortDownContainer>
                </SortContainer>
              )}
            </>
          )}
        </>
      )}
    </TitleContainer>
  );
};

export const CheckBoxContainer = styled.div<{ height?: number }>`
  border-bottom: 1px solid rgba(224, 224, 224, 1);
  width: 100%;
  //max-height: ${(p) => p.height}px;
  max-height: 22px;
  //min-height: ${(p) => p.height}px;
  min-height: 22px;
  // height: ${(p) => p.height}px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const CheckboxCell = (restProps: any) => {
  const { rowHeight, selectedMode, dataTestCheckboxCells }: any = restProps;
  const props: any = restProps[0];
  // eslint-disable-next-line react/prop-types
  const { selected, onToggle, row } = props;
  // eslint-disable-next-line react/prop-types
  if (!!selectedMode && !row?.isDisableChecked) {
    return (
      <CheckBoxContainer>
        {selectedMode === "many" ? (
          <Checkbox
            status={selected}
            onChange={onToggle}
            disabled={
              // eslint-disable-next-line react/prop-types
              !!!selectedMode && row?.isDisableChecked
            }
            dataTest={dataTestCheckboxCells}
          />
        ) : (
          <Radio
            checked={selected}
            // onClick={onToggle}
            onChange={onToggle}
            disabled={
              // eslint-disable-next-line react/prop-types
              !!!selectedMode && row?.isDisableChecked
            }
          />
        )}
      </CheckBoxContainer>
    );
  } else {
    return <CheckBoxContainer height={rowHeight + 5}></CheckBoxContainer>;
  }
};

export const HeaderSelectionContainer = styled.div`
  border-right: 1px solid ${(p) => p.theme.borderTh};
  border-bottom: 1px solid ${(p) => p.theme.borderTh};
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  height: 34px;
  max-height: 34px;
  min-height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const HeaderCellSelection = (props: any) => {
  const { selectAll, unselectAll } = props;
  return (
    <HeaderSelectionContainer>
      <CheckboxIconContainer onClick={() => selectAll()} title="Выделить все" data-test="table.select-all-button">
        <Icon width={16} name="selectAll" />
      </CheckboxIconContainer>
      <CheckboxIconContainer onClick={() => unselectAll()} title="Снять выделение">
        <Icon width={16} name="unSelectAll" />
      </CheckboxIconContainer>
    </HeaderSelectionContainer>
  );
};

export const TableComponentV2 = observer((props: TableComponentV2Props) => {
  const {
    columns,
    tableData,
    selectedMode,
    selected,
    setSelected,
    childrenKey,
    height,
    isLoading,
    disabledSearches,
    tableKey,
    expandedRowIds,
    setExpandedRowIds,
    isEnter,
    isSearchForChilds,
    isOverFlowHidden,
    hiddenColumnNames,
    initSorting,
    setExportTable,
    titleXLS,
    customizeCell,
    columnOrder,
    setColumnOrder,
    searchCustomFilter,
    filtersExternal,
    focusHeader,
    setFocusHeader,
    isFocusHeader,
    dataTest,
    dataTestDefaultCells,
    dataTestRows,
    dataTestCheckboxCells,
    findInCell,
    isVirtualTable = true,
    treeCellComponent: CustomTreeCell,
  } = props;
  const { tableStore } = useStores();

  const [columnWidths, setColumnWidths] = useState<any>([]);
  const [columnOrderLocal, setColumnOrderLocal] = useState<any>([]);
  const [filtersPrev, setFiltersPrev] = useState<any>([]);
  const [filters, setFilters] = useState<any[]>([]);
  const [filtersLocal, setFiltersLocal] = useState<any[]>([]);
  const [inCellFilterValue, setInCellFilterValue] = useState<string>("");
  const [rows, setRows] = useState<any[]>([]);

  useEffect(() => {
    if (filtersExternal) {
      setFilters(filtersExternal);
    }
  }, [filtersExternal]);

  useEffect(() => {
    if (columnOrder) {
      const data = columnOrder.map((el: any) => {
        const select = columnWidths.find((item: any) => item.columnName === el);
        const selectInit = columns.find((item: any) => item.name === el);
        return { ...select, position: selectInit?.position, name: select?.columnName, title: selectInit?.title };
      });
      if (data && data?.length > 0) {
        tableStore.setTableParams(data, tableKey, true);
      }
    } else {
      const data = columnOrderLocal.map((el: any) => {
        const select = columnWidths.find((item: any) => item.columnName === el);
        const selectInit = columns.find((item: any) => item.name === el);
        return { ...select, position: selectInit?.position, name: select?.columnName, title: selectInit?.title };
      });
      if (data && data?.length > 0) {
        tableStore.setTableParams(data, tableKey, true);
      }
    }
  }, [columnWidths, columnOrder, columnOrderLocal]);

  useEffect(() => {
    const initColumnWidth = columns.map((el: any) => ({ columnName: el.name, width: el.width }));
    const initColumnOrder = columns.map((el: any) => el.name);
    setColumnWidths(initColumnWidth);
    if (columnOrder) {
      setColumnOrder(initColumnOrder);
    } else {
      setColumnOrderLocal(initColumnOrder);
    }
  }, [columns]);

  const handleUserKeyPress = useCallback(
    (e: any) => {
      const { keyCode } = e;
      if (keyCode === 13) {
        const is_same =
          filters.length == filtersPrev.length &&
          filters.every(function (element: any, index: any) {
            return element.value === filtersPrev[index].value;
          });
        if (!is_same) {
          setFilters(filtersLocal);
          setFiltersPrev(filtersLocal);
        }
      }
    },
    [filtersLocal]
  );

  useEffect(() => {
    if (isEnter) {
      window.addEventListener("keydown", handleUserKeyPress);
      return () => {
        window.removeEventListener("keydown", handleUserKeyPress);
      };
    }
  }, [handleUserKeyPress]);

  const finalFilters = filters
    .map((el) => {
      if (el.value === "none") {
        return { ...el, value: "" };
      }
      return el;
    })
    .filter((el) => el.value.length > 0);

  const [sorting, setSorting] = useState<any>([]);

  const sortingValue = sorting.find((item: any) => item.columnName === findInCell)?.direction;
  useEffect(() => {
    setRows(
      finalFilters.length > 0
        ? filterTreeManyKeys(
            tableData,
            finalFilters.map((el) => ({ key: el.columnName, searchName: el.value })),
            isSearchForChilds
          )
        : tableData
      // findInCell != null && tableData?.length > 1
      // ? tableData.sort((a: string, b: string) => (sortingValue === "asc" ? a?.localeCompare(b) : b?.localeCompare(a)))
      // : tableData
    );
  }, [tableData, filters, sortingValue]);

  // sorting in table cell inner data
  useEffect(() => {
    if (findInCell != null) {
      // if (inCellFilterValue.length === 0) {
      //   setRows(tableData.sort((a: string, b: string) => (sortingValue === "asc" ? a.localeCompare(b) : b.localeCompare(a))));
      //   return;
      // }
      switch (sortingValue) {
        case "asc":
          setRows((prev) =>
            prev.map((item: any) => ({
              ...item,
              [findInCell]: item[findInCell]
                .filter((rec: string) => rec.toLowerCase().includes(inCellFilterValue.toLowerCase()))
                .sort((a: string, b: string) => a.localeCompare(b)),
            }))
          );
          break;
        case "desc":
          setRows((prev) =>
            prev.map((item: any) => ({
              ...item,
              [findInCell]: item[findInCell]
                .filter((rec: string) => rec.toLowerCase().includes(inCellFilterValue.toLowerCase()))
                .sort((a: string, b: string) => b.localeCompare(a)),
            }))
          );
          break;
        default:
          break;
      }
    }
  }, [sorting, findInCell, inCellFilterValue]);

  const isSearchMode = finalFilters.length > 0;

  const [expandedRowIdsLocal, setExpandedRowIdsLocal] = useState<any>([]);
  const [preFilterExpandedRowIds, setPreFilterExpandedRowIds] = useState<any[]>([]);

  const prevFiltersRef = useRef<any[]>([]);

  useEffect(() => {
    const hasActiveFilters = filters.some((el) => el.value.length > 0);
    const prevHasActiveFilters = prevFiltersRef.current.some((el: any) => el.value.length > 0);

    // 1. Пользователь НАЧИНАЕТ фильтрацию
    if (hasActiveFilters && !prevHasActiveFilters) {
      // Сохраняем текущее состояние раскрытых строк ПЕРЕД тем, как его изменить
      setPreFilterExpandedRowIds(expandedRowIds !== undefined ? expandedRowIds : expandedRowIdsLocal);

      // Раскрываем все найденные элементы для удобства просмотра результатов
      const allRowIds = rows.map((el) => el.tabId);
      if (expandedRowIds !== undefined && setExpandedRowIds) {
        setExpandedRowIds(allRowIds);
      } else {
        setExpandedRowIdsLocal(allRowIds);
      }
    }
    // 2. Пользователь СБРАСЫВАЕТ фильтрацию
    else if (!hasActiveFilters && prevHasActiveFilters) {
      // Восстанавливаем сохраненное состояние дерева таблицы
      if (expandedRowIds !== undefined && setExpandedRowIds) {
        setExpandedRowIds(preFilterExpandedRowIds);
      } else {
        setExpandedRowIdsLocal(preFilterExpandedRowIds);
      }
    }

    // Обновляем ref для следующего рендера
    prevFiltersRef.current = filters;
  }, [filters, rows, expandedRowIds, setExpandedRowIds, expandedRowIdsLocal, preFilterExpandedRowIds]);

  useEffect(() => {
    if (initSorting) {
      setSorting(initSorting);
    }
  }, [initSorting]);

  const rowHeight = 21;

  const [focus, setFocus] = useState<any>(null);

  const selectAll = () => {
    const res = rows
      .map((el) => {
        if (!el.isDisableChecked) {
          return el.tabId;
        } else {
          return null;
        }
      })
      .filter((el) => el);
    setSelected(res);
  };

  const unselectAll = () => {
    setSelected([]);
  };

  const exporterRef = useRef();

  useEffect(() => {
    if (exporterRef.current && setExportTable) {
      // @ts-ignore
      setExportTable(exporterRef?.current); //exportGrid
    }
  }, [exporterRef]);

  const onSave = (workbook: any) => {
    workbook.xlsx.writeBuffer().then((buffer: any) => {
      saveAs(new Blob([buffer], { type: "application/octet-stream" }), `${titleXLS}.xlsx`);
    });
  };

  const handleNetworkSort = (res: any) => {
    tableStore.setSortParams(tableKey, res);
  };

  const TreeCell = CustomTreeCell || TableTreeColumn.Cell;

  return (
    <Container isOverFlowHidden={isOverFlowHidden} id={`table-${tableKey}`} data-test={dataTest} isVirtualTable={isVirtualTable}>
      <PaperStyled>
        {columns.length > 0 ? (
          <>
            <Grid rows={rows} columns={columns.map((el: any) => ({ title: el.title, name: el.name }))} getRowId={getRowId}>
              <SortingState
                sorting={sorting}
                onSortingChange={(e) => {
                  setSorting(e);
                }}
              />
              {selectedMode && (
                <SelectionState
                  selection={selected}
                  onSelectionChange={(e) => {
                    const current = e[e.length - 1];
                    const isDisableChecked = rows.find((el) => el.tabId === current)?.isDisableChecked ?? false;
                    if (!isDisableChecked) {
                      if (selectedMode && selectedMode === "many") {
                        setSelected(e);
                      } else if (selectedMode && selectedMode === "one") {
                        setSelected([current]);
                      }
                    }
                  }}
                />
              )}
              <IntegratedSorting />
              {selectedMode && <IntegratedSelection />}
              {expandedRowIds ? (
                <TreeDataState expandedRowIds={expandedRowIds} onExpandedRowIdsChange={setExpandedRowIds} />
              ) : (
                <TreeDataState expandedRowIds={expandedRowIdsLocal} onExpandedRowIdsChange={setExpandedRowIdsLocal} />
              )}
              <CustomTreeData getChildRows={getChildRows} />
              {!isSearchMode && <DragDropProvider />}
              <TableShell
                isVirtualTable={isVirtualTable}
                rowComponent={({ row, ...restProps }: any) => TableRow({ row, setSelected, selectedMode, selected, tableKey, dataTestRows, ...restProps })}
                cellComponent={(cellProps: any) => (
                  <TableCell
                    cellProps={cellProps}
                    propsTable={props}
                    filters={filters}
                    dataTest={dataTestDefaultCells}
                    sortingValue={sortingValue}
                    findInCell={findInCell}
                  />
                )}
                messages={{ noData: "Нет данных" }}
                height={height ?? 800}
                columnExtensions={columns.map((el: any) => ({ width: el.width, columnName: el.name }))}
                estimatedRowHeight={rowHeight}
              />
              {!isSearchMode && (
                <TableColumnReordering
                  order={columnOrder ? columnOrder : columnOrderLocal}
                  onOrderChange={(e) => {
                    if (columnOrder) {
                      setColumnOrder(e);
                    } else {
                      setColumnOrderLocal(e);
                    }
                  }}
                />
              )}
              <TableColumnResizing columnWidths={columnWidths} onColumnWidthsChange={setColumnWidths} />
              <TableHeaderRow
                contentComponent={(...props) => (
                  <TableHeaderContent
                    filters={filters}
                    setFilters={setFilters}
                    setFiltersLocal={setFiltersLocal}
                    filtersLocal={filtersLocal}
                    setFiltersPrev={setFiltersPrev}
                    isEnter={isEnter}
                    disabledSearches={disabledSearches}
                    sorting={sorting}
                    setSorting={setSorting}
                    focus={isFocusHeader ? focusHeader : focus}
                    tableKey={tableKey}
                    setFocus={isFocusHeader ? setFocusHeader : setFocus}
                    searchCustomFilter={searchCustomFilter}
                    handleNetworkSort={handleNetworkSort}
                    // findInCell={findInCell}
                    setInCellFilterValue={setInCellFilterValue}
                    {...props}
                  />
                )}
              />
              {selectedMode && (
                <TableSelection
                  showSelectAll={selectedMode && selectedMode === "many"}
                  headerCellComponent={() => <HeaderCellSelection selectAll={selectAll} unselectAll={unselectAll} />}
                  cellComponent={(...resProps) => (
                    <CheckboxCell
                      {...resProps}
                      selected={selected}
                      setSelected={setSelected}
                      rowHeight={rowHeight}
                      selectedMode={selectedMode}
                      dataTestCheckboxCells={dataTestCheckboxCells}
                    />
                  )}
                />
              )}
              {hiddenColumnNames && <TableColumnVisibility defaultHiddenColumnNames={hiddenColumnNames} messages={{ noColumns: "Нет данных" }} />}
              <TableTreeColumn for={childrenKey} cellComponent={TreeCell} />
            </Grid>
            <GridExporter ref={exporterRef} columns={columns} rows={rows} onSave={onSave} customizeCell={customizeCell} />
          </>
        ) : (
          <div style={{ position: "relative", height: `${height}px` }}></div>
        )}
        {(isLoading || columns.length === 0 || initSorting === null) && <Loading />}
      </PaperStyled>
    </Container>
  );
});
