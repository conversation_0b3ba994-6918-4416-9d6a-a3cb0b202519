import { ComponentType } from "react";
import { ROOT_ID } from "../NetworkTableV2/NetworkTableV2.constant";

export interface TableComponentV2Props {
  columns?: any;
  tableData?: any;
  selectedMode?: any;
  selected?: any;
  setSelected?: any;
  childrenKey?: any;
  height?: any;
  tableKey?: any;
  setColumns?: any;
  customCells?: any;
  disabledSearches?: any;
  setExportTable?: any;
  isLoading?: any;
  defaultColumns?: any;
  customizeCell?: any;
  titleXLS?: any;
  columnWidths?: any;
  setColumnWidths?: any;
  columnOrder?: any;
  setColumnOrder?: any;
  openAllChilds?: any;
  expandedRowIds?: any;
  setExpandedRowIds?: any;
  isEnter?: any;
  isSearchForChilds?: any;
  isOverFlowHidden?: any;
  hiddenColumnNames?: any;
  initSorting?: any;
  customSortingColumns?: any;
  filtersExternal?: any;
  searchCustomFilter?: any;
  focusHeader?: any;
  setFocusHeader?: any;
  headerOverflow?: any;
  isFocusHeader?: boolean;
  isVirtualTable?: boolean;
  dataTest?: string;
  dataTestDefaultCells?: string;
  dataTestRows?: string;
  dataTestCheckboxCells?: string;
  findInCell?: string;
  treeCellComponent?: ComponentType<any>;
}

export interface TableRowProps {
  row?: any;
  setSelected?: any;
  selectedMode?: any;
  selected?: any[];
  restProps?: any;
  tableKey?: any;
  dataTestRows?: string;
}

export interface TableCellProps {
  cellProps?: any;
  propsTable?: any;
  filters?: any;
  dataTest?: string;
  sortingValue?: string;
  findInCell?: string;
}

export const getRowId = (row: any) => row.tabId;

export const getChildRows = (row: any, rows: any) => {
  const childRows = rows.filter((r: any) => r.parentId === (row ? row.tabId : ROOT_ID));
  return childRows.length ? childRows : null;
};

export const prepareRows = (data: any, parentId: any) => {
  let result: any[] = [];
  data.map((el: any) => {
    let childs = [];
    if (el?.childs?.length > 0) {
      childs = prepareRows(el?.childs, el.tabId);
    }
    result.push({ ...el, parentId });
    if (childs?.length > 0) {
      childs.map((item) => {
        result.push({ ...item });
      });
    }
  });
  return result;
};
