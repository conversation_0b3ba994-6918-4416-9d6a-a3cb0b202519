import styled, { css } from "styled-components";
import Paper from "@mui/material/Paper";
import { VirtualTable } from "@devexpress/dx-react-grid-material-ui";
import React from "react";
import CircularProgress from "@mui/material/CircularProgress";

export const PaperStyled = styled(Paper)`
  position: relative;
`;

export const Container = styled.div<{ isOverFlowHidden: boolean; isVirtualTable?: boolean }>`
  width: 100%;
  height: 100%;
  overflow: auto;
  display: flex;
  flex-flow: column;
  min-height: 0;
  flex-grow: 1;
  .MuiIconButton-root {
    margin-left: 10px;
  }
  th {
    border-right: 1px solid ${(p) => p.theme.borderTh};
  }
  .MuiPaper-root {
    height: 100%;
    background-color: ${(p) => p.theme.backgroundColorSecondary};
    overflow-y: auto;
    & > div {
      height: 100%;
    }
    ${(p) =>
      !p.isVirtualTable &&
      css`
        height: 97%;
        th {
          z-index: 1;
        }
      `}
  }
  .MuiTableCell-root {
    padding: 0 4px;
  }
  //.TableTreeExpandButton-button {
  //  margin-left: 4px;
  //}
  .MuiTableCell-head {
    height: 32px; //40px
    min-height: 32px;
    //display: flex;
    //align-items: center;
    padding: 0 10px;
    cursor: default;
    user-select: all;
    font-weight: bold;
    font-size: 14px; //14
    background-color: ${(p) => p.theme.backgroundColorSecondary};
    color: ${(p) => p.theme.textColor};
    width: auto;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }

  .MuiTableBody-root {
    .MuiTableRow-root {
      td {
        height: 21px; //20
        min-height: 21px;
        max-height: 21px;
        padding: 0;
        overflow: ${(p) => (p.isOverFlowHidden ? "hidden" : "visible")};
        // background-color: ${(p) => p.theme.backgroundColorSecondary};
        //color: ${(p) => p.theme.textColor};
        //border-right: 1px solid #e0e2e7;
      }

      [data-testid="ChevronRightIcon"] {
        color: ${(p) => p.theme.textColor};
      }

      td > span {
        width: 20px; //20
        height: 20px;
      }

      button {
        padding: 0;
        //color: ${(p) => p.theme.textColor};
      }

      // &:hover td {
      //   background-color: ${(p) => p.theme.hover} !important;
      // }

      span {
        //color: ${(p) => p.theme.primaryColor};
      }
    }
  }
  .MuiPaper-root {
    box-shadow: none;
  }
  .TableTreeCell-cell {
    color: ${(p) => p.theme.textColor};
  }
  .TableNoDataCell-text {
    color: ${(p) => p.theme.textColor};
  }
  .TableTreeExpandButton-button {
    color: ${(p) => p.theme.textColor};
  }
`;

export const CellStyled = styled(VirtualTable.Cell)``;

export const RowStyled = styled(VirtualTable.Row)<{ isselected?: boolean; ishover?: boolean; rowColor?: any }>`
  color: ${(p) => p.theme.textColor} !important;
  &:hover {
    background-color: ${(p) => p.theme.hover} !important;
  }
  ${(p) =>
    p.isselected &&
    css`
      background-color: ${(p) => p?.theme?.rowSelected};
    `}
  ${(p) =>
    p.ishover &&
    css`
      cursor: pointer;
    `}
  ${(p) =>
    p.rowColor === "orange" &&
    css`
      background-color: ${(p) => p.theme.rowColorOrange} !important;
      &:hover {
        background-color: ${(p) => p.theme.rowColorOrangeHover} !important;
      }
    `}
  ${(p) =>
    p.rowColor === "blue" &&
    css`
      background-color: ${(p) => p.theme.rowColorBlue} !important;
      &:hover {
        background-color: ${(p) => p.theme.rowColorBlueHover} !important;
      }
    `}
  ${(p) =>
    p.rowColor === "cdu" && // Стилизация строки СО ЕЭС в детализированной информации распространения ПГ
    css`
      background-color: ${(p) => p.theme.rowColorCdu} !important;
      &:hover {
        background-color: ${(p) => p.theme.rowColorCduHover} !important;
      }
    `}
  ${(p) =>
    p.rowColor === "odu" && // Стилизация строк ОДУ в детализированной информации распространения ПГ
    css`
      background-color: ${(p) => p.theme.rowColorOdu} !important;
      &:hover {
        background-color: ${(p) => p.theme.rowColorOduHover} !important;
      }
    `}
  ${(p) =>
    p.rowColor === "rdu" && // Стилизация строк РДУ в детализированной информации распространения ПГ
    css`
      background-color: ${(p) => p.theme.rowColorRdu} !important;
      &:hover {
        background-color: ${(p) => p.theme.rowColorRduHover} !important;
      }
    `}
`;

export const ContentCell = styled.div<{ position?: string }>`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  ${(p) =>
    p.position === "center" &&
    css`
      justify-content: center !important;
    `}
  ${(p) =>
    p.position === "left" &&
    css`
      justify-content: flex-start !important;
    `}
  ${(p) =>
    p.position === "right" &&
    css`
      justify-content: flex-end !important;
    `}
`;

export const CellName = styled.div`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: ${(p) => p.theme.textColor};
`;

export const getCellName = (value: any, filters: any) => {
  const isFind = filters.some((el: any) => el.value === value);
  if (isFind) {
    return (
      <CellName>
        <mark>{value}</mark>
      </CellName>
    );
  }
  return <CellName>{value}</CellName>;
};

export const ContainerLoading = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  background: rgba(255, 255, 255, 0.3);
`;

export const CircularProgressStyled = styled(CircularProgress)`
  position: absolute;
  font-size: 20px;
  top: calc(45% - 10px);
  left: calc(50% - 10px);
`;

export const Loading = () => (
  <ContainerLoading>
    <CircularProgressStyled />
  </ContainerLoading>
);
