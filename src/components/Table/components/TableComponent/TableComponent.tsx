import React, { FC, Fragment, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Icon } from "components/Icon";
import { useOnClickOutside } from "hooks/useOnClickOutside";
import { filterTree, filterTreeManyKeys } from "helpers/SearchUtils";
import { Radio } from "components/Radio";
import {
  SearchInput,
  SupportMenuElement,
  SupportMenuLeft,
  SearchContainer,
  ColResizer,
  CloseButton,
  MenuContainer,
  MenuArrowIcon,
  Column,
  TD,
  TRBody,
  TableBody,
  TableHeader,
  THead,
  TH,
  Container,
  IconContainer,
  Cell,
  CellName,
  CheckboxStyled,
  TextMenu,
  IconMenu,
} from "./TableComponent.style";
import { Checkbox } from "../../../Checkbox";
import styled, { css } from "styled-components";
import { NoData } from "components/NoData";
import { useLocation } from "react-router-dom";
import { result } from "lodash";
import { selectAll, unSelectAll } from "../../../Icon/IconsList";
import { toJS } from "mobx";

const SortContainer = styled.div`
  width: 20px;
  height: 20px;
  display: flex;
  flex-direction: column;
  margin-left: 10px;
`;

const SortUpContainer = styled.div<{ isActive?: boolean }>`
  height: 10px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.4;
  cursor: pointer;
  transition: all 0.3s;
  ${(p) =>
    p.isActive &&
    css`
      opacity: 1;
    `}
`;

const SortDownContainer = styled.div<{ isActive?: boolean }>`
  cursor: pointer;
  height: 10px;
  width: 100%;
  transform: rotate(180deg);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.4;
  transition: all 0.3s;
  ${(p) =>
    p.isActive &&
    css`
      opacity: 1;
    `}
`;

const CheckBoxNotAll = styled.div<{ level?: number }>`
  color: white;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background-color: ${(p) => p.theme.blueActiveSupport};
  font-size: 36px;
  //padding-bottom: 4px;
  padding: 0 0 4px 1px;
  margin-left: 20px;
  ${(p) =>
    p.level &&
    css`
      margin-left: ${p.level * 20 + 20}px;
    `}
`;

interface ITableComponentProps {
  columns: any[];
  tableData: any[];
  customCells?: any[];
  tableKey?: number;
  setColumns?: any;
  defaultColumns: any[];
  selectedMode?: null | "one" | "many";
  selected?: any;
  setSelected?: any;
  opacityMode?: boolean;
  childrenKey?: string;
  searchCustomFilter?: any[];
  isFullOpenChilds?: boolean;
  showChildrenWhenSearching?: boolean;
  setSearchParams: any;
  searchParams: any;
  rowHeight: any;
  onClickRow: any;
  originalTableData?: any;
}

const checkedChilds = (array: any) => {
  let result: any[] = [];
  array.forEach((el: any) => {
    if (!el.isDisableChecked) {
      result.push(el.tabId);
    }
    if (el.childs && el.childs.length > 0) {
      result = [...result, ...checkedChilds(el.childs)];
    }
  });
  return result;
};

export const FilterCustomCell = styled.div`
  //width: 200px;
  //height: 120px;
  width: auto;
  height: auto;
  box-shadow: 0 1px 2px rgb(0 0 0 / 30%), 0 2px 6px 2px rgb(0 0 0 / 15%);
  border-radius: 4px;
  top: 28px; //40px
  z-index: 5;
  position: absolute;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
`;

export const NoDataContainer = styled.div`
  position: absolute;
  top: 38px;
  bottom: 0;
  right: 0;
  left: 0;
`;

export const EmptyCheck = styled.div<{ selectedMode?: string }>`
  min-width: 26px;
  min-height: 28px;
  ${(p) =>
    p.selectedMode === "many" &&
    css`
      min-width: 36px;
    `}
`;

export const CheckboxIconContainer = styled.div`
  max-width: 20px;
  width: 20px;
  min-width: 20px;
  height: 20px;
  border-radius: 6px;
  margin: 0 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  color: ${(p) => p.theme.primaryColor};
  &:hover {
    background-color: ${(p) => p.theme.lightGray} !important;
    cursor: pointer;
  }
  &:active {
    background-color: ${(p) => p.theme.gray} !important;
    cursor: pointer;
  }
`;

// @ts-ignore
const renderChildren = (
  array: any[],
  columns: any[],
  childrenKey?: string,
  // @ts-ignore
  onClickOpenAndClose: (_: any) => void,
  indexLastFixed?: number | undefined,
  customCells?: any[] | undefined,
  tableOpened?: any[],
  tableKey?: string | number | undefined,
  defaultColumns: any[],
  selectedMode?: null | "one" | "many",
  selected: any[],
  setSelected: any,
  parent: any,
  // hoverRow: any,
  // setHoverRow: any,
  opacityMode?: boolean,
  isFullOpenChilds?: boolean,
  rowHeight: number,
  searchesButton: any,
  onClickRow: any,
  path: any,
  closeAllChild: any,
  clearSearch: any,
  searchRow: any,
  setSearchRow: any,
  level: number
) => {
  const isCustomCells = customCells && customCells.length > 0;

  const newPath = parent.tabId ? path + "/" + parent.tabId : "";

  return array?.map((item: any, index: number) => {
    const isOpen = tableOpened ? tableOpened.some((tabId) => tabId === item.tabId) : false; // ??
    const isChecked = selected ? selected.some((check) => check === item.tabId) : false;

    const onClick = () => {
      if (selectedMode === "one") {
        setSelected(() => {
          let result: any;
          const isSelected = selected.some((sel) => sel === item.tabId);
          if (isSelected) {
            result = [];
          } else {
            result = [item?.tabId];
          }
          if (result.length === 0) {
            localStorage.removeItem(`table-checked-items-${tableKey}`);
          } else {
            localStorage.setItem(`table-checked-items-${tableKey}`, JSON.stringify(result));
          }
          return result;
        });
      }
      if (selectedMode === "many") {
        setSelected((prev: any) => {
          let result: any;
          const isSelected = selected.some((sel) => sel === item.tabId);
          if (isSelected) {
            result = prev.filter((el: any) => el !== item.tabId);
          } else {
            result = [...prev, item?.tabId];
          }

          if (result.length === 0) {
            localStorage.removeItem(`table-checked-items-${tableKey}`);
          } else {
            localStorage.setItem(`table-checked-items-${tableKey}`, JSON.stringify(result));
          }

          return result;
        });
      }
    };

    const getSelectedChilds = (array: any, mode: boolean) => {
      return (
        array &&
        array?.length > 0 &&
        array?.some((el: any) => {
          let isSelectedChilds = false;
          if (el.childs && el.childs.length > 0) {
            isSelectedChilds = getSelectedChilds(el.childs, mode);
          }
          if (mode) {
            return (selected && selected?.length > 0 && selected?.some((sel: any) => sel === el.tabId)) || isSelectedChilds;
          } else {
            return (selected && selected?.length > 0 && !selected?.some((sel: any) => sel === el.tabId)) || isSelectedChilds;
          }
        })
      );
    };

    // const isAllSelectedChilds = getSelectedChilds(item?.childs, true);
    // const isNotAllSelectedChilds = getSelectedChilds(item?.childs, false);

    const getCellName = (value: any) => {
      const isFind = searchesButton.some((el: any) => el.searchName === value);
      if (isFind) {
        return (
          <CellName>
            <mark>{value}</mark>
          </CellName>
        );
      }
      return <CellName>{value}</CellName>;
    };

    return (
      <Fragment key={`tr-${level}-${index}`}>
        {/*  @ts-ignore*/}
        <TRBody selectedMode={selectedMode} rowColor={item.rowColor} id={item.tabId}>
          {columns.map((col, indexEl) => {
            // @ts-ignore
            const tooltip = customCells.find((el: any) => el.name === col.name)?.tooltip;
            const tooltipCell = tooltip ? tooltip(item[col?.name], item) : item[col.name];
            return (
              <TD
                data-col={`${col?.name}-${tableKey}`}
                key={`td-${level}-${index}-${indexEl}`}
                fixed={col?.fixedColumn}
                width={col?.width}
                minWidth={defaultColumns[indexEl]?.width}
                left={col?.left}
                isLastFixed={indexLastFixed === indexEl}
                isSearchRow={item.tabId === searchRow}
                onClick={() => {
                  setSearchRow(null);
                  const isSearchMode = searchesButton.some((el: any) => el.isOpen);
                  if (onClickRow && isSearchMode) {
                    setSearchRow(item.tabId);
                    const finalPath = newPath + "/" + item.tabId;
                    onClickRow(finalPath, onClickOpenAndClose, closeAllChild, clearSearch);
                    setTimeout(() => {
                      const element = document.getElementById(item.tabId);
                      if (element) {
                        element.scrollIntoView(false);
                      }
                    }, 0);
                  } else {
                    if (!item?.isDisableChecked) onClick();
                  }
                }}
              >
                <Cell
                  isFirstIndex={(item?.childs?.length > 0 || parent?.childs?.length > 0 || selectedMode) && indexEl === 0 && childrenKey}
                  title={tooltipCell}
                  position={col.position}
                  height={rowHeight}
                >
                  {indexEl === 0 && selectedMode && (
                    <>
                      {selectedMode === "one" && !item?.isDisableChecked && <Radio onClick={onClick} checked={isChecked} readOnly />}
                      {selectedMode === "many" && !item?.isDisableChecked && (
                        <>
                          <CheckboxStyled level={level} onChange={onClick} status={isChecked} />
                        </>
                      )}
                      {(!selectedMode || item?.isDisableChecked) && <EmptyCheck selectedMode={selectedMode} />}
                    </>
                  )}
                  {childrenKey === col.name && (
                    <IconContainer
                      isChild={childrenKey === col?.name && item?.childs && item?.childs?.length > 0}
                      isOpen={isOpen}
                      isFullOpenChilds={isFullOpenChilds}
                      onClick={(e) => {
                        if (!isFullOpenChilds) {
                          if (childrenKey === col?.name && item?.childs && item?.childs?.length > 0) {
                            e.preventDefault();
                            e.stopPropagation();
                          }
                          onClickOpenAndClose({ tabId: item?.tabId });
                        }
                      }}
                      level={level}
                    >
                      {childrenKey === col?.name && item?.childs && item?.childs.length > 0 && <Icon name="arrow" width={10} />}
                    </IconContainer>
                  )}
                  {isCustomCells && customCells.some((cus) => cus?.name === col?.name) ? (
                    <>{customCells.filter((cus) => cus.name === col.name)[0]?.render(item[col?.name], item, level, searchesButton) ?? <></>}</>
                  ) : (
                    <>{getCellName(String(item[col?.name]))}</>
                  )}
                </Cell>
              </TD>
            );
          })}
        </TRBody>
        {isOpen &&
          item?.childs &&
          item?.childs?.length > 0 &&
          renderChildren(
            item?.childs,
            columns,
            childrenKey,
            onClickOpenAndClose,
            indexLastFixed,
            customCells,
            tableOpened,
            tableKey,
            defaultColumns,
            selectedMode,
            selected,
            setSelected,
            item,
            // hoverRow,
            // setHoverRow,
            opacityMode,
            isFullOpenChilds,
            rowHeight,
            searchesButton,
            onClickRow,
            newPath,
            closeAllChild,
            clearSearch,
            searchRow,
            setSearchRow,
            level + 1
          )}
      </Fragment>
    );
  });
};

export const TableComponent: FC<ITableComponentProps> = (props) => {
  const {
    columns = [],
    setColumns,
    tableData,
    customCells,
    tableKey,
    defaultColumns,
    selectedMode,
    selected,
    setSelected,
    opacityMode,
    childrenKey,
    searchCustomFilter,
    isFullOpenChilds,
    setSearchParams,
    searchParams,
    rowHeight,
    originalTableData,
    showChildrenWhenSearching,
    onClickRow,
  } = props;

  const getFlatData = (arr: any) => {
    let result: any = [];
    arr.forEach((el: any) => {
      result.push(el);
      if (el?.childs?.length > 0) {
        const childs = getFlatData(el?.childs);
        childs.forEach((item: any) => {
          result.push(item);
        });
      }
    });
    return result;
  };

  const searchArrayColumn = columns.filter((item) => item.isSearch).map((item) => item.name);

  let initSearch: any = [];
  if (searchArrayColumn.length > 0) {
    initSearch = searchArrayColumn.map((item) => {
      return { isOpen: false, searchName: "", key: item };
    });
  }
  const [search, setSearch] = useState<any[]>(initSearch);

  const [searchesButton, setSearchesButton] = useState<any>([]);

  const clearSearch = () => {
    setSearch((prev) => {
      return prev.map((el) => {
        return { ...el, isOpen: false, searchName: "" };
      });
    });
    setSearchesButton((prev: any) => {
      return prev.map((el: any) => {
        return { ...el, isOpen: false, searchName: "" };
      });
    });
  };

  useEffect(() => {
    if (Object.keys(searchParams).length > 0) {
      setSearch(searchParams);
    }
  }, [searchParams]);

  const initTableOpened = JSON.parse(localStorage.getItem(`tableOpened-${tableKey}`) as string) ?? [];
  const [tableOpened, setTableOpened] = useState<any[]>(initTableOpened);

  const openAllChild = () => {
    const flatData = getFlatData(tableData).map((el: any) => el.tabId);
    setTableOpened(flatData);
    localStorage.setItem(`tableOpened-${tableKey}`, JSON.stringify(flatData));
  };

  const closeAllChild = () => {
    setTableOpened([]);
    localStorage.removeItem(`tableOpened-${tableKey}`);
  };

  useEffect(() => {
    if (isFullOpenChilds) {
      openAllChild();
    }
    if (tableOpened.length > 0) {
      localStorage.setItem(`tableOpened-${tableKey}`, JSON.stringify(tableOpened));
    }
    return () => {
      localStorage.removeItem(`table-columns-${tableKey}`);
    };
  }, []);

  const filterTableData =
    searchArrayColumn.length === 0
      ? tableData
      : !!setSearchParams || !searchesButton.some((el: any) => el?.searchName?.length > 0)
      ? tableData
      : filterTreeManyKeys(tableData, searchesButton, false); //searchesButton.length === 0
  const originalFilterTableData =
    searchArrayColumn.length === 0
      ? tableData
      : !!setSearchParams || !searchesButton.some((el: any) => el?.searchName?.length > 0)
      ? tableData
      : filterTreeManyKeys(tableData, searchesButton, false);

  useEffect(() => {
    if (!!setSearchParams) {
      setSearchParams(searchesButton);
    }
  }, [searchesButton]);

  const onClickOpenAndClose = ({ tabId, isSearchMode }: { tabId: string | number; isSearchMode: boolean }) => {
    setTableOpened((prev) => {
      const isValue = prev.some((item) => item === tabId);
      let result;
      if (isValue) {
        if (isSearchMode) {
          result = prev;
        } else {
          result = prev
            .map((item) => {
              if (item === tabId) {
                return null;
              }
              return item;
            })
            .filter((item) => item !== null);
        }
      } else {
        result = [...prev, tabId];
      }
      if (result.length > 0) {
        localStorage.setItem(`tableOpened-${tableKey}`, JSON.stringify(result));
      } else {
        localStorage.removeItem(`tableOpened-${tableKey}`);
      }
      return result;
    });
  };

  const tableWidth = columns.reduce((sum, cur) => {
    sum += cur.width;
    return sum;
  }, 0);

  let indexLastFixed = columns.some((item) => item.fixedColumn) ? 0 : undefined;

  for (let i = 0; i < columns.length - 1; i++) {
    if (columns[i].fixedColumn === true) {
      indexLastFixed = i;
    }
  }

  const isChild = tableData?.some((item) => item?.childs && item?.childs?.length > 0) ?? false;

  const checkAllElement = () => {
    setSelected(() => {
      let result: any[] = [];
      tableData.map((item) => {
        const childs = item.childs ? checkedChilds(item.childs) : [];
        if (!item.isDisableChecked) {
          result.push(item.tabId);
        }
        result.push(...childs);
      });
      localStorage.setItem(`table-checked-items-${tableKey}`, JSON.stringify(result));
      return result;
    });
  };

  const uncheckAllElement = () => {
    setSelected(() => []);
    localStorage.removeItem(`table-checked-items-${tableKey}`);
  };

  // const [hoverRow, setHoverRow] = useState(null);

  const [sortParams, setSortParams] = useState<any>(null);

  const collator = new Intl.Collator("ru");

  function SortArray(x: any, y: any) {
    return collator.compare(x[sortParams.split("-")[0]], y[sortParams.split("-")[0]]);
  }

  function SortArray2(x: any, y: any) {
    return collator.compare(y[sortParams.split("-")[0]], x[sortParams.split("-")[0]]);
  }

  function SortNumber(a: any, b: any) {
    return a[sortParams.split("-")[0]] - b[sortParams.split("-")[0]];
  }

  function SortNumber2(a: any, b: any) {
    return b[sortParams.split("-")[0]] - a[sortParams.split("-")[0]];
  }

  const prepareArray = (array: any, sortType: any) => {
    return array
      .map((el: any) => {
        let childs = el?.childs ?? [];
        if (childs.length > 0) {
          childs = prepareArray(childs, sortType);
        }
        return { ...el, childs };
      })
      .sort(sortType);
  };

  const getArray = () => {
    const typeSort = columns.find((el) => el.name === sortParams.split("-")[0]).isSort;
    if (typeSort == "alphabet") {
      return sortParams.split("-")[1] === "up" ? prepareArray(filterTableData, SortArray) : prepareArray(filterTableData, SortArray2);
    }
    if (typeSort == "number") {
      return sortParams.split("-")[1] === "up" ? prepareArray(filterTableData, SortNumber) : prepareArray(filterTableData, SortNumber2);
    }
    return sortParams.split("-")[1] === "up" ? prepareArray(filterTableData, SortArray) : prepareArray(filterTableData, SortArray2);
  };

  const sortData = sortParams ? getArray() : originalFilterTableData;

  const [searchRow, setSearchRow] = useState(null);

  return (
    <Container
      onMouseDown={(e) => {
        // @ts-ignore
        if (e.target.dataset.resize) {
          const resizer = e.target;
          // @ts-ignore
          const parent = resizer.closest('[data-type="resizable"]');
          const coords = parent.getBoundingClientRect();
          const keyParent = parent.dataset.col.split("-")[0];
          let deltaKey: number | null = null;

          document.onmousemove = (event) => {
            const delta = event.pageX - coords.right;
            const value = coords.width + delta + "px";
            deltaKey = coords.width + delta;
            parent.style.width = value;

            const childs = document.querySelectorAll(`[data-col="${parent.dataset.col}"]`);
            childs.forEach((el) => {
              // @ts-ignore
              el.style.width = value;
            });
          };

          document.onmouseup = () => {
            document.onmousemove = null;
            setColumns((prev: any) => {
              const prevFinal = prev.map((item: any) => {
                if (item.name === keyParent && deltaKey) {
                  const widthDefault = defaultColumns.find((item) => item.name === keyParent)?.width;
                  return { ...item, width: deltaKey < widthDefault ? widthDefault : deltaKey };
                }
                return item;
              });
              localStorage.setItem(`table-columns-${tableKey}`, JSON.stringify(prevFinal));
              return prevFinal;
            });
          };
        }
      }}
    >
      <TableHeader cellSpacing="0" cellPadding="0" width={tableWidth}>
        <colgroup>
          {columns.map((col, index) => {
            return <col key={`colgroup-${index}`} style={{ width: col.width }} />;
          })}
        </colgroup>
        <THead>
          <tr style={{ display: "inline-flex" }}>
            {columns.map((col, index) => {
              // eslint-disable-next-line react-hooks/rules-of-hooks
              const [isOpenMenu, setIsOpenMenu] = useState(false);
              // eslint-disable-next-line react-hooks/rules-of-hooks
              const [isOpenMenuFilter, setIsOpenMenuFilter] = useState<boolean>(false);

              // eslint-disable-next-line react-hooks/rules-of-hooks
              const refMenu = useRef<HTMLDivElement>(null);
              // eslint-disable-next-line react-hooks/rules-of-hooks
              useOnClickOutside(refMenu, () => setIsOpenMenu(false));

              const isSearchColumn = search.find((item: any) => item.key === col.name)?.isOpen ?? false;
              //
              const valueSearch = search.find((el: any) => el.key === col.name)?.searchName ?? "";

              const isCustomFilter = searchCustomFilter && searchCustomFilter.some((el) => el.name === col.name);

              // eslint-disable-next-line react-hooks/rules-of-hooks
              const refFilterCell = useRef<HTMLDivElement>(null);

              // eslint-disable-next-line react-hooks/rules-of-hooks
              useOnClickOutside(refFilterCell, () => {
                setIsOpenMenuFilter(false);
                // ??
                // setSearch({});
                // setSearch((prev) => {
                //   return prev.map((el) => {
                //     if (el.key === col.name) {
                //       return { ...el, isOpen: !el.isOpen };
                //     }
                //     return el;
                //   });
                // });
                // ??
                // if (isChild) {
                //   closeAllChild();
                // }
              });

              // const isSearchColumn = Object.prototype.hasOwnProperty.call(search, col.name);
              return (
                <TH
                  data-type="resizable"
                  data-col={`${col.name}-${tableKey}`}
                  key={`th-${index}`}
                  style={{ width: col.width, zIndex: columns.length - 1 - index }}
                  fixed={col.fixedColumn}
                  width={col.width}
                  minWidth={defaultColumns[index]?.width}
                  left={col.left}
                  isLastFixed={indexLastFixed === index}
                >
                  {!isCustomFilter && isSearchColumn ? (
                    <>
                      <SearchInput
                        placeholder="Введите текст и нажмите ENTER"
                        value={valueSearch}
                        onChange={(e) => {
                          // setSearch((prev: any) => {
                          //   const res: any = {};
                          //   res[col.name] = e.target.value;
                          //   return { ...prev, ...res };
                          // });
                          setSearch((prev: any) => {
                            return prev.map((el: any) => {
                              if (col.name === el.key) {
                                return { ...el, searchName: e.target.value };
                              }
                              return el;
                            });
                          });
                        }}
                        focus
                        // @ts-ignore
                        onKeyPress={(e: any) => {
                          if (e.code === "Enter" || e.code === "NumpadEnter") {
                            e.preventDefault();
                            setSearchesButton(search);
                            openAllChild();
                          }
                        }}
                      />
                      <CloseButton
                        onClick={() => {
                          setSearchesButton(search);
                          openAllChild();
                        }}
                      >
                        <Icon name={"search"} width={14} />
                      </CloseButton>
                      <CloseButton
                        onClick={() => {
                          // setSearch((prev: any) => {
                          //   delete prev[col.name];
                          //   return { ...prev };
                          // });
                          setSearch((prev: any) => {
                            const res = prev.map((el: any) => {
                              if (col.name === el.key) {
                                return { ...el, isOpen: false, searchName: "" };
                              }
                              return el;
                            });
                            const isCloseAll = res.every((el: any) => !el.isOpen);
                            if (isCloseAll) {
                              closeAllChild();
                            }
                            return res;
                          });

                          setSearchesButton((prev: any) => {
                            // return prev.filter((el: any) => el.key !== col.name);
                            return prev.map((el: any) => {
                              if (col.name === el.key) {
                                return { ...el, isOpen: false, searchName: "" };
                              }
                              return el;
                            });
                          });
                          // closeAllChild();
                        }}
                      >
                        <Icon name="close" width={10} />
                      </CloseButton>
                      {!col.fixedColumn && <ColResizer data-resize="col" />}
                    </>
                  ) : (
                    <>
                      {col.isSearch && (
                        <SearchContainer
                          isCustomSelect={isCustomFilter && Object.values(searchParams).some((el: any) => el.length > 0)}
                          onClick={() => {
                            if (isCustomFilter) {
                              setIsOpenMenuFilter(true);
                            } else {
                              // setSearch((prev: any) => {
                              //   const res: any = {};
                              //   res[col.name] = "";
                              //   return { ...prev, ...res };
                              // });
                              setSearch((prev: any) => {
                                return prev.map((el: any) => {
                                  if (col.name === el.key) {
                                    return { ...el, isOpen: true };
                                  }
                                  return el;
                                });
                              });
                            }
                          }}
                        >
                          <Icon name={isCustomFilter ? "filter" : "search"} width={isCustomFilter ? 14 : 14} />
                        </SearchContainer>
                      )}
                      {/*isCustomFilter && isSearchColumn &&*/}
                      {isOpenMenuFilter && (
                        <FilterCustomCell ref={refFilterCell}>
                          {searchCustomFilter?.find((el) => el.name === col.name)?.render(setSearch, col.name, setIsOpenMenuFilter)}
                        </FilterCustomCell>
                      )}
                      <Column>{col.title}</Column>
                      {col.isSort && (
                        <SortContainer>
                          <SortUpContainer
                            onClick={() => {
                              setSortParams((prev: any) => {
                                if (prev === `${col.name}-up`) {
                                  return null;
                                }
                                return `${col.name}-up`;
                              });
                            }}
                            isActive={sortParams === `${col.name}-up`}
                          >
                            <Icon width={10} name="arrow" />
                          </SortUpContainer>
                          <SortDownContainer
                            onClick={() => {
                              setSortParams((prev: any) => {
                                if (prev === `${col.name}-down`) {
                                  return null;
                                }
                                return `${col.name}-down`;
                              });
                            }}
                            isActive={sortParams === `${col.name}-down`}
                          >
                            <Icon width={10} name="arrow" />
                          </SortDownContainer>
                        </SortContainer>
                      )}
                      {!col.fixedColumn && <ColResizer data-resize="col" />}
                      {(isFullOpenChilds || selectedMode === "many") &&
                        childrenKey === col.name && ( //(isChild && childrenKey === col.name)
                          <MenuContainer>
                            <CheckboxIconContainer onClick={() => checkAllElement()} title="Выделить все">
                              <Icon width={18} name="selectAll" />
                            </CheckboxIconContainer>
                            <CheckboxIconContainer onClick={() => uncheckAllElement()} title="Снять выделение">
                              <Icon width={18} name="unSelectAll" />
                            </CheckboxIconContainer>
                          </MenuContainer>
                        )}
                      {isOpenMenu && (
                        <SupportMenuLeft ref={refMenu} isSelectMode={selectedMode === "many"}>
                          {isFullOpenChilds && (
                            <>
                              <SupportMenuElement
                                onClick={() => {
                                  if (!isFullOpenChilds) {
                                    setIsOpenMenu(false);
                                    openAllChild();
                                  }
                                }}
                                isSelectMode={selectedMode === "many"}
                              >
                                <IconMenu>
                                  <MenuArrowIcon level={0} isOpen={true}>
                                    <Icon name="arrow" width={10} />
                                  </MenuArrowIcon>
                                </IconMenu>
                                <TextMenu>Раскрыть все уровни</TextMenu>
                              </SupportMenuElement>
                              <SupportMenuElement
                                isSelectMode={selectedMode === "many"}
                                onClick={() => {
                                  if (!isFullOpenChilds) {
                                    setIsOpenMenu(false);
                                    closeAllChild();
                                  }
                                }}
                              >
                                <IconMenu>
                                  <MenuArrowIcon level={0} isOpen={false}>
                                    <Icon name="arrow" width={10} />
                                  </MenuArrowIcon>
                                </IconMenu>
                                <TextMenu>Свернуть все уровни</TextMenu>
                              </SupportMenuElement>
                            </>
                          )}
                          {/*{selectedMode === "many" && (*/}
                          {/*  <>*/}
                          {/*    <SupportMenuElement isSelectMode={selectedMode === "many"} onClick={() => checkAllElement()}>*/}
                          {/*      <IconMenu>*/}
                          {/*        <Checkbox status={true} readonly />*/}
                          {/*      </IconMenu>*/}
                          {/*      <TextMenu>Выделить все</TextMenu>*/}
                          {/*    </SupportMenuElement>*/}
                          {/*    <SupportMenuElement isSelectMode={selectedMode === "many"} onClick={() => uncheckAllElement()}>*/}
                          {/*      <IconMenu>*/}
                          {/*        <Checkbox status={false} readonly />*/}
                          {/*      </IconMenu>*/}
                          {/*      <TextMenu>Снять выделения</TextMenu>*/}
                          {/*    </SupportMenuElement>*/}
                          {/*  </>*/}
                          {/*)}*/}
                        </SupportMenuLeft>
                      )}
                    </>
                  )}
                </TH>
              );
            })}
          </tr>
        </THead>
      </TableHeader>
      <TableBody width={tableWidth} cellSpacing="0" cellPadding="0">
        <colgroup>
          {columns.map((col, index) => {
            return <col key={`colgroup-${index}`} style={{ width: col?.width ?? 100 }} />;
          })}
        </colgroup>
        <tbody>
          {renderChildren(
            sortData, //sortData
            // filterTableData,
            columns,
            childrenKey,
            onClickOpenAndClose,
            indexLastFixed,
            customCells,
            tableOpened,
            tableKey,
            defaultColumns,
            selectedMode,
            selected,
            setSelected,
            { childs: [], tabId: null },
            opacityMode,
            isFullOpenChilds,
            rowHeight,
            searchesButton,
            onClickRow,
            "",
            closeAllChild,
            clearSearch,
            searchRow,
            setSearchRow,
            0
          )}
          {filterTableData.length === 0 && ( //filterTableData
            <NoDataContainer>
              <NoData />
            </NoDataContainer>
          )}
          {/*<div style={{ width: "100%", height: "3000px" }}></div>*/}
        </tbody>
      </TableBody>
    </Container>
  );
};
