import { ComponentType } from "react";

export interface ITableProps {
  columns: any[];
  setColumns?: any;
  tableData: any[];
  customCells?: any[];
  tableKey?: any;
  titleXLS?: any;
  title?: string;
  isLoading?: boolean;
  headerComponents?: JSX.Element;
  defaultColumns: any[];
  selectedMode?: null | "one" | "many";
  selected?: any;
  setSelected?: any;
  opacityMode?: boolean;
  isFullOpenChilds?: boolean;
  childrenKey?: string;
  searchCustomFilter?: any[];
  setSearchParams?: any;
  onClickChildNetwork?: any;
  openChildsNetwork?: any;
  objectLoaderId?: any;
  rowHeight?: any;
  heightContainer?: number;
  search?: any[];
  tableHeight?: any;
  onClickRow?: any;
  expandedRowIds?: any;
  setExpandedRowIds?: any;
  filters?: any;
  setFilters?: any;
  disabledSearches?: any;
  setExportTable?: any;
  hiddenColumnNames?: any;
  originalTableData?: any[];
  initSorting?: any[];
  version?: any[];
  customSortingColumns?: any[];
  height?: number;
  networkMode?: boolean;
  isSearchForChilds?: boolean;
  isEnter?: boolean;
  isFlatTable?: boolean;
  isExport?: boolean;
  showChildrenWhenSearching?: boolean;
  hasMoreElements?: boolean;
  isOverFlowHidden?: boolean;
  isVirtualTable?: boolean;
  customizeCell?: any[];
  customizeCellExport?: any;
  columnOrder?: any;
  setColumnOrder?: any;
  focusHeader?: any;
  setFocusHeader?: any;
  isFocusHeader?: boolean;
  headerOverflow?: "visible" | "hidden";
  dataTest?: string;
  dataTestDefaultCells?: string;
  dataTestRows?: string;
  dataTestCheckboxCells?: string;
  dataTestSearch?: string;
  findInCell?: string;
  treeCellComponent?: ComponentType<any>;
}
