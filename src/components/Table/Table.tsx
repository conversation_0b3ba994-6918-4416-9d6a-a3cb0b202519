import React, { FC } from "react";
import { Spin<PERSON><PERSON><PERSON><PERSON>, MainTableContainer, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, HeaderContainers, MoreElementsContainer } from "./Table.style";
import { ITableProps } from "./Table.types";
import { TableComponentV2 } from "./components/TableComponentV2";
import { NetworkTableV2 } from "./components/NetworkTableV2";
import { Button } from "components/Button";
import styled from "styled-components";

export const Action = styled(Button)`
  width: 20px;
  height: 20px;
`;

export const Table: FC<ITableProps> = (props) => {
  const {
    columns = [], // состояние таблицы
    setColumns, // Изменение состояния таблицы
    tableData, //Данные таблицы
    customCells = [], //Для кастомизации конкретных ячеек
    tableKey = 1, // Если несколько таблиц задаем им уникальные ключи
    title, // Заголовок таблицы
    isLoading,
    headerComponents, // Для кастомизации HEADER таблицы
    defaultColumns, // Для задания начального состояния таблицы
    selectedMode = null, // Для выбора одного или нескольких объектов
    selected, // Для выбора одного или нескольких объектов
    setSelected, // Для выбора одного или нескольких объектов
    opacityMode = false,
    childrenKey = null, //"name"
    searchCustomFilter = [],
    isFullOpenChilds = false,
    setSearchParams = null, // Функция которая передает параметры поиска на компонент выше
    networkMode = false, // Режим подгрузки данных с сервера
    onClickChildNetwork,
    openChildsNetwork,
    objectLoaderId = null,
    search = {},
    disabledSearches = [],
    heightContainer,
    hasMoreElements = false,
    isFlatTable = false,
    tableHeight = 800,
    expandedRowIds = null,
    setExpandedRowIds,
    filters,
    setFilters,
    isEnter = false,
    isSearchForChilds = false,
    isOverFlowHidden = true,
    hiddenColumnNames,
    initSorting, //= [{ columnName: "name", direction: "asc" }]
    setExportTable = null,
    customizeCellExport = [],
    titleXLS = `Таблица-${tableKey}`,
    columnOrder = null,
    setColumnOrder = null,
    focusHeader,
    setFocusHeader,
    isFocusHeader = false,
    headerOverflow = "hidden",
    dataTest,
    dataTestDefaultCells,
    dataTestRows,
    dataTestCheckboxCells,
    dataTestSearch,
    findInCell,
    isVirtualTable = true,
    treeCellComponent,
  } = props;

  return (
    <MainTableContainer height={heightContainer}>
      <HeaderContainers>
        {title && <Header>{title}</Header>}
        {headerComponents}
      </HeaderContainers>
      <TableContainer id="App" isFlatTable={isFlatTable}>
        {networkMode ? (
          <>
            <NetworkTableV2
              columns={columns}
              setColumns={setColumns}
              tableData={tableData}
              customCells={customCells}
              tableKey={tableKey}
              defaultColumns={defaultColumns}
              selectedMode={selectedMode}
              selected={selected}
              setSelected={setSelected}
              opacityMode={opacityMode}
              childrenKey={childrenKey}
              searchCustomFilter={searchCustomFilter}
              isFullOpenChilds={isFullOpenChilds}
              setSearchParams={setSearchParams}
              onClickChildNetwork={onClickChildNetwork}
              openChildsNetwork={openChildsNetwork}
              objectLoaderId={objectLoaderId}
              searchParams={search}
              isLoading={isLoading}
              expandedRowIds={expandedRowIds}
              setExpandedRowIds={setExpandedRowIds}
              filtersExternal={filters}
              setFiltersExternal={setFilters}
              disabledSearches={disabledSearches}
              initSorting={initSorting}
              isEnter={isEnter}
              isOverFlowHidden={isOverFlowHidden}
              hiddenColumnNames={hiddenColumnNames}
              columnOrder={columnOrder}
              setColumnOrder={setColumnOrder}
              headerOverflow={headerOverflow}
              dataTest={dataTest}
              dataTestDefaultCells={dataTestDefaultCells}
              dataTestRows={dataTestRows}
              dataTestSearch={dataTestSearch}
            />
          </>
        ) : (
          <>
            <TableComponentV2
              columns={columns}
              setColumns={setColumns}
              tableData={tableData}
              treeCellComponent={treeCellComponent}
              customCells={customCells}
              tableKey={tableKey}
              selectedMode={selectedMode}
              selected={selected}
              setSelected={setSelected}
              childrenKey={childrenKey}
              height={tableHeight}
              disabledSearches={disabledSearches}
              isLoading={isLoading}
              defaultColumns={defaultColumns}
              expandedRowIds={expandedRowIds}
              setExpandedRowIds={setExpandedRowIds}
              isEnter={isEnter}
              isSearchForChilds={isSearchForChilds}
              isOverFlowHidden={isOverFlowHidden}
              hiddenColumnNames={hiddenColumnNames}
              initSorting={initSorting}
              setExportTable={setExportTable}
              titleXLS={titleXLS}
              customizeCell={customizeCellExport}
              columnOrder={columnOrder}
              setColumnOrder={setColumnOrder}
              searchCustomFilter={searchCustomFilter}
              filtersExternal={filters}
              focusHeader={focusHeader}
              setFocusHeader={setFocusHeader}
              isFocusHeader={isFocusHeader}
              headerOverflow={headerOverflow}
              dataTest={dataTest}
              dataTestDefaultCells={dataTestDefaultCells}
              dataTestRows={dataTestRows}
              dataTestCheckboxCells={dataTestCheckboxCells}
              isVirtualTable={isVirtualTable}
              findInCell={findInCell}
            />
          </>
        )}
      </TableContainer>
      {hasMoreElements && <MoreElementsContainer>Кол-во объектов превышает 100 (отображаются не все объекты). Пожалуйста уточните поиск .</MoreElementsContainer>}
    </MainTableContainer>
  );
};
