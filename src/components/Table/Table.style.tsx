import styled, { css } from "styled-components";

export const MoreElementsContainer = styled.div`
  color: ${(p) => p.theme.redActiveSupport};
  margin-left: auto;
  margin-right: 10px;
`;

export const SpinnerContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

// height: 96%; //100
//   min-height: 96%; //100
//   max-height: 96%;

export const MainTableContainer = styled.div<{ height?: number }>`
  width: 100%;
  height: 98%;
  //border: solid 1px red;
  display: flex;
  flex-direction: column;

  //
  ${(p) =>
    p.height &&
    css`
      height: ${p.height}%; //100
      min-height: ${p.height}%; //100
      max-height: ${p.height}%;
    `}
`;

export const TableContainer = styled.div<{ isFlatTable?: boolean }>`
  position: relative;
  width: auto;
  ${(p) =>
    !p.isFlatTable &&
    css`
      height: 100%;
    `}
`;

export const Header = styled.div`
  height: 32px; //40px
  min-height: 32px;
  display: flex;
  align-items: center;
  padding: 0 10px;
  cursor: default;
  user-select: none;
  font-weight: bold;
  font-size: 14px; //14
  //background-color: ${(p) => p.theme.backgroundColorSecondary};
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  position: relative;
  width: auto;
`;

export const HeaderContainers = styled.div`
  height: 32px; //50
  min-height: 32px; //50
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0 10px;
  border-bottom: solid 1px ${(p) => p.theme.lightGray};
  background-color: ${(p) => p.theme.backgroundColorSecondary};
`;
