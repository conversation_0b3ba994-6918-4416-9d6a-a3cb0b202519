import styled, { css } from "styled-components";

export const VerticalLine = styled.div<{ level?: number }>`
  width: 2px;
  height: 15px;
  background-color: ${(p) => p.theme.gray};
  ${(p) =>
    p.level === 2 &&
    css`
      margin-left: 20px;
    `}
`;

export const HorizontalLine = styled.div<{ level?: number }>`
  height: 2px;
  width: 15px;
  position: absolute;
  background-color: ${(p) => p.theme.gray};
  right: 1px;
  top: 15px;
  ${(p) =>
    p.level === 1 &&
    css`
      right: -10px;
    `}
  ${(p) =>
    p.level === 2 &&
    css`
      right: -10px;
    `}
`;

export const Container = styled.div<{ level: number }>`
  width: 30px;
  height: 31px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  position: relative;
  margin-right: ${(p) => p.level * 20}px;
  ${(p) =>
    p.level === 2 &&
    css`
      margin-left: 12px;
    `}
`;

export const ArrowContainer = styled.div<{ level?: number }>`
  transform: rotate(180deg);
  position: absolute;
  color: ${(p) => p.theme.gray};
  top: 6px;
  ${(p) =>
    p.level === 1 &&
    css`
      right: -16px;
    `}
`;

export const FullVerticalLine = styled.div<{ level?: number }>`
  width: 2px;
  height: 25px;
  background-color: ${(p) => p.theme.gray};
  position: absolute;
  ${(p) =>
    p.level === 1 &&
    css`
      right: 3px;
    `}
  ${(p) =>
    p.level === 2 &&
    css`
      right: 26px;
    `}
`;

export const FinalLIne = styled.div`
  height: 2px;
  width: 25px;
  background-color: ${(p) => p.theme.gray};
  position: absolute;
  top: 23px;
  left: 2px;
`;
