import React from "react";
import { Icon } from "../Icon";
import { VerticalLine, HorizontalLine, Container, ArrowContainer, FullVerticalLine, FinalLIne } from "./ElementTree.style";

export const ElementTree = ({ level = 0, isLast = false }: { level?: number; isLast?: boolean }) => {
  if (level === 0) {
    return (
      <Container level={level}>
        <ArrowContainer>
          <Icon width={16} name="arrow" />
        </ArrowContainer>
      </Container>
    );
  }
  if (level === 1) {
    return (
      <Container level={level}>
        <FullVerticalLine />
        <VerticalLine />
        <HorizontalLine level={0} />
        <ArrowContainer level={1}>
          <Icon width={16} name="arrow" />
        </ArrowContainer>
      </Container>
    );
  }
  if (level === 2) {
    return (
      <Container level={level}>
        <FullVerticalLine level={2} />
        <FullVerticalLine level={1} />
        <HorizontalLine level={2} />
        {isLast && <FinalLIne />}
      </Container>
    );
  }
  return <></>;
};
