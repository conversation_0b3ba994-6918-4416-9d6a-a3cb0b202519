import React, { FC } from "react";
import styled, { css } from "styled-components";
import { observer } from "mobx-react";
import { useStores } from "../../stores/useStore";
import { toJS } from "mobx";

const Container = styled.div`
  display: flex;
  height: 24px; //28px
  user-select: none;
`;

const Button = styled.div<{ isSelect?: boolean; width?: number; fontSize?: number; isNavBar?: boolean }>`
  width: 142px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: solid 1px ${(p) => p.theme.lightGray};
  border-left: none;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: bold;
  line-height: 24px;

  &:first-child {
    border-left: solid 1px ${(p) => p.theme.lightGray};
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  &:last-child {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  ${(p) =>
    p.isSelect &&
    css`
      color: ${(p) => p.theme.primaryColor};
    `}
  &:hover {
    background-color: ${(p) => p.theme.primaryColor};
    color: ${(p) => p.theme.white};
  }

  ${(p) =>
    p.width &&
    css`
      width: ${p.width}px;
    `}

  ${(p) =>
    p.fontSize &&
    css`
      font-size: ${p.fontSize}px;
    `}
  
  ${(p) =>
    p.isNavBar &&
    css`
      color: ${(p) => p.theme.buttonGroupsTextColor};
      background-color: ${(p) => p.theme.buttonGroupsBackgroundColor};
      &:hover {
        color: ${(p) => p.theme.buttonGroupsTextColorHover};
        background-color: ${(p) => p.theme.buttonGroupsBackgroundColorHover};
      }
    `}

${(p) =>
    p.isSelect &&
    p.isNavBar &&
    css`
      background-color: ${(p) => p.theme.buttonGroupsBackgroundColorActive};
      color: ${(p) => p.theme.buttonGroupsTextColorHover};
    `}
`;

interface ButtonsGroupProps {
  items?: any[];
  selectedValue?: any;
  isNavBar?: any;
  onClick?: (_: any) => any;
  className?: string;
  widthButton?: number;
  fontSize?: number;
}

export const ButtonsGroup: FC<ButtonsGroupProps> = observer((props) => {
  const { items = [], selectedValue, onClick, className, widthButton, fontSize = 12, isNavBar = false } = props;
  const { authStore } = useStores();
  const { userInfo } = authStore;

  const res = items.filter((el) => {
    return el?.roles && el?.roles?.length > 0
      ? el?.roles?.some((role: any) => {
          return userInfo.roles.some((item: any) => role === item);
        })
      : true;
  });

  return (
    <Container className={className}>
      {res.map(({ label, value, dataTest }) => {
        return (
          <Button
            isSelect={selectedValue === value}
            key={`button-groups-${value}`}
            onClick={() => {
              onClick && onClick(value);
            }}
            width={widthButton}
            fontSize={fontSize}
            isNavBar={isNavBar}
            data-test={dataTest}
          >
            {label}
          </Button>
        );
      })}
    </Container>
  );
});
