import axios, { AxiosInstance, AxiosResponse } from "axios";
import { getToken, getRefreshToken, setTokens, clearStorage, getTokenReceiptTime } from "./localStorage";
import { stores } from "stores/RootStore";
import { ServerError, ResponseError, ForbiddenError } from "./Error";
import { logTokenEvent, maskToken } from "./tokenLogger";
import { delay } from "~/helpers/delay";
import { InvalidTokenError, jwtDecode } from "./jwtDecode";

// Удален вывод в консоль в связи с обращением СИБов (SRPG-2840)

let tokenUpdatePromise: Promise<void> | null = null; // Защита от множественных вызовов

const createMainAxiosInstance = () => {
  return axios.create({
    headers: {
      "Content-Type": "application/json",
    },
    timeout: 900000,
  });
};

export const checkTokenTime = (token: string | null): boolean => {
  if (!token || token === "undefined" || token === "null") {
    return false;
  }

  try {
    const decoded = jwtDecode<{ exp?: number }>(token);

    if (!decoded.exp) {
      // Если нет поля exp, считаем токен невалидным
      return false;
    }

    const currentTime = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = decoded.exp - currentTime;

    // Считаем токен валидным, если до истечения больше 30 секунд
    // Это дает время на обновление токена до его фактического истечения
    return timeUntilExpiry > 30;
  } catch (error) {
    if (error instanceof InvalidTokenError) {
      logTokenEvent("TOKEN_DECODE_ERROR", {
        error: error.message,
        token: maskToken(token),
      });
    }
    return false;
  }
};

export const updateToken = () => {
  // Если обновление уже выполняется, возвращаем существующий промис
  if (tokenUpdatePromise) {
    return tokenUpdatePromise;
  }

  tokenUpdatePromise = new Promise((resolve, reject) => {
    // Функция для реализации логики повторов
    const attemptRefresh = async (attempt = 1) => {
      try {
        const refreshToken = getRefreshToken();

        logTokenEvent("REFRESH_INITIATED", {
          refreshToken: maskToken(refreshToken),
          attempt,
        });

        if (refreshToken === "undefined" || refreshToken === "null" || !refreshToken) {
          logTokenEvent("REFRESH_ABORTED", {
            reason: "Отсутствует refresh token в localstorage",
          });
          window.location.replace("/login");
          // Отклоняем промис, чтобы остановить любые зависимые запросы
          return reject(new Error("Нет refresh-токена"));
        }

        const response = await fetch("/srpg-control/api/v1/auth/refresh-token", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ refreshToken }),
        });

        if (response.status === 401 || response.status === 403) {
          logTokenEvent("REFRESH_FAILED/401_403", {
            status: response.status,
            usedRefreshToken: maskToken(refreshToken),
          });
          localStorage.removeItem("token");
          localStorage.removeItem("refreshToken");
          window.location.replace("/login");
          return reject(new Error(`Ошибка обновления токена, статус ${response.status}`));
        }

        if (!response.ok) {
          logTokenEvent("REFRESH_FAILED/OTHER", {
            status: response.status,
            usedRefreshToken: maskToken(refreshToken),
          });
          throw new Error(`Ошибка при обновлении токена: ${response.status}`);
        }

        const data = await response.json();
        setTokens({ token: data.token, refreshToken: data.refreshToken });

        logTokenEvent("REFRESH_SUCCESS", {
          newRefreshToken: maskToken(data.refreshToken),
        });

        resolve();
      } catch (e: any) {
        // Если это сетевая ошибка (браузер не смог отправить запрос) и это первая попытка
        if (e.message === "Failed to fetch" && attempt < 2) {
          logTokenEvent("REFRESH_RETRYING", {
            reason: e.message,
          });
          await delay(1000);
          attemptRefresh(attempt + 1); // Запускаем вторую попытку
        } else {
          // Если это не сетевая ошибка, или все попытки исчерпаны, то окончательно завершаем с ошибкой
          logTokenEvent("REFRESH_EXCEPTION", {
            error: e?.message || e?.toString() || "Неизвестная ошибка",
            finalAttempt: attempt,
          });
          // Отклоняем главный промис, чтобы все ожидающие запросы получили ошибку
          reject(e);
        }
      }
    };

    // Запускаем первую попытку
    attemptRefresh();
  });

  // Добавляем .finally() к промису, чтобы сбросить блокировку после того,
  // как он завершится (успешно или с ошибкой).
  tokenUpdatePromise.finally(() => {
    tokenUpdatePromise = null;
  });

  return tokenUpdatePromise;
};

// Interceptor для каждого запроса, который будет делать refresh токена, если он истек
const createRequestInterceptorAuth = (instance: AxiosInstance) => {
  instance.interceptors.request.use(
    async (config: any) => {
      let token;
      if (tokenUpdatePromise) {
        await tokenUpdatePromise;
      }
      token = getToken();
      const refreshToken = getRefreshToken();
      const isValidRefresh = refreshToken !== null && refreshToken !== "undefined" && refreshToken !== "null";
      const isValidToken = checkTokenTime(getToken());

      // Обновляем токен только если он истек И есть валидный refresh токен
      if (token && !isValidToken && isValidRefresh && !tokenUpdatePromise) {
        logTokenEvent("createRequestInterceptorAuth", {
          refreshToken: maskToken(refreshToken),
          token: maskToken(token),
          isValidToken,
          isValidRefresh,
        });
        await updateToken();
        token = getToken();
      }

      if (token) {
        config.headers["Authorization"] = token;
      }
      return config;
    },
    (err: string) => Promise.reject(err)
  );
};

const createResponseInterceptorAuth = (instance: AxiosInstance) => {
  instance.interceptors.response.use(
    (response: AxiosResponse) => response.data,
    async (err: any) => {
      logTokenEvent("createResponseInterceptorAuth/err", {
        error: err.message,
      });
      if (err.message === "request_canceled") {
        return Promise.reject(err.message);
      }

      // Защита от обработки запросов refresh-token
      const isRefreshTokenRequest = err?.config?.url?.includes("/auth/refresh-token");
      if (isRefreshTokenRequest) {
        return Promise.reject(err);
      }

      // Специальная обработка для запросов login
      const isLoginRequest = err?.config?.url?.includes("/auth/login");
      if (isLoginRequest) {
        // Для логина просто прокидываем ошибку дальше, уведомление покажет AuthStore
        return Promise.reject(err);
      }

      const refreshToken = getRefreshToken();
      const isValidRefresh = refreshToken !== null && refreshToken !== "undefined" && refreshToken !== "null";
      if (!isValidRefresh) {
        logTokenEvent("createResponseInterceptorAuth/isValidRefresh", {
          refreshToken: maskToken(refreshToken),
        });
        const pathname = `${location.pathname}${location.search}`;
        const login = localStorage.getItem("login") ?? null;
        const theme = localStorage.getItem("theme") ?? "SRPG_DEFAULT";
        clearStorage();
        if (pathname) {
          localStorage.setItem("pathname", pathname);
        }
        if (login) {
          localStorage.setItem("login", login);
        }
        localStorage.setItem("theme", theme);
        window.location.replace("/login");
      }
      logTokenEvent("createResponseInterceptorAuth/isValidRefresh/after", {
        isValidRefresh,
      });
      const isLogin = location.pathname === "/login";
      if (!isLogin) {
        if (err?.response?.status === 401 || err?.response?.status === 403) {
          // Добавлена проверка флага и повторение запроса
          if (!tokenUpdatePromise) {
            try {
              await updateToken();
              // Повторяем оригинальный запрос с обновленным токеном
              const newToken = getToken();
              if (newToken) {
                err.config.headers["Authorization"] = newToken;
                return instance.request(err.config);
              }
            } catch (refreshError) {
              // Если обновление токена не удалось, продолжаем обработку ошибки
              logTokenEvent("createResponseInterceptorAuth/catch", {
                refreshToken: maskToken(refreshToken),
                error: refreshError instanceof Error ? refreshError.message : String(refreshError),
              });
            }
          }
        }
      }

      const getErrorBlob = async () => {
        const jsonPromise =
          typeof err.response.data === "string" ? null : err?.response?.data.text ? await err?.response?.data?.text() : JSON.stringify({ error: "Неизвестная ошибка" });
        return jsonPromise ? JSON.parse(jsonPromise)?.error : "Сервис не доступен"; // "Для пользователя не назначена ни одна роль в Системе" "Неизвестная ошибка"
      };

      const errorDescription = err.response.data.error ? err.response.data.error : await getErrorBlob();

      if (err.response.status === 504) {
        stores.notificationStore.addNotification({
          title: "504",
          description: "Время ожидания шлюза истекло",
          icon: "error",
          type: "error",
        });
        return Promise.reject();
      } else if (err.response.status === 404) {
        stores.notificationStore.addNotification({
          title: "Предупреждение",
          description: errorDescription,
          icon: "warning",
          type: "warning",
        });
        return Promise.reject();
      } else if (err.response.status === 423) {
        stores.notificationStore.addNotification({
          title: "Предупреждение",
          description: errorDescription,
          icon: "warning",
          type: "warning",
        });
        return Promise.reject();
      } else if (err.response.status === 400) {
        stores.notificationStore.addNotification({
          title: "Предупреждение",
          description: errorDescription,
          icon: "warning",
          type: "warning",
        });
        return Promise.reject({ code: 400, message: "Предупреждение", data: errorDescription });
      } else if (err.response.status === 502) {
        if (isLogin) {
          stores.notificationStore.addNotification({
            title: "Ошибка",
            description: errorDescription,
            icon: "error",
            type: "error",
          });
        }
        return;
      } else if (err.response.status !== 429) {
        stores.notificationStore.addNotification({
          title: "Ошибка",
          description: errorDescription,
          icon: "error",
          type: "error",
        });
        return Promise.reject();
      }

      if (err.response.status === 429) {
        const RetryAfter = err.response.headers["retry-after"];
        const time = RetryAfter ? Number(RetryAfter) * 1000 : 10000;
        return Promise.reject({ code: 429, time });
      } else if (err.response.status === 404) {
        return Promise.reject({ code: 404 });
      } else if (err.response.status === 502) {
        return Promise.reject({ code: 502, message: "Сервис не доступен", data: "Сервис не доступен" });
      } else if (err.response.status === 500) {
        return Promise.reject(new ServerError({ ...err.response.data, responseStatusCode: err.response.status }));
      } else if (err.response.status === 403) {
        return Promise.reject(new ForbiddenError({ ...err.response.data, responseStatusCode: err.response.status }));
      } else {
        return Promise.reject(new ResponseError({ ...err.response.data, responseStatusCode: err.response.status })); // Любая другая ошибка
      }
    }
  );
};

const axiosInstance = createMainAxiosInstance();

createRequestInterceptorAuth(axiosInstance);
createResponseInterceptorAuth(axiosInstance);

export { axiosInstance };
