import { createGlobalStyle } from "styled-components";
import { primaryFont } from "./typography";
import { normalize } from "polished";
import "assets/fonts/styles.css";

export const GlobalStyle = createGlobalStyle`
  ${normalize()}
  html{
    font-size: 0.8rem; //14px
    box-sizing: border-box;
  }
  
  *, *:before, *:after{
    box-sizing: inherit;
  }
  
  body{
    margin:0;
    font-family: ${primaryFont};
  }
  #root{
    width: 100vw;
    height: 100vh;
  }
  
  main{
    width: 90%;
    margin: 0 auto;
  }
  
  input[type='number'] {
    -moz-appearance:textfield;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }

  input[readonly] {
    background-color:#f6f6f6;
  }

  &:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 1000px  ${(p) => p.theme.backgroundColorSecondary} inset;
    -webkit-text-fill-color: ${(p) => p.theme.textColor}
  }

  /* удаление иконки очистки поля в IE */
  input[type="text"]::-ms-clear {
    display: none;
  }

  /* удаление иконки отображения пароля в IE */
  input[type="password"]::-ms-reveal {
    display: none;
  }

  ::-webkit-scrollbar {
    width: 9px; /* ширина scrollbar */
    height: 9px;
  }
  ::-webkit-scrollbar-track {
    background:${(p: any) => p.theme.backgroundColor}; /* цвет дорожки */ //edf5ff
  }
  ::-webkit-scrollbar-thumb {
    background-color: ${(p: any) => p.theme.gray}; /* цвет плашки */
    border-radius: 7px; /* закругления плашки */
    width: 7px;
    //border: 3px solid orange;  /* padding вокруг плашки */
  }
  mark{
    font-weight: bold;
  }

  .MuiDateRangeCalendar-root{
    button{
      font-family: ${primaryFont} !important;
    }
  }
  .MuiDateRangeCalendar-root > div:first-child {
    display: none;
  }
  .Mui-selected{
    background-color: ${(p) => p.theme.primaryColor} !important;
  }
`;
