import { generateUUID } from "../helpers/GenerationUUID";

export const prepareRows = (data: any, parentId: any) => {
  let result: any[] = [];
  data.map((el: any) => {
    let childs = [];
    if (el?.childs?.length > 0) {
      childs = prepareRows(el?.childs, el.tabId);
    }
    result.push({ ...el, parentId });
    if (childs?.length > 0) {
      childs.map((item) => {
        result.push({ ...item });
      });
    }
  });
  return result;
};

export const prepareDataTable = (rows: any[]) => {
  return prepareRows(rows, 0).map((el, index) => {
    const uuid = generateUUID();
    const obj = {
      ...el,
      tabId: el.tabId ? String(el.tabId) : `${uuid}`,
      parentId: el.parentId ? String(el.parentId) : ``,
      // id: el.id ? String(el.id) : `${uuid}`,
    };
    // delete obj["childs"];
    return obj;
  });
};
