export const getTokens = () => {
  return {
    refreshToken: window.localStorage.getItem("refreshToken"),
    token: window.localStorage.getItem("token"),
  };
};

export const setTokens = ({ refreshToken, token }: { refreshToken: string; token: string }): void => {
  localStorage.setItem("refreshToken", refreshToken);
  localStorage.setItem("token", token);
  setTokenReceiptTime();
};

export const setTokenReceiptTime = () => {
  // Время когда был получен токен
  const timestamp = Math.floor(Date.now() / 1000);
  window.localStorage.setItem("tokenReceiptTime", String(timestamp));
  //Время когда записан токен
  const tokenTimeRec = new Date().getTime();
  window.localStorage.setItem("tokenTimeRec", String(tokenTimeRec));
};

export const getTokenReceiptTime = () => {
  const tokenReceiptTime = window.localStorage.getItem("tokenReceiptTime");
  return tokenReceiptTime ? +tokenReceiptTime : 0;
};

export const removeTokenReceiptTime = () => {
  window.localStorage.removeItem("tokenReceiptTime");
};

export const removeTokens = () => {
  window.localStorage.removeItem("refreshToken");
  window.localStorage.removeItem("token");
};

export const getToken = () => {
  const { token } = getTokens();
  return token;
};
export const getRefreshToken = () => {
  const { refreshToken } = getTokens();
  return refreshToken;
};

// export const setRememberProperty = (status = false) => {
//   window.localStorage.setItem("isRemember", String(status));
// };
//
// export const getRememberProperty = () => {
//   const isRemember = window.localStorage.getItem("isRemember"); // "true" || "false"  (string)
//   return isRemember === "true";
// };

//////////////////

export const setLogin = (login: string) => {
  window.localStorage.setItem("login", login);
};
// export const getLogin = () => {
//   return window.localStorage.getItem("login");
// };
export const removeLogin = () => {
  window.localStorage.removeItem("login");
};

export const getPolicies = (): string[] => {
  // Получить список прав
  const policies: string | null = window.localStorage.getItem("policies");
  if (policies) {
    return JSON.parse(policies);
  } else {
    return [];
  }
};

// export const setPolicies = (policies: string[]) => {
//   window.localStorage.setItem("policies", JSON.stringify(policies));
// };

export const removePolicies = () => {
  window.localStorage.removeItem("policies");
};
export const clearStorage = () => {
  removePolicies();
  removeLogin();
  removeTokens();
  removeTokenReceiptTime();
};
