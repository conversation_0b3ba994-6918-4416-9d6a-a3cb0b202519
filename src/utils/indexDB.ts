// @ts-ignore
export const idb = window.indexedDB || window.mozIndexedDB || window.webKitIndexedDB || window.msIndexedDB || window.shimIndexedDB;
// Удалены выводы в консоль в связи с обращением СИБов (SRPG-2840)
export const saveParams = (id: any, data: any) => {
  const dbPromise = idb.open("table-db", 1);
  dbPromise.onsuccess = () => {
    const db = dbPromise.result;
    const tx = db.transaction("userData", "readwrite");
    const userData = tx.objectStore("userData");
    const users = userData.put({
      id,
      data,
    });
    users.onsuccess = () => {
      tx.oncomplete = () => {
        db.close();
      };
    };
    users.onerror = () => {};
  };
};

export const deleteParams = (id: any) => {
  const dbPromise = idb.open("table-db", 1);
  dbPromise.onsuccess = () => {
    const db = dbPromise.result;
    const tx = db.transaction("userData", "readwrite");
    const userData = tx.objectStore("userData");
    const deleteUsers = userData.delete(id);
    deleteUsers.onsuccess = () => {};
    deleteUsers.onerror = () => {};
  };
};

export const deleteDB = () => {
  idb.deleteDatabase("table-db");
};
