import { format } from "date-fns";

export const maskToken = (token: string | null) => {
  if (!token) return "null";
  return `...${token.slice(-8)}`;
};

export const logTokenEvent = (eventName: string, details: Record<string, any>) => {
  try {
    const history = JSON.parse(localStorage.getItem("token_history") || "[]");
    const newEvent = {
      eventName,
      timestamp: format(new Date(), "MM.dd.yyyy HH:mm:ss.SSS"),
      // Добавляем случайный ID для вкладки, чтобы их различать
      tabId: window.sessionStorage.getItem("tabId"),
      ...details,
    };

    // Ограничиваем размер лога, чтобы не засорять localStorage
    const newHistory = [newEvent, ...history].slice(0, 100);
    localStorage.setItem("token_history", JSON.stringify(newHistory));
  } catch {
    /* Игнорируем обработку ошибок */
  }
};

// Инициализируем ID вкладки при загрузке
if (!window.sessionStorage.getItem("tabId")) {
  window.sessionStorage.setItem("tabId", Math.random().toString(36).substring(2, 10));
}
