import { useEffect } from "react";
import { getToken } from "../utils/localStorage";
import { checkTokenTime } from "../utils/axios";

export const useStorageSync = () => {
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      // Обрабатываем только изменения токенов и логина
      if (!["token", "refreshToken", "login"].includes(event.key || "")) {
        return;
      }

      const currentPath = window.location.pathname;
      const token = getToken();
      const isValidToken = checkTokenTime(token);
      const hasValidToken = token && isValidToken;

      // Если мы на странице логина и появился валидный токен - перезагружаем
      if (currentPath === "/login" && hasValidToken) {
        window.location.reload();
        return;
      }

      // Если мы НЕ на странице логина и токен исчез или стал невалидным - на логин
      if (currentPath !== "/login" && !hasValidToken && event.key === "token") {
        window.location.replace("/login");
        return;
      }

      // Если изменился логин и мы авторизованы - перезагружаем для переинициализации
      if (event.key === "login" && hasValidToken) {
        const oldLogin = event.oldValue;
        const newLogin = event.newValue;

        if (oldLogin && newLogin && oldLogin !== newLogin) {
          window.location.reload();
        }
      }
    };

    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);
};
