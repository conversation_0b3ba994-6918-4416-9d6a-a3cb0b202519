import { axiosInstance as api } from "utils/axios";

export const getRoles = () => {
  return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/administration/roles/with-system-roles`);
};

export const getGroups = () => {
  return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/administration/ad/groups/unlinked`);
};

export const getAd = () => {
  return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/administration/ad`);
};

export const saveGroups = (dnGroups: any, role: any) => {
  return api.post<{}, {}>(` /srpg-control/api/v1/administration/ad/role/${role}`, { dnGroups });
};

export const deleteObjects = (adRoleId: any) => {
  return api.delete<{}, {}>(`/srpg-control/api/v1/administration/ad`, { data: { adRoleId } });
};

export const editObjects = (obj: any) => {
  return api.put<{}, {}>(`/srpg-control/api/v1/administration/ad`, { ...obj });
};

export interface IGetGroupInfoByRoleResponseGroup {
  adGroup: string;
  dnGroup: string;
  users: string[];
  groups: IGetGroupInfoByRoleResponseGroup[] | null;
}

interface IGetGroupInfoByRoleResponse {
  roleName: string;
  roleCode: string;
  groups: IGetGroupInfoByRoleResponseGroup[] | null;
}

export const getGroupInfoByRole = (role: string): Promise<IGetGroupInfoByRoleResponse> => api.get(`/srpg-control/api/v1/administration/ad/role/${role}/group-info`);
