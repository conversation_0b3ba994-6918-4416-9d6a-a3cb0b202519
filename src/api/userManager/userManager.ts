import { axiosInstance as api } from "utils/axios";
import { isModeCenter } from "../../utils/getMode";

export const login = (props: { login: string; password: string; toRemember: boolean }) => {
  const { login, password, toRemember } = props;
  return api.post<{}, { token: string; refreshToken: string }>(`/srpg-control/api/v1/auth/login`, { login, password, toRemember });
};

export const logout = (refreshToken: any) => {
  return api.post<{}, {}>(`/srpg-control/api/v1/auth/logout`, { refreshToken });
};

export const getUserInformation = () => {
  return api.get<{}, any>(`/srpg-control/api/v1/user/info`, {});
};

export const getNotifications = (isCenter: any) => {
  if (isModeCenter && isCenter) {
    return api.get<{}, { events: any[] }>(`/srpg-center/api/v1/notification/updates`);
  }
  return api.get<{}, { events: any[] }>(`/srpg-control/api/v1/notification/updates`);
};
