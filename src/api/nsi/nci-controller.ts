import { axiosInstance as api } from "utils/axios";
import {
  checkTaskEssProps,
  createTaskEssProps,
  getDateComparisonProps,
  getRegistryDCProps,
  getErrorsTaskEssProps,
  getRegistriesProps,
  getSrpgsProps,
  genericTypesArray,
} from "./nci-controller.types";
import axios from "axios";
const CancelToken = axios.CancelToken;
export const sourceDepartments = CancelToken.source();
import { isModeCenter } from "../../utils/getMode";

export const createTaskFile = (file: any) => {
  return api.post<{}, createTaskEssProps>(`/srpg-center/api/v1/nsi/import/xml`, file, { headers: { "Content-Type": "multipart/form-data" } });
};

export const createTaskEss = (date: string) => {
  return api.post<{}, createTaskEssProps>(`/srpg-center/api/v1/nsi/import`, { date });
};

export const publishTaskEss = (taskId: string, controlIds: any[]) => {
  return api.post(`/srpg-center/api/v1/nsi/import/publish`, { taskId, depIds: controlIds });
};

export const cancelTaskEss = (taskId: string) => {
  return api.post(`/srpg-center/api/v1/nsi/import/cancel`, { taskId });
};

export const checkTaskEss = (taskId: string) => {
  return api.post<{}, checkTaskEssProps>(`/srpg-center/api/v1/nsi/import/info`, { taskId });
};

export const checkStatusInitLoading = (taskId: string) => {
  return api.post<{}, { initiator: any; name: any }>(`/srpg-center/api/v1/nsi/import/initiator-info`, { taskId });
};

export const checkStatusDistLoading = (taskId: string) => {
  return api.post<{}, { initiator: any; name: any }>(`/srpg-center/api/v1/nsi/distribution/initiator-info`, { taskId });
};

export const getLastPublish = (isCenter: boolean) => {
  if (isModeCenter && isCenter) {
    return api.get<{}, checkTaskEssProps>(`/srpg-center/api/v1/nsi/import/last-publish`);
  }
  return api.get<{}, checkTaskEssProps>(`/srpg-control/api/v1/nsi/last-publish`);
};

export const getErrorsTaskEss = (taskId: string) => {
  return api.post<{}, getErrorsTaskEssProps>(`srpg-center/api/v1/nsi/import/errors`, { taskId });
};

export const getDepartments = ({ date, isCenter }: { date: string; isCenter: boolean }) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, getRegistryDCProps>(
      `/srpg-center/api/v1/nsi/departments`,
      { date },
      {
        cancelToken: sourceDepartments.token,
      }
    );
  }
  return api.post<{}, getRegistryDCProps>(
    `/srpg-control/api/v1/nsi/departments`,
    { date },
    {
      cancelToken: sourceDepartments.token,
    }
  );
};

export const getModalMap = (type: any, objectPost: any, isCenter: boolean) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { departments: any }>(`/srpg-center/api/v1/nsi/${type}/find-obj-departments`, objectPost);
  }
  return api.post<{}, { departments: any }>(`/srpg-control/api/v1/nsi/${type}/find-obj-departments`, objectPost);
};

export const getRegistryDC = ({ date, isCenter }: { date: string; isCenter: boolean }) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, getRegistryDCProps>(`/srpg-center/api/v1/nsi/departments`, { date });
  }
  return api.post<{}, getRegistryDCProps>(`/srpg-control/api/v1/nsi/departments`, { date });
};

export const getRegistries = ({ date, selectDc, isCenter }: { date: string; selectDc: string; isCenter: boolean }) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, getRegistriesProps>(`/srpg-center/api/v1/nsi/gous/dc/${selectDc}`, { date });
  }
  return api.post<{}, getRegistriesProps>(`/srpg-control/api/v1/nsi/gous/dc/${selectDc}`, { date });
};

export const getSrpgs = ({ date, selectDc, isCenter }: { date: string; selectDc: string; isCenter: boolean }) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, getSrpgsProps>(`/srpg-center/api/v1/nsi/srpgs/dc/${selectDc}`, { date });
  }
  return api.post<{}, getSrpgsProps>(`/srpg-control/api/v1/nsi/srpgs/dc/${selectDc}`, { date });
};

export const getAllSrpgs = ({ date, isCenter }: { isCenter: boolean; date: any }) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, getSrpgsProps>(`/srpg-center/api/v1/nsi/srpgs`, { date });
  }
  return api.post<{}, getSrpgsProps>(`/srpg-control/api/v1/nsi/srpgs`, { date });
};

export const getAdders = ({ date, selectDc, isCenter }: { date: string; selectDc: string; isCenter: boolean }) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: any[] }>(`/srpg-center/api/v1/nsi/adders/dc/${selectDc}`, { date });
  }
  return api.post<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/adders/dc/${selectDc}`, { date });
};

export const getDateComparison = ({ date1, date2, isCenter }: { date1: string; date2: string; isCenter: boolean }) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, getDateComparisonProps>(`/srpg-center/api/v1/nsi/compare`, { date1, date2 });
  }
  return api.post<{}, getDateComparisonProps>(`/srpg-control/api/v1/nsi/compare`, { date1, date2 });
};

export const getProtocolChanges = (taskId: string) => {
  return api.post<{}, getDateComparisonProps>(`/srpg-center/api/v1/nsi/import/changes`, { taskId });
};

export const getLastTaskDistribution = () => {
  return api.get<{}, { taskId: string }>(`/srpg-center/api/v1/nsi/distribution/last-task`);
};

export const getStatusDistribution = (taskId: string) => {
  return api.post<{}, { item: any }>(`/srpg-center/api/v1/nsi/distribution/status`, { taskId });
};

export const getLastTaskId = () => {
  return api.get<{}, { lastTaskId: any; distributedInfo: any; downloadInfo: any }>(`/srpg-center/api/v1/nsi/last-task`);
};

export const restartDistribution = (taskId: string) => {
  return api.post(`/srpg-center/api/v1/nsi/distribution/restart`, { taskId });
};

export const getInfoSync = (isCenter: boolean) => {
  if (isModeCenter && isCenter) {
    return api.get<{}, { items: any[] }>(`/srpg-center/api/v1/nsi/sinchronization-info`);
  }
  return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/sinchronization-info`);
};

export const getListDcAtTask = (taskId: any) => {
  return api.post<{}, { department: any[] }>(`/srpg-center/api/v1/nsi/import/departments`, { taskId });
};

export const getStatusAllDC = (taskId: any) => {
  return api.post<{}, { status: string; info: string; startedAt: string; finishedAt: string }>(`/srpg-center/api/v1/nsi/distribution/general-status`, { taskId });
};

export const getGouListIA = (date: string, isCenter: any, type: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: any[] }>(`/srpg-center/api/v1/nsi/collation/gous-ea`, { date });
  }
  return api.post<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/gous`, { date, type });
};

export const getGouListIAMatched = (date: string, isCenter: any, type: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: any[] }>(`/srpg-center/api/v1/nsi/collation/gous/id11`, { date });
  }
  return api.post<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/gous/ck11`, { date, type });
};

export const getGouListIANotMatched = (date: string, isCenter: any, type: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: any[] }>(`/srpg-center/api/v1/nsi/collation/gous/no-id11`, { date });
  }
  return api.post<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/gous/no-ck11`, { date, type });
};

export const getSRPGListIA = (date: string, type: any) => {
  return api.post<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/srpg`, { date, type });
};

export const getSRPGListIAMatched = (date: string, type: any) => {
  return api.post<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/srpg/ck11`, { date, type });
};

export const getSRPGListIANotMatched = (date: string, type: any) => {
  return api.post<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/srpg/no-ck11`, { date, type });
};

export const getSummatorsListIA = (date: string, type: any) => {
  return api.post<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/adders`, { date, type });
};

export const getSummatorsListIAMatched = (date: string, type: any) => {
  return api.post<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/adders/id11`, { date, type });
};

export const getSummatorsListIANotMatched = (date: string, type: any) => {
  return api.post<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/adders/no-id11`, { date, type });
};

export const getSK11 = () => {
  return api.get<{}, { items: any[] }>(`/srpg-center/api/v1/nsi/collation/ck11/gous`);
};

export const getSK11Gou = () => {
  return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/ck11/gou`);
};

export const saveConnections = (items: any[], isCenter: any, operation: null | "WRITE" | "REPLACE") => {
  //REPLACE отвязать все | WRITE записать один ко многим
  if (isModeCenter && isCenter) {
    return api.post<{}, { duplicates: any[]; duplicatesFromRequest: any[] }>(`/srpg-center/api/v1/nsi/collation/save/id11`, { items, operation });
  }
  return api.post<{}, { duplicates: any[]; duplicatesFromRequest: any[] }>(`/srpg-control/api/v1/nsi/collation/save/id11`, { items, operation });
};

export const saveConnectionsSrpg = (items: any[], type: any) => {
  return api.post<{}, {}>(`/srpg-control/api/v1/nsi/collation/srpg/save/id11`, { items, type });
};

export const saveConnectionsSummators = (items: any[], type: any) => {
  return api.post<{}, {}>(`/srpg-control/api/v1/nsi/collation/adder/save/id11`, { items, type });
};

export const startDistribution = (date: string) => {
  return api.post<{}, {}>(`/srpg-center/api/v1/nsi/collation/distribution/gous/start`, { date });
};

export const getStatusDistributionProtocol = (date: string) => {
  return api.post<{}, { status: string; startedAt: string; finishedAt: string; error: string; notSentTo: string[] }>(
    `/srpg-center/api/v1/nsi/collation/distribution/gous/protocol`,
    { date }
  );
};

// !!!!

export const getTypesGou = () => {
  return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/gou/types`);
};

export const getTypesSrpg = () => {
  return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/srpg/types`);
};
export const getTypesSummator = () => {
  return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/adder/types`);
};

// !!!!

export const getSrpgSk11 = (type: any) => {
  return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/ck11/srpg/type/${type}`);
};

export const getBlockSk11 = () => {
  return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/ck11/gou/block`);
};

export const getRgeSk11 = () => {
  return api.get<{}, { rgeNoGou: any[]; rgeWithGou: any[] }>(`/srpg-control/api/v1/nsi/collation/ck11/gou/rge`);
};

export const getRgeSummator = () => {
  return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/ck11/adders`);
};

// generic

export const getTypeForSk11 = (type: any, registry: any) => {
  const reg = registry === "goy" ? "gou" : registry === "summator" ? "adder" : registry;
  const finalType = type === "goy" ? "gou" : type;
  if (reg === "gou" && finalType === "gou") {
    return api.get<{}, { items: any }>(`/srpg-control/api/v1/nsi/collation/ck11/objects/${reg}/type/GOU`);
  }
  return api.get<{}, { items: any }>(`/srpg-control/api/v1/nsi/collation/ck11/objects/${reg}/type/${finalType}`);
};

export const startSK11 = (loadType: any) => {
  return api.post<{}, { taskId: string }>(`/srpg-control/api/v1/nsi/collation/ck11/objects/tasks/load`, { loadType });
};

export const startGeneric = () => {
  return api.post<{}, { taskId: string }>(`/srpg-control/api/v1/nsi/collation/ck11/objects/tasks/load`, { loadType: "GENERIC" });
};

export const infoSK11 = (loadType: any) => {
  return api.get<{}, { status: any; activeTaskId: any; activeLoadedAt: any; activeModelVersion: any }>(
    `/srpg-control/api/v1/nsi/collation/ck11/objects/load-type/${loadType}/last-task`
  );
};

export const infoGeneric = () => {
  return api.get<{}, { status: any; activeTaskId: any; activeLoadedAt: any; activeModelVersion: any }>(
    `/srpg-control/api/v1/nsi/collation/ck11/objects/load-type/GENERIC/last-task`
  );
};

export const getSearchSK11 = (taskId: any, uid: string, name: string, loadType: any, isCenter: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: genericTypesArray[]; hasMoreElements: any }>(`/srpg-center/api/v1/nsi/collation/ck11/objects/tasks/${taskId}/search`, {
      uid,
      name,
      loadType,
    });
  }
  return api.post<{}, { items: genericTypesArray[]; hasMoreElements: any }>(`/srpg-control/api/v1/nsi/collation/ck11/objects/tasks/${taskId}/search`, {
    uid,
    name,
    loadType,
  });
};

export const getSearchPakEss = (type: any, objectPost: any, isCenter: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: any[]; hasMoreElements: any }>(`/srpg-center/api/v1/nsi/collation/${type}/search`, objectPost);
  }
  return api.post<{}, { items: any[]; hasMoreElements: any }>(`/srpg-control/api/v1/nsi/collation/${type}/search`, objectPost);
};

export const getSearchNsi = (dcId: any, selectedSegment: any, postObject: any, isCenter: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: genericTypesArray[]; hasMoreElements: any }>(`/srpg-center/api/v1/nsi/${selectedSegment}/dc/${dcId}/search`, postObject);
  }
  return api.post<{}, { items: genericTypesArray[]; hasMoreElements: any }>(`/srpg-control/api/v1/nsi/${selectedSegment}/dc/${dcId}/search`, postObject);
};

export const getSearchNsiNoSelect = (selectedSegment: any, postObject: any, isCenter: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: genericTypesArray[]; hasMoreElements: any }>(`/srpg-center/api/v1/nsi/${selectedSegment}/search`, postObject);
  }
  return api.post<{}, { items: genericTypesArray[]; hasMoreElements: any }>(`/srpg-control/api/v1/nsi/${selectedSegment}/search`, postObject);
};

export const getGenericOnSearch = (taskId: any, uid: string, name: string, isCenter: boolean) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: genericTypesArray[]; hasMoreElements: boolean }>(`/srpg-center/api/v1/nsi/collation/ck11/objects/tasks/${taskId}/search`, {
      uid,
      name,
      loadType: "GENERIC",
    });
  }
  return api.post<{}, { items: genericTypesArray[]; hasMoreElements: boolean }>(`/srpg-control/api/v1/nsi/collation/ck11/objects/tasks/${taskId}/search`, {
    uid,
    name,
    loadType: "GENERIC",
  });
};

export const getSK11OnParentId = (taskId: string, parentId: any, loadType: any, isCenter: boolean) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: genericTypesArray[] }>(`/srpg-center/api/v1/nsi/collation/ck11/objects/tasks/${taskId}/search-by-parent`, {
      parentId,
      loadType,
    });
  }
  return api.post<{}, { items: genericTypesArray[] }>(`/srpg-control/api/v1/nsi/collation/ck11/objects/tasks/${taskId}/search-by-parent`, {
    parentId,
    loadType,
  });
};

export const prepareDataConnection = (isCenter: boolean, objectPost: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: any[] }>(` /srpg-center/api/v1/nsi/collation/parse-tab-ids`, objectPost);
  }
  return api.post<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/parse-tab-ids`, objectPost);
};

export const getComparisonParentId = (type: string, objectPost: any, isCenter: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: any[] }>(`/srpg-center/api/v1/nsi/collation/${type}/search-by-parent`, objectPost);
  }
  return api.post<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/${type}/search-by-parent`, objectPost);
};

export const getGenericOnParentId = (taskId: string, parentId: any, isCenter: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: genericTypesArray[] }>(`/srpg-center/api/v1/nsi/collation/ck11/objects/tasks/${taskId}/search-by-parent`, {
      parentId,
      loadType: "GENERIC",
    });
  }
  return api.post<{}, { items: genericTypesArray[] }>(`/srpg-control/api/v1/nsi/collation/ck11/objects/tasks/${taskId}/search-by-parent`, {
    parentId,
    loadType: "GENERIC",
  });
};

export const getNsiOnParentId = (dcId: any, parentId: any, selectedSegment: any, date: any, isCenter: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: any[] }>(`/srpg-center/api/v1/nsi/${selectedSegment}/dc/${dcId}/search-by-parent`, { parentId, date });
  }
  return api.post<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/${selectedSegment}/dc/${dcId}/search-by-parent`, { parentId, date });
};

export const getNsiOnParentIdNoSelected = (parentId: any, selectedSegment: any, date: any, isCenter: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: any[] }>(`/srpg-center/api/v1/nsi/${selectedSegment}/search-by-parent`, { parentId, date });
  }
  return api.post<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/${selectedSegment}/search-by-parent`, { parentId, date });
};

export const getCalendar = (year: number | string, isCenter: boolean) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { dates: string[] }>(`/srpg-center/api/v1/nsi/available-from/dates`, { year });
  }
  return api.post<{}, { dates: string[] }>(`/srpg-control/api/v1/nsi/available-from/dates`, { year });
};

export const getCharacterByType = (type: any, id: any) => {
  return api.get<{}, { name: string; code: string; id: number; pbr: any; ppbr: any; pdg: any; per: any; subParams: any }>(
    `/srpg-control/api/v1/pg/params/type/${type}/id/${id}`
  );
};

export const pushCharactersNotConnect = (type: any, id: any, items: any[]) => {
  return api.post<{}, { name: string; code: string; id: number; params: any[] }>(`/srpg-control/api/v1/pg/params/type/${type}/id/${id}/no-send`, { items });
};

export const deleteCharacteristics = (type: any, opamCode: any) => {
  return api.delete<{}, {}>(`/srpg-center/api/v1/pg/params/type/${type}`, { data: { opamCode } });
};

export const deleteSubs = (id: any) => {
  return api.delete<{}, {}>(`/srpg-center/api/v1/pg/params/subs/${id}`);
};

export const getListCharacteristics = (isCenter: boolean) => {
  if (isModeCenter && isCenter) {
    return api.get<{}, { items: any[] }>(`/srpg-center/api/v1/pg/params`);
  }
  return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/pg/params`);
};

export const getUnrelated = (type: any) => {
  return api.get<{}, { items: any[] }>(`/srpg-center/api/v1/pg/params/type/${type}/unrelated`);
};

export const saveCharacteristics = (type: any, items: any[]) => {
  return api.post<{}, { items: any[] }>(`/srpg-center/api/v1/pg/params/type/${type}`, { items });
};

export const editCharacteristics = (type: any, items: any[]) => {
  return api.put<{}, {}>(`/srpg-center/api/v1/pg/params/type/${type}`, { items });
};

export const startDistributionCharacteristics = () => {
  return api.post<{}, {}>(`/srpg-center/api/v1/pg/params/distribution/start`);
};

export const getProtocolDistributionCharacteristics = () => {
  return api.get<{}, { status: "SENDING" | "NOT_FULLY_SENT" | "ERROR" | "DONE"; startedAt: string; finishedAt: string; error: string; notSentTo: string[] }>(
    `/srpg-center/api/v1/pg/params/distribution/protocol`
  );
};

export const getProtocolErrorsXLS = (taskId: any) => {
  return api.post<{}, {}>(`/srpg-center/api/v1/nsi/import/errors/xls`, { taskId }, { responseType: "blob" });
};

export const getProtocolChangesXLS = (taskId: any) => {
  return api.post<{}, {}>(`/srpg-center/api/v1/nsi/import/changes/xls`, { taskId }, { responseType: "blob" });
};

export const getProtocolComparisonXLS = (date: any, isCenter: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { dates: any[] }>(`/srpg-center/api/v1/nsi/compare/xls`, date, { responseType: "blob" });
  }
  return api.post<{}, { dates: any[] }>(`/srpg-control/api/v1/nsi/compare/xls`, date, { responseType: "blob" });
};

export const getProtocolComparisonCSV = (objectPost: any, isCenter: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { dates: any[] }>(`/srpg-center/api/v1/nsi/collation/gous/csv`, objectPost, { responseType: "blob" });
  }
  return api.post<{}, { dates: any[] }>(` /srpg-control/api/v1/nsi/collation/csv`, objectPost, { responseType: "blob" });
};

export const getProtocolComparisonCSVFilter = (objectType: any, registryType: any, date: any, isCenter: any, reportKind: string) => {
  if (isModeCenter && isCenter) {
    return api.get<{}, { dates: any[] }>(`/srpg-center/api/v1/nsi/collation/${objectType}/${registryType}/${reportKind}/csv?date=${date}`, { responseType: "blob" });
  }
  return api.get<{}, { dates: any[] }>(`/srpg-control/api/v1/nsi/collation/${objectType}/${registryType}/${reportKind}/csv?date=${date}`, { responseType: "blob" });
};

export const getSearchForMeasurementsLastTaskId = () => {
  return api.get<{}, { taskId: any }>(`/srpg-control/api/v1/nsi/collation/ck11/check-params/last-task`);
};

export const startSearchForMeasurements = () => {
  return api.post<{}, { taskId?: any }>(`/srpg-control/api/v1/nsi/collation/ck11/check-params/start`);
};

export const getSearchForMeasurements = (taskId: any) => {
  return api.get<{}, { status?: any; startedAt?: any; finishedAt?: any; error?: any }>(`/srpg-control/api/v1/nsi/collation/ck11/check-params/task/${taskId}`);
};

export const getSearchForMeasurementsXML = (taskId: any) => {
  return api.get<{}, {}>(`/srpg-control/api/v1/nsi/collation/ck11/check-params/task/${taskId}/download-xml`);
};

export const getDuplicateById = (uuid: any, date: any, isCenter: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: any[]; nameCk11: any }>(`/srpg-center/api/v1/nsi/collation/uuid/${uuid}`, { date });
  }
  return api.post<{}, { items: any[]; nameCk11: any }>(`/srpg-control/api/v1/nsi/collation/uuid/${uuid}`, { date });
};

export const getMultipleConnections = (isCenter: any, objectPost: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: any[] }>(`/srpg-center/api/v1/nsi/collation/multiple-link`, objectPost);
  }
  return api.post<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/multiple-link`, objectPost);
};

export const exportXlsMultipleConnections = (isCenter: any, objectPost: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { items: any[] }>(`/srpg-center/api/v1/nsi/collation/multiple-link/xls`, objectPost, { responseType: "blob" });
  }
  return api.post<{}, { items: any[] }>(`/srpg-control/api/v1/nsi/collation/multiple-link/xls`, objectPost, { responseType: "blob" });
};

export const startTryMultipleConnections = (isCenter: any, objectPost: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { taskId: any }>(`/srpg-center/api/v1/nsi/collation/multiple-link/validation/task/start`, objectPost);
  }
  return api.post<{}, { taskId: any }>(`/srpg-control/api/v1/nsi/collation/multiple-link/validation/task/start`, objectPost);
};

export const getMultipleById = (isCenter: any, objectPost: any) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, { essName: any; essParents: any }>(`/srpg-center/api/v1/nsi/collation/multiple-link/load-ess`, objectPost);
  }
  return api.post<{}, { essName: any; essParents: any }>(`/srpg-control/api/v1/nsi/collation/multiple-link/load-ess`, objectPost);
};

export const getTryMultipleConnections = (isCenter: any, taskId: any) => {
  if (isModeCenter && isCenter) {
    return api.get<{}, { status: any; startedAt: any; finishedAt: any; error: any; result: any }>(
      `/srpg-center/api/v1/nsi/collation/multiple-link/validation/task/${taskId}`
    );
  }
  return api.get<{}, { status: any; startedAt: any; finishedAt: any; error: any; result: any }>(
    `/srpg-control/api/v1/nsi/collation/multiple-link/validation/task/${taskId}`
  );
};

export const saveSubs = (objectPost: any) => {
  return api.post<{}>(`/srpg-center/api/v1/pg/params/subs`, objectPost);
};

export const editSubs = (objectPost: any, id: any) => {
  return api.put<{}>(`/srpg-center/api/v1/pg/params/subs/${id}`, objectPost);
};

export const getListSubs = (objectPost: any) => {
  return api.post<{}, { items: any }>(`/srpg-center/api/v1/pg/params/subs/filtered`, objectPost);
};

export const saveParamsSubs = (subId: any, type: any, id: any) => {
  return api.post<{}, { items: any }>(`/srpg-control/api/v1/pg/params/sub/${subId}/type/${type}/id/${id}`, {});
};

//

export const getListSubsWithParams = (type: any, isCenter: any) => {
  if (isModeCenter && isCenter) {
    return api.get<{}, { items: any }>(`/srpg-center/api/v1/pg/params/type/${type}/subs-with-params`);
  }
  return api.get<{}, { items: any }>(`/srpg-control/api/v1/pg/params/type/${type}/subs-with-params`);
};

export const getListParamsByObject = (type: any) => {
  return api.get<{}, { items: any }>(`/srpg-center/api/v1/pg/params/type/${type}`);
};

export const saveTypesWithParams = (type: any, items: any) => {
  return api.post<{}, { items: any }>(`/srpg-center/api/v1/pg/params/type/${type}/subs-with-params`, { items });
};

export const getSubsForObject = (type: any, id: any) => {
  return api.get<{}, { names: any }>(`/srpg-control/api/v1/pg/params/type/${type}/id/${id}/subtypes`);
};

export const saveSubsForObject = (type: any, id: any, name: any) => {
  return api.put<{}, { names: any }>(`/srpg-control/api/v1/pg/params/type/${type}/id/${id}/subtypes`, { name });
};
