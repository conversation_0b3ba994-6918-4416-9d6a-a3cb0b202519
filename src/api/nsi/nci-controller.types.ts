export interface createTaskEssProps {
  taskId: string;
}

export interface checkTaskEssProps {
  error: string;
  status: string;
  loadedAt: string;
  availableFrom: string;
  publishedAt: string;
}

export interface getErrorsTaskEssProps {
  duplication: string[];
  errorGou: string[];
  errorKartaVed: string[];
}

export interface getRegistryDCProps {
  department: any;
}

export interface getRegistriesProps {
  items: any[];
}

export interface getSrpgsProps {
  archmGroups: any[];
  areas: any[];
  area2s: any[];
  consumers: any[];
  nBlocks: any[];
  nGroups: any[];
  powerSystems: any[];
  rges: any[];
  sechens: any[];
  vetvs: any[];
  wSums: any[];
}

export interface getDateComparisonProps {
  addEnties: any[];
  deleteEntries: any[];
  changeEntries: any[];
}

export interface genericTypesArray {
  hasChildren: boolean;
  id11: string;
  isTarget: boolean;
  name: string;
}
