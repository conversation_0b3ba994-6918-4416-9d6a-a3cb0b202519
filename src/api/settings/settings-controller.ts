import { axiosInstance as api } from "utils/axios";
import { isModeCenter, isEast } from "utils/getMode";

interface getListPGProps {
  items: any;
}

interface getTableDataProps {
  types: any;
}

interface Notification {
  title: string;
  kind: string;
  selected: boolean;
}

interface NotificationGroup {
  order: number;
  title: string;
  mails: Notification[];
}

interface NotificationsResponse {
  groups: NotificationGroup[];
  rid: string;
  timestamp: string;
}

type NotificationsPayload = Notification["kind"][];

export const getListPG = () => {
  return api.get<{}, getListPGProps>(`/srpg-control/api/v1/pg/oik/types`);
};

export const updatePG = (items: any[]) => {
  return api.put<{}, {}>(`/srpg-center/api/v1/pg/parameters/pbr`, { items });
};

export const updateUDDG = (items: any[]) => {
  return api.put<{}, {}>(`/srpg-center/api/v1/pg/parameters/uddg`, { items });
};

export const getTableData = (pgType: any) => {
  return api.get<{}, getTableDataProps>(`/srpg-control/api/v1/pg/oik/types/${pgType}/params`);
};

export const saveTableData = (pgType: any, data: any) => {
  return api.post<{}, {}>(`/srpg-control/api/v1/pg/oik/types/${pgType}/params`, data);
};

export const getLoadPG = () => {
  return api.get<{}, { items: any }>(`/srpg-center/api/v1/pg/download-params`);
};

export const getTypeAccept = () => {
  return api.get<{}, { items: any }>(`/srpg-center/api/v1/pg/accept-types`);
};

export const saveLoadPG = (items: any[]) => {
  return api.put<{}, {}>(`/srpg-center/api/v1/pg/download-params`, { items });
};

export const loadStorageDepth = (isCenter: any) => {
  if (isModeCenter && isCenter) {
    return api.get<{}, { items: { name: string; months: string }[] }>(`/srpg-center/api/v1/settings/storage-depth`);
  } else {
    return api.get<{}, { items: { name: string; months: string }[] }>(`/srpg-control/api/v1/settings/storage-depth`);
  }
};

export const saveStorageDepth = (items: any, isCenter: boolean) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, {}>(`/srpg-center/api/v1/settings/storage-depth`, {
      items,
    });
  } else {
    return api.post<{}, {}>(`/srpg-control/api/v1/settings/storage-depth`, {
      items,
    });
  }
};

export const getNotifications = () => {
  const zoneParam = isEast ? "SECOND" : "FIRST";
  return api.get<{}, NotificationsResponse>(`/srpg-control/api/v1/notification/mails?zone=${zoneParam}`);
};

export const saveNotifications = (items: NotificationsPayload) => {
  return api.post<{}, {}>(`/srpg-control/api/v1/notification/mails`, {
    items,
  });
};

export const getListNoValue = (isCenter: boolean) => {
  if (isModeCenter && isCenter) {
    return api.get<{}, { items: any }>(`/srpg-center/api/v1/pg/params/no-value`);
  }
  return api.get<{}, { items: any }>(`/srpg-control/api/v1/pg/params/no-value`);
};

export const getListNoValueCodes = () => {
  return api.get<{}, { items: any }>(`/srpg-center/api/v1/pg/params/no-value/types`);
};

export const addNoValue = (items: any) => {
  return api.post<{}, { items: any }>(`/srpg-center/api/v1/pg/params/no-value`, { items });
};

export const editNoValue = (items: any) => {
  return api.put<{}, { items: any }>(`/srpg-center/api/v1/pg/params/no-value`, { items });
};

export const deleteNoValue = (items: any) => {
  return api.delete<{}, {}>(`/srpg-center/api/v1/pg/params/no-value`, {
    data: { items },
  });
};

export const getInformationExternalSystem = (isCenter: any) => {
  return api.get<{}, { names: any[]; systems: any[] }>(`/configuration/api/v1/settings/ext-system/${isModeCenter && isCenter ? "center" : "control"}`);
};

export const saveExternalSystem = (objectPOST: any) => {
  return api.put<{}, {}>(`/configuration/api/v1/settings/ext-system`, objectPOST);
};

export const testExternalSystem = (objectPOST: any) => {
  return api.put<{}, { results: any[]; services: any[] }>(`/configuration/api/v1/settings/ext-system/test`, objectPOST);
};

export const getMODES = (isModeCenter: any) => {
  if (isModeCenter) {
    return api.get<{}, { items: any[] }>(`/srpg-center/api/v1/pg/params/modes`);
  } else {
    return api.get<{}, { items: any[] }>(`/srpg-control/api/v1/pg/params/modes`);
  }
};

export const saveMODES = (types: any[], params: any[]) => {
  return api.put<{}, { items: any[] }>(`/srpg-center/api/v1/pg/params/modes`, {
    types,
    params,
  });
};

export interface IGetPgTypeListResponseItem {
  type: string;
  name: string;
  actionUid: string;
  endUid: string;
  noUid: string;
  startUid: string;
}
interface IGetPgTypeListResponse {
  items: IGetPgTypeListResponseItem[];
}

export const getPgTypeList = (): Promise<IGetPgTypeListResponse> => api.get(`/srpg-control/api/v1/pg/types/info-stored`);

export const updatePgTypeList = (items: Omit<IGetPgTypeListResponseItem, "name">) => api.put("/srpg-control/api/v1/pg/types/info-stored", { items });
