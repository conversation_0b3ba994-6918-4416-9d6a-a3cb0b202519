import { axiosInstance as api } from "utils/axios";
import { isModeCenter } from "utils/getMode";

interface getUsersJournalProps {
  items: any[];
  isOverfilled: boolean;
}

export const getUsersJournal = (fromDate: any, toDate: any, isCenter: boolean) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, getUsersJournalProps>(`/srpg-center/api/v1/logging/user-activity`, { fromDate, toDate });
  }
  return api.post<{}, getUsersJournalProps>(`/srpg-control/api/v1/logging/user-activity`, { fromDate, toDate });
};

export const exportXLSUsersJournal = (fromDate: any, toDate: any, isCenter: boolean) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, getUsersJournalProps>(`/srpg-center/api/v1/logging/user-activity/xls`, { fromDate, toDate }, { responseType: "blob" });
  }
  return api.post<{}, getUsersJournalProps>(`/srpg-control/api/v1/logging/user-activity/xls`, { fromDate, toDate }, { responseType: "blob" });
};

export const getExternalSystems = (fromDate: any, toDate: any, isCenter: boolean) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, getUsersJournalProps>(`/srpg-center/api/v1/logging/external-system`, { fromDate, toDate });
  }
  return api.post<{}, getUsersJournalProps>(`/srpg-control/api/v1/logging/external-system`, { fromDate, toDate });
};

export const exportXLSExternalSystems = (fromDate: any, toDate: any, isCenter: boolean) => {
  if (isModeCenter && isCenter) {
    return api.post<{}, getUsersJournalProps>(`/srpg-center/api/v1/logging/external-system/xls`, { fromDate, toDate }, { responseType: "blob" });
  }
  return api.post<{}, getUsersJournalProps>(`/srpg-control/api/v1/logging/external-system/xls`, { fromDate, toDate }, { responseType: "blob" });
};
