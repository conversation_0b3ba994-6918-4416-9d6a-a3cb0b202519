import React, { useEffect, useMemo, useState } from "react";
import styled from "styled-components";
import { ListOfUploadedPG } from "./components/ListOfUploadedPG";
import { useStores } from "stores/useStore";
import queryString from "query-string";
import { ListOfDC } from "./components/ListOfDC";
import { observer } from "mobx-react";
import { typesSRPG } from "../../../Nsi/Nsi";
import { Table } from "components/Table";

export const Container = styled.div`
  display: flex;
  height: 85vh;
  width: 100%;
  color: ${(p) => p.theme.textColor};
  margin: 10px 0;
  box-shadow: 0 12px 16px -4px rgb(16 24 40 / 10%), 0px 4px 6px -2px rgb(16 24 40 / 5%);
  border-radius: 8px;
  flex-direction: column;
`;

const UpContainer = styled.div`
  width: 100%;
  height: 300px;
  border-bottom: solid 1px ${(p) => p.theme.lightGray};
  background-color: ${(p) => p.theme.backgroundColorSecondary};
`;

const DownContainer = styled.div`
  width: 100%;
  height: 470px;
  margin-top: 50px;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
`;

const CustomCell = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const View = observer(() => {
  const { plannedSchedulesStore } = useStores();

  const { plannedSchedules, listDC, isLoadingPlannedSchedules, isLoadingListDc } = plannedSchedulesStore;

  const [columnsPlannedSchedules] = useState([
    { name: "name", width: 400, title: "Название" },
    { name: "createdAt", width: 150, title: "Время загрузки" },
    { name: "type", width: 150, title: "Тип" },
    { name: "syncZone", width: 150, title: "СЗ" },
    { name: "source", width: 300, title: "Источник загрузки" },
  ]);

  const [columnsListOfDC] = useState([
    { name: "name", title: "Название", width: 400, isSearch: true }, //fixedColumn: true, left: "0"
    { name: "test2", title: "Получен", width: 150 },
    { name: "test3", title: "Локальный акцепт", width: 150 },
    { name: "test4", title: "Актор", width: 150 },
    { name: "test5", title: "Глобальный акцепт", width: 150 },
    { name: "test6", title: "ОИК СК-11", width: 150 },
    { name: "test7", title: "ПАК и MODES-Terminal", width: 200 },
  ]);

  const customCellsListOfDC = [
    {
      name: "test2",
      render: (value) => {
        return value ? <CustomCell>{value}</CustomCell> : <CustomCell>-</CustomCell>;
      },
    },
    {
      name: "test3",
      render: (value) => {
        return value ? <CustomCell>{value}</CustomCell> : <CustomCell>-</CustomCell>;
      },
    },
    {
      name: "test4",
      render: (value) => {
        return value ? <CustomCell>{value}</CustomCell> : <CustomCell>-</CustomCell>;
      },
    },
    {
      name: "test5",
      render: (value) => {
        return value ? <CustomCell>{value}</CustomCell> : <CustomCell>-</CustomCell>;
      },
    },
    {
      name: "test6",
      render: (value) => {
        return value ? <CustomCell>{value}</CustomCell> : <CustomCell>-</CustomCell>;
      },
    },
    {
      name: "test7",
      render: (value) => {
        return value ? <CustomCell>{value}</CustomCell> : <CustomCell>-</CustomCell>;
      },
    },
  ];

  return (
    <Container>
      <UpContainer>
        <Table
          columns={columnsPlannedSchedules}
          tableData={plannedSchedules}
          isLoading={isLoadingPlannedSchedules}
          title="Список загруженных ПГ"
          height={300}
          tableKey={1}
        />
        {/*<ListOfUploadedPG tableData={plannedSchedules} />*/}
      </UpContainer>
      <DownContainer>
        <Table columns={columnsListOfDC} tableData={listDC} isLoading={isLoadingListDc} title="Список ДЦ" height={380} tableKey={2} customCells={customCellsListOfDC} />
        {/*<ListOfDC tableData={listDC} />*/}
      </DownContainer>
    </Container>
  );
});
