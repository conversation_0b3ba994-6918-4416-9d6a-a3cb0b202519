import styled, { css } from "styled-components";
import Paper from "@mui/material/Paper";

export const IconContainer = styled.div<{ status?: string }>`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  ${(p) =>
    p.status &&
    p.status === "FAILED" &&
    css`
      color: ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.status &&
    p.status === "CREATED" &&
    css`
      color: ${(p) => p.theme.orangeActiveSupport};
    `}
  ${(p) =>
    p.status &&
    p.status === "CONFIRMED" &&
    css`
      color: ${(p) => p.theme.greenActiveSupport};
    `}
`;

export const PaperStyled = styled(Paper)`
  > div > div {
    z-index: 3;
  }

  > div > div > div > table {
    z-index: 3;
  }
`;

export const Container = styled.div`
  width: 100%;
  height: 100%;
  overflow: auto;
  display: flex;
  flex-flow: column;
  min-height: 0;
  flex-grow: 1;

  button {
    color: ${(p) => p.theme.textColor};
  }

  .MuiPaper-root {
    height: 100%;
    overflow-y: auto;
    background-color: ${(p) => p.theme.backgroundColorSecondary};
    color: ${(p) => p.theme.textColor};
    & > div {
      height: 100%;
    }
  }
  .MuiTableCell-root {
    padding: 0 4px;
    color: ${(p) => p.theme.textColor};
  }
  .MuiTableCell-head {
    font-weight: bold;
    background: ${(p) => p.theme.backgroundColorSecondary};
    border-bottom: 3px solid ${(p) => p.theme.lightGray};
    border-right: 1px solid ${(p) => p.theme.lightGray};
    color: ${(p) => p.theme.textColor};
    text-align: center;
  }
  .MuiTableBody-root {
    .MuiTableRow-root {
      &:nth-child(2n) {
        td {
          background-color: ${(p) => p.theme.backgroundColorSecondary};
        }
      }

      &:hover td {
        color: ${(p) => p.theme.textColor};
        background-color: ${(p) => p.theme.backgroundColor} !important;
      }
    }
  }
`;

export const BodyCell = styled.div`
  flex: 1 1 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 24px;
  font-weight: bold;

  &:last-child {
    border-right: none;
  }
`;
