import React, { FC, useState } from "react";
import { BodyCell, IconContainer, PaperStyled, Container } from "./ListOfUploadedPG.style";
import { CustomTreeData, TreeDataState } from "@devexpress/dx-react-grid";
import {
  Grid,
  VirtualTable,
  Table,
  TableHeaderRow,
  TableColumnResizing,
  DragDropProvider,
  TableColumnReordering,
  TableTreeColumn,
} from "@devexpress/dx-react-grid-material-ui";
import { Icon } from "components/Icon";
import { Loader } from "components/Loader";

const TableCell = ({ ...restProps }) => {
  const { column, value } = restProps;

  if (column.name === "status") {
    return (
      // @ts-ignore
      <Table.Cell tabIndex={0} {...restProps}>
        <BodyCell
          // @ts-ignore
          hoverable={true}
        >
          {value === "CONFIRMED" && (
            <IconContainer status="CONFIRMED">
              <Icon width={28} name="done" />
            </IconContainer>
          )}
          {value === "CREATED" && (
            <IconContainer status="CREATED">
              <Loader spinnerSize={20} />
            </IconContainer>
          )}
          {value === "FAILED" && (
            <IconContainer status="FAILED">
              <Icon width={24} name="error" />
            </IconContainer>
          )}
        </BodyCell>
      </Table.Cell>
    );
  }

  return (
    // @ts-ignore
    <Table.Cell tabIndex={0} {...restProps}>
      <BodyCell
        // @ts-ignore
        hoverable={true}
      >
        {value}
      </BodyCell>
    </Table.Cell>
  );
};

const getChildRows = (row: any, rootRows: any) => {
  return row ? row.items : rootRows;
};

interface TableComponentsProps {
  tableData?: any[];
}

export const ListOfUploadedPG: FC<TableComponentsProps> = (props) => {
  const { tableData = [] } = props;

  const defaultColumnWidths = [
    { columnName: "name", width: 400, align: "center" },
    { columnName: "createdAt", width: 150, align: "center" },
    { columnName: "type", width: 150, align: "center" },
    { columnName: "syncZone", width: 150, align: "center" },
    { columnName: "source", width: 300, align: "center" },
  ];

  const defaultColumnOrder = ["name", "createdAt", "type", "syncZone", "source"];

  const [columns] = useState([
    { name: "name", title: "Название" },
    { name: "createdAt", title: "Время загрузки" },
    { name: "type", title: "Тип" },
    { name: "syncZone", title: "СЗ" },
    { name: "source", title: "Источник загрузки" },
  ]);

  const [tableColumnExtensions] = useState([
    { columnName: "name", width: 400, align: "center" },
    { columnName: "createdAt", width: 150, align: "center" },
    { columnName: "type", width: 150, align: "center" },
    { columnName: "syncZone", width: 150, align: "center" },
    { columnName: "source", width: 300, align: "center" },
  ]);

  const [columnWidths, setColumnWidths] = useState(defaultColumnWidths);
  const [columnOrder, setColumnOrder] = useState(defaultColumnOrder);

  const hasData = tableData.length > 0;
  return (
    <Container>
      <PaperStyled>
        <Grid rows={hasData ? tableData : []} columns={columns}>
          <DragDropProvider />
          <TreeDataState />
          <CustomTreeData getChildRows={getChildRows} />
          <VirtualTable
            cellComponent={(props) => <TableCell {...props} />}
            // @ts-ignore
            columnExtensions={tableColumnExtensions}
            messages={{ noData: "Нет данных" }}
          />
          <TableColumnResizing columnWidths={columnWidths} onColumnWidthsChange={(array: any[]) => setColumnWidths(array)} />
          <TableColumnReordering order={columnOrder} onOrderChange={setColumnOrder} />
          <TableHeaderRow />
          <TableTreeColumn for="name" />
        </Grid>
      </PaperStyled>
    </Container>
  );
};
