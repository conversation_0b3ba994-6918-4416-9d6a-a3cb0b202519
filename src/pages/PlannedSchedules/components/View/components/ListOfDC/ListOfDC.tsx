import React, { FC, useState } from "react";
import { BodyCell, IconContainer, PaperStyled, Container } from "./ListOfDC.style";
import { CustomTreeData, TreeDataState } from "@devexpress/dx-react-grid";
import {
  Grid,
  VirtualTable,
  Table,
  TableHeaderRow,
  TableColumnResizing,
  DragDropProvider,
  TableColumnReordering,
  TableTreeColumn,
} from "@devexpress/dx-react-grid-material-ui";
import { Icon } from "components/Icon";
import { Loader } from "components/Loader";

const TableCell = ({ ...restProps }) => {
  const { column, value } = restProps;

  return (
    // @ts-ignore
    <Table.Cell tabIndex={0} {...restProps}>
      <BodyCell
        // @ts-ignore
        hoverable={true}
      >
        {value ?? "-"}
      </BodyCell>
    </Table.Cell>
  );
};

const getChildRows = (row: any, rootRows: any) => {
  return row ? row.items : rootRows;
};

interface TableComponentsProps {
  tableData?: any[];
}

export const ListOfDC: FC<TableComponentsProps> = (props) => {
  const { tableData = [] } = props;

  const defaultColumnWidths = [
    { columnName: "name", width: 250, align: "center" },
    { columnName: "test2", width: 150, align: "center" },
    { columnName: "test3", width: 150, align: "center" },
    { columnName: "test4", width: 150, align: "center" },
    { columnName: "test5", width: 200, align: "center" },
    { columnName: "test6", width: 200, align: "center" },
    { columnName: "test7", width: 200, align: "center" },
  ];

  const defaultColumnOrder = ["name", "test2", "test3", "test4", "test5", "test6", "test7"];

  const [columns] = useState([
    { name: "name", title: "Название" },
    { name: "test2", title: "Получен" },
    { name: "test3", title: "Локальный акцепт" },
    { name: "test4", title: "Актор" },
    { name: "test5", title: "Глобальный акцепт" },
    { name: "test6", title: "ОИК СК-11" },
    { name: "test7", title: "ПАК и MODES-Terminal" },
  ]);

  const [tableColumnExtensions] = useState([
    { columnName: "name", width: 250, align: "center" },
    { columnName: "test2", width: 150, align: "center" },
    { columnName: "test3", width: 150, align: "center" },
    { columnName: "test4", width: 150, align: "center" },
    { columnName: "test5", width: 200, align: "center" },
    { columnName: "test6", width: 200, align: "center" },
    { columnName: "test7", width: 200, align: "center" },
  ]);

  const [columnWidths, setColumnWidths] = useState(defaultColumnWidths);
  const [columnOrder, setColumnOrder] = useState(defaultColumnOrder);

  const hasData = tableData.length > 0;
  return (
    <Container>
      <PaperStyled>
        <Grid rows={hasData ? tableData : []} columns={columns}>
          <DragDropProvider />
          <TreeDataState />
          <CustomTreeData getChildRows={getChildRows} />
          <VirtualTable
            cellComponent={(props) => <TableCell {...props} />}
            // @ts-ignore
            columnExtensions={tableColumnExtensions}
            messages={{ noData: "Нет данных" }}
          />
          <TableColumnResizing columnWidths={columnWidths} onColumnWidthsChange={(array: any[]) => setColumnWidths(array)} />
          <TableColumnReordering order={columnOrder} onOrderChange={setColumnOrder} />
          <TableHeaderRow />
          <TableTreeColumn for="name" />
        </Grid>
      </PaperStyled>
    </Container>
  );
};
