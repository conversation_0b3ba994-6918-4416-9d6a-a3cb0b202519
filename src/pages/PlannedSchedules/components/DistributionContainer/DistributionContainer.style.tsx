import styled, { css } from "styled-components";
import { Button } from "components/Button";
import { Icon } from "components/Icon";

export const IconNavigation = styled.div`
  margin-top: 4px;
`;

export const Slash = styled.div`
  transform: rotate(90deg);
  color:${(p) => p.theme.gray}
  margin: 0 4px;
  display:flex;
  align-items:center;
  justify-content:center;
  padding: 2px 4px;
  border-radius: 4px;
  user-select:none;
`;

export const NavigationChainEl = styled.div<{ isActive?: boolean; isPage?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 4px;
  border-radius: 4px;
  user-select: none;
  cursor: pointer;
  color: ${(p) => p.theme.primaryColor};
  border: solid 1px ${(p) => p.theme.lightGray};
  transition: all 0.3s;
  width: 160px;
  height: 24px;
  font-size: 1rem;
  &:hover {
    background-color: ${(p) => p.theme.primaryColorHover};
    color: ${(p) => p.theme.white};
  }
  ${(p) =>
    p.isActive &&
    css`
      background-color: ${(p) => p.theme.primaryColor};
      color: ${(p) => p.theme.white};
    `}
`;

export const NavigationChainContainer = styled.div`
  display: flex;
`;

export const StatusContainer = styled.div`
  white-space: nowrap;
  margin-right: 10px;
`;

export const LoaderHeader = styled.div`
  color: ${(p) => p.theme.orangeActiveSupport};
  display: flex;
  align-items: center;
  justify-content: space-between;
  //margin-left: auto;
  user-select: none;
  width: 160px;
`;

export const Container = styled.div`
  display: flex;
  height: 96%;
  width: 100%;
  color: ${(p) => p.theme.textColor};
  //margin: 10px 0;
  //box-shadow: 0 12px 16px -4px rgb(16 24 40 / 10%), 0px 4px 6px -2px rgb(16 24 40 / 5%);
  border-radius: 8px;
  flex-direction: column;
  background-color: ${(p) => p.theme.backgroundColor};
`;

export const UpContainer = styled.div`
  width: 100%;
  height: 300px;
  border-bottom: solid 1px ${(p) => p.theme.lightGray};
  background-color: ${(p) => p.theme.backgroundColorSecondary};
`;

export const TableContainer = styled.div`
  height: calc(100vh - 32px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 80px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 76px);
  }
`;

export const CustomCell = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
`;

export const Buttons = styled.div`
  display: flex;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  margin-left: auto;
  margin-right: 10px;
`;

export const ButtonStyled = styled(Button)`
  margin: 0 10px;
  width: 200px;
  //width: 160px;
  //height: 28px;
`;

export const IconStyled = styled(Icon)`
  color: red;
`;

export const TooltipContent = styled.div`
  width: 360px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 10px;
`;

export const TooltipContainer = styled.div`
  position: absolute;
  top: -5px;
  right: 0;
`;

export const CellDate = styled.div`
  margin: 0 10px;
`;

export const CellStatusContainer = styled.div<{ oik?: boolean }>`
  width: 100%;
  height: 100%;
  font-weight: bold;
  display: flex;
  align-items: center;
  color: ${(p) => p.theme.textColor} !important;
  ${(p) =>
    p.oik &&
    css`
      justify-content: center;
    `};
`;

export const StatusCircle = styled.div<
  React.ComponentPropsWithRef<"div"> & { status?: "DONE" | "FAIL" | "AWAIT" | "RESEND_REQUIRED" | "RESEND_REQUIRED_ACCEPT" | "COND_FAIL" }
>`
  width: 14px;
  height: 14px;
  min-width: 14px;
  min-height: 14px;
  border: solid 1px ${(p) => p.theme.lightGray};
  margin-right: 4px;
  border-radius: 50%;
  background-color: ${(p) => p.theme.lightGray};

  ${(p) =>
    p.status === "DONE" &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport};
    `}

  ${(p) =>
    p.status === "FAIL" &&
    css`
      background-color: ${(p) => p.theme.redActiveSupport};
    `}

  ${(p) =>
    p.status === "AWAIT" &&
    css`
      background-color: ${(p) => p.theme.orangeActiveSupport};
    `}
  ${(p) =>
    p.status === "RESEND_REQUIRED" &&
    css`
      background-color: ${(p) => p.theme.orangeActiveSupport};
      border: solid 3px ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.status === "RESEND_REQUIRED_ACCEPT" &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport};
      border: solid 3px ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.status === "COND_FAIL" &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport};
      border: solid 3px ${(p) => p.theme.redActiveSupport};
    `}
`;

export const DateAndTime = styled.div`
  margin-right: 10px;
`;

export const NameLabel = styled.div`
  //width: 300px;
  //display: flex;
  position: relative;
  width: 100%;
  height: 20px;
  max-height: 20px;
`;

export const InfoContainer = styled.div`
  //position: absolute;
  color: ${(p) => p.theme.primaryColor};
  //left: auto;
  //right: 10px;
  margin-top: 2px;
  cursor: pointer;
`;

export const NameText = styled.div`
  display: flex;
  align-items: center;
`;
