import React, { useEffect, useMemo, useState } from "react";
import { Container, TableContainer, Buttons, ButtonStyled, CellStatusContainer, StatusCircle, NameLabel, InfoContainer } from "./DistributionContainer.style";
import { useStores } from "stores/useStore";
import { observer } from "mobx-react";
import { Table } from "components/Table";
import { AccessControl } from "components/AccessControl";
import { Icon } from "components/Icon";
import { dateFormat, renderStatus, renderStatusModesAndEg, renderStatusModesAndEgTooltip } from "./DistributionContainer.constants";
import { DetailModal } from "./components/DetailModal";
import { prepareDataTable } from "../../../../utils";
import { getWidthModal } from "../../../../helpers/adaptive";
import { ModalManualAccept } from "./components/ModalManualAccept";
import { ComboboxStyled, EmptyCircle, ListItem } from "../ViewContainer/components/UpContainer/DC";
import { Department, TableData } from "stores/PlannedSchedulesStore/PlannedSchedulesStore";

export const DistributionContainer = observer(
  ({ year, month, day, selectedPG, selectedDown, setSelectedDown }: { year: any; month: any; day: any; selectedPG: any; selectedDown: any; setSelectedDown: any }) => {
    const { plannedSchedulesStore, tableStore, authStore } = useStores();
    const { userInfo } = authStore;
    const isAccess = userInfo.roles.some((el: string) => {
      return el === "viewer";
    });
    const { plannedSchedules, listDC, isLoadingListDc, canMakeGlobalAccept, canRepeatGlobalAccept, alreadyAccepted } = plannedSchedulesStore;
    let defaultColumnsListOfDC: any[];

    const [columnOrder, setColumnOrder] = useState<any>([]);

    const setDefaultColumns = () => {
      setColumnsListOfDC(() => {
        return columnOrder.map((el: any) => {
          return getDefaultColumns().find((item) => item.name === el);
        });
      });
    };
    const [initScreenWidth, setInitScreenWidth] = useState<any>(getWidthModal(document.documentElement.clientWidth));
    const [screenWidth, setScreenWidth] = useState<any>(initScreenWidth);
    const [isEditWidth, setEditWidth] = useState(false);

    const resizeWeb = () => {
      setScreenWidth(getWidthModal(document.documentElement.clientWidth));
    };

    useEffect(() => {
      window.addEventListener("resize", resizeWeb);
      return () => {
        window.removeEventListener("resize", resizeWeb);
        setSelectedDown([]);
      };
    }, []);

    useEffect(() => {
      if (initScreenWidth !== screenWidth && !isEditWidth) {
        setEditWidth(true);
        setDefaultColumns();
      } else if (initScreenWidth !== screenWidth && isEditWidth) {
        setDefaultColumns();
      } else if (initScreenWidth === screenWidth && isEditWidth) {
        setDefaultColumns();
      }
    }, [screenWidth]);

    const getDefaultColumns = () => {
      if (screenWidth >= 650 && screenWidth < 700) {
        return [
          { name: "name", title: "Название", width: 200, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 170 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 200 },
          { name: "srdkValue", title: "Запись в СРДК", width: 125 },
        ];
      }
      if (screenWidth >= 700 && screenWidth < 800) {
        return [
          { name: "name", title: "Название", width: 200, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 170 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 200 },
          { name: "srdkValue", title: "Запись в СРДК", width: 125 },
        ];
      }
      if (screenWidth >= 800 && screenWidth < 900) {
        return [
          { name: "name", title: "Название", width: 200, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 170 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 200 },
          { name: "srdkValue", title: "Запись в СРДК", width: 125 },
        ];
      }
      if (screenWidth >= 900 && screenWidth < 1024) {
        return [
          { name: "name", title: "Название", width: 280, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 170 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 200 },
          { name: "srdkValue", title: "Запись в СРДК", width: 125 },
        ];
      }
      if (screenWidth >= 1024 && screenWidth < 1152) {
        return [
          { name: "name", title: "Название", width: 300, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 200 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 200 },
          { name: "srdkValue", title: "Запись в СРДК", width: 200 },
        ];
      }
      if (screenWidth >= 1152 && screenWidth < 1280) {
        return [
          { name: "name", title: "Название", width: 332, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 232 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 232 },
          { name: "srdkValue", title: "Запись в СРДК", width: 232 },
        ];
      }
      if (screenWidth >= 1280 && screenWidth < 1400) {
        return [
          { name: "name", title: "Название", width: 364, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 264 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 264 },
          { name: "srdkValue", title: "Запись в СРДК", width: 264 },
        ];
      }
      if (screenWidth >= 1400 && screenWidth < 1600) {
        return [
          { name: "name", title: "Название", width: 394, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 294 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 294 },
          { name: "srdkValue", title: "Запись в СРДК", width: 294 },
        ];
      }
      if (screenWidth >= 1600 && screenWidth < 1700) {
        return [
          { name: "name", title: "Название", width: 430, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 350 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 350 },
          { name: "srdkValue", title: "Запись в СРДК", width: 350 },
        ];
      }
      if (screenWidth >= 1700 && screenWidth < 1800) {
        return [
          { name: "name", title: "Название", width: 455, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 375 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 375 },
          { name: "srdkValue", title: "Запись в СРДК", width: 375 },
        ];
      }
      if (screenWidth >= 1800 && screenWidth < 1900) {
        return [
          { name: "name", title: "Название", width: 480, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 400 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 400 },
          { name: "srdkValue", title: "Запись в СРДК", width: 400 },
        ];
      }
      if (screenWidth >= 1900 && screenWidth < 2000) {
        return [
          { name: "name", title: "Название", width: 505, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 425 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 425 },
          { name: "srdkValue", title: "Запись в СРДК", width: 425 },
        ];
      }
      if (screenWidth >= 2000 && screenWidth < 2100) {
        return [
          { name: "name", title: "Название", width: 530, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 450 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 450 },
          { name: "srdkValue", title: "Запись в СРДК", width: 450 },
        ];
      }
      if (screenWidth >= 2100 && screenWidth < 2200) {
        return [
          { name: "name", title: "Название", width: 555, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 475 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 475 },
          { name: "srdkValue", title: "Запись в СРДК", width: 475 },
        ];
      }
      if (screenWidth >= 2200 && screenWidth < 2560) {
        return [
          { name: "name", title: "Название", width: 580, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 500 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 500 },
          { name: "srdkValue", title: "Запись в СРДК", width: 500 },
        ];
      }
      if (screenWidth >= 2560 && screenWidth < 3000) {
        return [
          { name: "name", title: "Название", width: 670, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 590 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 590 },
          { name: "srdkValue", title: "Запись в СРДК", width: 590 },
        ];
      }
      if (screenWidth >= 3000 && screenWidth < 3840) {
        return [
          { name: "name", title: "Название", width: 780, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 700 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 700 },
          { name: "srdkValue", title: "Запись в СРДК", width: 700 },
        ];
      }
      if (screenWidth >= 3840 && screenWidth < 4000) {
        return [
          { name: "name", title: "Название", width: 990, isSearch: true },
          { name: "information", title: " ", width: 50, isSearch: true },
          { name: "oikValue", title: "Запись в ОИК СК-11", width: 910 },
          { name: "modesValue", title: "Запись в MODES-Terminal", width: 910 },
          { name: "srdkValue", title: "Запись в СРДК", width: 910 },
        ];
      }
      return [
        { name: "name", title: "Название", width: 505, isSearch: true },
        { name: "information", title: " ", width: 50, isSearch: true },
        { name: "oikValue", title: "Запись в ОИК СК-11", width: 425 },
        { name: "modesValue", title: "Запись в MODES-Terminal", width: 425 },
        { name: "srdkValue", title: "Запись в СРДК", width: 425 },
      ];
    };

    defaultColumnsListOfDC = getDefaultColumns();

    const [columnsListOfDC, setColumnsListOfDC] = useState<any>([]);

    useEffect(() => {
      tableStore.getTableParams("7_v3").then((data: any) => {
        if (data) {
          setColumnsListOfDC(data);
        } else {
          setColumnsListOfDC(defaultColumnsListOfDC);
        }
      });
      return () => {
        localStorage.removeItem("expandedRowIds-7_v3");
        //new
        localStorage.removeItem("dist-task-id");
      };
    }, []);

    const lastTaskId = plannedSchedules.find(({ tabId }: { tabId: string }) => tabId === selectedPG)?.lastTaskId ?? null;

    useEffect(() => {
      localStorage.setItem("dist-task-id", lastTaskId);
      plannedSchedulesStore.stopDistribution();
      plannedSchedulesStore.stopView();
      plannedSchedulesStore.startCheckingDistribution(year, month, day, lastTaskId);
    }, [lastTaskId]); //plannedSchedules, selectedPG

    const confirmAccept = (lastTaskId: any) => {
      localStorage.setItem("dist-task-id", lastTaskId);
      plannedSchedulesStore.startDistribution(lastTaskId).then(async () => {
        plannedSchedulesStore.stopDistribution();
        plannedSchedulesStore.startCheckingDistribution(year, month, day, lastTaskId);
      });
    };

    const startDistribution = async () => {
      const lastTaskId = plannedSchedules.find(({ tabId }: { tabId: string }) => tabId === selectedPG)?.lastTaskId ?? null;
      localStorage.setItem("dist-task-id", lastTaskId);
      await plannedSchedulesStore.checkAccept(lastTaskId).then((isModal: boolean) => {
        if (isModal) {
          setIsModalManualAccept(true);
        } else {
          confirmAccept(lastTaskId);
        }
      });
    };

    const resetDistribution = () => {
      const lastTaskId = plannedSchedules.find(({ tabId }: { tabId: string }) => tabId === selectedPG)?.lastTaskId ?? null;
      localStorage.setItem("dist-task-id", lastTaskId);
      plannedSchedulesStore.resetDistribution(year, month, day, lastTaskId, selectedDown).then(async () => {
        plannedSchedulesStore.stopDistribution();
        plannedSchedulesStore.startCheckingDistribution(year, month, day, lastTaskId);
        setSelectedDown([]);
      });
    };

    const repeatSendAccept = () => {
      const lastTaskId = plannedSchedules.find(({ tabId }: { tabId: string }) => tabId === selectedPG)?.lastTaskId ?? null;
      localStorage.setItem("dist-task-id", lastTaskId);
      plannedSchedulesStore.repeatSendAccept(lastTaskId, selectedDown).then(async () => {
        plannedSchedulesStore.stopDistribution();
        plannedSchedulesStore.startCheckingDistribution(year, month, day, lastTaskId);
        setSelectedDown([]);
      });
    };

    const getTooltipTooltip = (status: any, finishedAt: any, noData: boolean) => {
      if (noData) {
        return "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется";
      }
      if (!status || typeof status !== "string") {
        return "";
      }
      if (status === "SENDING_TO_DC_1") {
        return "Отправка ПГ в ДЦ";
      }
      if (status === "WAITING_FOR_ACCEPT") {
        return "ПГ получен в ДЦ. Ожидается квитанция о записи в ОИК СК-11";
      }
      if (status === "WAITING_FOR_GLOBAL_ACCEPT") {
        if (noData) {
          return "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется";
        }
        return "Квитанция о записи в ОИК СК-11 получена. Ожидается акцепт";
      }
      if (status === "SENDING_GLOBAL_ACCEPT") {
        return "Отправка признака акцепта в ДЦ";
      }
      if (status === "SENDING_TO_OIK") {
        return "Запись ПГ в ОИК СК-11";
      }
      if (status === "ERROR_SENDING_TO_DC") {
        return "Ошибка отправки ПГ в ДЦ";
      }
      if (status === "ERROR_SENDING_GLOBAL_ACCEPT") {
        return "Ошибка отправки признака акцепта в ДЦ";
      }
      if (status === "ERROR_SENDING_TO_OIK") {
        return "Ошибка записи ПГ в ОИК СК-11";
      }
      if (status === "DONE") {
        const date = dateFormat(finishedAt)?.join(" ") ?? "";
        return `ПГ записан в ОИК СК-11 (${date})`;
      }
      if (status === "UNSENT_TO_DC") {
        return "Отправка в ДЦ не требуется";
      }

      return "Передача ПГ не выполнялась";
    };

    const [detailInformation, setDetailInformation] = useState<any>(null);

    const filters = (type: string) => {
      return [
        {
          value: "NO_DATA",
          label: (
            <ListItem>
              <StatusCircle />
              Нет информации
            </ListItem>
          ),
          // color: "#4c4c4c",
          icon: "empty",
        },
        {
          value: "SENDING_TO_DC_1",
          label: (
            <ListItem>
              <StatusCircle />
              Отправка ПГ в ДЦ
            </ListItem>
          ),
          // color: "#4c4c4c",
          icon: "empty",
        },
        {
          value: "WAITING_FOR_GLOBAL_ACCEPT",
          label: (
            <ListItem>
              <StatusCircle status="AWAIT" data-test="planned-schedules-detailed-block.await-status-pg" />
              Квитанция о записи в ОИК СК-11 получена. Ожидается акцепт
            </ListItem>
          ),
          // color: "#FFB200",
          icon: "empty",
        },
        {
          value: "SENDING_GLOBAL_ACCEPT",
          label: (
            <ListItem>
              <StatusCircle status="AWAIT" data-test="planned-schedules-detailed-block.await-status-pg" />
              Отправка признака акцепта в ДЦ
            </ListItem>
          ),
          // color: "#FFB200",
          icon: "empty",
        },
        {
          value: "SENDING_TO_OIK",
          label: (
            <ListItem>
              <StatusCircle status="AWAIT" data-test="planned-schedules-detailed-block.await-status-pg" />
              Запись ПГ в ОИК СК-11
            </ListItem>
          ),
          // color: "#FFB200",
          icon: "empty",
        },
        {
          value: "WAITING_FOR_ACCEPT",
          label: (
            <ListItem>
              <StatusCircle />
              ПГ получен в ДЦ. Ожидается квитанция о записи в ОИК СК-11
            </ListItem>
          ),
          // color: "#FFB200",
          icon: "empty",
        },
        {
          value: "SAVING_PG",
          label: (
            <ListItem>
              <StatusCircle status="AWAIT" data-test="planned-schedules-detailed-block.await-status-pg" />
              Запись акцептованного ПГ
            </ListItem>
          ),
          // color: "#FFB200",
          icon: "empty",
        },
        {
          value: "SAVING_PRE_PG",
          label: (
            <ListItem>
              <StatusCircle status="AWAIT" data-test="planned-schedules-detailed-block.await-status-pg" />
              Запись неакцептованного ПГ
            </ListItem>
          ),
          // color: "#FFB200",
          icon: "empty",
        },
        {
          value: "SAVED_PRE_PG",
          label: (
            <ListItem>
              <StatusCircle status="AWAIT" data-test="planned-schedules-detailed-block.await-status-pg" />
              ПГ записан. Ожидается акцепт.
            </ListItem>
          ),
          // color: "#FFB200",
          icon: "empty",
        },
        {
          value: "ERROR_SAVING_PRE_PG",
          label: (
            <ListItem>
              <StatusCircle status="FAIL" data-test="planned-schedules-detailed-block.fail-status-pg" />
              Ошибка записи неакцептованного ПГ
            </ListItem>
          ),
          // color: "#FF3A29",
          icon: "empty",
        },
        {
          value: "ERROR_SAVING_PG",
          label: (
            <ListItem>
              <StatusCircle status="FAIL" data-test="planned-schedules-detailed-block.fail-status-pg" />
              Ошибка записи акцептованного ПГ
            </ListItem>
          ),
          // color: "#FF3A29",
          icon: "empty",
        },
        {
          value: "ERROR_SENDING_TO_DC",
          label: (
            <ListItem>
              <StatusCircle status="FAIL" data-test="planned-schedules-detailed-block.fail-status-pg" />
              Ошибка отправки ПГ в ДЦ
            </ListItem>
          ),
          // color: "#FF3A29",
          icon: "empty",
        },
        {
          value: "ERROR_SENDING_GLOBAL_ACCEPT",
          label: (
            <ListItem>
              <StatusCircle status="FAIL" data-test="planned-schedules-detailed-block.fail-status-pg" />
              Ошибка отправки признака акцепта в ДЦ
            </ListItem>
          ),
          // color: "#FF3A29",
          icon: "empty",
        },
        {
          value: "ERROR_SENDING_TO_OIK",
          label: (
            <ListItem>
              <StatusCircle status="FAIL" data-test="planned-schedules-detailed-block.fail-status-pg" />
              Ошибка записи ПГ в ОИК СК-11
            </ListItem>
          ),
          // color: "#FF3A29",
          icon: "empty",
        },
        {
          value: "DONE",
          label: (
            <ListItem>
              <StatusCircle status="DONE" data-test="planned-schedules-detailed-block.success-status-pg" />
              {`ПГ записан в ${type}`}
            </ListItem>
          ),
          // color: "#34B53A",
          icon: "empty",
        },
        {
          value: "CONFIRMED",
          label: (
            <ListItem>
              <StatusCircle status="DONE" data-test="planned-schedules-detailed-block.success-status-pg" />
              {`ПГ записан в ${type}`}
            </ListItem>
          ),
          // color: "#34B53A",
          icon: "empty",
        },
        {
          value: "FAILED",
          label: (
            <ListItem>
              <StatusCircle status="FAIL" />
              Ошибка
            </ListItem>
          ),
          // color: "#FF3A29",
          icon: "empty",
        },
        {
          value: "RESEND_REQUIRED",
          label: (
            <ListItem>
              <StatusCircle status="RESEND_REQUIRED_ACCEPT" />
              Требуется повторная отправка
            </ListItem>
          ),
          // color: "#CONFIRMED",
          icon: "empty",
        },
        {
          value: "RESEND_REQUIRED_ACCEPT",
          label: (
            <ListItem>
              <StatusCircle status="RESEND_REQUIRED_ACCEPT" />
              Требуется повторная отправка
            </ListItem>
          ),
          // color: "#CONFIRMED",
          icon: "empty",
        },
        {
          value: "CREATED",
          label: (
            <ListItem>
              <StatusCircle status="AWAIT" />
              Запись акцептованного ПГ
            </ListItem>
          ),
          // color: "#FFB200",
          icon: "empty",
        },
        {
          value: "UNSENT_TO_DC",
          label: (
            <ListItem>
              <StatusCircle />
              Отправка в ДЦ не требуется
            </ListItem>
          ),
          // color: "#4c4c4c",
          icon: "empty",
        },
        {
          value: "UNSENT",
          label: (
            <ListItem>
              <StatusCircle />
              Отправка не выполнялась
            </ListItem>
          ),
          // color: "#4c4c4c",
          icon: "empty",
        },
      ];
    };

    // Определяет цвет строки по типу ДЦ
    const getRowColor = (item: Department) => {
      const colors = { CDU: "cdu", ODU: "odu", RDU: "rdu" } as const;
      return colors[item.depType];
    };

    const preparedRows: TableData = prepareDataTable(listDC);

    const rows = preparedRows.map((el) => {
      let oik = el.oik;
      let modes = el.modes;
      let srdk = el.srdk;
      let oikValue = el.oikValue;
      let modesValue = el.modesValue;
      let srdkValue = el.srdkValue;

      if (oikValue === "SENDING_TO_DC") {
        oik = { ...el.oik, status: "SENDING_TO_DC_1" };
        oikValue = "SENDING_TO_DC_1";
      }
      if (modesValue === "SENDING_TO_DC") {
        modes = { ...el.modes, status: "SENDING_TO_DC_1" };
        modesValue = "SENDING_TO_DC_1";
      }
      if (srdkValue === "SENDING_TO_DC") {
        srdk = { ...el.srdk, status: "SENDING_TO_DC_1" };
        srdkValue = "SENDING_TO_DC_1";
      }

      const rowColor = getRowColor(el);

      return { ...el, oik, modes, srdk, oikValue, modesValue, srdkValue, rowColor };
    });
    const tableData = selectedPG ? rows : [];

    const resFiltersOik = filters("ОИК").filter((el) => {
      return tableData.some((item) => item.oikValue === el.value);
    });

    const resFiltersModes = filters("MODES-Terminal").filter((el) => {
      return tableData.some((item) => item.modesValue === el.value);
    });

    const resFiltersSrdk = filters("СРДК").filter((el) => {
      return tableData.some((item) => item.srdkValue === el.value);
    });

    const customCellsListOfDC = [
      {
        name: "oikValue",
        render: (value: any, row: any) => {
          return renderStatus(row?.oik?.status, row?.oik?.finishedAt ? row?.oik?.finishedAt : row?.oik?.startedAt ?? null, false);
        },
        tooltip: (value: any, row: any) =>
          getTooltipTooltip(row?.oik?.status, row?.oik?.finishedAt ? row?.oik?.finishedAt : row?.oik?.startedAt ?? null, row?.oik?.noData),
      },
      {
        name: "modesValue",
        render: (value: any, row: any) => {
          return renderStatusModesAndEg(row?.modes);
        },
        tooltip: (value: any, row: any) => renderStatusModesAndEgTooltip(row?.modes, "MODES-Terminal"),
      },
      {
        name: "srdkValue",
        render: (value: any, row: any) => {
          return renderStatusModesAndEg(row?.srdk);
        },
        tooltip: (value: any, row: any) => renderStatusModesAndEgTooltip(row?.srdk, "СРДК"),
      },
      {
        name: "information",
        render: (value: any, row: any) => {
          return (
            <NameLabel>
              {row.controlId && (
                <AccessControl rules={["sys_admin", "engineer", "viewer"]}>
                  <InfoContainer
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setDetailInformation({ controlId: row.controlId, name: row.name });
                    }}
                  >
                    <Icon width={20} name="information" />
                  </InfoContainer>
                </AccessControl>
              )}
            </NameLabel>
          );
        },
      },
    ];

    const [expandedRowIds, setExpandedRowIds] = useState<any>([]);
    const [initExpandedRowIds, setInitExpandedRowIds] = useState<any>([]);

    useEffect(() => {
      if (rows.length !== initExpandedRowIds.length) {
        const initExpandedRowIds = JSON.parse(localStorage.getItem("expandedRowIds-7_v3") as string) ?? rows.map((el) => el.tabId);
        setExpandedRowIds(initExpandedRowIds);
        setInitExpandedRowIds(initExpandedRowIds);
      }
    }, [listDC]);

    const errorsRetryAccept =
      selectedDown.length > 0 &&
      selectedDown.every((el: any) => {
        return rows?.find((item) => item.tabId === el)?.oik?.status === "ERROR_SENDING_GLOBAL_ACCEPT";
      });

    const errorsRetry =
      selectedDown.length > 0 &&
      selectedDown.every((el: any) => {
        return rows?.find((item) => item.tabId === el)?.oik?.status === "ERROR_SENDING_TO_DC";
      });

    const [initSorting, setInitSorting] = useState<any>(null);

    useEffect(() => {
      tableStore.getSortParams("7_v3").then((data: any) => {
        if (data) {
          setInitSorting(data);
        } else {
          setInitSorting([{ columnName: "name", direction: "asc" }]);
        }
      });
    }, []);

    const [isModalManualAccept, setIsModalManualAccept] = useState(false);

    const searchCustomFilter = [
      {
        name: "oikValue",
        render: (status: any, setStatus: any) => {
          const width = columnsListOfDC.find((el: any) => el.name === "oikValue").width;
          return (
            <ComboboxStyled
              items={[
                {
                  value: "none",
                  label: (
                    <ListItem>
                      <EmptyCircle />
                      Не фильтровать
                    </ListItem>
                  ),
                  color: "#000",
                  icon: "empty",
                },
                ...resFiltersOik,
              ]}
              widthLabel={width}
              width={500}
              selectedValue={status}
              onChange={({ value }) =>
                setStatus((prev: any) => {
                  return prev.map((el: any) => {
                    if (el.columnName === "oikValue") {
                      return { ...el, value };
                    }
                    return el;
                  });
                })
              }
            />
          );
        },
      },
      {
        name: "modesValue",
        render: (status: any, setStatus: any) => {
          const width = columnsListOfDC.find((el: any) => el.name === "modesValue").width;
          return (
            <ComboboxStyled
              items={[{ value: "none", label: "Не фильтровать", color: "#000", icon: "empty" }, ...resFiltersModes]}
              widthLabel={width}
              width={500}
              selectedValue={status}
              onChange={({ value }) =>
                setStatus((prev: any) => {
                  return prev.map((el: any) => {
                    if (el.columnName === "modesValue") {
                      return { ...el, value };
                    }
                    return el;
                  });
                })
              }
            />
          );
        },
      },
      {
        name: "srdkValue",
        render: (status: any, setStatus: any) => {
          const width = columnsListOfDC.find((el: any) => el.name === "srdkValue").width;
          return (
            <ComboboxStyled
              items={[{ value: "none", label: "Не фильтровать", color: "#000", icon: "empty" }, ...resFiltersSrdk]}
              widthLabel={width}
              width={500}
              selectedValue={status}
              onChange={({ value }) =>
                setStatus((prev: any) => {
                  return prev.map((el: any) => {
                    if (el.columnName === "srdkValue") {
                      return { ...el, value };
                    }
                    return el;
                  });
                })
              }
            />
          );
        },
      },
    ];

    return (
      <Container>
        {detailInformation && <DetailModal controlId={detailInformation.controlId} name={detailInformation.name} onCancel={() => setDetailInformation(null)} />}
        {isModalManualAccept && (
          <ModalManualAccept
            onConfirm={() => {
              const lastTaskId = plannedSchedules.find(({ tabId }: { tabId: string }) => tabId === selectedPG)?.lastTaskId ?? null;
              confirmAccept(lastTaskId);
              setIsModalManualAccept(false);
            }}
            onCancel={() => setIsModalManualAccept(false)}
            alreadyAccepted={alreadyAccepted}
          />
        )}
        <TableContainer>
          <Table
            columns={columnsListOfDC}
            setColumns={setColumnsListOfDC}
            columnOrder={columnOrder}
            initSorting={initSorting}
            setColumnOrder={setColumnOrder}
            tableData={tableData}
            isLoading={isLoadingListDc && selectedPG && listDC.length === 0}
            tableKey={"7_v3"}
            customCells={customCellsListOfDC}
            defaultColumns={defaultColumnsListOfDC}
            expandedRowIds={expandedRowIds}
            setExpandedRowIds={(e: any) => {
              localStorage.setItem("expandedRowIds-7_v3", JSON.stringify(e));
              setExpandedRowIds(e);
            }}
            selected={selectedDown}
            setSelected={setSelectedDown}
            selectedMode="many"
            childrenKey="name"
            disabledSearches={["information"]}
            searchCustomFilter={searchCustomFilter}
            dataTest="opened-pg-table.container"
            headerComponents={
              <>
                {/*<LoaderHeader>*/}
                {/*  {plannedSchedulesStore.isProcessing && selectedPG && (*/}
                {/*    <>*/}
                {/*      <StatusContainer>Идет подгрузка данных</StatusContainer>*/}
                {/*      <StatusCircle status="AWAIT" />*/}
                {/*    </>*/}
                {/*  )}*/}
                {/*</LoaderHeader>*/}
                <Buttons>
                  <AccessControl rules={["engineer"]}>
                    {canRepeatGlobalAccept && (
                      <ButtonStyled
                        disabled={!canRepeatGlobalAccept || !errorsRetryAccept} //selectedDown.length === 0
                        title="Повторить отправку акцепта"
                        onClick={() => repeatSendAccept()}
                      />
                    )}
                  </AccessControl>
                  <AccessControl rules={["engineer"]}>
                    {canMakeGlobalAccept && (
                      <ButtonStyled disabled={!canMakeGlobalAccept} title="Акцептовать" onClick={() => startDistribution()} dataTest="opened-pg-header.accept-button" />
                    )}
                  </AccessControl>
                  <AccessControl rules={["engineer"]}>
                    <ButtonStyled
                      // disabled={selectedDown.length === 0}
                      disabled={!errorsRetry}
                      title="Повторить отправку"
                      onClick={() => resetDistribution()}
                    />
                  </AccessControl>
                </Buttons>
              </>
            }
          />
        </TableContainer>
      </Container>
    );
  }
);
