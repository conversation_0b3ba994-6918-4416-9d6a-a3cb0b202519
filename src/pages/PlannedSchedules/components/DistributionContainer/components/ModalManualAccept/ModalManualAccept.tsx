import React, { FC } from "react";
import { Modal } from "components/Modal";

export const ModalManualAccept = (props: any) => {
  const { onCancel, onConfirm, alreadyAccepted } = props;
  const dateText = alreadyAccepted.dates ? alreadyAccepted.dates.join(" ") : "";
  return (
    <Modal
      title={`ПБР-${alreadyAccepted?.pgNum ?? ""} на ${alreadyAccepted?.actionDate ?? ""} акцептован ${dateText}. Выполнить повторный акцепт?`}
      onCancel={onCancel}
      width={600}
      height={150}
      onConfirm={onConfirm}
      confirmText="Выполнить"
      cancelText="Отменить"
      dataTestConfirmButton="opened-pg-confirm-accept-modal.confirm-button"
    />
  );
};
