import React, { FC, useEffect, useState } from "react";
import styled, { css } from "styled-components";
import { Modal } from "components/Modal";
import { observer } from "mobx-react";
import { useStores } from "stores/useStore";
import { Table } from "components/Table";
import { prepareDateTable } from "helpers/DateUtils";
import { prepareDataTable } from "../../../../../../utils";

export const ModalStyled = styled(Modal)`
  width: 800px;
  height: 500px;
`;

export const TableContainer = styled.div<{ height?: number }>`
  height: ${(p) => p.height}px;
`;

export const Status = styled.div<{ status?: string }>`
  padding: 2px;
  width: 60px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  ${(p) =>
    p.status === "CONFIRMED" &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport};
      color: ${(p) => p.theme.white};
    `}
  ${(p) =>
    p.status === "FAILED" &&
    css`
      background-color: ${(p) => p.theme.redActiveSupport};
      color: ${(p) => p.theme.white};
    `}
`;

export const ContainerProcessedAt = styled.div`
  color: ${(p) => p.theme.textColor};
`;

interface DetailModalProps {
  controlId?: any;
  name?: any;
  onCancel?: any;
}

export const DetailModal: FC<DetailModalProps> = observer((props) => {
  const { controlId, name, onCancel } = props;
  const { plannedSchedulesStore, tableStore } = useStores();

  const { eventsDistribution, isLoadingDetailEvents } = plannedSchedulesStore;

  useEffect(() => {
    plannedSchedulesStore.initDetailDistribution(controlId);
  }, [controlId]);

  const defaultColumns = [
    { name: "text", title: "Событие", width: 310 },
    { name: "status", title: "Статус события", width: 180 },
    { name: "processedAt", title: "Время события", width: 230 },
  ];

  const [columns, setColumns] = useState(defaultColumns);

  const getText = (status: any) => {
    if (status === "CONFIRMED") {
      return "Успешно";
    }
    return "Ошибка";
  };

  const customCells = [
    {
      name: "status",
      render: (value: any, row: any) => {
        return <Status status={value}>{getText(value)}</Status>;
      },
      tooltip: (value: any) => {
        return getText(value);
      },
    },
    {
      name: "processedAt",
      render: (value: any) => {
        return <ContainerProcessedAt>{prepareDateTable(value) ?? ""}</ContainerProcessedAt>;
      },
    },
  ];

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("detail-modal").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "name", direction: "asc" }]);
      }
    });
  }, []);

  const [modalHeight, setModalHeight] = useState(500);

  return (
    <ModalStyled title={name} isOverLay onCancel={onCancel} width={800} height={500} setModalHeight={setModalHeight}>
      <TableContainer height={modalHeight - 200}>
        <Table
          columns={columns}
          setColumns={setColumns}
          tableData={prepareDataTable(eventsDistribution)}
          defaultColumns={defaultColumns}
          customCells={customCells}
          isLoading={isLoadingDetailEvents}
          tableKey={`detail-modal`}
          initSorting={initSorting}
        />
      </TableContainer>
    </ModalStyled>
  );
});
