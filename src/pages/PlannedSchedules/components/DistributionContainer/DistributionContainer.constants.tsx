import { CellStatusContainer, DateAndTime, NameLabel, NameText, StatusCircle, InfoContainer } from "./DistributionContainer.style";
import { ElementTree } from "../../../../components/ElementTree";
import React from "react";

export const dateFormat = (date: any) => {
  if (date) {
    return date.split("T").map((item: any, index: any) => {
      if (index === 1) {
        return item.split(".")[0];
      }
      const [year, month, day] = item.split("-");
      return `${day}.${month}.${year}`;
    });
  }
  return ["-", "-"];
};

export const renderStatus = (status: any, finishedAt: any, isHidden: boolean) => {
  if (status === "SENDING_TO_DC_1") {
    return (
      <CellStatusContainer oik={true}>
        <StatusCircle />
        {finishedAt && dateFormat(finishedAt).join(" ")}
        {isHidden && <>ПГ не записан в ОИК СК-11</>}
      </CellStatusContainer>
    );
  }
  if (status === "WAITING_FOR_ACCEPT") {
    return (
      <CellStatusContainer oik={true}>
        <StatusCircle />
        {finishedAt && dateFormat(finishedAt).join(" ")}
        {isHidden && <>ПГ не записан в ОИК СК-11</>}
      </CellStatusContainer>
    );
  }
  if (status === "WAITING_FOR_GLOBAL_ACCEPT") {
    return (
      <CellStatusContainer oik={true}>
        <StatusCircle status="AWAIT" />
        {finishedAt && dateFormat(finishedAt).join(" ")}
        {isHidden && <>ПГ записан в ОИК СК-11</>}
      </CellStatusContainer>
    );
  }
  if (status === "SENDING_GLOBAL_ACCEPT") {
    return (
      <CellStatusContainer oik={true}>
        <StatusCircle status="AWAIT" />
        {finishedAt && dateFormat(finishedAt).join(" ")}
        {isHidden && <>ПГ записан в ОИК СК-11</>}
      </CellStatusContainer>
    );
  }
  if (status === "SENDING_TO_OIK") {
    return (
      <CellStatusContainer oik={true}>
        <StatusCircle status="AWAIT" />
        {finishedAt && dateFormat(finishedAt).join(" ")}
        {isHidden && <>ПГ не записан в ОИК СК-11</>}
      </CellStatusContainer>
    );
  }
  if (status === "ERROR_SENDING_TO_DC") {
    return (
      <CellStatusContainer oik={true}>
        <StatusCircle status="FAIL" data-test="planned-schedules.fail-status-pg" />
        {finishedAt && dateFormat(finishedAt).join(" ")}
        {isHidden && <>ПГ не записан в ОИК СК-11</>}
      </CellStatusContainer>
    );
  }
  if (status === "ERROR_SENDING_GLOBAL_ACCEPT") {
    return (
      <CellStatusContainer oik={true}>
        <StatusCircle status="FAIL" />
        {finishedAt && dateFormat(finishedAt).join(" ")}
        {isHidden && <>ПГ не записан в ОИК СК-11</>}
      </CellStatusContainer>
    );
  }
  if (status === "ERROR_SENDING_TO_OIK") {
    return (
      <CellStatusContainer oik={true}>
        <StatusCircle status="FAIL" />
        {finishedAt && dateFormat(finishedAt).join(" ")}
        {isHidden && <>ПГ не записан в ОИК СК-11</>}
      </CellStatusContainer>
    );
  }
  if (status === "DONE") {
    return (
      <CellStatusContainer oik={true}>
        <StatusCircle status="DONE" data-test="planned-schedules.done-status-pg" />
        {finishedAt && dateFormat(finishedAt).join(" ")}
        {isHidden && <>ПГ записан в ОИК СК-11</>}
      </CellStatusContainer>
    );
  }
  if (status === "UNSENT_TO_DC") {
    return (
      <CellStatusContainer oik={true}>
        <StatusCircle />
      </CellStatusContainer>
    );
  }
  return (
    <CellStatusContainer oik={true}>
      <StatusCircle />
      {isHidden && <>Передача ПГ не выполнялась</>}
    </CellStatusContainer>
  );
};

export const renderStatusModesAndEgTooltip = (value: any, type: string) => {
  if (!value || typeof value !== "object" || !value.status) {
    return "";
  }
  if (value.status === "CONFIRMED") {
    return `ПГ записан в ${type} (${dateFormat(value.finishedAt).join(" ")})`;
  }
  if (value.status === "CREATED") {
    return "В обработке";
  }
  if (value.status === "FAILED") {
    return `Ошибка записи ПГ в ${type}`;
  }
  return "Нет информации";
};

export const renderStatusModesAndEg = (value: any) => {
  if (value?.status === "CONFIRMED") {
    return (
      <CellStatusContainer oik={true}>
        <StatusCircle status="DONE" />
        {dateFormat(value?.finishedAt).join(" ")}
      </CellStatusContainer>
    );
  }
  if (value?.status === "CREATED") {
    return (
      <CellStatusContainer oik={true}>
        <StatusCircle status="AWAIT" />В обработке
      </CellStatusContainer>
    );
  }
  if (value?.status === "FAILED") {
    return (
      <CellStatusContainer oik={true}>
        <StatusCircle status="FAIL" />
        {dateFormat(value?.finishedAt).join(" ")}
        {/*{value.error}*/}
      </CellStatusContainer>
    );
  }
  return (
    <CellStatusContainer oik={true}>
      <StatusCircle />
      Нет информации
    </CellStatusContainer>
  );
};
