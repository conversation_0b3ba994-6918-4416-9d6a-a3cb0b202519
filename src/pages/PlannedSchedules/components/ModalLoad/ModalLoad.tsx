import { FC, useState } from "react";
import styled from "styled-components";
import { Modal } from "components/Modal";
import { DropZone } from "components/DropZone";

export const ModalStyled = styled(Modal)`
  width: 560px;
  height: 260px;
`;

export interface ModalLoadProps {
  onCancel?: any;
  onConfirm?: any;
}

export const ModalLoad: FC<ModalLoadProps> = ({ onCancel, onConfirm }) => {
  const [files, setFiles] = useState([]);
  return (
    <ModalStyled
      width={560}
      height={260}
      onCancel={() => onCancel()}
      isOverLay={true}
      title="Загрузка файла"
      onConfirm={() => onConfirm(files[0])}
      confirmText="Загрузить"
      cancelText="Отменить"
      isDisabledConfirm={files.length === 0}
      dataTestContainer="planned-schedules-upload-modal.container"
      dataTestConfirmButton="planned-schedules-upload-modal.confirm-button"
    >
      <DropZone
        label="Кликните чтобы выбрать XML файл или переместите его сюда вручную"
        accept=".xml"
        files={files}
        setFiles={setFiles}
        // disabled={hasData}
        readOnly={true}
        // onBlur={handleBlurDropZone("file")}
        // errors={errors.file && touched.file ? [errors.file] : []}
        dataTest="planned-schedules-upload-pg-modal.upload-pg-input"
      />
    </ModalStyled>
  );
};
