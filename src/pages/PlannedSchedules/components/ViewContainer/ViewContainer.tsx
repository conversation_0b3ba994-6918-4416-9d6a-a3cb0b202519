import React, { FC, lazy, useEffect, useMemo, useState } from "react";
import { Container, RightContainer, LoaderContainer } from "./ViewContainer.style";
import { useStores } from "stores/useStore";
import { observer } from "mobx-react";
import { Loader } from "components/Loader";
import { isModeCenter } from "utils/getMode";
import { ModalLoad } from "../ModalLoad/ModalLoad";
import { AccessControl } from "components/AccessControl";
import { LeftContainer } from "./components/LeftContainer";
import { UpContainer } from "./components/UpContainer";
import { DownContainer } from "./components/DownContainer";
import { getWidthModal } from "../../../../helpers/adaptive";

interface ViewContainerProps {
  year: any;
  month: any;
  day: any;
  isModalLoad: any;
  setIsModalLoad: any;
  selected: any;
  setSelected: any;
  isCenter: boolean;
}

export const ViewContainer: FC<ViewContainerProps> = observer((props) => {
  const { year, month, day, isModalLoad, setIsModalLoad, isCenter, selected, setSelected } = props;
  const { plannedSchedulesStore, authStore } = useStores();
  const { userInfo } = authStore;
  const { viewTableData, objectLoaded } = plannedSchedulesStore;

  const [isOpenLeft, setIsOpenLeft] = useState(true);

  useEffect(() => {
    setSelected([]);
    //maybe
    localStorage.removeItem("calendar-date");
    localStorage.removeItem("selectedPG");
    plannedSchedulesStore.stopDistribution();
    plannedSchedulesStore.stopView();
    plannedSchedulesStore.stopStatusPlanned();
    // plannedSchedulesStore.timerLeftMenu();
    //
    localStorage.removeItem(`table-sort-52_v2`);
    localStorage.removeItem(`table-sort-51_v2`);
    localStorage.removeItem(`table-sort-53`);
    localStorage.removeItem(`table-sort-7`);
    //maybe
  }, [isCenter]);

  useMemo(() => {
    plannedSchedulesStore.abortXml();
    localStorage.removeItem("table-checked-items-5");
    plannedSchedulesStore.stopDistribution();
    plannedSchedulesStore.stopView();
    plannedSchedulesStore.stopLoadData();
    plannedSchedulesStore.stopStatusPlanned();
    plannedSchedulesStore.resetListDC();
    if (isCenter === objectLoaded?.isCenter) {
      plannedSchedulesStore.changeObjectView(day, month, year, isCenter, selected[0]);
    } else {
      plannedSchedulesStore.changeObjectView(day, month, year, isCenter, null);
    }
  }, [day, month, year, selected, isCenter]);

  const [objectId, setObjectId] = useState(null);

  useEffect(() => {
    if (JSON.stringify(objectLoaded) !== JSON.stringify(objectId)) {
      setObjectId({ ...objectLoaded });
    }
  }, [objectLoaded]);

  useEffect(() => {
    if (objectId) {
      plannedSchedulesStore.initTableView(year, month, day, true, isModeCenter, selected[0]);
    }
  }, [objectId]);

  // const [loadDataModal, setLoadDataModal] = useState<Function>(() => {});

  const loadFileAll = (file: File) => {
    setIsModalLoad(false);
    plannedSchedulesStore.loadFileAll(file).then(async () => {
      const pgId = viewTableData.find(({ tabId }: { tabId: any }) => tabId === selected[0]).pgId;
      await plannedSchedulesStore.getTableViewLoad(year, month, day, pgId);
      // if (loadDataModal) {
      //   loadDataModal();
      // }
    });
  };

  useEffect(() => {
    plannedSchedulesStore.startRepeat();
    return () => {
      plannedSchedulesStore.stopRepeat();
    };
  }, []);

  const documentWidth = document.documentElement.clientWidth;

  const [initScreenWidth, setInitScreenWidth] = useState<any>(document.documentElement.clientWidth);
  const [screenWidth, setScreenWidth] = useState<any>(initScreenWidth);

  const resizeWeb = () => {
    // setScreenWidth(getWidthModal(document.documentElement.clientWidth));
    setScreenWidth(document.documentElement.clientWidth);
  };

  useEffect(() => {
    window.addEventListener("resize", resizeWeb);
    return () => {
      window.removeEventListener("resize", resizeWeb);
    };
  }, []);

  const isAccess = userInfo.roles.some((el: string) => {
    return ["nsi_admin", "engineer"].some((rule: any) => rule === el);
  });

  const getWidthRight = () => {
    // if (!isCenter || !isModeCenter) {
    //   return screenWidth - 20;
    // } else {
    //   return screenWidth - 320;
    // }
    if (!isCenter || !isModeCenter || !isAccess) {
      return screenWidth - 20;
    } else {
      return screenWidth - 320;
    }
  };

  return (
    <Container>
      <AccessControl rules={["nsi_admin", "engineer"]}>
        {/*sys_admin*/}
        {isModeCenter && isCenter && (
          <LeftContainer
            // setLoadDataModal={setLoadDataModal}
            isOpenLeft={isOpenLeft}
            day={day}
            month={month}
            year={year}
            setIsModalLoad={setIsModalLoad}
            selected={selected}
          />
        )}
      </AccessControl>
      <RightContainer width={getWidthRight()} isOpen={!isCenter || !isModeCenter}>
        <UpContainer isCenter={isCenter} selected={selected} setSelected={setSelected} year={year} month={month} day={day} />
        {selected.length > 0 && (
          <DownContainer isCenterMode={isCenter && isModeCenter} isCenter={isCenter} selected={selected} setSelected={setSelected} month={month} year={year} day={day} />
        )}
      </RightContainer>
      {isModalLoad && (
        <ModalLoad
          onCancel={() => setIsModalLoad(false)}
          onConfirm={(file: File) => {
            loadFileAll(file);
          }}
        />
      )}
    </Container>
  );
});
