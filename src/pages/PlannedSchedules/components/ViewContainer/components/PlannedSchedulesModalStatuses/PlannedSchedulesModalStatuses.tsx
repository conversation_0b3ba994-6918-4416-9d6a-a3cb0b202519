import React from "react";
import { Modal } from "components/Modal";
import { useStores } from "../../../../../../stores/useStore";
import { observer } from "mobx-react";
import styled from "styled-components";
import { checkStatus } from "../UpContainer";
import { Loader } from "~/components/Loader";

const ScrollableContent = styled.div`
  display: flex;
  flex-direction: column;
  padding: 20px 30px 20px 0px; // 30px - отступ справа под скролл
  height: 100%;
  overflow-y: auto;

  ul {
    list-style-type: none;
    padding-left: 0;
    margin: 0;
  }

  li {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    padding: 12px 0;
    margin: 0;
    word-break: break-word;

    /* Псевдоэлемент для линии-разделителя */
    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: -20px;
      right: -20px;
      height: 1px;
      background-color: #e5e5e5;
    }
  }

  p {
    margin: auto;
  }
`;

type PlannedSchedulesModalStatusesProps = {
  onClose: () => void;
  pgName: string;
  pgId: string;
};

export const PlannedSchedulesModalStatuses = observer(({ onClose, pgName, pgId }: PlannedSchedulesModalStatusesProps) => {
  const { plannedSchedulesStore } = useStores();
  const { summaryStatus } = plannedSchedulesStore;

  const title = `Статус ${pgName}`;
  const messages = summaryStatus?.messages ?? [];
  const isLoading = summaryStatus === null;

  const renderContent = () => {
    if (isLoading) {
      return (
        <Loader
          spinnerSize={100}
          style={{
            height: "100%",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        />
      );
    }

    if (messages.length > 0) {
      return (
        <ul>
          {messages.map((msg) => (
            <li key={`${msg.status}-${msg.message}`}>
              {checkStatus(msg.status)}
              {msg.message}
            </li>
          ))}
        </ul>
      );
    }

    return <p>Нет данных</p>;
  };

  return (
    <Modal title={title} onCancel={onClose} width={600} height={400} scrollableContent>
      <ScrollableContent>{renderContent()}</ScrollableContent>
    </Modal>
  );
});
