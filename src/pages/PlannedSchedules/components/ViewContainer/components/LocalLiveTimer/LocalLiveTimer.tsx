import { FC, useEffect, useState } from "react";
import styled, { css } from "styled-components";
import { useStores } from "stores/useStore";
import { observer } from "mobx-react";
import { prepareDate } from "helpers/DateUtils";
import { Tooltip } from "components/Tooltip";
import { Loader } from "components/Loader";
import { isModeCenter } from "../../../../../../utils/getMode";

const Container = styled.div<{ isServer?: boolean }>`
  color: ${(p) => p.theme.textColor};
  text-align: center;
  display: flex;
  align-items: center;

  ${(p) =>
    !p.isServer &&
    css`
      color: ${(p) => p.theme.gray};
    `}
`;

const TooltipContent = styled.div`
  padding: 10px;
  max-width: 350px;
  min-width: 150px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const Status = styled.div<{ isServer?: boolean; isLoading?: boolean }>`
  width: 14px;
  height: 14px;
  background-color: ${(p) => p.theme.orangeActiveSupport};
  margin: 0 10px;
  // border: solid 1px ${(p) => p.theme.lightGray};
  border-radius: 50%;

  ${(p) =>
    p.isServer &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport} !important;
    `}
  ${(p) =>
    p.isLoading &&
    css`
      color: ${(p) => p.theme.blueActiveSupport} !important;
    `}
`;

export const Span = styled.span<{ isSRDK?: boolean }>`
  user-select: none;
  ${(p) =>
    p.isSRDK &&
    css`
      color: ${(p) => p.theme.topMenuTextColor};
    `}
`;

const prepareData = (data: any) => {
  if (data) {
    const buffer = data.split("T");
    const [y, month, d] = buffer[0].split("-");
    const [hours, minutes, sms] = buffer[1].split(":");
    const [s, ms] = sms.split(".");
    return new Date(Number(y), Number(month) - 1, Number(d), Number(hours), Number(minutes), Number(s), Number(ms));
  } else {
    // return new Date();
    return null;
  }
};

interface LiveTimerProps {
  type?: "DT" | "D"; // "DATE-TIME" | "DATE"
  className?: string;
  viewStatus?: boolean;
  isSRDK?: boolean;
  keyTimer?: number;
  setTimer?: any;
  setNumberPbr?: any;
  getNumberPbr?: any;
}

export const LocalLiveTimer: FC<LiveTimerProps> = observer((props) => {
  const { type = "DT", className, viewStatus = false, isSRDK = false, setTimer, setNumberPbr, getNumberPbr } = props;
  const { liveTimerStore, plannedSchedulesStore } = useStores();

  const { dateTime, isLoadingTime } = liveTimerStore;

  const [visibilityState, setVisibilityState] = useState(true);

  window.addEventListener("focus", () => {
    setVisibilityState(true);
  });
  window.addEventListener("blur", () => {
    setVisibilityState(false);
  });

  window.addEventListener("visibilitychange", function () {
    if (document.hidden) {
      setVisibilityState(false);
    } else {
      setVisibilityState(true);
    }
  });

  // eslint-disable-next-line react-hooks/exhaustive-deps
  // @ts-ignore
  useEffect(async () => {
    if (visibilityState && plannedSchedulesStore.pbrList.length > 0) {
      await plannedSchedulesStore.loadPbr(isModeCenter).then(() => {
        setNumberPbr(getNumberPbr(time));
      });
    }
  }, [visibilityState]);

  useEffect(() => {
    liveTimerStore.getTimeServer();
    return () => {
      liveTimerStore.stopTimer();
    };
  }, [visibilityState]);

  useEffect(() => {
    liveTimerStore.getTimeServer();
    return () => {
      liveTimerStore.stopTimer();
    };
  }, []);

  const [time, setTime] = useState<any>(null); //prepareData(dateTime)
  const [prevHour, setPrevHour] = useState<any>(null);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  // @ts-ignore
  useEffect(async () => {
    if (time !== null && time?.getHours() !== prevHour && plannedSchedulesStore.pbrList.length > 0) {
      setPrevHour(time?.getHours()); //getHours
      await plannedSchedulesStore.loadPbr(isModeCenter).then(() => {
        setNumberPbr(getNumberPbr(time));
      });
      //maybe
    }
  }, [time]);

  useEffect(() => {
    setTime(prepareData(dateTime));
    setTimer(prepareData(dateTime));
  }, [dateTime]);

  useEffect(() => {
    let myInterval = setInterval(() => {
      setTime((prev: any) => {
        if (prev) {
          return new Date(prev.getTime() + 1000);
        }
      });
    }, 1000);
    return () => clearInterval(myInterval);
  }, [dateTime]);

  // init number pbr
  // eslint-disable-next-line react-hooks/exhaustive-deps
  // @ts-ignore
  useEffect(async () => {
    if (plannedSchedulesStore.pbrList.length > 0 && time !== null) {
      await plannedSchedulesStore.loadPbr(isModeCenter).then(() => {
        setNumberPbr(getNumberPbr(time));
      });
    }
  }, [plannedSchedulesStore.pbrList.length, prevHour, visibilityState]); //time
  // init number pbr

  const timer = () => {
    if (type === "DT") {
      return (
        <>
          <Span isSRDK={isSRDK} className={className}>
            {time?.toLocaleString() ?? "-"}
          </Span>
        </>
      );
    }
    if (type === "D") {
      const dateBuffer = prepareDate(String(time?.getFullYear() ?? ""), String(time?.getMonth() + 1), String(time?.getDate()));
      const [year, month, day] = dateBuffer.split("-");
      const finalData = [day, month, year].join("-");
      return (
        <>
          <Span isSRDK={isSRDK} className={className}>
            {finalData}
          </Span>
        </>
      );
    }
  };

  return (
    <Container isServer={dateTime}>
      {viewStatus && (
        <Tooltip
          content={dateTime ? <TooltipContent>Время с сервера</TooltipContent> : <TooltipContent>Время сервера недоступно.Повторите попытку позже...</TooltipContent>}
        >
          <Status isServer={dateTime} isLoading={isLoadingTime} />
        </Tooltip>
      )}
      {dateTime ? <>{timer()}</> : <Span>XX.XX.XXXX, XX:XX:XX</Span>}
    </Container>
  );
});
