import React, { FC, useEffect, useState } from "react";
import { ViewDistribution, TableContainer } from "../../../ViewContainer.style";
import { prepareDateTable } from "../../../../../../../helpers/DateUtils";
import { useStores } from "../../../../../../../stores/useStore";
import { observer } from "mobx-react";
import { checkStatus } from "../UpContainer";
import { Table } from "components/Table";
import { useNavigate } from "react-router-dom";
import { Icon } from "components/Icon";
import { Label } from "components/Label";
import { prepareDataTable } from "../../../../../../../utils";
import styled from "styled-components";
import { Combobox } from "../../../../../../../components/Combobox";
import { PlannedSchedulesModalStatuses } from "../../PlannedSchedulesModalStatuses";

export const ListItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
`;

export const ComboboxStyled = styled(Combobox)<{ widthLabel: number }>`
  font-size: 1rem;
  width: ${(p) => p.widthLabel - 40}px;
  & > div {
    white-space: nowrap;
    overflow-x: hidden;
    text-overflow: ellipsis;
  }
`;

interface CenterProps {
  year: string;
  month: string;
  day: string;
  selected: string[]; // выбранные строки — массив ID (pgId)
  setSelected: React.Dispatch<React.SetStateAction<string[]>>;
}

export const Center: FC<CenterProps> = observer((props) => {
  const { year, month, day, selected, setSelected } = props;

  const { plannedSchedulesStore, tableStore } = useStores();
  const { viewTableDataCenter, plannedSchedules, openStatusModal, closeStatusModal, activeStatusModalPgId } = plannedSchedulesStore;

  const history = useNavigate();

  const defaultColumnsCenter = [
    { name: "name", title: "Название", width: 220, isSearch: true, isSort: "alphabet" },
    { name: "createdAt", title: "Время формирования", width: 220, isSort: "alphabet" },
    { name: "type", title: "Тип", width: 80, isSort: "alphabet" },
    { name: "syncZone", title: "СЗ", width: 80, isSort: "alphabet" },
    { name: "loadedBy", title: "Инициатор загрузки ПГ", width: 230 },
    { name: "oikValue", title: "Статус ПГ", width: 100 },
    { name: "acceptedAt", title: "Время акцепта", width: 230 },
    { name: "author", title: "Инициатор команды акцепт", width: 250 },
  ];

  const [columnsCenter, setColumnsCenter] = useState<any>([]);
  const [statusModalName, setStatusModalName] = useState("");

  useEffect(() => {
    tableStore.getTableParams("51_v4").then((data: any) => {
      if (data) {
        setColumnsCenter(data);
      } else {
        setColumnsCenter(defaultColumnsCenter);
      }
    });
  }, []);

  const customCellCenter = [
    {
      name: "oikValue",
      render: (_: any, row: any) => {
        return (
          <Label
            onClick={(event) => {
              event.stopPropagation(); // Останавливаем распространение события, чтобы не отрисовывался DownContainer
              setStatusModalName(row.name);
              openStatusModal(row.pgId);
            }}
          >
            {checkStatus(row?.status)}
          </Label>
        );
      },
      // Явно указываем компоненту Table, что title отсутствует, иначе он сформирует его автоматически.
      // (см TableCell, формирование tooltipCell)
      tooltip: () => null,
    },
    {
      name: "name",
      render: (value: any, row: any) => {
        return (
          <>
            <Label>{value}</Label>
            <ViewDistribution
              onClick={() => {
                history(`?year=${year}&month=${month}&day=${day}&viewPage=distribution&selectedPG=${row.pgId}`);
                localStorage.setItem("selectedPG", plannedSchedules.find(({ tabId }: { tabId: string }) => tabId === row.pgId)?.lastTaskId ?? null);
                plannedSchedulesStore.initPlannedSchedules(year, month, day);
              }}
            >
              <Icon name="goToLink" width={20} dataTest="planned-schedules-table.open-pg-button" />
            </ViewDistribution>
          </>
        );
      },
    },
    {
      name: "createdAt",
      render: (value: any, row: any) => {
        return <Label>{prepareDateTable(value)}</Label>;
      },
    },
    {
      name: "acceptedAt",
      render: (value: any, row: any) => {
        return <Label>{value}</Label>;
      },
    },
  ];

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("51_v4").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "createdAt", direction: "desc" }]);
      }
    });
  }, []);

  // Очистка содержимого модального окна статуса ПГ при уходе на другую вкладку
  useEffect(() => {
    return () => {
      closeStatusModal();
    };
  }, []);

  const tableData = prepareDataTable(viewTableDataCenter);

  return (
    <>
      {!!activeStatusModalPgId && <PlannedSchedulesModalStatuses pgId={activeStatusModalPgId} pgName={statusModalName} onClose={closeStatusModal} />}
      <TableContainer isOpen={selected.length > 0}>
        <Table
          columns={columnsCenter}
          setColumns={setColumnsCenter}
          tableData={tableData} //viewTableDataCenter
          isLoading={false} // plannedSchedulesStore.isLoadingListDc
          title="Список плановых графиков"
          tableKey="51_v4"
          defaultColumns={defaultColumnsCenter}
          selectedMode="one"
          selected={selected}
          setSelected={setSelected}
          customCells={customCellCenter}
          initSorting={initSorting}
          disabledSearches={["oikValue"]}
          dataTest="planned-schedules-table.container"
          dataTestRows="planned-schedules-table.item-row"
        />
      </TableContainer>
    </>
  );
});
