import React, { FC, useEffect, useState } from "react";
import { CellCustom, CellStatusContainer, LoaderHeader } from "../../../ViewContainer.style";
import { StatusCircle, StatusContainer } from "../../../../DistributionContainer/DistributionContainer.style";
import { prepareDateTable } from "../../../../../../../helpers/DateUtils";
import { observer } from "mobx-react";
import { useStores } from "../../../../../../../stores/useStore";
import { Table } from "components/Table";
import { Label } from "components/Label";
import { prepareDataTable } from "../../../../../../../utils";
import styled, { css } from "styled-components";
import { Combobox } from "../../../../../../../components/Combobox";

export const EmptyCircle = styled.div`
  min-height: 18px;
  min-width: 18px;
  height: 18px;
  width: 18px;
`;

export const ListItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
`;

export const TableContainer = styled.div<{ isOpen?: boolean }>`
  height: calc(100vh - 52px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 92px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 90px);
  }
  ${(p) =>
    p.isOpen &&
    css`
      height: calc(100vh - 460px);
      @media (min-height: 520px) and (max-height: 800px) {
        height: calc(100vh - 460px);
      }
      @media (min-height: 800px) and (max-height: 900px) {
        height: calc(100vh - 460px);
      }
    `}
`;

export const ComboboxStyled = styled(Combobox)<{ widthLabel: number }>`
  //font-size: 12px;
  font-size: 1rem;
  width: ${(p) => p.widthLabel - 40}px;
  & > div {
    //border: solid 1px red;
    // width: ${(p) => p.widthLabel - 30}px;
    white-space: nowrap;
    overflow-x: hidden;
    text-overflow: ellipsis;
  }
`;

//  height: 900px;
//   @media (min-height: 520px) and (max-height: 599px) {
//     height: 420px;
//   }
//   @media (min-height: 600px) and (max-height: 619px) {
//     height: 540px;
//   }
//   @media (min-height: 620px) and (max-height: 639px) {
//     height: 530px;
//   }
//   @media (min-height: 640px) and (max-height: 699px) {
//     height: 550px;
//   }
//   @media (min-height: 700px) and (max-height: 739px) {
//     height: 620px;
//   }
//   @media (min-height: 740px) and (max-height: 767px) {
//     height: 650px;
//   }
//   @media (min-height: 768px) and (max-height: 779px) {
//     height: 670px;
//   }
//   @media (min-height: 780px) and (max-height: 799px) {
//     height: 690px;
//   }
//   @media (min-height: 800px) and (max-height: 839px) {
//     height: 700px;
//   }
//   @media (min-height: 840px) and (max-height: 859px) {
//     height: 750px;
//   }
//   @media (min-height: 860px) and (max-height: 869px) {
//     height: 780px;
//   }
//   @media (min-height: 870px) and (max-height: 899px) {
//     height: 780px;
//   }
//   @media (min-height: 900px) and (max-height: 929px) {
//     height: 840px;
//   }
//   @media (min-height: 930px) and (max-height: 959px) {
//     height: 870px;
//   }
//   @media (min-height: 960px) and (max-height: 999px) {
//     height: 900px;
//   }
//   @media (min-height: 1000px) and (max-height: 1023px) {
//     height: 940px;
//   }
//   @media (min-height: 1024px) and (max-height: 1049px) {
//     height: 950px;
//   }
//   @media (min-height: 1050px) and (max-height: 1079px) {
//     height: 1010px; //1010 990
//   }
//   @media (min-height: 1080px) and (max-height: 1159px) {
//     height: 1000px;
//   }
//   @media (min-height: 1160px) and (max-height: 1199px) {
//     height: 1110px;
//   }
//   // new
//   @media (min-height: 1200px) and (max-height: 1279px) {
//     height: 1150px;
//   }
//   @media (min-height: 1280px) and (max-height: 1299px) {
//     height: 1230px;
//   }
//   @media (min-height: 1300px) and (max-height: 1399px) {
//     height: 1250px;
//   }
//   @media (min-height: 1400px) and (max-height: 1439px) {
//     height: 1350px;
//   }
//   @media (min-height: 1440px) and (max-height: 1499px) {
//     height: 1390px;
//   }
//   @media (min-height: 1500px) and (max-height: 1599px) {
//     height: 1450px;
//   }
//   @media (min-height: 1600px) and (max-height: 1699px) {
//     height: 1550px;
//   }
//   @media (min-height: 1700px) and (max-height: 1799px) {
//     height: 1650px;
//   }
//   @media (min-height: 1800px) and (max-height: 1899px) {
//     height: 1750px;
//   }
//   @media (min-height: 1900px) and (max-height: 1999px) {
//     height: 1850px;
//   }
//   @media (min-height: 2000px) and (max-height: 2099px) {
//     height: 1950px;
//   }
//   @media (min-height: 2100px) and (max-height: 2199px) {
//     height: 2050px;
//   }
//   @media (min-height: 2200px) and (max-height: 2299px) {
//     height: 2150px;
//   }
//   @media (min-height: 2300px) and (max-height: 2399px) {
//     height: 2250px;
//   }
//   @media (min-height: 2400px) and (max-height: 2499px) {
//     height: 2350px;
//   }
//   ${(p) =>
//     p.isOpen &&
//     css`
//       height: 800px;
//       @media (min-height: 520px) and (max-height: 599px) {
//         height: 160px;
//       }
//       @media (min-height: 600px) and (max-height: 739px) {
//         height: 190px;
//       }
//       @media (min-height: 740px) and (max-height: 767px) {
//         height: 310px;
//       }
//       @media (min-height: 768px) and (max-height: 799px) {
//         height: 330px;
//       }
//       @media (min-height: 800px) and (max-height: 839px) {
//         height: 370px;
//       }
//       @media (min-height: 840px) and (max-height: 859px) {
//         height: 410px;
//       }
//       @media (min-height: 860px) and (max-height: 869px) {
//         height: 430px;
//       }
//       @media (min-height: 870px) and (max-height: 899px) {
//         //height: 420px;
//         height: 440px;
//       }
//       @media (min-height: 900px) and (max-height: 929px) {
//         height: 470px;
//       }
//       @media (min-height: 930px) and (max-height: 999px) {
//         height: 500px; //420px
//       }
//       @media (min-height: 1000px) and (max-height: 1023px) {
//         height: 590px;
//       }
//       @media (min-height: 1024px) and (max-height: 1049px) {
//         height: 600px;
//       }
//       @media (min-height: 1050px) and (max-height: 1079px) {
//         height: 600px;
//       }
//       @media (min-height: 1080px) and (max-height: 1159px) {
//         height: 700px;
//       }
//       @media (min-height: 1160px) and (max-height: 1199px) {
//         height: 730px;
//       }
//       // new
//       @media (min-height: 1200px) and (max-height: 1279px) {
//         height: 740px;
//       }
//       @media (min-height: 1280px) and (max-height: 1299px) {
//         height: 820px;
//       }
//       @media (min-height: 1300px) and (max-height: 1399px) {
//         height: 860px;
//       }
//       @media (min-height: 1400px) and (max-height: 1499px) {
//         height: 960px;
//       }
//       @media (min-height: 1500px) and (max-height: 1599px) {
//         height: 1060px;
//       }
//       @media (min-height: 1600px) and (max-height: 1699px) {
//         height: 1160px;
//       }
//       @media (min-height: 1700px) and (max-height: 1799px) {
//         height: 1260px;
//       }
//       @media (min-height: 1800px) and (max-height: 1899px) {
//         height: 1360px;
//       }
//       @media (min-height: 1900px) and (max-height: 1999px) {
//         height: 1460px;
//       }
//       @media (min-height: 2000px) and (max-height: 2099px) {
//         height: 1560px;
//       }
//       @media (min-height: 2100px) and (max-height: 2199px) {
//         height: 1660px;
//       }
//       @media (min-height: 2200px) and (max-height: 2299px) {
//         height: 1760px;
//       }
//       @media (min-height: 2300px) and (max-height: 2399px) {
//         height: 1860px;
//       }
//       @media (min-height: 2400px) and (max-height: 2499px) {
//         height: 1960px;
//       }
//     `}

const getStatusTooltipOik = (status: string, row: any, error: any, isHidden: boolean, time: any, typeName: string) => {
  if (status) {
    if (isHidden) {
      return "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется";
    }
    if (status === "WAITING_FOR_GLOBAL_ACCEPT") {
      const timer = time ? prepareDateTable(time) : " ";
      return `ПГ записан (${timer}). Ожидается акцепт`;
    }
    if (status === "SAVING_PG") {
      return "Запись акцептованного ПГ";
    }
    if (status === "SAVING_PRE_PG") {
      return "Запись неакцептованного ПГ";
    }
    if (status === "SAVED_PRE_PG") {
      const timer = time ? prepareDateTable(time) : " ";
      // return "Выполнена успешная запись неакцептованного ПГ";
      return `ПГ записан (${timer}). Ожидается акцепт.`;
    }
    if (status === "ERROR_SAVING_PRE_PG") {
      return "Ошибка записи неакцептованного ПГ";
    }
    if (status === "ERROR_SAVING_PG") {
      return "Ошибка записи акцептованного ПГ";
    }
    if (status === "DONE") {
      // return "Обработка завершена";
      const timer = time ? prepareDateTable(time) : " ";
      return `ПГ записан в ${typeName} (${timer})`;
    }
    if (status === "CONFIRMED") {
      // return "Обработка завершена";
      const timer = time ? prepareDateTable(time) : " ";
      return `ПГ записан в ${typeName} (${timer})`;
    }
    if (status === "CREATED") {
      if (typeName === "MODES-Terminal" || typeName === "СРДК") {
        return "Запись акцептованного ПГ";
      } else {
        return "Нет данных";
      }
    }
    if (status === "FAILED") {
      return error ?? "Ошибка";
    }
    if (status === "RESEND_REQUIRED") {
      return error ?? "Требуется повторная отправка";
    }
    if (status === "RESEND_REQUIRED_ACCEPT") {
      return error ?? "Требуется повторная отправка";
    }
    if (status === "UNSENT") {
      return "Отправка не выполнялась";
    }
    if (status === "UNSENT_TO_DC") {
      return "Отправка в ДЦ не требуется";
    }
  }
  return "Нет данных";
};

const checkStatusTooltip = (value: any, row: any) => {
  if (row?.noData) {
    return "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется";
  } else {
    if (value?.status === "SENDING_TO_DC_1") {
      return "Отправка ПГ в ДЦ";
    }
    if (value?.status === "WAITING_FOR_ACCEPT") {
      return "ПГ получен в ДЦ. Ожидается квитанция о записи в ОИК СК-11";
    }
    if (value?.status === "WAITING_FOR_GLOBAL_ACCEPT") {
      return row?.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : "Квитанция о записи в ОИК СК-11 получена. Ожидается акцепт";
    }
    if (value?.status === "SENDING_GLOBAL_ACCEPT") {
      return "Отправка признака акцепта в ДЦ";
    }
    if (value?.status === "SENDING_TO_OIK") {
      return "Отправка ПГ в ОИК СК-11";
    }
    if (value?.status === "ERROR_SENDING_TO_DC") {
      return "Ошибка отправки ПГ в ДЦ";
    }
    if (value?.status === "ERROR_SENDING_GLOBAL_ACCEPT") {
      return "Ошибка отправки признака акцепта в ДЦ";
    }
    if (value?.status === "ERROR_SENDING_TO_OIK") {
      return "Ошибка отправки ПГ в ОИК СК-11";
    }
    if (value?.status === "DONE") {
      return row?.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : "Обработка завершена (время)";
    }
    return "Нет информации";
  }
};

const statusRender = (status: string, row: any, isHidden: boolean, error: any, typeName: any) => {
  if (status === "WAITING_FOR_GLOBAL_ACCEPT") {
    return (
      <CellStatusContainer>
        <StatusCircle status="AWAIT" data-test="planned-schedules-table-in-dc.await-status-pg" />
        {/*{isHidden && <>ПГ записан. Ожидается акцепт.</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "SAVING_PG") {
    return (
      <CellStatusContainer>
        <StatusCircle status="AWAIT" data-test="planned-schedules-table-in-dc.await-status-pg" />
        {/*{isHidden && <>Запись акцептованного ПГ.</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "SAVING_PRE_PG") {
    return (
      <CellStatusContainer>
        <StatusCircle status="AWAIT" data-test="planned-schedules-table-in-dc.await-status-pg" />
      </CellStatusContainer>
    );
  }
  if (status === "SAVED_PRE_PG") {
    return (
      <CellStatusContainer>
        <StatusCircle status="AWAIT" data-test="planned-schedules-table-in-dc.await-status-pg" />
      </CellStatusContainer>
    );
  }
  if (status === "ERROR_SAVING_PRE_PG") {
    return (
      <CellStatusContainer>
        <StatusCircle status="FAIL" data-test="planned-schedules-table-in-dc.fail-status-pg" />
        {/*{isHidden && <>Ошибка записи неакцептованного ПГ.</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "ERROR_SAVING_PG") {
    return (
      <CellStatusContainer>
        <StatusCircle status="FAIL" data-test="planned-schedules-table-in-dc.fail-status-pg" />
        {/*{isHidden && <>Ошибка записи акцептованного ПГ.</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "DONE") {
    return (
      <CellStatusContainer>
        <StatusCircle status="DONE" data-test="planned-schedules-table-in-dc.success-status-pg" />
        {/*{isHidden && <>Обработка завершена.</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "CONFIRMED") {
    return (
      <CellStatusContainer>
        <StatusCircle status="DONE" data-test="planned-schedules-table-in-dc.success-status-pg" />
        {/*{isHidden && <>Обработка завершена.</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "FAILED") {
    return (
      <CellStatusContainer>
        <StatusCircle status="FAIL" data-test="planned-schedules-table-in-dc.fail-status-pg" />
        {/*{isHidden && <>{error}</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "RESEND_REQUIRED") {
    return (
      <CellStatusContainer>
        <StatusCircle status="RESEND_REQUIRED" />
        {/*{isHidden && <>{error}</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "RESEND_REQUIRED_ACCEPT") {
    return (
      <CellStatusContainer>
        <StatusCircle status="RESEND_REQUIRED_ACCEPT" />
        {/*{isHidden && <>{error}</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "CREATED") {
    if (typeName === "MODES-Terminal" || typeName === "СРДК") {
      return (
        <CellStatusContainer>
          <StatusCircle status="AWAIT" data-test="planned-schedules-table-in-dc.await-status-pg" />
        </CellStatusContainer>
      );
    } else {
      return (
        <CellStatusContainer>
          <StatusCircle />
        </CellStatusContainer>
      );
    }
  }
  if (status === "UNSENT_TO_DC") {
    return (
      <CellStatusContainer>
        <StatusCircle />
      </CellStatusContainer>
    );
  }
  return (
    <CellStatusContainer>
      <StatusCircle data-test="planned-schedules-table-in-dc.no-data-status-pg" />
      {/*{isHidden && <>Нет данных</>}*/}
    </CellStatusContainer>
  );
};

const renderCell = (value: any, row: any, error: any, isHidden: boolean, isOik: boolean, typeName: any) => {
  const hidden = isOik ? isHidden : true;
  if (value?.status) {
    return (
      <CellCustom>
        {value.status && statusRender(value.status, row, isHidden, error, typeName)}
        {value.status &&
          value.status !== "CREATED" &&
          hidden && ( //value.status !== "FAILED"
            <>{value?.finishedAt ? <Label>{prepareDateTable(value?.finishedAt)}</Label> : <></>}</>
          )}
      </CellCustom>
    );
  } else {
    return (
      <CellStatusContainer>
        <StatusCircle data-test="planned-schedules-table-in-dc.no-data-status-pg" />
        {/*Нет данных*/}
      </CellStatusContainer>
    );
  }
};

export const DC: FC<any> = observer((props) => {
  const { plannedSchedulesStore, tableStore } = useStores();
  const { viewTableData, viewTableDataOriginal } = plannedSchedulesStore;
  const { selected, setSelected } = props;

  const defaultColumnsDC = [
    { name: "name", title: "Название", width: 250, isSearch: true, isSort: "alphabet" },
    { name: "createdAt", title: "Время формирования", width: 220, isSort: "alphabet" },
    { name: "type", title: "Тип", width: 130, isSort: "alphabet" },
    { name: "syncZone", title: "СЗ", width: 130, isSort: "alphabet" },
    { name: "oikValue", title: "Запись в ОИК СК-11", width: 280 },
    { name: "eg3Value", title: "Запись в ёЖ-3", width: 280 },
    { name: "modesValue", title: "Запись в MODES-Terminal", width: 280 },
    { name: "srdkValue", title: "Запись в СРДК", width: 280 },
  ];
  const [columnsDC, setColumnsDC] = useState<any>([]);

  useEffect(() => {
    tableStore.getTableParams("52_v4").then((data: any) => {
      if (data) {
        setColumnsDC(data);
      } else {
        setColumnsDC(defaultColumnsDC);
      }
    });
  }, []);

  const customCellDC = [
    {
      name: "oikValue",
      render: (value: any, row: any) => {
        const isHidden = row?.oik?.noData === true ?? true;
        const typeName = "ОИК СК-11";
        return renderCell(row?.oik, row, row?.oik?.error, !isHidden, true, typeName);
      },
      tooltip: (value: any, row: any) => {
        const time = row?.oik?.finishedAt ? row?.oik?.finishedAt : " T ";
        const isHidden = row?.oik?.noData === true ?? true;
        const typeName = "ОИК СК-11";
        return getStatusTooltipOik(row?.oik?.status, row, row?.oik?.error, isHidden, time, typeName);
      },
    },
    {
      name: "modesValue",
      render: (value: any, row: any) => {
        const typeName = "MODES-Terminal";
        return renderCell(row.modes, row, row?.modes?.error, true, false, typeName);
      },
      tooltip: (value: any, row: any) => {
        const time = row.modes?.finishedAt ? row.modes?.finishedAt : " T ";
        const typeName = "MODES-Terminal";
        return getStatusTooltipOik(row.modes?.status, row, row.modes?.error, false, time, typeName);
      },
    },
    {
      name: "srdkValue",
      render: (value: any, row: any) => {
        const typeName = "СРДК";
        return renderCell(row?.srdk, row, row?.srdk?.error, true, false, typeName);
      },
      tooltip: (value: any, row: any) => {
        const time = row?.srdk?.finishedAt ? row?.srdk?.finishedAt : " T ";
        const typeName = "СРДК";
        return getStatusTooltipOik(row?.srdk?.status, row, row?.srdk?.error, false, time, typeName);
      },
    },
    {
      name: "eg3Value",
      render: (value: any, row: any) => {
        const typeName = "ёЖ-3";
        return renderCell(row.eg3, row, row?.eg3?.error, true, false, typeName);
      },
      tooltip: (value: any, row: any) => {
        const time = row.eg3?.finishedAt ? row.eg3?.finishedAt : " T ";
        const typeName = "ёЖ-3";
        return getStatusTooltipOik(row.eg3?.status, row, row.eg3?.error, false, time, typeName);
      },
    },
    {
      name: "createdAt",
      render: (value: any, row: any) => {
        return <Label>{prepareDateTable(value)}</Label>;
      },
    },
  ];

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("52_v4").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "createdAt", direction: "desc" }]);
      }
    });
  }, []);

  const filters = (type: any) => {
    return [
      {
        value: "NO_DATA",
        label: (
          <ListItem>
            <StatusCircle />
            Нет информации
          </ListItem>
        ),
        // color: "#4c4c4c",
        icon: "empty",
      },
      {
        value: "SENDING_TO_DC_1",
        label: (
          <ListItem>
            <StatusCircle />
            Отправка ПГ в ДЦ
          </ListItem>
        ),
        // color: "#4c4c4c",
        icon: "empty",
      },
      {
        value: "WAITING_FOR_GLOBAL_ACCEPT",
        label: (
          <ListItem>
            <StatusCircle status="AWAIT" />
            Квитанция о записи в ОИК СК-11 получена. Ожидается акцепт
          </ListItem>
        ),
        // color: "#FFB200",
        icon: "empty",
      },
      {
        value: "SAVING_PG",
        label: (
          <ListItem>
            <StatusCircle status="AWAIT" />
            Запись акцептованного ПГ
          </ListItem>
        ),
        // color: "#FFB200",
        icon: "empty",
      },
      {
        value: "SAVING_PRE_PG",
        label: (
          <ListItem>
            <StatusCircle status="AWAIT" />
            Запись неакцептованного ПГ
          </ListItem>
        ),
        // color: "#FFB200",
        icon: "empty",
      },
      {
        value: "SAVED_PRE_PG",
        label: (
          <ListItem>
            <StatusCircle status="AWAIT" />
            ПГ записан. Ожидается акцепт.
          </ListItem>
        ),
        // color: "#FFB200",
        icon: "empty",
      },
      {
        value: "ERROR_SAVING_PRE_PG",
        label: (
          <ListItem>
            <StatusCircle status="FAIL" />
            Ошибка записи неакцептованного ПГ
          </ListItem>
        ),
        // color: "#FF3A29",
        icon: "empty",
      },
      {
        value: "ERROR_SAVING_PG",
        label: (
          <ListItem>
            <StatusCircle status="FAIL" />
            Ошибка записи акцептованного ПГ
          </ListItem>
        ),
        // color: "#FF3A29",
        icon: "empty",
      },
      {
        value: "DONE",
        label: (
          <ListItem>
            <StatusCircle status="DONE" />
            {`ПГ записан в ${type}`}
          </ListItem>
        ),
        // color: "#34B53A",
        icon: "empty",
      },
      {
        value: "CONFIRMED",
        label: (
          <ListItem>
            <StatusCircle status="DONE" />
            {`ПГ записан в ${type}`}
          </ListItem>
        ),
        // color: "#34B53A",
        icon: "empty",
      },
      {
        value: "FAILED",
        label: (
          <ListItem>
            <StatusCircle status="FAIL" />
            Ошибка
          </ListItem>
        ),
        // color: "#FF3A29",
        icon: "empty",
      },
      {
        value: "RESEND_REQUIRED",
        label: (
          <ListItem>
            <StatusCircle status="RESEND_REQUIRED_ACCEPT" />
            Требуется повторная отправка
          </ListItem>
        ),
        // color: "#CONFIRMED",
        icon: "empty",
      },
      {
        value: "RESEND_REQUIRED_ACCEPT",
        label: (
          <ListItem>
            <StatusCircle status="RESEND_REQUIRED_ACCEPT" />
            Требуется повторная отправка
          </ListItem>
        ),
        // color: "#CONFIRMED",
        icon: "empty",
      },
      {
        value: "CREATED",
        label: (
          <ListItem>
            <StatusCircle status="AWAIT" />
            Запись акцептованного ПГ
          </ListItem>
        ),
        // color: "#FFB200",
        icon: "empty",
      },
      {
        value: "UNSENT_TO_DC",
        label: (
          <ListItem>
            <StatusCircle />
            Отправка в ДЦ не требуется
          </ListItem>
        ),
        // color: "#4c4c4c",
        icon: "empty",
      },
      {
        value: "UNSENT",
        label: (
          <ListItem>
            <StatusCircle />
            Отправка не выполнялась
          </ListItem>
        ),
        // color: "#4c4c4c",
        icon: "empty",
      },
    ];
  };

  const tableData = prepareDataTable(viewTableData).map((el) => {
    let oik = el.oik;
    let modes = el.modes;
    let srdk = el.srdk;
    let oikValue = el.oikValue;
    let modesValue = el.modesValue;
    let srdkValue = el.srdkValue;

    if (oikValue === "SENDING_TO_DC") {
      oik = { ...el.oik, status: "SENDING_TO_DC_1" };
      oikValue = "SENDING_TO_DC_1";
    }
    if (modesValue === "SENDING_TO_DC") {
      modes = { ...el.modes, status: "SENDING_TO_DC_1" };
      modesValue = "SENDING_TO_DC_1";
    }
    if (srdkValue === "SENDING_TO_DC") {
      srdk = { ...el.srdk, status: "SENDING_TO_DC_1" };
      srdkValue = "SENDING_TO_DC_1";
    }
    return { ...el, oik, modes, srdk, oikValue, modesValue, srdkValue };
  });

  const resFiltersOik = filters("ОИК").filter((el) => {
    return tableData.some((item) => item.oikValue === el.value);
  });

  const resFiltersModes = filters("MODES-Terminal").filter((el) => {
    return tableData.some((item) => item.modesValue === el.value);
  });

  const resFiltersSrdk = filters("СРДК").filter((el) => {
    return tableData.some((item) => item.srdkValue === el.value);
  });

  const resFiltersEg = filters("ёЖ-3").filter((el) => {
    return tableData.some((item) => item.eg3Value === el.value);
  });

  const searchCustomFilter = [
    {
      name: "oikValue",
      render: (status: any, setStatus: any) => {
        const width = columnsDC.find((el: any) => el.name === "oikValue").width;
        return (
          <ComboboxStyled
            items={[
              {
                value: "none",
                label: (
                  <ListItem>
                    <EmptyCircle />
                    Не фильтровать
                  </ListItem>
                ),
                color: "#000",
                icon: "empty",
              },
              ...resFiltersOik,
            ]}
            widthLabel={width}
            width={300}
            selectedValue={status}
            onChange={({ value }) =>
              setStatus((prev: any) => {
                return prev.map((el: any) => {
                  if (el.columnName === "oikValue") {
                    return { ...el, value };
                  }
                  return el;
                });
              })
            }
          />
        );
      },
    },
    {
      name: "modesValue",
      render: (status: any, setStatus: any) => {
        const width = columnsDC.find((el: any) => el.name === "modesValue").width;
        return (
          <ComboboxStyled
            items={[{ value: "none", label: "Не фильтровать", color: "#000", icon: "empty" }, ...resFiltersModes]}
            widthLabel={width}
            width={300}
            selectedValue={status}
            onChange={({ value }) =>
              setStatus((prev: any) => {
                return prev.map((el: any) => {
                  if (el.columnName === "modesValue") {
                    return { ...el, value };
                  }
                  return el;
                });
              })
            }
          />
        );
      },
    },
    {
      name: "srdkValue",
      render: (status: any, setStatus: any) => {
        const width = columnsDC.find((el: any) => el.name === "srdkValue").width;
        return (
          <ComboboxStyled
            items={[{ value: "none", label: "Не фильтровать", color: "#000", icon: "empty" }, ...resFiltersSrdk]}
            widthLabel={width}
            width={300}
            selectedValue={status}
            onChange={({ value }) =>
              setStatus((prev: any) => {
                return prev.map((el: any) => {
                  if (el.columnName === "srdkValue") {
                    return { ...el, value };
                  }
                  return el;
                });
              })
            }
          />
        );
      },
    },
    {
      name: "eg3Value",
      render: (status: any, setStatus: any) => {
        const width = columnsDC.find((el: any) => el.name === "eg3Value").width;
        return (
          <ComboboxStyled
            items={[{ value: "none", label: "Не фильтровать", color: "#000", icon: "empty" }, ...resFiltersEg]}
            widthLabel={width}
            width={300}
            selectedValue={status}
            onChange={({ value }) =>
              setStatus((prev: any) => {
                return prev.map((el: any) => {
                  if (el.columnName === "eg3Value") {
                    return { ...el, value };
                  }
                  return el;
                });
              })
            }
          />
        );
      },
    },
  ];

  return (
    <TableContainer isOpen={selected.length > 0}>
      <Table
        columns={columnsDC}
        setColumns={setColumnsDC}
        tableData={tableData} //viewTableData
        // originalTableData={viewTableDataOriginal}
        isLoading={false} // plannedSchedulesStore.isLoadingListDc
        title="Список плановых графиков"
        initSorting={initSorting}
        tableKey="52_v4"
        defaultColumns={defaultColumnsDC}
        selectedMode="one"
        selected={selected}
        setSelected={setSelected}
        customCells={customCellDC}
        searchCustomFilter={searchCustomFilter}
        dataTest="planned-schedules-table_in_dc.container"
        dataTestRows="planned-schedules-table-in-dc.item-row"
        dataTestDefaultCells="planned-schedules-table-in-dc.cell"
      />
    </TableContainer>
  );
});
