import React, { <PERSON> } from "react";
import { CellStatusContainer, Up } from "../../ViewContainer.style";
import { isModeCenter } from "utils/getMode";
import { StatusCircle } from "../../../DistributionContainer/DistributionContainer.style";
import { observer } from "mobx-react";
import { Center } from "./Center";
import { DC } from "./DC";
import { SummaryStatusResponse } from "~/api/plannedSchedules/plannedSchedules-controller";

export const checkStatus = (status: SummaryStatusResponse["status"]) => {
  switch (status) {
    case "STAGE1":
      return (
        <CellStatusContainer>
          <StatusCircle /> {/* Серый */}
        </CellStatusContainer>
      );
    case "STAGE2":
      return (
        <CellStatusContainer>
          <StatusCircle status="AWAIT" /> {/* Желтый */}
        </CellStatusContainer>
      );
    case "DONE":
      return (
        <CellStatusContainer>
          <StatusCircle status="DONE" /> {/* Зеленый */}
        </CellStatusContainer>
      );
    case "ERROR":
      return (
        <CellStatusContainer>
          <StatusCircle status="FAIL" /> {/* Красный */}
        </CellStatusContainer>
      );
    case "COND_ERROR":
      return (
        <CellStatusContainer>
          <StatusCircle status="COND_FAIL" /> {/* Зеленый с красной обводкой */}
        </CellStatusContainer>
      );
    default:
      return (
        <CellStatusContainer>
          <StatusCircle />
        </CellStatusContainer>
      );
  }
};

interface UpContainerProps {
  isCenter?: boolean;
  selected: any[];
  setSelected?: any;
  year?: any;
  month?: any;
  day?: any;
  innerHeight?: any;
}

export const UpContainer: FC<UpContainerProps> = observer((props) => {
  const { isCenter, selected, setSelected, year, month, day, innerHeight } = props;

  return (
    <Up>
      {isCenter && isModeCenter ? (
        <Center year={year} month={month} day={day} selected={selected} setSelected={setSelected} isCenter={isCenter} innerHeight={innerHeight} />
      ) : (
        <DC selected={selected} setSelected={setSelected} />
      )}
    </Up>
  );
});
