import React, { FC, lazy, useEffect, useState } from "react";
import {
  ButtonDownLoad,
  CardLabel,
  CardLeft,
  CardRight,
  CardRow,
  CardStatus,
  CardTime,
  CloseIconLeft,
  ComboboxPrb,
  EmptyLabel,
  FilterDatePickerStyled,
  InputCard,
  LeftHeader,
  PencilContainer,
  SubCarTime,
  LeftContainerMain,
  ButtonGreenSave,
} from "../../ViewContainer.style";
import { useStores } from "stores/useStore";
import { prepareDate } from "helpers/DateUtils";
import { Icon } from "components/Icon";
const LocalLiveTimer = lazy(() => import("../LocalLiveTimer"));
import { Loader } from "components/Loader";
import { observer } from "mobx-react";
import { isEast, isModeCenter } from "utils/getMode";
import { ModalWrong } from "./components/ModalWrong";
import { ModalReplace } from "./components/ModalReplace";
import { ModalNotAccepted } from "./components/ModalNotAccepted";
import { ModalLoadedLater } from "./components/ModalLoadedLater";

const dateFormat = (dateBegin: any, type: any, pbrList: any[], numberPbr: any) => {
  if (dateBegin) {
    if (type === "ППБР" || type === "ПДГ" || type === "ДДГ") {
      const date = new Date(dateBegin);
      date.setDate(date.getDate() + 1);
      const finalArray = prepareDate(String(date.getFullYear()), String(date.getMonth() + 1), String(date.getDate())).split("-");
      return `${finalArray[2]}-${finalArray[1]}-${finalArray[0]}`;
    }

    if (type === "ПЭР") {
      const date = new Date(dateBegin);
      date.setDate(date.getDate() + 2);
      const finalArray = prepareDate(String(date.getFullYear()), String(date.getMonth() + 1), String(date.getDate())).split("-");
      return `${finalArray[2]}-${finalArray[1]}-${finalArray[0]}`;
    }

    if (type === "ПБР" || type === "УДДГ") {
      const date = new Date(dateBegin);
      const nextDay = pbrList.find((el) => el.number === numberPbr)?.nextDay ?? false;
      if (nextDay) {
        date.setDate(date.getDate() + 1);
      }
      const finalArray = prepareDate(String(date.getFullYear()), String(date.getMonth() + 1), String(date.getDate())).split("-");
      return `${finalArray[2]}-${finalArray[1]}-${finalArray[0]}`;
    }

    const finalArray = prepareDate(String(dateBegin.getFullYear()), String(dateBegin.getMonth() + 1), String(dateBegin.getDate())).split("-");
    return `${finalArray[2]}-${finalArray[1]}-${finalArray[0]}`;
  }
  return "";
};

interface LeftContainerProps {
  isOpenLeft?: boolean;
  numberPbr?: any;
  day?: any;
  month?: any;
  year?: any;
  setIsModalLoad?: any;
  selected?: any;
  setLoadDataModal?: any;
}

export const LeftContainer: FC<LeftContainerProps> = observer((props) => {
  const { isOpenLeft, day, month, year, setIsModalLoad, selected, setLoadDataModal } = props;
  const { plannedSchedulesStore, settingsStore, liveTimerStore } = useStores();

  const [timeLeftEdit, setTimeLeftEdit] = useState<any>([]);

  const { dateTime } = liveTimerStore;
  const { pgListForLoadPf } = settingsStore;
  const { statusPlanned, pbrList, isLoadingFile } = plannedSchedulesStore;

  const [numberPbr, setNumberPbr] = useState<any>("-"); //1

  const getNumberPbr = (date: any) => {
    const hour = date?.getHours();
    const el = pbrList?.find((item: any) => Number(item?.time?.split(":")[0]) === hour);
    return el?.number ?? "-";
  };
  const [timer, setTimer] = useState(null);

  const loadData = (type: string) => {
    const findEl = timeLeftEdit.find((el: any) => el.type === type);
    if (findEl) {
      const pgNum = findEl.number;
      plannedSchedulesStore
        .loadData(type, findEl.year, findEl.month, findEl.day, year, month, day, true, pgNum, pgListForLoadPf, isModeCenter)
        .then(() => {
          plannedSchedulesStore.stopStatusPlanned();
        })
        .then(() => {
          plannedSchedulesStore.getStatusPlanned();
        });
    } else {
      const pgNum = numberPbr;
      const [yearCurrent, monthCurrent, dayCurrent] = dateTime?.split("T")[0]?.split("-")?.map(Number);
      plannedSchedulesStore
        .loadData(type, yearCurrent, monthCurrent, dayCurrent, year, month, day, false, pgNum, pgListForLoadPf, isModeCenter)
        .then(() => {
          plannedSchedulesStore.stopStatusPlanned();
        })
        .then(() => {
          plannedSchedulesStore.getStatusPlanned();
        });
    }
  };

  useEffect(() => {
    plannedSchedulesStore.getStatusPlanned();
    // setLoadDataModal(() => loadData()); //maybe
  }, []);

  const [objectWrong, setObjectWrong] = useState<any>(null);
  const [objectReplace, setObjectReplace] = useState<any>(null);
  const [objectNotAccepted, setObjectNotAccepted] = useState<any>(null);
  const [objectLoadedLater, setObjectLoadedLater] = useState<any>(null);

  useEffect(() => {
    plannedSchedulesStore.initView(Number(timeLeftEdit[0]?.year ?? year), Number(timeLeftEdit[0]?.month ?? month), Number(timeLeftEdit[0]?.day ?? day), isModeCenter);
  }, [timeLeftEdit]);

  return (
    <LeftContainerMain>
      {/* isOpen={isOpenLeft}*/}
      {objectWrong && (
        <ModalWrong
          object={objectWrong}
          onCancel={(toLoad: boolean) => {
            plannedSchedulesStore.saveUnSaveHours(objectWrong.taskId, toLoad).then(() => {
              setObjectWrong(null);
            });
          }}
          onConfirm={(toLoad: boolean) => {
            plannedSchedulesStore.saveUnSaveHours(objectWrong.taskId, toLoad).then(() => {
              setObjectWrong(null);
            });
          }}
        />
      )}
      {objectNotAccepted && (
        <ModalNotAccepted
          object={objectNotAccepted}
          onCancel={(toLoad: boolean) => {
            plannedSchedulesStore.notAccepted(toLoad, objectNotAccepted.taskId).then(() => {
              setObjectNotAccepted(null);
              plannedSchedulesStore.getStatusPlanned();
            });
          }}
          onConfirm={(toLoad: boolean) => {
            plannedSchedulesStore.notAccepted(toLoad, objectNotAccepted.taskId).then(() => {
              setObjectNotAccepted(null);
              plannedSchedulesStore.getStatusPlanned();
            });
          }}
        />
      )}
      {objectReplace && (
        <ModalReplace
          object={objectReplace}
          onCancel={() => {
            plannedSchedulesStore.alreadyExist(false, objectReplace.taskId).then(() => {
              setObjectReplace(null);
              plannedSchedulesStore.getStatusPlanned();
            });
          }}
          onConfirm={() => {
            plannedSchedulesStore.alreadyExist(true, objectReplace.taskId).then(() => {
              setObjectReplace(null);
              plannedSchedulesStore.getStatusPlanned();
            });
          }}
        />
      )}
      {objectLoadedLater && (
        <ModalLoadedLater
          object={objectLoadedLater}
          onCancel={(toLoad: boolean) => {
            plannedSchedulesStore.handleLoadedLater(toLoad, objectLoadedLater.taskId).then(() => {
              setObjectLoadedLater(null);
            });
          }}
          onConfirm={(toLoad: boolean) => {
            plannedSchedulesStore.handleLoadedLater(toLoad, objectLoadedLater.taskId).then(() => {
              setObjectLoadedLater(null);
            });
          }}
        />
      )}
      <LeftHeader isOpen={isOpenLeft} innerHeight={innerHeight}>
        <LocalLiveTimer setTimer={setTimer} setNumberPbr={setNumberPbr} getNumberPbr={getNumberPbr} />
      </LeftHeader>
      {isOpenLeft &&
        statusPlanned.map((item: any, index: number) => {
          const isFindEdit = timeLeftEdit?.find((el: any) => el.type === item.type);
          const pbrListCombobox = pbrList?.map((el: any) => ({ ...el, value: el.number, label: el.number })) ?? [];
          const [day, month, year] = dateFormat(timer, item.typeName, pbrList, numberPbr).split("-");
          const dataForCard = { day, month, year };
          const isNumberTypeLeft = typeof timeLeftEdit?.find((el: any) => el.type === "UDDG")?.number !== "number";
          if (item.type === "XML") {
            return (
              <InputCard key={`input-card-${index}`}>
                <CardRow>
                  <CardLeft>
                    <CardLabel>{item.typeName}</CardLabel>
                  </CardLeft>
                  <CardRight>
                    <ButtonDownLoad
                      dataTest="planned-schedules-left-container.upload-pg-from-file-btn"
                      title={isLoadingFile ? <Loader /> : "Загрузить"}
                      onClick={() => setIsModalLoad(true)}
                      isLoading={isLoadingFile}
                      disabled={isLoadingFile}
                      message={
                        item.status === "CREATED" || item.status === "WRONG_HOURS_NUM"
                          ? `Загружается пользователем : ${item.user}`
                          : isLoadingFile
                          ? "Идет проверка файла"
                          : "Загрузить"
                      }
                    />
                    {item.status === "WRONG_HOURS_NUM" && (
                      <ButtonGreenSave
                        onClick={() => {
                          setObjectWrong({
                            type: item.typeName,
                            date: dateFormat(timer, item.typeName, pbrList, numberPbr),
                            hourErrors: item?.hourErrors ?? [],
                            taskId: item?.taskId,
                            pgShortName: item?.pgShortName,
                            pgActionDate: item?.pgActionDate,
                          });
                        }}
                        isXML={true}
                      >
                        <Icon width={18} name="error" />
                      </ButtonGreenSave>
                    )}
                    {item.status === "NOT_ACCEPTED_PG_WITH_LOWER_NUMS" && (
                      <ButtonGreenSave
                        onClick={() => {
                          setObjectNotAccepted({
                            type: item.typeName,
                            date: dateFormat(timer, item.typeName, pbrList, numberPbr),
                            hourErrors: item?.hourErrors ?? [],
                            taskId: item?.taskId,
                            notAccepted: item?.notAccepted ?? {},
                          });
                        }}
                        isXML={true}
                      >
                        <Icon width={18} name="error" />
                      </ButtonGreenSave>
                    )}
                    {item.status === "ALREADY_EXIST" && (
                      <ButtonGreenSave
                        onClick={() => {
                          setObjectReplace({
                            type: item.typeName,
                            date: dateFormat(timer, item.typeName, pbrList, numberPbr),
                            hourErrors: item?.hourErrors ?? [],
                            taskId: item?.taskId,
                            alreadyCreated: item?.alreadyCreated ?? {},
                          });
                        }}
                        isXML={true}
                      >
                        <Icon width={18} name="error" dataTest="planned-schedules-left-container.already-exist-icon" />
                      </ButtonGreenSave>
                    )}
                    {item.status === "LOADED_LATER" && (
                      <ButtonGreenSave
                        onClick={() => {
                          setObjectLoadedLater({
                            typeName: item.typeName,
                            date: item.loadedLaterDate,
                            taskId: item?.taskId,
                            pgShortName: item?.pgShortName,
                            type: item?.type,
                          });
                        }}
                        isXML={true}
                      >
                        <Icon width={18} name="error" data-test="planned-schedules-left-container.loaded-later-xml-icon" />
                      </ButtonGreenSave>
                    )}
                  </CardRight>
                </CardRow>
              </InputCard>
            );
          }

          return (
            <InputCard key={`input-card-${index}`} innerHeight={innerHeight} data-test="planned-schedules-left-container.card-item">
              <CardRow>
                <CardLeft>
                  <CardLabel data-test="planned-schedules-left-container.type-name">{item.typeName}</CardLabel>
                  {item.typeName === "ПБР" || item.typeName === "УДДГ" ? (
                    <>
                      {isFindEdit ? (
                        <ComboboxPrb
                          width={56}
                          items={pbrListCombobox}
                          selectedValue={isFindEdit.number}
                          dataTest="planned-schedules-left-container.number-combobox"
                          onChange={({ value }) => {
                            setTimeLeftEdit((prev: any) => {
                              return prev.map((el: any) => {
                                if (el.type === item.type) {
                                  return { ...el, number: value, day: Number(el.day), month: Number(el.month), year: Number(el.year) };
                                }
                                return el;
                              });
                            });
                          }}
                        />
                      ) : (
                        <CardLabel isNumber>№ {numberPbr}</CardLabel>
                      )}
                    </>
                  ) : (
                    <EmptyLabel />
                  )}
                </CardLeft>
                <CardRight>
                  {isFindEdit ? (
                    <>
                      <FilterDatePickerStyled
                        day={isFindEdit.day ?? dataForCard.day}
                        month={isFindEdit.month ?? dataForCard.month}
                        year={isFindEdit.year ?? dataForCard.year}
                        onClick={({ day, month, year }) => {
                          setTimeLeftEdit((prev: any) => {
                            return prev.map((el: any) => {
                              if (el.type === item.type) {
                                return { ...el, day, month, year, number: isFindEdit?.number ?? numberPbr, typeName: item.typeName };
                              }
                              return el;
                            });
                          });
                        }}
                        hiddenChooseButton={true}
                        dataTest="planned-schedules-left-container.date-picker"
                      />
                      <CloseIconLeft
                        onClick={() =>
                          setTimeLeftEdit((prev: any) => {
                            return prev.filter((el: any) => el.type !== item.type);
                          })
                        }
                      >
                        <Icon width={12} name="close" />
                      </CloseIconLeft>
                    </>
                  ) : (
                    <CardTime>
                      <SubCarTime>{dateFormat(timer, item.typeName, pbrList, numberPbr)}</SubCarTime>
                      <PencilContainer
                        type="secondary"
                        onClick={() =>
                          setTimeLeftEdit((prev: any) => {
                            return [...prev, { type: item.type, day, month, year, number: numberPbr }];
                          })
                        }
                        icon="pencil"
                        widthIcon={20}
                        dataTest="planned-schedules-left-container.edit-date-button"
                      />
                    </CardTime>
                  )}
                </CardRight>
              </CardRow>
              <CardRow>
                <CardLeft>
                  <CardStatus>{/*<Status value={item.status} />*/}</CardStatus>
                </CardLeft>
                <CardRight>
                  <ButtonDownLoad
                    message={item.status === "CREATED" || item.status === "WRONG_HOURS_NUM" ? `Загружается пользователем: ${item.user}` : "Загрузить"}
                    title={item.status === "CREATED" || item.status === "WRONG_HOURS_NUM" ? <Loader /> : <>Загрузить</>}
                    disabled={
                      (item.typeName === "УДДГ" && numberPbr === "-" && isNumberTypeLeft) ||
                      item.status === "CREATED" ||
                      item.status === "WRONG_HOURS_NUM" ||
                      // item.type === isLoadData ||
                      item.status === "CREATED"
                    }
                    onClick={() => {
                      if (item.status !== "CREATED") {
                        loadData(item.type);
                      }
                    }}
                    isLoading={item.status === "CREATED"}
                    dataTest="planned-schedules-left-container.download-button"
                  />
                  {item.status === "WRONG_HOURS_NUM" && (
                    <ButtonGreenSave
                      onClick={() => {
                        setObjectWrong({
                          type: item.typeName,
                          date: dateFormat(timer, item.typeName, pbrList, numberPbr),
                          hourErrors: item?.hourErrors ?? [],
                          taskId: item?.taskId,
                          pgShortName: item?.pgShortName,
                          pgActionDate: item?.pgActionDate,
                        });
                      }}
                    >
                      <Icon width={18} name="error" dataTest="planned-schedules-left-container.wrong-hours-num-icon" />
                    </ButtonGreenSave>
                  )}
                  {item.status === "NOT_ACCEPTED_PG_WITH_LOWER_NUMS" && (
                    <ButtonGreenSave
                      onClick={() => {
                        setObjectNotAccepted({
                          type: item.typeName,
                          date: dateFormat(timer, item.typeName, pbrList, numberPbr),
                          hourErrors: item?.hourErrors ?? [],
                          taskId: item?.taskId,
                          notAccepted: item?.notAccepted ?? {},
                        });
                      }}
                    >
                      <Icon width={18} name="error" dataTest="planned-schedules-left-container.not-accepted-icon" />
                    </ButtonGreenSave>
                  )}
                  {item.status === "ALREADY_EXIST" && (
                    <ButtonGreenSave
                      onClick={() => {
                        setObjectReplace({
                          type: item.typeName,
                          date: dateFormat(timer, item.typeName, pbrList, numberPbr),
                          hourErrors: item?.hourErrors ?? [],
                          taskId: item?.taskId,
                          alreadyCreated: item?.alreadyCreated ?? {},
                        });
                      }}
                    >
                      <Icon width={18} name="error" dataTest="planned-schedules-left-container.already-exist-icon" />
                    </ButtonGreenSave>
                  )}
                  {item.status === "LOADED_LATER" && (
                    <ButtonGreenSave
                      onClick={() => {
                        setObjectLoadedLater({
                          typeName: item.typeName,
                          date: item.loadedLaterDate,
                          taskId: item?.taskId,
                          pgShortName: item?.pgShortName,
                          type: item?.type,
                        });
                      }}
                    >
                      <Icon width={18} name="error" data-test="planned-schedules-left-container.loaded-later-xml-icon" />
                    </ButtonGreenSave>
                  )}
                </CardRight>
              </CardRow>
            </InputCard>
          );
        })}
    </LeftContainerMain>
  );
});
