import React, { FC, useState } from "react";
import styled from "styled-components";
import { Modal } from "components/Modal";

export const ModalStyled = styled(Modal)`
  //height: 300px;
`;

export const Container = styled.div<{ height?: number }>`
  //width: 750px;
  width: 100%;
  height: ${(p) => p.height}px;
  //width: 100%;
  //height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
`;

interface ModalWrongProps {
  object?: any;
  onCancel?: any;
  onConfirm?: any;
}

export const Row = styled.div`
  height: 20px;
  width: 100%;
  border: solid 1px ${(p) => p.theme.lightGray};
  &:first-child {
    border-bottom: none;
  }
`;

interface ModalNotAcceptedProps {
  object?: any;
  onCancel?: any;
  onConfirm?: any;
}

export const ModalNotAccepted: FC<ModalNotAcceptedProps> = (props) => {
  const { object, onCancel, onConfirm } = props;

  const [modalHeight, setModalHeight] = useState(400);

  const notAcceptedNums = object.notAccepted.notAcceptedNums.length > 0 ? object.notAccepted.notAcceptedNums.join(" , ") : "";

  return (
    <ModalStyled
      isOverLay
      title={`Не выполнено формирование команды акцепта ${object?.notAccepted?.pgType ?? ""}-${notAcceptedNums}. Выполнить загрузку ${
        object?.notAccepted?.pgType ?? ""
      }-${object?.notAccepted?.pgNum} ?`}
      onCancel={() => onCancel(false)}
      confirmText="Да"
      cancelText="Нет"
      onConfirm={() => onConfirm(true)}
      width={800}
      height={120} //400
      setModalHeight={setModalHeight}
      dataTestConfirmButton="planned-schedules-not-accepted-modal.confirm-button"
    >
      {/*<Container height={modalHeight - 130}>*/}
      {/*  {object.hourErrors.map((el: any, index: number) => {*/}
      {/*    return <Row key={`hours-${index}`}>{el}</Row>;*/}
      {/*  })}*/}
      {/*</Container>*/}
    </ModalStyled>
  );
};
