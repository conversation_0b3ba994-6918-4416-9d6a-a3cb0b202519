import React, { FC } from "react";
import { Modal } from "components/Modal";
import { format, parseISO } from "date-fns";

interface ModalLoadedLaterProps {
  object: {
    typeName: string;
    taskId: string;
    date?: string; // Ожидается формат YYYY-MM-DD из поля loadedLaterDate api pg/last-tasks
    pgShortName?: string;
    type?: string; // Тип ПГ: "XML", "PBR", "PER" и т.д.
  };
  onCancel: (toLoad: boolean) => void; // Передаёт false
  onConfirm: (toLoad: boolean) => void; // Передаёт true
}

export const ModalLoadedLater: FC<ModalLoadedLaterProps> = (props) => {
  const { object, onCancel, onConfirm } = props;

  const formattedDate = object?.date ? format(parseISO(object.date), "dd.MM.yyyy") : "";

  // Для XML файлов используем реальное название ПГ, для остальных - typeName
  const isXmlFile = object?.type === "XML";
  const pgName = isXmlFile && object?.pgShortName ? object.pgShortName : object?.typeName ?? "";
  const sourceText = isXmlFile ? " из файла" : "";

  const title = `В СРПГ уже загружен более поздний ${pgName} на ${formattedDate}. Выполнить загрузку и запись ${pgName}${sourceText}?`;

  return (
    <Modal
      title={title}
      confirmText="Да"
      cancelText="Нет"
      onCancel={() => onCancel(false)}
      onConfirm={() => onConfirm(true)}
      width={800}
      height={150}
      dataTestConfirmButton="planned-schedules-loaded-later-modal.confirm-button"
    />
  );
};
