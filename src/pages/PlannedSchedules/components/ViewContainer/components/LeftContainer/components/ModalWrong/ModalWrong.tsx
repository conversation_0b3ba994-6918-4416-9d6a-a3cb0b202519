import { FC, useState } from "react";
import { Modal } from "components/Modal";
import styled from "styled-components";
import { format, isValid, parseISO } from "date-fns";

export const ModalStyled = styled(Modal)`
  //height: 300px;
`;

export const Container = styled.div<{ height?: number }>`
  //width: 750px;
  width: 100%;
  height: ${(p) => p.height}px;
  //width: 100%;
  //height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
`;

interface ModalWrongProps {
  object: {
    type: string;
    date: string;
    hourErrors: string[];
    taskId: string;
    pgShortName: string;
    pgActionDate: string; // Строка в формате YYYY-MM-DD
  };
  onCancel: (toLoad: boolean) => void;
  onConfirm: (toLoad: boolean) => void;
}

export const Row = styled.div`
  height: 20px;
  width: 100%;
  border: solid 1px ${(p) => p.theme.lightGray};
  &:first-child {
    border-bottom: none;
  }
`;

export const ModalWrong: FC<ModalWrongProps> = (props) => {
  const { object, onCancel, onConfirm } = props;

  const [modalHeight, setModalHeight] = useState(400);

  const pgShortName = object.pgShortName;
  const parsedDate = parseISO(object.pgActionDate);
  const formattedDate = parsedDate && isValid(parsedDate) ? format(parsedDate, "dd.MM.yyyy") : null;

  const title = `ПГ типа ${pgShortName} из ПАК ОпАМ на дату ${formattedDate} содержит не все часы`;

  return (
    <ModalStyled
      isOverLay
      title={title}
      onCancel={() => onCancel(false)}
      confirmText="Загрузить"
      cancelText="Отменить"
      onConfirm={() => onConfirm(true)}
      width={800}
      height={400}
      setModalHeight={setModalHeight}
      dataTestConfirmButton="planned-schedules-wrong-hours-modal.confirm-button"
    >
      <Container height={modalHeight - 130}>
        {object.hourErrors.map((el: any, index: number) => {
          return <Row key={`hours-${index}`}>{el}</Row>;
        })}
      </Container>
    </ModalStyled>
  );
};
