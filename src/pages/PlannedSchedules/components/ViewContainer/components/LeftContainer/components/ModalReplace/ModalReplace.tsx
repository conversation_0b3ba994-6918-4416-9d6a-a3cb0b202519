import React, { FC, useState } from "react";
import { Modal } from "components/Modal";
import styled from "styled-components";
import { format, parseISO } from "date-fns";

interface ModalWrongProps {
  object?: any;
  onCancel?: any;
  onConfirm?: any;
}

export const Container = styled.div<{ height?: number }>`
  //width: 750px;
  width: 100%;
  height: ${(p) => p.height}px;
  //width: 100%;
  //height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
`;

export const ModalReplace: FC<ModalWrongProps> = (props) => {
  const { object, onCancel, onConfirm } = props;
  const [modalHeight, setModalHeight] = useState(400);

  const date = object?.alreadyCreated?.dates.length > 0 ? object?.alreadyCreated?.dates.join(" , ") : "";

  const getTypeAndNumber = () => {
    if (object?.alreadyCreated?.pgNum) {
      return `${object?.type}-${object?.alreadyCreated?.pgNum}`;
    } else {
      return `${object?.type}`;
    }
  };

  const textObject = object?.alreadyCreated?.pgNum
    ? `${object?.alreadyCreated?.pgType ?? ""}-${object?.alreadyCreated?.pgNum}`
    : `${object?.alreadyCreated?.pgType ?? ""}`;

  const formattedDate = object?.alreadyCreated?.actionDate ? format(parseISO(object.alreadyCreated.actionDate), "dd.MM.yyyy") : "";

  return (
    <Modal
      title={`${textObject} на ${formattedDate} загружен ${date}. Выполнить повторную загрузку?`}
      confirmText="Загрузить"
      cancelText="Отменить"
      onCancel={() => onCancel(false)}
      onConfirm={() => onConfirm(true)}
      width={800}
      height={150}
      setModalHeight={setModalHeight}
      dataTestConfirmButton="planned-schedules-repeat-modal.confirm-button"
    />
  );
};
