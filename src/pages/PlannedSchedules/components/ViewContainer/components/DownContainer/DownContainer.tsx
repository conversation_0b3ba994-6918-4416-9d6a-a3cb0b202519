import React, { FC, useEffect, useState } from "react";
import { isModeCenter } from "utils/getMode";
import {
  ButtonActionCell,
  Cell,
  CellStatusContainer,
  ComboboxStyled,
  CustomCellDC,
  Down,
  LoaderContainerFile,
  LoaderLabel,
  Row,
  SpinnerContainer,
  FinishedAtContainer,
  FinishedAtDetailAction,
  TableRow,
  InformationDetail,
  ComboboxRetry,
} from "../../ViewContainer.style";
import { useStores } from "stores/useStore";
import { Loader } from "components/Loader";
import { Combobox } from "components/Combobox";
import { Table } from "components/Table";
import { StatusCircle } from "../../../DistributionContainer/DistributionContainer.style";
import { AccessControl } from "components/AccessControl";
import { observer } from "mobx-react";
import { prepareDateTable } from "../../../../../../helpers/DateUtils";
import { Icon } from "components/Icon";
import { DetailModal } from "./components/DetailModal";
import { Label } from "../../../../../../components/Label";
import { prepareDataTable } from "../../../../../../utils";

const statusRenderTooltip = (status: string, row: any, isHidden: boolean) => {
  if (status === "WAITING_FOR_GLOBAL_ACCEPT") {
    return row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : "Квитанция о записи в ОИК СК-11 получена. Ожидается акцепт"; //"ПГ записан. Ожидается акцепт.";
  }
  if (status === "SAVING_PG") {
    return row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : "Запись акцептованного ПГ";
  }
  if (status === "SAVING_PRE_PG") {
    return row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : "Запись неакцептованного ПГ";
  }
  if (status === "SAVED_PRE_PG") {
    return row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : "ПГ записан. Ожидается акцепт"; //Выполнена успешная запись неакцептованного ПГ
  }
  if (status === "ERROR_SAVING_PRE_PG") {
    return row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : "Ошибка записи неакцептованного ПГ";
  }
  if (status === "ERROR_SAVING_PG") {
    return row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : "Ошибка записи акцептованного ПГ";
  }
  if (status === "DONE") {
    const time = row.endAt ? prepareDateTable(row.endAt) : prepareDateTable(row.startAt);
    return row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : `Обработка завершена`; //ПГ записан в ОИК СК-11 ${time}
  }
  if (status === "CONFIRMED") {
    const time = row.endAt ? prepareDateTable(row.endAt) : prepareDateTable(row.startAt);
    return row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : `Обработка завершена`;
  }
  if (status === "CREATED") {
    const time = row.endAt ? prepareDateTable(row.endAt) : prepareDateTable(row.startAt);
    return row.noData
      ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется"
      : row.tabId === "modes" || row.tabId === "srdk"
      ? "Запись акцептованного ПГ"
      : `Обработка завершена`;
  }
  if (status === "FAILED") {
    return row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : row?.error ?? "Ошибка";
  }
  if (status === "RESEND_REQUIRED") {
    return row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : row?.error ?? "Требуется повторная отправка";
  }
  if (status === "RESEND_REQUIRED_ACCEPT") {
    return row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : row?.error ?? "Требуется повторная отправка";
  }
  if (status === "UNSENT") {
    return row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : "Отправка не выполнялась";
  }
  if (status === "UNSENT_TO_DC") {
    return row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : "Отправка в ДЦ не требуется";
  }
  return "Нет данных";
};

const statusRender = (status: string, isHidden: boolean, row: any) => {
  if (status === "WAITING_FOR_GLOBAL_ACCEPT") {
    return (
      <CellStatusContainer title={row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : "Квитанция о записи в ОИК СК-11 получена. Ожидается акцепт"} data-test="planned-schedules-down-container.await-status-pg">
        {/*"ПГ записан. Ожидается акцепт"*/}
        <StatusCircle status="AWAIT" />
        {/*{isHidden && <>ПГ записан. Ожидается акцепт.</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "SAVING_PG") {
    return (
      <CellStatusContainer title="Запись акцептованного ПГ">
        <StatusCircle status="AWAIT" data-test="planned-schedules-down-container.await-status-pg" />
        {/*{isHidden && <>Запись акцептованного ПГ.</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "SAVING_PRE_PG") {
    return (
      <CellStatusContainer title="Запись неакцептованного ПГ">
        <StatusCircle status="AWAIT" data-test="planned-schedules-down-container.await-status-pg" />
        {/*{isHidden && <>Запись акцептованного ПГ.</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "SAVED_PRE_PG") {
    return (
      <CellStatusContainer title="ПГ записан. Ожидается акцепт">
        <StatusCircle status="AWAIT" data-test="planned-schedules-down-container.await-status-pg" />
        {/*{isHidden && <>Запись акцептованного ПГ.</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "ERROR_SAVING_PRE_PG") {
    return (
      <CellStatusContainer title="Ошибка записи неакцептованного ПГ">
        <StatusCircle status="FAIL" data-test="planned-schedules-down-container.fail-status-pg" />
        {/*{isHidden && <>Ошибка записи неакцептованного ПГ.</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "ERROR_SAVING_PG") {
    return (
      <CellStatusContainer title="Ошибка записи акцептованного ПГ">
        <StatusCircle status="FAIL" data-test="planned-schedules-down-container.fail-status-pg" />
        {/*{isHidden && <>Ошибка записи акцептованного ПГ.</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "DONE") {
    return (
      <CellStatusContainer title={row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : "Обработка завершена"}>
        <StatusCircle status="DONE" data-test="planned-schedules-down-container.success-status-pg" />
        {/*{isHidden && <>Обработка завершена.</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "CREATED") {
    if (row.tabId === "modes" || row.tabId === "srdk") {
      return (
        <CellStatusContainer title={row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : "Запись акцептованного ПГ"}>
          <StatusCircle status="AWAIT" data-test="planned-schedules-down-container.await-status-pg" />
        </CellStatusContainer>
      );
    } else {
      return (
        <CellStatusContainer title={row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : "Обработка завершена"}>
          <StatusCircle />
        </CellStatusContainer>
      );
    }
  }
  if (status === "CONFIRMED") {
    return (
      <CellStatusContainer title={row.noData ? "ПГ получен в ДЦ. Запись ПГ в ОИК ДЦ не требуется" : "Обработка завершена"}>
        <StatusCircle status="DONE" data-test="planned-schedules-down-container.success-status-pg" />
        {/*{isHidden && <>Обработка завершена.</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "FAILED") {
    return (
      <CellStatusContainer title={row?.error ?? "Ошибка"}>
        <StatusCircle status="FAIL" data-test="planned-schedules-down-container.fail-status-pg" />
        {/*{isHidden && <>{row?.error}</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "RESEND_REQUIRED") {
    return (
      <CellStatusContainer title={row?.error ?? "Требуется повторная отправка"}>
        <StatusCircle status="RESEND_REQUIRED" />
        {/*{isHidden && <>{row?.error}</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "RESEND_REQUIRED_ACCEPT") {
    return (
      <CellStatusContainer title={row?.error ?? "Требуется повторная отправка"}>
        <StatusCircle status="RESEND_REQUIRED_ACCEPT" />
        {/*{isHidden && <>{row?.error}</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "UNSENT") {
    return (
      <CellStatusContainer title="Отправка не выполнялась">
        <StatusCircle data-test="planned-schedules-down-container.no-data-status-pg" />
        {/*{isHidden && <>Нет данных</>}*/}
      </CellStatusContainer>
    );
  }
  if (status === "UNSENT_TO_DC") {
    return (
      <CellStatusContainer title="Нет данных" data-test="planned-schedules-down-container.no-data-status-pg">
        <StatusCircle />
      </CellStatusContainer>
    );
  }
  return (
    <CellStatusContainer title="Нет данных" data-test="planned-schedules-down-container.no-data-status-pg">
      <StatusCircle />
      {/*{isHidden && <>Нет данных</>}*/}
    </CellStatusContainer>
  );
};

interface DownContainerProps {
  isCenter: boolean;
  selected: any[];
  setSelected: any;
  year: any;
  month: any;
  day: any;
  isCenterMode: any;
}

export const DownContainer: FC<DownContainerProps> = observer((props) => {
  const { isCenter, selected, setSelected, year, month, day, isCenterMode } = props;
  const { plannedSchedulesStore, tableStore } = useStores();
  const {
    infoForSelected,
    isLoadingXML,
    isLoadingXMLPackage,
    viewTableData,
    isAcceptSystem,
    isRepeatAccept,
    isLoadingRetry,
    isLoadingAccept,
    isLoadingBeforeAccept,
    isLoadingResetAccept,
  } = plannedSchedulesStore;

  const tableDC = [];

  if (infoForSelected.oik) {
    tableDC.push({
      tabId: "oik",
      name: "Запись в ОИК СК-11",
      status: infoForSelected?.oik?.status,
      noData: infoForSelected?.oik?.noData,
      finishedAt: infoForSelected?.oik?.finishedAt ?? "",
      ...infoForSelected?.oik,
    });
  } else {
    tableDC.push({ tabId: "oik", name: "Запись в ОИК СК-11", status: "UNAVAILABLE", finishedAt: "", noData: false });
  }

  if (infoForSelected.eg3) {
    tableDC.push({
      tabId: "eg3",
      name: "Запись в ёЖ-3",
      status: infoForSelected?.eg3?.status,
      finishedAt: infoForSelected?.eg3?.finishedAt ?? "",
      noData: infoForSelected?.eg3?.noData,
      ...infoForSelected?.eg3,
    });
  } else {
    tableDC.push({ tabId: "eg3", name: "Запись в ёЖ-3", status: "UNAVAILABLE", finishedAt: "", noData: false });
  }

  if (infoForSelected.modes) {
    tableDC.push({
      tabId: "modes",
      name: "Запись в MODES-Terminal",
      status: infoForSelected?.modes?.status,
      finishedAt: infoForSelected?.modes?.finishedAt ?? "",
      noData: infoForSelected?.modes?.noData,
      ...infoForSelected?.modes,
    });
  } else {
    tableDC.push({ tabId: "modes", name: "Запись в MODES-Terminal", status: "UNAVAILABLE", finishedAt: "", noData: false });
  }
  if (infoForSelected.srdk) {
    tableDC.push({
      tabId: "srdk",
      name: "Запись в СРДК",
      status: infoForSelected?.srdk?.status,
      finishedAt: infoForSelected?.srdk?.finishedAt ?? "",
      noData: infoForSelected?.srdk?.noData,
      ...infoForSelected?.srdk,
    });
  } else {
    tableDC.push({ tabId: "srdk", name: "Запись в СРДК", status: "UNAVAILABLE", finishedAt: "", noData: false });
  }

  const sendAccept = () => {
    plannedSchedulesStore.sendAccept(selected[0]).then(() => {
      const pgId = viewTableData.find(({ tabId }: { tabId: any }) => tabId === selected[0]).pgId;
      plannedSchedulesStore.getTableViewLoad(year, month, day, pgId);
      // plannedSchedulesStore.stopView();
      // plannedSchedulesStore.initTableView(year, month, day, true, isModeCenter, selected[0]);

      // plannedSchedulesStore.startCheckingTableView(year, month, day, selected[0], false);
      // plannedSchedulesStore.loadInfoForSelected(selected[0]);
    });
  };

  const retrySystem = (id: string) => {
    plannedSchedulesStore.retrySystem(id).then(() => {
      const pgId = viewTableData.find(({ tabId }: { tabId: any }) => tabId === selected[0]).pgId;
      plannedSchedulesStore.getTableViewLoad(year, month, day, pgId);
      // plannedSchedulesStore.stopView();
      // plannedSchedulesStore.initTableView(year, month, day, true, isModeCenter, selected[0]);
      // plannedSchedulesStore.initTableView(year, month, day, false, isModeCenter, selected[0]);
      // plannedSchedulesStore.loadInfoForSelected(selected[0]);
    });
  };

  let defaultColumnsDC: any[];

  const getDefaultColumnsDc = () => {
    if (innerWidth === 1600) {
      return [
        { name: "status", title: "Статус", width: 100 }, //fixedColumn: true, left: "0" isSearch: true
        { name: "name", title: "Внешняя система", width: 359 },
        { name: "finishedAt", title: "Время записи", width: 359 },
        { name: "action", title: "Действие", width: 359 },
      ];
    }
    if (innerWidth === 1680) {
      return [
        { name: "status", title: "Статус", width: 100 }, //fixedColumn: true, left: "0" isSearch: true
        { name: "name", title: "Внешняя система", width: 386 },
        { name: "finishedAt", title: "Время записи", width: 386 },
        { name: "action", title: "Действие", width: 386 },
      ];
    }
    if (innerWidth === 1920) {
      return [
        { name: "status", title: "Статус", width: 100 }, //fixedColumn: true, left: "0" isSearch: true
        { name: "name", title: "Внешняя система", width: 476 },
        { name: "finishedAt", title: "Время записи", width: 500 },
        { name: "action", title: "Действие", width: 800 },
      ];
    }
    return [
      { name: "status", title: "Статус", width: 100 }, //fixedColumn: true, left: "0" isSearch: true
      { name: "name", title: "Внешняя система", width: 476 },
      { name: "finishedAt", title: "Время записи", width: 500 },
      { name: "action", title: "Действие", width: 800 },
    ];
  };

  defaultColumnsDC = getDefaultColumnsDc();

  const [columnsDC, setColumnsDC] = useState<any>([]);

  useEffect(() => {
    tableStore.getTableParams(53).then((data: any) => {
      if (data) {
        setColumnsDC(data);
      } else {
        setColumnsDC(defaultColumnsDC);
      }
    });
    return () => {
      setSelected([]);
    };
  }, []);

  const [detailModal, setDetailModal] = useState(null);

  const customCellDC = [
    {
      name: "status",
      render: (value: any, row: any) => {
        const isHidden = !row.noData;
        return <CustomCellDC>{statusRender(value, isHidden, row)}</CustomCellDC>;
      },
      tooltip: (value: any, row: any) => {
        const isHidden = !row.noData;
        return statusRenderTooltip(value, row, isHidden);
      },
    },
    {
      name: "finishedAt",
      render: (value: any, row: any) => {
        const isHidden = !row.noData;
        return (
          <FinishedAtContainer>
            <Label>{isHidden ? (value.length > 0 ? prepareDateTable(value) : "") : ""}</Label>
            {row.status && row.status !== "UNSENT" && row.status !== "CREATED" && row.status !== "UNAVAILABLE" && row.status !== "SAVING_PRE_PG" && (
              <FinishedAtDetailAction onClick={() => setDetailModal(row.system)} title="История">
                <InformationDetail>
                  <Icon width={20} name="information" />
                </InformationDetail>
              </FinishedAtDetailAction>
            )}
          </FinishedAtContainer>
        );
      },
      tooltip: (value: any, row: any) => {
        const isHidden = !row.noData;
        return isHidden ? (value.length > 0 ? prepareDateTable(value) : "") : "";
      },
    },
    {
      name: "action",
      render: (_: any, row: any) => {
        const isLoading = row?.localAccept?.status === "CREATED" ?? false;
        return (
          <CustomCellDC>
            <AccessControl rules={["engineer"]}>
              {row.canStorePre ? (
                <ButtonActionCell
                  title={isLoading || isLoadingAccept ? <Loader /> : <>Отправить</>}
                  message="Отправить"
                  disabled={isLoading || isLoadingAccept}
                  onClick={() => sendAccept()}
                />
              ) : (
                <ButtonActionCell isEmpty={true} title="" />
              )}
            </AccessControl>
            <ButtonActionCell
              title={isLoadingRetry === row.systemStatusId ? <Loader /> : <>Повторить</>}
              message="Повторить"
              type="secondary"
              icon={isLoadingRetry === row.systemStatusId ? null : "reset"}
              disabled={!row.canReExport || isLoadingRetry === row.systemStatusId} //(!row.systemStatusId || row.status !== "FAILED"
              onClick={() => retrySystem(row.systemStatusId)}
              isLoading={isLoadingRetry === row.systemStatusId}
            />
          </CustomCellDC>
        );
      },
      tooltip: (value: any, row: any) => {
        return null;
      },
    },
  ];

  const sendPGBeforeAccept = () => {
    const pgId = viewTableData.find(({ tabId }: { tabId: any }) => tabId === selected[0]).pgId;
    plannedSchedulesStore.sendPGBeforeAccept(pgId).then(() => {
      plannedSchedulesStore.getTableViewLoad(year, month, day, pgId);
      // plannedSchedulesStore.stopView();
      // plannedSchedulesStore.initTableView(year, month, day, true, isModeCenter, selected[0]);
    });
  };

  const resetAccept = (type: string) => {
    const pgId = viewTableData.find(({ tabId }: { tabId: any }) => tabId === selected[0]).pgId;
    plannedSchedulesStore.resetAccept(pgId, type).then(() => {
      plannedSchedulesStore.getTableViewLoad(year, month, day, pgId);
      // plannedSchedulesStore.stopView();
      // plannedSchedulesStore.initTableView(year, month, day, true, isModeCenter, selected[0]);
    });
  };

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("53").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "name", direction: "asc" }]);
      }
    });
  }, []);

  const retryList = [
    { value: "ALL", label: "Отправить во все ИУС" },
    { value: "OIK", label: "Отправить в ОИК СК-11" },
    { value: "MODES", label: "Отправить в MODES-Terminal" },
    { value: "SRDK", label: "Отправить в СРДК" },
  ];

  const isOpam = infoForSelected.type !== "ПЭР" && infoForSelected.type !== "ПДГ";

  return (
    <>
      {detailModal && (
        <DetailModal
          onCancel={() => setDetailModal(null)}
          // onConfirm={() => {
          //   setDetailModal(null);
          // }}
          system={detailModal}
          pgId={viewTableData.find(({ tabId }: { tabId: any }) => tabId === selected[0])?.pgId}
        />
      )}
      {isModeCenter && isCenter ? (
        <Down isOpam={isOpam} isSelected={selected.length > 0} isCenterMode={isCenterMode} data-test="planned-schedules-detailed-block.container">
          <Row>
            <Cell>Название</Cell>
            <Cell data-test="planned-schedules-detailed-block.name-cell">{infoForSelected["name"] ?? ""}</Cell>
          </Row>
          <Row>
            <Cell>Тип пакета</Cell>
            <Cell data-test="planned-schedules-detailed-block.type-cell">{infoForSelected["type"] ?? ""}</Cell>
          </Row>
          <Row>
            <Cell>Временной интервал (начало)</Cell>
            <Cell data-test="planned-schedules-detailed-block.start-cell">{prepareDateTable(infoForSelected["startDate"]) ?? ""}</Cell>
          </Row>
          <Row>
            <Cell>Временной интервал (конец)</Cell>
            <Cell data-test="planned-schedules-detailed-block.end-cell">{prepareDateTable(infoForSelected["endDate"]) ?? ""}</Cell>
          </Row>
          <Row>
            <Cell>Действие</Cell>
            <Cell>
              {!isLoadingXML ? (
                <Combobox
                  placeholder="Выгрузка в XML"
                  icon="xml"
                  items={[
                    { value: "default", label: "Обычный XML", icon: "xml", color: "default" },
                    { value: "detail", label: "Детальный XML", icon: "xml", color: "default" },
                  ]}
                  position="top"
                  disabled={selected.length === 0}
                  onChange={({ value }) => {
                    plannedSchedulesStore.getXmlData(value, selected[0]);
                  }}
                  dataTest="planned-schedules-detailed-block.export-combobox"
                />
              ) : (
                <LoaderContainerFile>
                  <SpinnerContainer>
                    <Loader />
                  </SpinnerContainer>
                  <LoaderLabel>Идет выгрузка</LoaderLabel>
                </LoaderContainerFile>
              )}
            </Cell>
          </Row>
          {isOpam && (
            <Row>
              <Cell>Акцепт в ОпАМ</Cell>
              <Cell data-test="planned-schedules-detailed-block.accept-cell">{infoForSelected["opam"]?.storedAt ? prepareDateTable(infoForSelected["opam"]?.storedAt) : ""}</Cell>
            </Row>
          )}
        </Down>
      ) : (
        <Down isModeCenter={isModeCenter && isCenter} isSelected={selected.length > 0} data-test="planned-schedules-detailed-block.container">
          <Row>
            <Cell full data-test="planned-schedules-detailed-block.name-cell">{infoForSelected ? infoForSelected?.item?.name : ""}</Cell>
          </Row>
          <Row>
            <Cell>Тип пакета :</Cell>
            <Cell data-test="planned-schedules-detailed-block.type-cell">
              {infoForSelected ? infoForSelected?.item?.type : ""}
              {!isLoadingXMLPackage ? (
                <ComboboxStyled
                  placeholder="Выгрузка в XML"
                  dataTest="planned-schedules-detailed-block.export-combobox"
                  icon="xml"
                  items={[
                    { value: "default", label: "Обычный XML", icon: "xml", color: "default" },
                    { value: "detail", label: "Детальный XML", icon: "xml", color: "default" },
                  ]}
                  disabled={selected.length === 0}
                  onChange={({ value }) => {
                    plannedSchedulesStore.getXmlDataPackage(value, selected[0]);
                  }}
                />
              ) : (
                <LoaderContainerFile>
                  <SpinnerContainer>
                    <Loader />
                  </SpinnerContainer>
                  <LoaderLabel>Идет выгрузка</LoaderLabel>
                </LoaderContainerFile>
              )}
            </Cell>
          </Row>
          <Row>
            <Cell>Инициатор загрузки ПГ:</Cell>
            <Cell>{infoForSelected?.item?.loader ?? ""}</Cell>
          </Row>
          <Row>
            <Cell>Время получения ПГ в ДЦ:</Cell>
            <Cell data-test="planned-schedules-detailed-block.start-cell">{prepareDateTable(infoForSelected?.item?.loadedAt) ?? ""}</Cell>
          </Row>
          <Row>
            <Cell>Инициатор команды Акцепт:</Cell>
            <Cell>{infoForSelected?.item?.globalAccept?.author ?? ""}</Cell>
          </Row>
          <Row>
            <Cell>Акцепт получен в ДЦ:</Cell>
            <Cell>{infoForSelected?.item?.globalAccept?.acceptedAt ? prepareDateTable(infoForSelected?.item?.globalAccept?.acceptedAt) : ""}</Cell>
          </Row>
          <Row>
            <Cell>Действие :</Cell>
            <Cell>
              {/*{infoForSelected?.item?.accept?.canRepeat && (*/}
              <ButtonActionCell
                title={isLoadingBeforeAccept ? <Loader /> : "Отправить ПГ (до акцепта)"}
                type="primary"
                onClick={() => sendPGBeforeAccept()}
                disabled={!infoForSelected?.item?.localAccept?.canRepeat || isLoadingBeforeAccept}
                // isLoading={isLoadingBeforeAccept}
              />
              {/*)}*/}
              {/*{infoForSelected?.item?.globalAccept?.canRepeat && (*/}
              {isLoadingResetAccept ? (
                <ButtonActionCell title={<Loader />} disabled />
              ) : (
                <ComboboxRetry
                  items={retryList}
                  width={200}
                  selectedValue={null}
                  placeholder={"Повторить акцепт"}
                  disabled={!infoForSelected?.item?.globalAccept?.canRepeat || isLoadingResetAccept}
                  onChange={({ value }) => {
                    resetAccept(value);
                  }}
                />
              )}
              {/*<ButtonActionCell*/}
              {/*  title={isLoadingResetAccept ? <Loader /> : "Повторить акцепт"}*/}
              {/*  type="primary"*/}
              {/*  onClick={() => resetAccept()}*/}
              {/*  disabled={!infoForSelected?.item?.globalAccept?.canRepeat || isLoadingResetAccept}*/}
              {/*  // isLoading={isLoadingResetAccept}*/}
              {/*/>*/}
            </Cell>
          </Row>
          <Table
            columns={columnsDC}
            setColumns={setColumnsDC}
            tableData={prepareDataTable(tableDC)}
            isLoading={false}
            // title="Запись"
            tableKey={53}
            defaultColumns={defaultColumnsDC}
            customCells={customCellDC}
            tableHeight={130}
            heightContainer={100}
            initSorting={initSorting}
            disabledSearches={["status", "action"]}
            dataTestRows="planned-schedules-down-container.table-rows"
          />
        </Down>
      )}
    </>
  );
});
