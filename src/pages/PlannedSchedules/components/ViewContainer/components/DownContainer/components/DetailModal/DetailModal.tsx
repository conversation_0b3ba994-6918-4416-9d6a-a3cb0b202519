import { FC, useEffect, useState } from "react";
import styled, { css } from "styled-components";
import { Modal } from "components/Modal";
import { observer } from "mobx-react";
import { useStores } from "stores/useStore";
import { Table } from "components/Table";
import { prepareDateTable } from "helpers/DateUtils";
import { prepareDataTable } from "../../../../../../../../utils";

export const TableContainer = styled.div<{ height?: number }>`
  width: 100%;
  height: ${(p) => p.height}px;
`;

export const StatusContainer = styled.div<{ status?: any }>`
  border-radius: 4px;
  width: 100px;
  padding: 2px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${(p) => p.theme.white};
  ${(p) =>
    p.status === "Успешно" &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport};
    `}
  ${(p) =>
    p.status === "Ошибка" &&
    css`
      background-color: ${(p) => p.theme.redActiveSupport};
    `}
`;

export const ModalStyled = styled(Modal)`
  //width: 900px;
  //height: 400px;
`;

export interface DetailModalProps {
  onCancel?: any;
  system?: any;
  pgId?: any;
}

const ProcessedAtContainer = styled.div`
  color: ${(p) => p.theme.textColor};
`;

export const DetailModal: FC<DetailModalProps> = observer((props) => {
  const { onCancel, system, pgId } = props;
  const { plannedSchedulesStore } = useStores();
  const { infoDetailModalViewExternalSystem } = plannedSchedulesStore;

  useEffect(() => {
    plannedSchedulesStore.initDetailModalView(system, pgId);
  }, []);

  const defaultColumns: any[] = [
    { name: "text", title: "Событие", width: 400 },
    { name: "status", title: "Статус события", width: 200 },
    { name: "processedAt", title: "Время события", width: 200 },
  ];

  const [columns, setColumns] = useState(defaultColumns);

  const customCells = [
    {
      name: "processedAt",
      render: (value: any) => {
        return <ProcessedAtContainer>{value ? prepareDateTable(value) : ""}</ProcessedAtContainer>;
      },
    },
    {
      name: "status",
      render: (value: any) => {
        return <StatusContainer status={value}>{value}</StatusContainer>;
      },
    },
  ];

  const [modalHeight, setModalHeight] = useState(400);

  return (
    <ModalStyled
      width={900}
      height={400}
      setModalHeight={setModalHeight}
      onCancel={onCancel}
      isOverLay
      title={`Запись в ${infoDetailModalViewExternalSystem.systemName}-${infoDetailModalViewExternalSystem.pgName}`}
    >
      <TableContainer height={modalHeight - 100}>
        <Table
          customCells={customCells}
          columns={columns}
          setColumns={setColumns}
          defaultColumns={[]}
          tableData={prepareDataTable(infoDetailModalViewExternalSystem?.events) ?? []}
          tableHeight={340}
        />
      </TableContainer>
    </ModalStyled>
  );
});
