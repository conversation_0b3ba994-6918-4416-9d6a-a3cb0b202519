import styled, { css } from "styled-components";
import { Button } from "components/Button";
import { Icon } from "components/Icon";
import { Modal } from "components/Modal";
import { Status } from "components/Status";
import { Combobox } from "components/Combobox";
import { FilterDatePicker } from "components/FilterDatePicker";

export const TableContainer: React.FC<React.PropsWithChildren<{ isOpen?: boolean }>> = styled.div<{ isOpen?: boolean }>`
  height: calc(100vh - 52px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 92px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 90px);
  }
  ${(p) =>
    p.isOpen &&
    css`
      height: calc(100vh - 300px);
      @media (min-height: 520px) and (max-height: 800px) {
        height: calc(100vh - 300px);
      }
      @media (min-height: 800px) and (max-height: 900px) {
        height: calc(100vh - 300px);
      }
    `}
`;

//  height: 900px;
//   @media (min-height: 520px) and (max-height: 599px) {
//     height: 420px;
//   }
//   @media (min-height: 600px) and (max-height: 619px) {
//     height: 500px;
//   }
//   @media (min-height: 620px) and (max-height: 639px) {
//     height: 530px;
//   }
//   @media (min-height: 640px) and (max-height: 699px) {
//     height: 545px;
//   }
//   @media (min-height: 700px) and (max-height: 739px) {
//     height: 610px;
//   }
//   @media (min-height: 740px) and (max-height: 767px) {
//     height: 650px;
//   }
//   @media (min-height: 768px) and (max-height: 799px) {
//     height: 670px;
//     //  750
//   }
//   @media (min-height: 800px) and (max-height: 839px) {
//     height: 700px;
//   }
//   @media (min-height: 840px) and (max-height: 859px) {
//     height: 740px;
//   }
//   @media (min-height: 870px) and (max-height: 899px) {
//     height: 780px;
//   }
//   @media (min-height: 900px) and (max-height: 929px) {
//     height: 840px;
//   }
//   @media (min-height: 930px) and (max-height: 999px) {
//     height: 908px;
//   }
//   @media (min-height: 1000px) and (max-height: 1023px) {
//     height: 940px;
//   }
//   @media (min-height: 1024px) and (max-height: 1049px) {
//     height: 950px;
//   }
//   @media (min-height: 1050px) and (max-height: 1079px) {
//     height: 990px;
//   }
//   @media (min-height: 1080px) and (max-height: 1159px) {
//     height: 1000px;
//   }
//   @media (min-height: 1160px) and (max-height: 1199px) {
//     height: 1110px;
//   }
//   //
//   // new
//   @media (min-height: 1200px) and (max-height: 1299px) {
//     height: 1150px;
//   }
//   @media (min-height: 1300px) and (max-height: 1399px) {
//     height: 1250px;
//   }
//   @media (min-height: 1400px) and (max-height: 1499px) {
//     height: 1350px;
//   }
//   @media (min-height: 1500px) and (max-height: 1599px) {
//     height: 1450px;
//   }
//   @media (min-height: 1600px) and (max-height: 1699px) {
//     height: 1550px;
//   }
//   @media (min-height: 1700px) and (max-height: 1799px) {
//     height: 1650px;
//   }
//   @media (min-height: 1800px) and (max-height: 1899px) {
//     height: 1750px;
//   }
//   @media (min-height: 1900px) and (max-height: 1999px) {
//     height: 1850px;
//   }
//   @media (min-height: 2000px) and (max-height: 2099px) {
//     height: 1950px;
//   }
//   @media (min-height: 2100px) and (max-height: 2199px) {
//     height: 2050px;
//   }
//   @media (min-height: 2200px) and (max-height: 2299px) {
//     height: 2150px;
//   }
//   @media (min-height: 2300px) and (max-height: 2399px) {
//     height: 2250px;
//   }
//   @media (min-height: 2400px) and (max-height: 2499px) {
//     height: 2350px;
//   }
//   ${(p) =>
//     p.isOpen &&
//     css`
//       height: 800px;
//       @media (min-height: 520px) and (max-height: 599px) {
//         height: 250px;
//       }
//       @media (min-height: 600px) and (max-height: 639px) {
//         height: 320px;
//       }
//       @media (min-height: 620px) and (max-height: 639px) {
//         height: 360px;
//       }
//       @media (min-height: 640px) and (max-height: 699px) {
//         height: 360px;
//       }
//       @media (min-height: 700px) and (max-height: 767px) {
//         height: 420px;
//       }
//       @media (min-height: 740px) and (max-height: 767px) {
//         height: 460px;
//       }
//       @media (min-height: 768px) and (max-height: 799px) {
//         height: 480px;
//       }
//       @media (min-height: 800px) and (max-height: 839px) {
//         height: 520px;
//       }
//       @media (min-height: 840px) and (max-height: 859px) {
//         height: 560px;
//       }
//       @media (min-height: 860px) and (max-height: 869px) {
//         height: 580px;
//       }
//       @media (min-height: 870px) and (max-height: 899px) {
//         height: 590px;
//       }
//       @media (min-height: 900px) and (max-height: 929px) {
//         height: 610px;
//       }
//       @media (min-height: 930px) and (max-height: 999px) {
//         height: 660px;
//       }
//       @media (min-height: 1000px) and (max-height: 1023px) {
//         height: 720px;
//       }
//       @media (min-height: 1024px) and (max-height: 1049px) {
//         height: 740px;
//       }
//       @media (min-height: 1050px) and (max-height: 1079px) {
//         height: 760px;
//       }
//       @media (min-height: 1080px) and (max-height: 1200px) {
//         height: 790px;
//       }
//       @media (min-height: 1160px) and (max-height: 1199px) {
//         height: 890px;
//       }
//       // new
//       @media (min-height: 1200px) and (max-height: 1299px) {
//         height: 920px;
//       }
//       @media (min-height: 1300px) and (max-height: 1399px) {
//         height: 1020px;
//       }
//       @media (min-height: 1400px) and (max-height: 1499px) {
//         height: 1120px;
//       }
//       @media (min-height: 1500px) and (max-height: 1599px) {
//         height: 1220px;
//       }
//       @media (min-height: 1600px) and (max-height: 1699px) {
//         height: 1320px;
//       }
//       @media (min-height: 1700px) and (max-height: 1799px) {
//         height: 1420px;
//       }
//       @media (min-height: 1800px) and (max-height: 1899px) {
//         height: 1520px;
//       }
//       @media (min-height: 1900px) and (max-height: 1999px) {
//         height: 1620px;
//       }
//       @media (min-height: 2000px) and (max-height: 2099px) {
//         height: 1720px;
//       }
//       @media (min-height: 2100px) and (max-height: 2199px) {
//         height: 1820px;
//       }
//       @media (min-height: 2200px) and (max-height: 2299px) {
//         height: 1920px;
//       }
//       @media (min-height: 2300px) and (max-height: 2399px) {
//         height: 2020px;
//       }
//       @media (min-height: 2400px) and (max-height: 2499px) {
//         height: 2120px;
//       }
//     `}

export const Down = styled.div<{ isModeCenter?: boolean; isSelected?: boolean; innerHeight?: number; height?: number; isOpam?: boolean; isCenterMode?: boolean }>`
  width: 100%;
  border-radius: 8px;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  //height: 0;
  ${(p) =>
    p.isSelected &&
    p.isCenterMode &&
    css`
      //margin-top: 24px;
      margin-top: ${p.isOpam ? 48 : 75}px;
      //height: 170px;
    `}
  ${(p) =>
    p.isSelected &&
    !p.isCenterMode &&
    css`
      //margin-top: 24px;
      margin-top: 24px;
      //height: 170px;
    `}
`;

export const InformationDetail = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  padding-top: 4px;
  margin-left: 4px;
`;

export const TableRow = styled.div`
  border: solid 1px red;
  height: 170px;
  overflow: hidden;
`;

export const FinishedAtDetailAction = styled.div`
  //position: absolute;
  //margin-top: 4px;
  //margin-left: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${(p) => p.theme.primaryColor};
  cursor: pointer;
  transition: all 0.2s;
  &:hover {
    color: ${(p) => p.theme.primaryColorHover};
  }
  &:active {
    color: ${(p) => p.theme.primaryColorActive};
  }
`;

export const FinishedAtContainer = styled.div`
  //position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
`;

export const ButtonGreenSave = styled.div<{ isXML?: boolean }>`
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${(p) => p.theme.orangeActiveSupport};
  background-color: ${(p) => p.theme.white};
  cursor: pointer;
  position: absolute;
  //top: 56px;
  top: 54px;
  left: auto;
  right: 2px;
  padding-top: 3px;
  border-radius: 6px;
  transition: all 0.3s;
  &:hover {
    background-color: ${(props) => props.theme.lightGray};
    border-color: ${(props) => props.theme.primaryColorHover};
  }

  &:active {
    color: ${(props) => props.theme.primaryColorActive};
    border-color: ${(props) => props.theme.primaryColorActive};
  }
  ${(p) =>
    p.isXML &&
    css`
      top: 38px;
    `}
`;

export const LoaderHeader = styled.div`
  display: flex;
`;

export const SpinnerContainer = styled.div`
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const LoaderContainerFile = styled.div`
  width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
`;

export const LoaderLabel = styled.div`
  margin-left: 2px;
  width: 100px;
`;

export const ViewDistribution = styled.div<React.HTMLAttributes<HTMLDivElement>>`
  width: 18px;
  height: 18px;
  margin-left: auto;
  margin-right: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 2px;
  background: transparent;
  color: ${(props) => props.theme.buttonSecondaryColor};

  &:hover {
    background-color: ${(props) => props.theme.lightGray};
    border-color: ${(props) => props.theme.primaryColorHover};
  }

  &:active {
    color: ${(props) => props.theme.buttonSecondaryColorActive};
    border-color: ${(props) => props.theme.buttonSecondaryColorActive};
  }
`;

export const EmptyLabel = styled.div`
  width: 64px;
  height: 20px;
`;

export const IconStyled = styled(Icon)``;

export const BackgroundIcon = styled.div`
  position: absolute;
  background-color: ${(p) => p.theme.white};
  right: 7px;
  top: 7px;
  z-index: 0;
  width: 14px;
  height: 14px;
  border-radius: 50%;
`;

export const Container = styled.div<{ innerHeight?: number }>`
  display: flex;
  flex-direction: row;
  height: 94%; //92
  width: 100%;
  color: ${(p) => p.theme.textColor};
  margin-top: 10px;
  padding: 0 10px;
  //overflow: hidden;
`;

export const Up = styled.div<{ isModeCenter?: boolean; isSelected?: boolean; innerHeight?: number; height?: number }>`
  // width: 100%;
  // height: ${(p) => p.height}%;
  // border-radius: 8px;
  // background-color: ${(p) => p.theme.backgroundColorSecondary};
`;

export const Row = styled.div<{ isModeCenter?: boolean }>`
  width: 100%;
  height: 20%;
  border-bottom: solid 1px ${(p) => p.theme.lightGray};
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  display: flex;
  user-select: none;
  ${(p) =>
    !p.isModeCenter &&
    css`
      height: 28px; //20
    `}
`;

export const Cell = styled.div<{ full?: boolean }>`
  border-right: solid 1px ${(p) => p.theme.lightGray};
  width: 70%;
  display: flex;
  align-items: center;
  justify-content: center;
  &:first-child {
    width: 30%;
  }

  ${(p) =>
    p.full &&
    css`
      width: 100% !important;
      font-weight: bold;
    `}
`;

export const ButtonStyled = styled(Button)`
  height: 30px;
`;

export const LeftContainerMain = styled.div<{ isOpen?: boolean }>`
  min-width: 280px;
  width: 280px;
  max-width: 280px;
  //width: 16%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 5px;
  position: relative;
  transition: all 0.3s;
`;

export const ComboboxRetry = styled(Combobox)`
  min-height: 20px;
  max-height: 20px;
  height: 20px;
  margin: 0 4px;
`;

// ${(p) =>
//   !p.isOpen &&
//   css`
//     width: 0.3%;
//   `};

export const LeftHeader = styled.div<{ isOpen?: boolean; innerHeight?: number }>`
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  width: 100%;
  height: 32px; //65
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  border: solid 1px ${(p) => p.theme.lightGray};
  font-size: 14px;

  ${(p) =>
    !p.isOpen &&
    css`
      background-color: transparent;
      border: none;
    `}

  ${(p) =>
    p.innerHeight &&
    p.innerHeight <= 641 &&
    p.innerHeight >= 549 &&
    css`
      height: 26px;
    `}

  ${(p) =>
    p.innerHeight &&
    p.innerHeight === 499 &&
    css`
      height: 24px;
    `}
`;

export const RightContainer = styled.div<{ isOpen?: boolean; isModeCenter?: boolean; width?: number }>`
  //width: 83%;
  //min-width: 67%;
  height: 100%;
  border-left: solid 1px ${(p) => p.theme.lightGray};
  border-top: solid 1px ${(p) => p.theme.lightGray};
  //height: 890px;
  //height: 100%;
  //overflow: hidden;
  //box-shadow: 0 12px 16px -4px rgb(16 24 40 / 10%), 0px 4px 6px -2px rgb(16 24 40 / 5%);
  //width: 84%;
  width: ${(p) => p.width}px;
  ${(p) =>
    p.isOpen &&
    css`
      width: 100%;
    `}
`;
// ${(p) =>
//   !p.isOpen &&
//   css`
//     width: 79% !important;
//     min-width: 79% !important;
//     max-width: 79% !important;
//   `}

export const InputCard = styled.div<{ innerHeight?: number }>`
  width: 100%;
  height: 100px;
  //box-shadow: 0 8px 8px rgb(50 50 71 / 8%), 0 8px 16px rgb(50 50 71 / 6%);
  border: solid 1px ${(p) => p.theme.lightGray};
  margin-top: 5px; //10
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 10px;
  user-select: none;
  position: relative;
  ${(p) =>
    p.innerHeight &&
    p.innerHeight >= 549 &&
    p.innerHeight <= 620 &&
    css`
      height: 70px;
    `}
  ${(p) =>
    p.innerHeight &&
    p.innerHeight === 499 &&
    css`
      height: 60px;
    `}
`;

export const CardLabel = styled.div<{ isNumber?: boolean }>`
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;

  ${(p) =>
    p.isNumber &&
    css`
      margin-left: 10px;
      background-color: ${(p) => p.theme.greenLightSupport};
      color: ${(p) => p.theme.greenActiveSupport};
      padding: 0 5px;
      border-radius: 10px;
    `}
`;

export const CardTime = styled.div`
  background-color: ${(p) => p.theme.blueLightSupport};
  color: ${(p) => p.theme.blueActiveSupport};
  width: 100px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

export const CardRow = styled.div`
  width: 100%;
  display: flex;
  margin-top: 10px;
`;

export const CardLeft = styled.div`
  width: 100%;
  height: 50%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 10px;
`;
export const CardRight = styled.div`
  width: 100%;
  height: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const ButtonDownLoad = styled(Button)<{ isLoading?: boolean }>`
  height: 20px; //30px
  width: 100px; //120px
  //font-size: 14px;
  margin: 0 5px;
  ${(p) =>
    p.isLoading &&
    css`
      background-color: ${(p) => p.theme.backgroundColorSecondary};
      cursor: no-drop;
      border: solid 1px ${(p) => p.theme.lightGray};
      &:hover {
        background-color: ${(p) => p.theme.backgroundColorSecondary};
      }
    `}
`;

export const IconContainer = styled.div<{ isOpen?: boolean; level?: number; isChild?: boolean }>`
  width: 24px;
  height: 24px;
  min-width: 24px;
  min-height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(90deg);
  margin-right: 10px;
  margin-left: 20px;
  transition: all 0.3s;
  position: absolute;
  right: -20px;
  background-color: ${(p) => p.theme.primaryColor} !important;
  color: ${(p) => p.theme.white};

  &:hover {
    background-color: ${(p) => p.theme.primaryColorActive} !important;
    cursor: pointer;
  }
  &:active {
    background-color: ${(p) => p.theme.primaryColorActive} !important;
    cursor: pointer;
  }
  ${(p) =>
    p.isOpen &&
    css`
      transform: rotate(270deg) !important;
    `}
`;

export const SubCarTime = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const CardStatus = styled.div``;

export const Information = styled.div`
  // color: ${(p) => p.theme.blueActiveSupport};
  color: ${(p) => p.theme.redActiveSupport};
  cursor: pointer;
  position: absolute;
  right: 6px;
  top: 5px;
  z-index: 3;
`;

export const TooltipContent = styled.div`
  max-width: 500px;
  min-width: 250px;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 10px;
`;

export const LoaderContainer = styled.div<{ height?: number }>`
  width: 100%;
  height: ${(p) => p.height}%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const ModalStyled = styled(Modal)`
  width: 1000px;
  height: 800px;
`;

export const TotalXml = styled.div``;

export const CellCustom = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row; //new
  font-weight: bold;
`;

export const IconStyledInformation = styled(Icon)`
  position: absolute;
  right: 0;
`;

export const InformationCustomCell = styled(Information)`
  left: auto;
  right: -25px;
  top: -1px;
  color: red;
`;

export const CustomCellDC = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const ButtonActionCell = styled(Button)<{ isEmpty?: boolean; isLoading?: boolean }>`
  //height: 24px;
  overflow: visible;
  width: 200px;
  max-width: 200px;
  min-width: 200px;
  margin: 0 4px;
  min-height: 20px;
  max-height: 20px;
  height: 20px;
  ${(p) =>
    p.isEmpty &&
    css`
      cursor: default;
      background-color: transparent;
      color: transparent;
      &:hover {
        background-color: transparent;
        color: transparent;
      }
    `}
  ${(p) =>
    p.isLoading &&
    css`
      background-color: transparent !important;
    `}
`;

export const StatusGlobalAccept = styled.div`
  margin-right: 10px;
`;

export const ComboboxStyled = styled(Combobox)`
  margin-left: 10px;
`;

export const FilterDatePickerStyled = styled(FilterDatePicker)`
  height: 24px;
  top: -12px;
  right: -55px;
  position: absolute;
  width: 130px;
  max-width: 130px;
  min-width: 130px;
`;

export const PencilContainer = styled(Button)`
  position: absolute;
  width: 20px;
  height: 20px;
  top: 20px;
  right: 4px;
  cursor: pointer;
  color: ${(p) => p.theme.primaryColor};
  transition: all 0.3s;
  &:hover {
    color: ${(p) => p.theme.primaryColorActive};
  }
`;

export const CloseIconLeft = styled.div`
  position: absolute;
  //right: 5px;
  right: 3px;
  top: 25px;
  cursor: pointer;
  opacity: 0.4;
  transition: all 0.3s;
  &:hover {
    opacity: 1;
  }
`;

export const ComboboxPrb = styled(Combobox)`
  width: 56px;
  margin: 0 10px;
  height: 22px;
`;

export const CellStatusContainer = styled.div`
  font-weight: bold;
  display: flex;
  align-items: center;
`;

export const StatusStyled = styled(Status)`
  width: auto;
  margin-top: 5px;
`;

export const ButtonLoadAllLeft = styled(Button)`
  margin-top: 5px; //10
  width: 300px;
  height: 20px;
`;
