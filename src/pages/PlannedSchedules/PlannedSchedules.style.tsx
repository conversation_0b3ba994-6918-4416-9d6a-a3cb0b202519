import styled from "styled-components";
import { Combobox } from "components/Combobox";
import { Button } from "components/Button";

export const ButtonDownloadDc = styled(Button)`
  width: 170px;
  height: 20px;
  margin-left: auto;
  margin-right: 10px;
  border: solid 1px ${(p) => p.theme.lightGray};
  border-radius: 6px;
  &:hover {
    background-color: ${(p) => p.theme.primaryColor};
    color: ${(p) => p.theme.white};
  }
`;

export const HeaderActions = styled.div`
  margin-left: 10px;
`;

export const ButtonBack = styled(Button)`
  margin-left: auto;
  margin-right: 10px;
`;

export const LoaderContainer = styled.div`
  width: 170px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  margin-right: 10px;
`;

export const LoaderLabel = styled.div`
  margin-left: 2px;
  width: 210px;
`;

export const Container = styled.div`
  width: 100%;
  height: 100%;
`;

export const ComboboxStyled = styled(Combobox)`
  margin-left: 10px;
`;
