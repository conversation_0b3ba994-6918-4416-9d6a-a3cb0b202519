import styled, { css } from "styled-components";

import { Button } from "components/Button";
import { FilterDatePicker } from "components/FilterDatePicker";
import { ButtonsGroup } from "components/ButtonsGroup";

export const ResetButton = styled.div`
  width: 22px;
  height: 20px;
  margin-left: 10px;
  //border: solid 1px red;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(270deg);
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    color: ${(props) => props.theme.primaryColorHover};
    background-color: ${(props) => props.theme.lightGray};
  }

  &:active {
    color: ${(props) => props.theme.primaryColorActive};
    background-color: ${(props) => props.theme.gray};
  }
`;

export const ButtonDownloadDc = styled(Button)`
  width: 170px;
  height: 20px; //28
  margin-left: auto;
  margin-right: 10px;
  border: solid 1px ${(p) => p.theme.lightGray};
  border-radius: 6px;
  &:hover {
    background-color: ${(p) => p.theme.primaryColor};
    color: ${(p) => p.theme.white};
  }
`;

export const ButtonsGroupStyled = styled(ButtonsGroup)`
  margin-left: auto;
  margin-right: 10px;
`;

export const Container = styled.div`
  width: 100%;
  height: 100%;
  //box-sizing: border-box;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
`;

export const ActionBar = styled.div`
  width: 100%;
  height: 24px; //50
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  //box-shadow: 0 8px 8px rgba(50, 50, 71, 0.08), 0 8px 16px rgba(50, 50, 71, 0.06);
  //border-radius: 6px;
  display: flex;
  align-items: center;
  //padding: 0 20px;
  padding: 15px 0;
`;

export const FilterDatePickerStyled = styled(FilterDatePicker)`
  margin-left: 10px;
`;

export const ComparisonButton = styled(Button)`
  margin: 0 5px;
  height: 16px; //28
  width: 150px;
`;

export const EqualButton = styled(Button)`
  margin: 0 5px;
  height: 16px; //40px
  width: 280px;
`;

export const SyncButton = styled(ComparisonButton)`
  width: 200px;
`;

export const LabelStatus = styled.div<{ justify?: string }>`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  ${(p) =>
    p.justify &&
    css`
      justify-content: ${p.justify};
    `}
`;
