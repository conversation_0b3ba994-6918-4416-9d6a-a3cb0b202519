import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>er, <PERSON>Bar, ComparisonButton, FilterDatePickerStyled, ButtonsGroupStyled, SyncButton } from "./Nsi.style";
import { Combobox } from "components/Combobox";
import { useLocation, useNavigate } from "react-router-dom";
import queryString from "query-string";
import { observer } from "mobx-react";
import { ModalDateComparison } from "./components/ModalDateComparison";
import { ModalEss } from "./components/ModalEss";
import { ModalViewError } from "./components/ModalViewError";
import { ModalProtocolChanges } from "./components/ModalProtocolChanges";
import { ViewContainer } from "./components/ViewContainer";
import { DistributionContainer } from "./components/DistributionContainer";
import { isEast, isModeCenter } from "utils/getMode";
import { ModalSync } from "./components/ModalSync";
import { ModalFile } from "./components/ModalFile";
import { useStores } from "../../stores/useStore";
import { AccessControl } from "../../components/AccessControl";
import { ModalComparisonSk } from "./components/ModalComparisonSk";
import { deleteParams } from "../../utils/indexDB";

export const typesSRPG = [
  { value: "rges", label: "РГЕ" },
  { value: "nBlocks", label: "ЕГО" },
  { value: "vetvs", label: "Ветвь" },
  { value: "sechens", label: "Сечение" },
  { value: "consumers", label: "Потребитель" },
  { value: "area2s", label: "Территория" },
  { value: "areas", label: "Район электрической сети" },
  { value: "wSums", label: "Интегральное ограничение" },
  { value: "powerSystems", label: "Объединенная энергосистема" },
  { value: "nGroups", label: "Нагрузочная группа" },
];

export const Nsi = observer(() => {
  const location = useLocation();
  const history = useNavigate();
  const { authStore, nsiStore, liveTimerStore } = useStores();
  const { isCenter } = authStore;
  const { loadedDayNsi } = nsiStore;

  const { DEFAULT_DAY, DEFAULT_MONTH, DEFAULT_YEAR } = liveTimerStore;

  const { day = DEFAULT_DAY, month = DEFAULT_MONTH, year = DEFAULT_YEAR, selectedSegment, srpg, viewPage = "view" }: any = queryString.parse(location.search);

  const listCombobox: any[] = [
    { value: "ess", label: "из ЕСС", icon: "data", color: "default", disabled: false },
    { value: "file", label: "из Файла", icon: "file", color: "default", disabled: false },
  ];

  //clean memory
  useEffect(() => {
    return () => {
      localStorage.removeItem("table-checked-items-4_v2");
      deleteParams("2_v2");
    };
  }, []);
  //clean memory

  const [isOpenComparison, setIsOpenComparison] = useState(false);

  const [isModalEss, setIsModalEss] = useState(false);
  const [isModalFile, setIsModalFile] = useState(false);
  const [isModalViewError, setIsModalViewError] = useState(false);
  const [isModalProtocolChanges, setIsModalProtocolChanges] = useState(false);

  const [isModalSync, setIsModalSync] = useState(false);

  const [loadMode, setLoadMode] = useState<any>(null);

  const isAccessStand = isCenter && isModeCenter;

  const generationListButtonGroup = () => {
    if (isAccessStand && !isEast) {
      return [
        { value: "view", label: "Отображение", roles: ["nsi_admin", "engineer", "viewer", "sys_admin", "nsi_ess_admin"], dataTest: "nsi-header.view-button" },
        { value: "distribution", label: "Распространение", roles: ["nsi_admin", "nsi_ess_admin", "viewer", "sys_admin"], dataTest: "nsi-header.disrtibution-button" },
        { value: "comparisonSK", label: "Сопоставление объектов с СК-11", roles: ["nsi_admin", "engineer"], dataTest: "nsi-header.comparison-sk-button" },
      ];
    } else {
      if (isEast) {
        if (isCenter && isModeCenter) {
          return [{ value: "view", label: "Отображение", roles: ["nsi_admin", "engineer", "viewer", "sys_admin", "nsi_ess_admin"] }];
        } else {
          return [
            { value: "view", label: "Отображение", roles: ["nsi_admin", "engineer", "viewer", "sys_admin", "nsi_ess_admin"] },
            { value: "comparisonSK", label: "Сопоставление объектов с СК-11", roles: ["nsi_admin", "engineer"], dataTest: "distributed-nsi-header.comparison-sk-button" },
          ];
        }
      }
      //new HF3
      if (isCenter && isModeCenter) {
        return [
          { value: "view", label: "Отображение", roles: ["nsi_admin", "engineer", "viewer", "sys_admin", "nsi_ess_admin"] },
          { value: "distribution", label: "Распространение", roles: ["nsi_admin", "nsi_ess_admin", "viewer", "sys_admin"] }, //new HF3
          { value: "comparisonSK", label: "Сопоставление объектов с СК-11", roles: ["nsi_admin", "engineer"] },
        ];
      } else {
        return [
          { value: "view", label: "Отображение", roles: ["nsi_admin", "engineer", "viewer", "sys_admin", "nsi_ess_admin"] },
          { value: "comparisonSK", label: "Сопоставление объектов с СК-11", roles: ["nsi_admin", "engineer"], dataTest: "distributed-nsi-header.comparison-sk-button" },
        ];
      }
      //new HF3
    }
  };

  return (
    <Container>
      <ActionBar>
        {isModeCenter && isCenter && !isEast ? (
          <>
            {viewPage === "view" && (
              <AccessControl rules={["nsi_ess_admin"]}>
                <Combobox
                  items={listCombobox}
                  selectedValue={null}
                  onChange={({ value }) => {
                    if (value === "ess") {
                      setIsModalEss(true);
                      setLoadMode("ess");
                    }
                    if (value === "file") {
                      setIsModalFile(true);
                      setLoadMode("file");
                    }
                  }}
                  placeholder="Загрузка НСИ"
                  icon="download"
                  width={175}
                  isTextColorPlaceHolder={true}
                  dataTest="view-header.upload-nsi-combobox"
                />
              </AccessControl>
            )}
          </>
        ) : (
          <SyncButton title="История загрузки НСИ" type="secondary" icon="sync" onClick={() => setIsModalSync(true)} dataTest="view-header.history-nsi-button" />
        )}
        {viewPage === "view" && (
          <>
            <FilterDatePickerStyled
              day={day}
              month={month}
              year={year}
              onClick={({ day, month, year }) => {
                history(`?year=${year}&month=${month}&day=${day}&selectedSegment=${selectedSegment ?? "goy"}&srpg=${srpg ?? typesSRPG[0].value}&viewPage=${viewPage}`);
              }}
              loadDay={loadedDayNsi}
              onLoadDay={(tempYear: number | string) => nsiStore.getCalendar(tempYear)}
              dataTest="view-header.date-picker"
            />
            <ComparisonButton
              icon="comparison"
              type="secondary"
              title="Сравнение НСИ"
              onClick={() => setIsOpenComparison(true)}
              dataTest="view-header.comparison-nsi-button"
            />
          </>
        )}
        <AccessControl rules={["nsi_admin", "engineer", "sys_admin", "nsi_ess_admin", "viewer"]}>
          <ButtonsGroupStyled
            items={generationListButtonGroup()}
            selectedValue={viewPage}
            onClick={(value) => {
              if (value !== viewPage) {
                nsiStore.clearTables();
                history(
                  `?year=${year ?? DEFAULT_YEAR}&month=${month ?? DEFAULT_MONTH}&day=${day ?? DEFAULT_DAY}&selectedSegment=${selectedSegment ?? "goy"}&srpg=${
                    srpg ?? typesSRPG[0].value
                  }&viewPage=${value}`
                );
              }
            }}
            widthButton={200}
          />
        </AccessControl>
      </ActionBar>
      {viewPage === "view" && <ViewContainer isCenter={isCenter} />}
      {viewPage === "distribution" && <DistributionContainer isCenter={isCenter} />}
      {viewPage === "comparisonSK" && <ModalComparisonSk isCenter={isCenter} />}
      {isOpenComparison && <ModalDateComparison onCancel={() => setIsOpenComparison(false)} />}
      {isModalEss && (
        <ModalEss
          onCancel={() => {
            setLoadMode(null);
            setIsModalEss(false);
          }}
          onTransition={() => {
            setIsModalEss(false);
          }}
          onViewError={() => setIsModalViewError(true)}
          onProtocolChanges={() => setIsModalProtocolChanges(true)}
        />
      )}
      {isModalFile && (
        <ModalFile
          onCancel={() => {
            setLoadMode(null);
            setIsModalFile(false);
          }}
          onTransition={() => {
            setIsModalFile(false);
          }}
          onViewError={() => setIsModalViewError(true)}
          onProtocolChanges={() => setIsModalProtocolChanges(true)}
        />
      )}
      {isModalViewError && (
        <ModalViewError loadMode={loadMode} onCancel={() => setIsModalViewError(false)} onModalEss={() => setIsModalEss(true)} onModalFile={() => setIsModalFile(true)} />
      )}
      {isModalProtocolChanges && (
        <ModalProtocolChanges
          loadMode={loadMode}
          onCancel={() => setIsModalProtocolChanges(false)}
          onModalEss={() => setIsModalEss(true)}
          onModalFile={() => setIsModalFile(true)}
        />
      )}
      {isModalSync && (
        <ModalSync
          onCancel={() => {
            setIsModalSync(false);
          }}
        />
      )}
    </Container>
  );
});
