import styled, { css } from "styled-components";
import { Modal } from "components/Modal";
import { FilterDatePicker } from "components/FilterDatePicker";
import { Button } from "components/Button";

export const Excel = styled.div<{ disabled?: boolean }>`
  color: ${(p) => p.theme.colorExcel};
  height: 24px;
  margin: 0 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 8px;
  user-select: none;
  transition: all 0.3s;
  text-align: center;
  &:hover {
    background-color: ${(p) => p.theme.lightGray};
  }
  &:active {
    background-color: ${(p) => p.theme.gray};
  }
  ${(p) =>
    p.disabled &&
    css`
      background-color: ${(p) => p.theme.lightGray} !important;
      color: ${(p) => p.theme.textColor} !important;
      cursor: no-drop !important;
    `}
`;

export const ButtonStyled = styled(Button)`
  width: 160px;
  height: 22px;
  margin: 0 5px;
`;

export const ModalStyled = styled(Modal)`
  width: 725px;
  height: 700px;
`;

export const FilterContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 20px;
`;

export const FilterDatePickerStyled = styled(FilterDatePicker)`
  margin-left: 5px;
`;

export const ChildrenTabsContainer = styled.div<{ height?: number }>`
  border: solid 1px ${(p) => p.theme.lightGray};
  width: 100%;
  //height: 470px;
  height: ${(p) => p.height}px;
  border-radius: 2px;
  overflow-x: auto;
`;

export const TabsContainer = styled.div`
  height: 50px;
  width: 100%;
  margin: 10px 0;
`;

export const Entries = styled.div<{ colorScheme?: "red" | "green" | "purple" }>`
  border-radius: 8px;
  //height: 50px;
  height: 46px;
  align-items: center;
  padding: 0 10px;
  margin: 10px;

  //display: -webkit-box;
  //-webkit-box-orient: vertical;
  //overflow: hidden;
  white-space: pre-wrap;
  //text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;

  ${(p) =>
    p.colorScheme === "green" &&
    css`
      border: solid 1px ${(p) => p.theme.greenActiveSupport};
      color: ${(p) => p.theme.greenActiveSupport};
      background-color: ${(p) => p.theme.greenLightSupport};
    `}

  ${(p) =>
    p.colorScheme === "red" &&
    css`
      border: solid 1px ${(p) => p.theme.redActiveSupport};
      color: ${(p) => p.theme.redActiveSupport};
      background-color: ${(p) => p.theme.redLightSupport};
    `}

  ${(p) =>
    p.colorScheme === "purple" &&
    css`
      border: solid 1px ${(p) => p.theme.purpleActiveSupport};
      color: ${(p) => p.theme.purpleActiveSupport};
      background-color: ${(p) => p.theme.purpleLightSupport};
    `}
`;

export const LoaderContainer = styled.div<{ height?: number }>`
  width: 100%;
  //height: 470px;
  height: ${(p) => p.height}px;
  display: flex;
  border: solid 1px ${(p) => p.theme.lightGray};
`;
