import React, { FC, useEffect, useState } from "react";
import {
  ModalStyled,
  FilterContainer,
  FilterDatePickerStyled,
  ChildrenTabsContainer,
  TabsContainer,
  Entries,
  LoaderContainer,
  ButtonStyled,
  Excel,
} from "./ModalDateComparison.style";
import { Tabs } from "components/Tabs";
import { Loader } from "components/Loader";
import { NoData } from "components/NoData";
import { useStores } from "stores/useStore";
import { ModalDateComparisonProps } from "./ModalDateComparison.types";
import { observer } from "mobx-react";
import { Icon } from "components/Icon";
import { nsi } from "~/api/nsi";
import { runInAction } from "mobx";

type CalendarDate = {
  day: number | null;
  month: number | null;
  year: number | null;
};

type TabValue = "ALL" | "addEntries" | "deleteEntries" | "changeEntries";
type ColorScheme = "default" | "green" | "red" | "purple";

export const ModalDateComparison: FC<ModalDateComparisonProps> = observer((props) => {
  const { onCancel } = props;

  const { nsiStore } = useStores();
  const { dateComparison, isLoadingDateComparison } = nsiStore;

  const [filterDate1, setFilterDate1] = useState<CalendarDate>({ day: null, month: null, year: null });
  const [filterDate2, setFilterDate2] = useState<CalendarDate>({ day: null, month: null, year: null });

  const [loadedDaysCalendar1, setLoadedDaysCalendar1] = useState<string[]>([]);
  const [loadedDaysCalendar2, setLoadedDaysCalendar2] = useState<string[]>([]);

  const [selectedTabs, setSelectedTabs] = useState("ALL");

  const [modalHeight, setModalHeight] = useState(700);

  useEffect(() => {
    return () => {
      runInAction(() => {
        nsiStore.dateComparison = { addEntries: [], deleteEntries: [], changeEntries: [] };
      });
    };
  }, []);

  const handleLoadDaysCalendar1 = async (year: number | string) => {
    try {
      const { dates } = await nsi.nsiController.getCalendar(year, nsiStore.rootStore.authStore.isCenter);
      setLoadedDaysCalendar1(dates);
    } catch (e) {}
  };

  const handleLoadDaysCalendar2 = async (year: number | string) => {
    try {
      const { dates } = await nsi.nsiController.getCalendar(year, nsiStore.rootStore.authStore.isCenter);
      setLoadedDaysCalendar2(dates);
    } catch (e) {}
  };

  const tabs: Array<{ value: TabValue; label: string; count: number; colorScheme: ColorScheme }> = [
    {
      value: "ALL",
      label: "Все",
      count: (dateComparison?.addEntries?.length ?? 0) + (dateComparison?.deleteEntries?.length ?? 0) + (dateComparison?.changeEntries?.length ?? 0),
      colorScheme: "default",
    },
    { value: "addEntries", label: "Добавленные", count: dateComparison?.addEntries?.length ?? 0, colorScheme: "green" },
    { value: "deleteEntries", label: "Удаленные", count: dateComparison?.deleteEntries?.length ?? 0, colorScheme: "red" },
    { value: "changeEntries", label: "Измененные", count: dateComparison?.changeEntries?.length ?? 0, colorScheme: "purple" },
  ];

  const isError = (() => {
    if (!filterDate1.day || !filterDate1.month || !filterDate1.year || !filterDate2.day || !filterDate2.month || !filterDate2.year) {
      return false;
    }

    const d1 = new Date(filterDate1.year, filterDate1.month - 1, filterDate1.day);
    const d2 = new Date(filterDate2.year, filterDate2.month - 1, filterDate2.day);
    return d1 >= d2;
  })();

  const isDisabled =
    filterDate1.day === null ||
    filterDate1.month === null ||
    filterDate1.year === null ||
    filterDate2.day === null ||
    filterDate2.month === null ||
    filterDate2.year === null ||
    isError;

  const handleCompare = () => {
    if (
      !isDisabled &&
      filterDate1.day !== null &&
      filterDate1.month !== null &&
      filterDate1.year !== null &&
      filterDate2.day !== null &&
      filterDate2.month !== null &&
      filterDate2.year !== null
    ) {
      const calendarDate1 = {
        day: filterDate1.day,
        month: filterDate1.month,
        year: filterDate1.year,
      };

      const calendarDate2 = {
        day: filterDate2.day,
        month: filterDate2.month,
        year: filterDate2.year,
      };

      nsiStore.initDateComparison(calendarDate1, calendarDate2);
    }
  };

  const handleExport = () => {
    if (
      !isDisabled &&
      filterDate1.day !== null &&
      filterDate1.month !== null &&
      filterDate1.year !== null &&
      filterDate2.day !== null &&
      filterDate2.month !== null &&
      filterDate2.year !== null
    ) {
      const calendarDate1 = {
        day: filterDate1.day,
        month: filterDate1.month,
        year: filterDate1.year,
      };

      const calendarDate2 = {
        day: filterDate2.day,
        month: filterDate2.month,
        year: filterDate2.year,
      };

      nsiStore.exportXLSProtocolComparison(calendarDate1, calendarDate2);
    }
  };

  return (
    <ModalStyled
      setModalHeight={setModalHeight}
      isOverLay
      onCancel={onCancel}
      title="Сравнение НСИ"
      description="Выберите 2 даты и нажмите на кнопку сравнить"
      width={725}
      height={700}
      dataTestContainer="comparison-nsi-modal.container"
    >
      <FilterContainer>
        <FilterDatePickerStyled
          day={filterDate1.day}
          month={filterDate1.month}
          year={filterDate1.year}
          onClick={({ day, month, year }) => {
            setFilterDate1({ day, month, year });
          }}
          hiddenChooseButton
          loadDay={loadedDaysCalendar1}
          onLoadDay={handleLoadDaysCalendar1}
          dataTest="comparison-nsi-modal.first-date-picker"
        />
        <FilterDatePickerStyled
          day={filterDate2.day}
          month={filterDate2.month}
          year={filterDate2.year}
          onClick={({ day, month, year }) => {
            setFilterDate2({ day, month, year });
          }}
          hiddenChooseButton
          loadDay={loadedDaysCalendar2}
          onLoadDay={handleLoadDaysCalendar2}
          dataTest="comparison-nsi-modal.second-date-picker"
        />
        <ButtonStyled
          title={"Сравнить"}
          message={isError ? "Дата 2 должна быть больше Дата 1" : "Сравнить"}
          disabled={isDisabled}
          isError={isError}
          onClick={handleCompare}
          dataTest="comparison-nsi-modal.compare-button"
        />
        <Excel disabled={isDisabled} onClick={handleExport} data-test="comparison-nsi-modal.export-button">
          <Icon width={18} name="excel" />
          Выгрузка в EXCEL
        </Excel>
      </FilterContainer>
      <TabsContainer>
        <Tabs type="secondary" tabs={tabs} selectedValue={selectedTabs} onClick={({ value }: { value: string }) => setSelectedTabs(value)} />
      </TabsContainer>
      {isLoadingDateComparison ? (
        <LoaderContainer height={modalHeight - 230}>
          <Loader spinnerSize={100} />
        </LoaderContainer>
      ) : (
        <ChildrenTabsContainer height={modalHeight - 230}>
          {(selectedTabs === "ALL" &&
            dateComparison?.addEntries?.length === 0 &&
            dateComparison?.deleteEntries?.length === 0 &&
            dateComparison?.changeEntries?.length === 0) ||
          (selectedTabs !== "ALL" &&
            ((selectedTabs === "addEntries" && dateComparison.addEntries.length === 0) ||
              (selectedTabs === "deleteEntries" && dateComparison.deleteEntries.length === 0) ||
              (selectedTabs === "changeEntries" && dateComparison.changeEntries.length === 0))) ? (
            <NoData />
          ) : (
            <>
              {(selectedTabs === "ALL" || selectedTabs === "addEntries") &&
                dateComparison.addEntries.map((item: string) => {
                  return (
                    <Entries title={item} key={`entries-${item}`} colorScheme="green" data-test="comparison-nsi-modal-table.item-row">
                      {item}
                    </Entries>
                  );
                })}
              {(selectedTabs === "ALL" || selectedTabs === "deleteEntries") &&
                dateComparison.deleteEntries.map((item: string) => {
                  return (
                    <Entries title={item} key={`entries-${item}`} colorScheme="red" data-test="comparison-nsi-modal-table.item-row">
                      {item}
                    </Entries>
                  );
                })}
              {(selectedTabs === "ALL" || selectedTabs === "changeEntries") &&
                dateComparison.changeEntries.map((item: string) => {
                  return (
                    <Entries title={item} key={`entries-${item}`} colorScheme="purple" data-test="comparison-nsi-modal-table.item-row">
                      {item}
                    </Entries>
                  );
                })}
            </>
          )}
        </ChildrenTabsContainer>
      )}
    </ModalStyled>
  );
});
