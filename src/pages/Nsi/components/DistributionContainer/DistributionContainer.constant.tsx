import React from "react";
import styled from "styled-components";

export const InformationContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  color: ${(p) => p.theme.textColor};
`;

export const NameLabel = styled.div`
  width: 300px;
  display: flex;
`;

export const NameText = styled.div`
  display: flex;
  align-items: center;
  color: ${(p) => p.theme.textColor};
`;
