import styled, { css } from "styled-components";
import { Button } from "components/Button";

export const StatusContainer = styled.div`
  white-space: nowrap;
  margin-right: 10px;
`;

export const StatusCircle = styled.div<{ status?: "DONE" | "FAIL" | "AWAIT" | "RESEND_REQUIRED" }>`
  width: 14px;
  height: 14px;
  min-width: 14px;
  min-height: 14px;
  border: solid 1px ${(p) => p.theme.lightGray};
  margin-right: 4px;
  border-radius: 50%;
  background-color: ${(p) => p.theme.lightGray};

  ${(p) =>
    p.status === "DONE" &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport};
    `}

  ${(p) =>
    p.status === "FAIL" &&
    css`
      background-color: ${(p) => p.theme.redActiveSupport};
    `}
  
  ${(p) =>
    p.status === "AWAIT" &&
    css`
      background-color: ${(p) => p.theme.orangeActiveSupport};
    `}
  ${(p) =>
    p.status === "RESEND_REQUIRED" &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport};
      border: solid 3px ${(p) => p.theme.redActiveSupport};
    `}
`;

export const Container = styled.div`
  display: flex;
  //height: 900px;
  width: 100%;
  color: ${(p) => p.theme.textColor};
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  //box-shadow: 0 12px 16px -4px rgb(16 24 40 / 10%), 0px 4px 6px -2px rgb(16 24 40 / 5%);
  border-radius: 8px;
  box-sizing: border-box;
  height: calc(100vh - 42px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 80px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 76px);
  }
`;

export const Buttons = styled.div`
  //width: 100%;
  display: flex;
  //justify-content: flex-end;
  margin-left: auto;
  margin-right: 10px;
`;

export const ButtonStyled = styled(Button)`
  margin: 10px;
  width: 160px;
  //height: 28px;
`;
