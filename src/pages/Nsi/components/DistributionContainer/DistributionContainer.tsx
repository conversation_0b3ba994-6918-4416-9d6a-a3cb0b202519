import React, { useEffect, useState } from "react";
import { Container, Buttons, ButtonStyled } from "./DistributionContainer.style";
import { useStores } from "stores/useStore";
import { observer } from "mobx-react";
import { Table } from "components/Table";
import { Status } from "components/Status";
import { typesSRPG } from "../../Nsi";
import { useNavigate } from "react-router-dom";
import { DEFAULT_DAY, DEFAULT_MONTH, DEFAULT_YEAR } from "helpers/DateUtils";
import queryString from "query-string";
import { InformationContainer, NameLabel, NameText } from "./DistributionContainer.constant";
import { prepareDataTable } from "utils";
import { AccessControl } from "components/AccessControl";
import { Combobox } from "components/Combobox";
import styled, { css } from "styled-components";
import { getWidthModal } from "helpers/adaptive";

export const ComboboxStyled = styled(Combobox)`
  //font-size: 12px;
  font-size: 1rem;
`;

export const DistributionContainer = observer(({ isCenter }: { isCenter?: boolean }) => {
  const { nsiStore, tableStore } = useStores();
  const history = useNavigate();
  const { day = DEFAULT_DAY, month = DEFAULT_MONTH, year = DEFAULT_YEAR, selectedSegment }: any = queryString.parse(location.search);

  const { distribution } = nsiStore;

  useEffect(() => {
    nsiStore.initDistribution();
    return () => {
      nsiStore.stopCheckStatus();
      nsiStore.stopNsiDistribution();
      localStorage.removeItem(`table-sort-111`);
    };
  }, []);

  let defaultColumns: any;

  const [columnOrder, setColumnOrder] = useState<any>([]);

  const setDefaultColumns = () => {
    setColumns(() => {
      return columnOrder.map((el: any) => {
        return getDefaultColumns().find((item) => item.name === el);
      });
    });
  };
  const [initScreenWidth, setInitScreenWidth] = useState<any>(getWidthModal(document.documentElement.clientWidth));
  const [screenWidth, setScreenWidth] = useState<any>(initScreenWidth);
  const [isEditWidth, setEditWidth] = useState(false);

  const resizeWeb = () => {
    setScreenWidth(getWidthModal(document.documentElement.clientWidth));
  };

  useEffect(() => {
    window.addEventListener("resize", resizeWeb);
    return () => {
      window.removeEventListener("resize", resizeWeb);
    };
  }, []);

  useEffect(() => {
    if (initScreenWidth !== screenWidth && !isEditWidth) {
      setEditWidth(true);
      setDefaultColumns();
    } else if (initScreenWidth !== screenWidth && isEditWidth) {
      setDefaultColumns();
    } else if (initScreenWidth === screenWidth && isEditWidth) {
      setDefaultColumns();
    }
  }, [screenWidth]);

  const getDefaultColumns = () => {
    if (screenWidth >= 650 && screenWidth < 700) {
      return [
        { name: "name", title: "Название", width: 225, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 225, isSearch: true },
      ];
    }
    if (screenWidth >= 700 && screenWidth < 800) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 225, isSearch: true },
      ];
    }
    if (screenWidth >= 800 && screenWidth < 900) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 320, isSearch: true },
      ];
    }
    if (screenWidth >= 900 && screenWidth < 1024) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 420, isSearch: true },
      ];
    }
    if (screenWidth >= 1024 && screenWidth < 1152) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 520, isSearch: true },
      ];
    }
    if (screenWidth >= 1152 && screenWidth < 1280) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 520, isSearch: true },
      ];
    }
    if (screenWidth >= 1280 && screenWidth < 1400) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 800, isSearch: true },
      ];
    }
    if (screenWidth >= 1400 && screenWidth < 1600) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 920, isSearch: true },
      ];
    }
    if (screenWidth >= 1600 && screenWidth < 1700) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 1120, isSearch: true },
      ];
    }
    if (screenWidth >= 1700 && screenWidth < 1800) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 1210, isSearch: true },
      ];
    }
    if (screenWidth >= 1800 && screenWidth < 1900) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 1310, isSearch: true },
      ];
    }
    if (screenWidth >= 1900 && screenWidth < 2000) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 1410, isSearch: true },
      ];
    }
    if (screenWidth >= 2000 && screenWidth < 2100) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 1510, isSearch: true },
      ];
    }
    if (screenWidth >= 2100 && screenWidth < 2200) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 1610, isSearch: true },
      ];
    }
    if (screenWidth >= 2200 && screenWidth < 2560) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 1710, isSearch: true },
      ];
    }
    if (screenWidth >= 2560 && screenWidth < 3000) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 2070, isSearch: true },
      ];
    }
    if (screenWidth >= 3000 && screenWidth < 3840) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 2510, isSearch: true },
      ];
    }
    if (screenWidth >= 3840 && screenWidth < 4000) {
      return [
        { name: "name", title: "Название", width: 270, isSearch: true },
        { name: "status", title: "Статус", width: 200, isSearch: true },
        { name: "information", title: "Информация", width: 3340, isSearch: true },
      ];
    }
    return [
      { name: "name", title: "Название", width: 270, isSearch: true },
      { name: "status", title: "Статус", width: 200, isSearch: true },
      { name: "information", title: "Информация", width: 1410, isSearch: true },
    ];
  };

  defaultColumns = getDefaultColumns();

  const [columns, setColumns] = useState([]);

  useEffect(() => {
    tableStore.getTableParams(111).then((data: any) => {
      if (data) {
        setColumns(data);
      } else {
        setColumns(defaultColumns);
      }
    });
    return () => {
      localStorage.removeItem("expandedRowIds-111");
    };
  }, []);

  const filters = [
    { value: "none", label: "Не фильтровать", color: "#000", icon: "empty" },
    { value: "Готово", label: "Готово", color: "#34B53A", icon: "done" },
    { value: "В процессе", label: "В процессе", color: "#0071FF", icon: "load" },
    { value: "Ошибка", label: "Ошибка", color: "#FF3A29", icon: "error" },
  ];

  const searchCustomFilter = [
    {
      name: "status",
      render: (status: any, setStatus: any) => {
        return (
          <ComboboxStyled
            items={filters}
            selectedValue={status}
            onChange={({ value }) =>
              setStatus((prev: any) => {
                return prev.map((el: any) => {
                  if (el.columnName === "status") {
                    return { ...el, value };
                  }
                  return el;
                });
              })
            }
          />
        );
      },
    },
  ];

  useEffect(() => {
    if (!isCenter) {
      history(`?year=${year}&month=${month}&day=${day}&selectedSegment=${selectedSegment ?? "goy"}&srpg=${typesSRPG[0].value}&viewPage=view`);
    }
  }, [isCenter]);

  const checkStatus = (array: any) => {
    return array.some((el: any) => {
      let isCheckChilds = false;
      if (el?.childs && el?.childs?.length > 0) {
        isCheckChilds = checkStatus(el?.childs);
      }
      return el.status === "FAILED" || el.status === "CREATED" || isCheckChilds;
    });
  };

  const getStatus = (status: any) => {
    if (status === "Готово") {
      return "CONFIRMED";
    }
    if (status === "В процессе") {
      return "CREATED";
    }
    if (status === "Ошибка") {
      return "FAILED";
    }
    return "none";
  };

  const customCells = [
    {
      name: "status",
      render: (value: "CONFIRMED" | "CREATED" | "FAILED") => <Status value={getStatus(value)} />,
    },
    {
      name: "information",
      render: (value: any) => <InformationContainer>{value}</InformationContainer>,
    },
    {
      name: "name",
      render: (value: any) => {
        return (
          <NameLabel>
            <NameText>{value}</NameText>
          </NameLabel>
        );
      },
    },
  ];

  // const initExpandedRowIds = JSON.parse(localStorage.getItem("expandedRowIds") as string) ?? [];
  const [expandedRowIds, setExpandedRowIds] = useState<any>([]);

  const prepareRows = (data: any, parentId: any) => {
    let result: any[] = [];
    data.map((el: any) => {
      let childs = [];
      if (el?.childs?.length > 0) {
        childs = prepareRows(el?.childs, el.tabId);
      }
      result.push({ ...el, parentId });
      if (childs?.length > 0) {
        childs.map((item) => {
          result.push({ ...item });
        });
      }
    });
    return result;
  };

  const rows = prepareRows(distribution, 0);

  const disableRetry = rows.some((el) => el.status === "В процессе") ? true : !rows.some((el) => el.status === "Ошибка");

  const [prevDistribution, setPrevDistribution] = useState<any>([]);

  useEffect(() => {
    if (prevDistribution.length !== rows.length) {
      setPrevDistribution(rows);
    }
  }, [rows]);

  useEffect(() => {
    const initExpandedRowIds = JSON.parse(localStorage.getItem("expandedRowIds-111") as string) ?? rows.map((el) => el.tabId);
    setExpandedRowIds(initExpandedRowIds);
  }, [prevDistribution]);

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("111").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "name", direction: "asc" }]);
      }
    });
  }, []);

  return (
    <Container>
      <Table
        columns={columns}
        tableData={prepareDataTable(distribution)}
        customCells={customCells}
        isLoading={false}
        tableKey={"111"}
        expandedRowIds={expandedRowIds}
        setExpandedRowIds={(e: any) => {
          localStorage.setItem("expandedRowIds-111", JSON.stringify(e));
          setExpandedRowIds(e);
        }}
        setColumns={setColumns}
        defaultColumns={defaultColumns}
        searchCustomFilter={searchCustomFilter}
        childrenKey="name"
        columnOrder={columnOrder}
        initSorting={initSorting}
        setColumnOrder={setColumnOrder}
        dataTest="distribution-nsi-table.container"
        dataTestRows="distribution-nsi-table.row"
        dataTestDefaultCells="distribution-nsi-table.cell"
        headerComponents={
          <>
            <AccessControl rules={["nsi_ess_admin"]}>
              <Buttons>
                <ButtonStyled disabled={disableRetry} onClick={() => nsiStore.restartDistribution()} title="Повторить отправку" dataTest="distribution-nsi-table-header.restart-distribution-button" />
              </Buttons>
            </AccessControl>
          </>
        }
      />
    </Container>
  );
});
