import React, { FC, useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>led, ChildrenTabsContainer, TabsContainer, Entries, LoaderContainer, Arrow, Excel } from "./ModalProtocolChanges.style";
import { Tabs } from "components/Tabs";
import { Loader } from "components/Loader";
import { NoData } from "components/NoData";
import { Icon } from "components/Icon";
import { useStores } from "stores/useStore";
import { observer } from "mobx-react";

interface ModalProtocolChangesProps {
  onCancel: () => void;
  onModalEss: () => void;
  onModalFile: () => void;
  loadMode: any;
}

export const ModalProtocolChanges: FC<ModalProtocolChangesProps> = observer(({ onCancel, onModalEss, loadMode, onModalFile }) => {
  const { nsiStore } = useStores();

  const { protocolChanges, isLoadingProtocolChanges } = nsiStore;

  useEffect(() => {
    nsiStore.initProtocolChanges(loadMode);
    return () => {
      nsiStore.resetProtocolChanges();
    };
  }, []);

  const tabs = [
    {
      value: "ALL",
      label: "Все",
      count: (protocolChanges?.addEntries?.length ?? 0) + (protocolChanges?.deleteEntries?.length ?? 0) + (protocolChanges?.changeEntries?.length ?? 0),
      colorScheme: "default",
    },
    { value: "addEntries", label: "Добавленные", count: protocolChanges?.addEntries?.length ?? 0, colorScheme: "green" },
    { value: "deleteEntries", label: "Удаленные", count: protocolChanges?.deleteEntries?.length ?? 0, colorScheme: "red" },
    { value: "changeEntries", label: "Измененные", count: protocolChanges?.changeEntries?.length ?? 0, colorScheme: "purple" },
  ];

  const [selectedTabs, setSelectedTabs] = useState<string>("ALL");

  const [modalHeight, setModalHeight] = useState(700);

  return (
    <ModalStyled
      isOverLay
      onCancel={() => {
        if (loadMode === "ess") {
          onModalEss();
        } else {
          onModalFile();
        }
        onCancel();
      }}
      title="Просмотр протокола изменений"
      width={725}
      height={700}
      setModalHeight={setModalHeight}
      dataTestContainer="protocol-changes-modal.container"
    >
      <TabsContainer>
        <Tabs type="secondary" tabs={tabs} selectedValue={selectedTabs} onClick={({ value }: { value: string }) => setSelectedTabs(value)} />
        <Excel onClick={() => nsiStore.exportXLSProtocolChanges()} data-test="protocol-changes-modal.export-button">
          <Icon width={18} name="excel" />
          <>Выгрузка в EXCEL</>
        </Excel>
      </TabsContainer>
      {isLoadingProtocolChanges ? (
        <LoaderContainer>
          <Loader spinnerSize={100} />
        </LoaderContainer>
      ) : (
        <ChildrenTabsContainer height={modalHeight - 150}>
          {(selectedTabs === "ALL" &&
            protocolChanges?.addEntries?.length === 0 &&
            protocolChanges?.deleteEntries?.length === 0 &&
            protocolChanges?.changeEntries?.length === 0) ||
          (selectedTabs !== "ALL" && (protocolChanges[selectedTabs]?.length === 0 || !protocolChanges[selectedTabs])) ? (
            <>
              <NoData />
            </>
          ) : (
            <>
              {(selectedTabs === "ALL" || selectedTabs === "addEntries") &&
                protocolChanges.addEntries &&
                protocolChanges.addEntries.map((item: string, index: number) => {
                  return (
                    <Entries key={`entries-addEntries-${index}`} colorScheme="green" data-test="protocol-changes-modal-table.item-row">
                      {item}
                    </Entries>
                  );
                })}
              {(selectedTabs === "ALL" || selectedTabs === "deleteEntries") &&
                protocolChanges.deleteEntries &&
                protocolChanges.deleteEntries.map((item: string, index: number) => {
                  return (
                    <Entries key={`entries-deleteEntries-${index}`} colorScheme="red" data-test="protocol-changes-modal-table.item-row">
                      {item}
                    </Entries>
                  );
                })}
              {(selectedTabs === "ALL" || selectedTabs === "changeEntries") &&
                protocolChanges.changeEntries &&
                protocolChanges.changeEntries.map((item: string, index: number) => {
                  return (
                    <Entries key={`entries-changeEntries-${index}`} colorScheme="purple" data-test="protocol-changes-modal-table.item-row">
                      {item}
                    </Entries>
                  );
                })}
            </>
          )}
        </ChildrenTabsContainer>
      )}
    </ModalStyled>
  );
});
