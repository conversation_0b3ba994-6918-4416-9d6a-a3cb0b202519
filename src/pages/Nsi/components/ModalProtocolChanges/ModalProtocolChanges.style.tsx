import styled, { css } from "styled-components";
import { Modal } from "components/Modal";
import { FilterDatePicker } from "components/FilterDatePicker";

export const Excel = styled.div`
  color: ${(p) => p.theme.colorExcel};
  margin: 0 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 8px;
  user-select: none;
  transition: all 0.3s;
  text-align: center;
  &:hover {
    background-color: ${(p) => p.theme.lightGray};
  }
  &:active {
    background-color: ${(p) => p.theme.gray};
  }
`;

export const ModalStyled = styled(Modal)`
  //width: 725px;
  //height: 700px;
`;

export const FilterContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 20px;
`;

export const FilterDatePickerStyled = styled(FilterDatePicker)`
  margin-left: 10px;
`;

export const ChildrenTabsContainer = styled.div<{ height?: number }>`
  border: solid 1px ${(p) => p.theme.lightGray};
  width: 100%;
  height: ${(p) => p.height}px;
  border-radius: 2px;
  overflow-x: auto;
`;

export const TabsContainer = styled.div`
  height: 50px;
  width: 100%;
  margin: 10px 0;
  display: flex;
`;

export const Entries = styled.div<{ colorScheme?: "red" | "green" | "purple" }>`
  border-radius: 8px;
  //height: 70px;
  display: flex;
  align-items: center;
  padding: 0 10px;
  margin: 10px;
  //font-size: 13px;
  white-space: pre-wrap;

  ${(p) =>
    p.colorScheme === "green" &&
    css`
      border: solid 1px ${(p) => p.theme.greenActiveSupport};
      color: ${(p) => p.theme.greenActiveSupport};
      background-color: ${(p) => p.theme.greenLightSupport};
    `}

  ${(p) =>
    p.colorScheme === "red" &&
    css`
      border: solid 1px ${(p) => p.theme.redActiveSupport};
      color: ${(p) => p.theme.redActiveSupport};
      background-color: ${(p) => p.theme.redLightSupport};
    `}

  ${(p) =>
    p.colorScheme === "purple" &&
    css`
      border: solid 1px ${(p) => p.theme.purpleActiveSupport};
      color: ${(p) => p.theme.purpleActiveSupport};
      background-color: ${(p) => p.theme.purpleLightSupport};
    `}
`;

export const LoaderContainer = styled.div`
  width: 100%;
  height: 550px;
  display: flex;
  border: solid 1px ${(p) => p.theme.lightGray};
`;

export const Arrow = styled.div`
  width: 20px;
  height: auto;
  display: flex;
  transform: rotate(180deg) scale(1);
  color: ${(p) => p.theme.primaryColor};
  cursor: pointer;
  transition: 0.3s;

  &:hover {
    transform: rotate(180deg) scale(1.2);
  }
`;
