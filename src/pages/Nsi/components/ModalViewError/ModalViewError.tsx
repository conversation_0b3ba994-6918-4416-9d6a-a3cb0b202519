import React, { useEffect, useState } from "react";
import { useStores } from "stores/useStore";
import { Modal } from "components/Modal";
import { observer } from "mobx-react";
import { Tabs } from "components/Tabs";
import styled from "styled-components";
import { NoData } from "components/NoData";
import { Icon } from "components/Icon";
import { LoaderContainer } from "../ModalDateComparison/ModalDateComparison.style";
import { Loader } from "components/Loader";

export const Excel = styled.div`
  color: ${(p) => p.theme.colorExcel};
  margin: 0 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 8px;
  user-select: none;
  transition: all 0.3s;
  text-align: center;
  &:hover {
    background-color: ${(p) => p.theme.lightGray};
  }
  &:active {
    background-color: ${(p) => p.theme.gray};
  }
`;

const ModalStyled = styled(Modal)`
  //width: 725px;
  //height: 500px;
`;

const Content = styled.div<{ height?: number }>`
  width: 100%;
  margin-top: 10px;
  //height: 360px;
  height: ${(p) => p.height}px;
  border: solid 1px #e0e2e7;
  border-radius: 2px;
  overflow-x: auto;
`;

const TabsContainer = styled.div`
  height: 50px;
  display: flex;
`;

const ItemsContainer = styled.div`
  width: 100%;
  height: 100%;
`;

const Item = styled.div`
  border-radius: 8px;
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 10px;
  margin: 10px;
  background-color: ${(p) => p.theme.redLightSupport};
  color: ${(p) => p.theme.redActiveSupport};
`;

const Arrow = styled.div`
  width: 20px;
  height: auto;
  display: flex;
  transform: rotate(180deg) scale(1);
  color: ${(p) => p.theme.primaryColor};
  cursor: pointer;
  transition: 0.3s;

  &:hover {
    transform: rotate(180deg) scale(1.2);
  }
`;

export const ModalViewError = observer(({ onCancel, onModalEss, loadMode, onModalFile }: { onCancel: any; onModalEss: any; loadMode: any; onModalFile: any }) => {
  const { nsiStore } = useStores();
  const { errorByTask, isLoadingViewError } = nsiStore;

  useEffect(() => {
    nsiStore.getErrorsTask(loadMode);
  }, []);

  const tabs = [
    { value: "duplication", label: "Дублирование", count: errorByTask.duplication.length },
    { value: "errorGou", label: "Ошибки ГОУ", count: errorByTask.errorGou.length },
    { value: "errorKartaVed", label: "Ошибки в Картах ведения", count: errorByTask.errorKartaVed.length },
  ];

  const [selectedTab, setSelectedTab] = useState("duplication");

  const [modalHeight, setModalHeight] = useState(500);

  return (
    <ModalStyled
      width={725}
      height={500}
      setModalHeight={setModalHeight}
      onCancel={() => {
        if (loadMode === "ess") {
          onModalEss();
        } else {
          onModalFile();
        }
        onCancel();
      }}
      isOverLay
      title="Протокол ошибок"
      dataTestContainer="protocol-errors-modal.container"
    >
      <TabsContainer>
        <Tabs tabs={tabs} selectedValue={selectedTab} type="secondary" onClick={({ value }: { value: any }) => setSelectedTab(value)} />
        <Excel onClick={() => nsiStore.exportXLSProtocolErrors()} data-test="protocol-errors-modal.excel-button">
          <Icon width={18} name="excel" />
          <>Выгрузка в EXCEL</>
        </Excel>
      </TabsContainer>
      {isLoadingViewError ? (
        <LoaderContainer data-test="protocol-errors-modal.loader">
          <Loader spinnerSize={100} />
        </LoaderContainer>
      ) : (
        <Content height={modalHeight - 140}>
          {errorByTask[selectedTab].length > 0 ? (
            <ItemsContainer>
              {errorByTask[selectedTab].map((item: any, index: any) => {
                return <Item key={`item-error-${index}`} data-test="protocol-errors-modal.item-row">{item}</Item>;
              })}
            </ItemsContainer>
          ) : (
            <NoData />
          )}
        </Content>
      )}
    </ModalStyled>
  );
});
