import React, { FC, useEffect, useState } from "react";
import { Loader } from "components/Loader";
import { useStores } from "stores/useStore";
import { Icon } from "components/Icon";
import { observer } from "mobx-react";
import { Content, Row, RowCell, ButtonsContainer, ButtonStyled, ModalStyled, TitleConfirmModal, ButtonDownload, LoaderContainer } from "./ModalFile.style";
import { ModalFileProps } from "./ModalFile.types";
import { FilterDatePickerStyled, LabelStatus } from "../../Nsi.style";
import { DEFAULT_DAY, DEFAULT_MONTH, DEFAULT_YEAR, prepareDate } from "helpers/DateUtils";
import { DropZone } from "components/DropZone";
import { IconStyled, LabelStatusTitle, StatusText } from "../ModalEss/ModalEss.style";
import { ModalInputDc } from "../ModalInputDc";
import { ButtonStyled as SmallButton } from "components/FilterDatePicker/FilterDatePicker.style";
import { useNavigate } from "react-router-dom";

export const ModalFile: FC<ModalFileProps> = observer(({ onCancel, onViewError, onProtocolChanges, onTransition }) => {
  const { nsiStore, liveTimerStore } = useStores();
  const { essStatus, isLoadingEss, isLoadingPublication, mainStatusLoading, lastTaskIdNsi, downloadInfo, distributedInfo, isLoadingFileEss } = nsiStore;

  const isLoading = downloadInfo.isDownloaded || distributedInfo.isDistributed; //isLoadingEss

  const [taskIdEss, setTaskIdEss] = useState(null);

  useEffect(() => {
    const initTaskId: any = lastTaskIdNsi ?? null;
    setTaskIdEss(initTaskId);
  }, [isLoadingEss, lastTaskIdNsi]);

  useEffect(() => {
    nsiStore.startLoadingEss();
    nsiStore.initModalFile();
    return () => {
      nsiStore.stopLoadingEss();
      nsiStore.stopModalEss();
    };
  }, []);

  const dateCurrent = new Date(`${DEFAULT_YEAR}-${DEFAULT_MONTH}, ${DEFAULT_DAY}`);
  dateCurrent.setDate(dateCurrent.getDate() + 1);
  const [DEF_YEAR, DEF_MONTH, DEF_DAY] = prepareDate(String(dateCurrent.getFullYear()), String(dateCurrent.getMonth() + 1), String(dateCurrent.getDate())).split("-");

  const initDate = JSON.parse(localStorage.getItem("date-loaded") as string) ?? { day: Number(DEF_DAY), month: Number(DEF_MONTH), year: Number(DEF_YEAR) };
  const [date, setDate] = useState(initDate);
  const [isDist, setIsDist] = useState(false);

  useEffect(() => {
    liveTimerStore.getTimeServer().then(({ dateTime }: any) => {
      const [serverDate] = dateTime.split("T");
      const [year, month, day] = serverDate.split("-");
      const dateCurrent = new Date(`${year}-${month}, ${day}`);
      dateCurrent.setDate(dateCurrent.getDate() + 1);
      const [DEF_YEAR, DEF_MONTH, DEF_DAY] = prepareDate(String(dateCurrent.getFullYear()), String(dateCurrent.getMonth() + 1), String(dateCurrent.getDate())).split("-");
      setDate({ year: Number(DEF_YEAR), day: Number(DEF_DAY), month: Number(DEF_MONTH) });
    });
    return () => {
      nsiStore.stopModalEss();
      nsiStore.resetPublication();
      setIsDist(false);
    };
  }, []);

  const [isModalConfirm, setIsModalConfirm] = useState(false);

  const [files, setFiles] = useState([]);

  const isDisabledButtons =
    isLoading || (essStatus.status && (essStatus.status === "CANCELED" || essStatus.status === "PUBLISHED" || essStatus.status === "FAILED")) || isLoadingPublication;

  const [isModalInputDC, setIsModalInputDC] = useState(false);

  const [selected, setSelected] = useState([]);

  const getTitleLoading = () => {
    if (downloadInfo.isDownloaded) {
      return `Пользователь ${downloadInfo?.initiator?.initiatorName} загружает НСИ`;
    }
    if (distributedInfo.isDistributed) {
      return `Пользователь ${distributedInfo?.initiator?.initiatorName} распространяет НСИ`;
    }
    return "Идет проверка файла";
  };

  const history = useNavigate();

  useEffect(() => {
    if (essStatus.status === "PUBLISHED" && isDist) {
      const [day, month, year] = essStatus.availableFrom.split(".");
      setIsDist(false);
      history(`?year=${Number(year)}&month=${Number(month)}&day=${Number(day)}&viewPage=distribution`);
      onCancel();
    }
  }, [essStatus]);

  const [modalHeight, setModalHeight] = useState(540);

  return (
    <ModalStyled
      isOverLay
      width={580}
      height={540}
      setModalHeight={setModalHeight}
      onCancel={() => {
        onCancel();
        localStorage.removeItem("date-loaded");
      }}
      title="Загрузка НСИ из Файла"
      dataTestContainer="upload-nsi-from-file-modal.container"
    >
      {isModalInputDC && (
        <ModalInputDc
          selected={selected}
          setSelected={setSelected}
          onCancel={() => setIsModalInputDC(false)}
          onConfirm={() => {
            setIsModalInputDC(false);
            setIsDist(true);
            nsiStore
              .publication(selected)
              .then(() => {
                nsiStore.stopLoadingEss();
                nsiStore.stopModalEss();
              })
              .then(() => {
                nsiStore.startLoadingEss();
                nsiStore.initModalFile();
              });
          }}
        />
      )}
      {!isModalConfirm ? (
        <Content height={modalHeight - 100}>
          <Row>
            <LabelStatusTitle>
              {essStatus.availableFrom && essStatus.loadedAt && (
                <>
                  НСИ на {essStatus.availableFrom} загружена {essStatus.loadedAt}
                </>
              )}
            </LabelStatusTitle>
          </Row>
          <Row>
            <DropZone
              label="Кликните чтобы выбрать XML файл или переместите его сюда вручную"
              accept=".xml"
              files={files}
              setFiles={setFiles}
              disabled={isLoading}
              readOnly={true}
              // onBlur={handleBlurDropZone("file")}
              // errors={errors.file && touched.file ? [errors.file] : []}
              dataTest="upload-nsi-from-file-modal.file-input"
            />
          </Row>
          <div style={{ marginTop: 110 }}>
            <Row>
              <RowCell justify="flex-end">
                <FilterDatePickerStyled
                  day={date.day}
                  month={date.month}
                  year={date.year}
                  hiddenChooseButton={true}
                  prevDisabled
                  onClick={({ day, month, year }) => {
                    setDate({ day, month, year });
                    localStorage.setItem("date-loaded", JSON.stringify({ day, month, year }));
                  }}
                  disabled={isLoading || isLoadingPublication || isLoadingFileEss}
                  dataTest="upload-nsi-from-file-modal.date-field"
                />
              </RowCell>
              <RowCell justify="flex-start">
                <SmallButton
                  disabled={files.length === 0 || isLoading || isLoadingPublication || isLoadingFileEss}
                  onClick={() => {
                    nsiStore
                      .createTaskFile({ day: date.day, month: date.month, year: date.year, file: files[0] })
                      .then(() => {
                        nsiStore.stopLoadingEss();
                        nsiStore.stopModalEss();
                      })
                      .then(async () => {
                        setFiles([]);
                        nsiStore.startLoadingEss();
                        await nsiStore.initModalFile();
                      });
                  }}
                  // title="Загрузить"
                  title={isLoading || isLoadingPublication || isLoadingFileEss ? <Loader /> : <>Загрузить</>}
                  message={isLoading || isLoadingPublication || isLoadingFileEss ? getTitleLoading() : "Загрузить"}
                  dataTest="upload-nsi-from-file-modal.upload-button"
                />
              </RowCell>
            </Row>
            <Row>
              <RowCell>
                <LabelStatus justify="flex-end">Статус задачи :</LabelStatus>
              </RowCell>
              {isLoading ? (
                <RowCell status="LOADED" data-test="upload-nsi-from-file-modal.status">
                  <LoaderContainer>
                    <Loader spinnerSize={20} />
                  </LoaderContainer>
                  <StatusText>Загрузка</StatusText>
                </RowCell>
              ) : (
                <RowCell status={taskIdEss ? essStatus.status : null} data-test="upload-nsi-from-file-modal.status">
                  {essStatus.status === "LOADED" && taskIdEss && (
                    <>
                      <IconStyled width={20} name="success" />
                      <StatusText>Успешно загружена</StatusText>
                    </>
                  )}
                  {essStatus.status === "CANCELED" && taskIdEss && (
                    <>
                      <IconStyled width={20} name="close" />
                      <StatusText>Отменена</StatusText>
                    </>
                  )}
                  {essStatus.status === "PUBLISHED" && taskIdEss && (
                    <>
                      <IconStyled width={20} name="save" />
                      <StatusText>Сохранена</StatusText>
                    </>
                  )}
                  {(essStatus.status === "FAILED" || !taskIdEss) && <StatusText>Недоступен</StatusText>}
                </RowCell>
              )}
            </Row>
            <Row>
              <RowCell>
                <LabelStatus justify="flex-end">Время загрузки НСИ :</LabelStatus>
              </RowCell>
              <RowCell justify="flex-start" data-test="upload-nsi-from-file-modal.upload-date">{essStatus.loadedAtCurrent ? essStatus.loadedAtCurrent : "Недоступно"}</RowCell>
            </Row>
            <Row>
              <RowCell>
                <LabelStatus justify="flex-end">Начало действия НСИ :</LabelStatus>
              </RowCell>
              <RowCell data-test="upload-nsi-from-file-modal.beginning-nsi-action">{essStatus.availableFromCurrent ? essStatus.availableFromCurrent : "Недоступно"}</RowCell>
            </Row>
            <Row justify="center">
              <RowCell>
                <LabelStatus style={{ justifyContent: "flex-end" }}>Инициатор загрузки НСИ :</LabelStatus>
              </RowCell>
              <RowCell data-test="upload-nsi-from-file-modal.user">{essStatus.status === "FAILED" || essStatus.status === "CANCELED" || !taskIdEss ? "Недоступен" : mainStatusLoading}</RowCell>
            </Row>
            {essStatus.error ? (
              <Row isError status={essStatus.status}>
                {essStatus.error}
              </Row>
            ) : (
              <Row isError />
            )}
          </div>
          <ButtonsContainer>
            <ButtonStyled
              title="Протокол ошибок"
              disabled={isDisabledButtons || isLoadingFileEss}
              onClick={() => {
                onTransition && onTransition();
                onViewError && onViewError();
              }}
              dataTest="upload-nsi-from-file-modal.protocol-errors-button"
            />
            <ButtonStyled
              title="Протокол изменений"
              disabled={isDisabledButtons || isLoadingFileEss}
              onClick={() => {
                onTransition && onTransition();
                onProtocolChanges && onProtocolChanges();
              }}
              dataTest="upload-nsi-from-file-modal.protocol-changes-button"
            />
            <ButtonStyled
              title={isLoadingPublication || distributedInfo.isDistributed ? <Loader /> : <>Сохранить</>}
              // message="Сохранить"
              message={isLoading || isLoadingPublication ? getTitleLoading() : "Сохранить"}
              isLoading={isLoadingPublication}
              disabled={(isDisabledButtons && essStatus.isPossibleSave) || essStatus.status !== "LOADED" || isLoading || isLoadingFileEss}
              onClick={() => {
                if (!isLoadingPublication) {
                  if (essStatus.error) {
                    setIsModalConfirm(true);
                  } else {
                    setIsModalInputDC(true);
                  }
                }
              }}
              dataTest="upload-nsi-from-file-modal.save-button"
            />
            <ButtonStyled
              title="Отменить загрузку"
              disabled={isDisabledButtons || isLoadingFileEss}
              onClick={() => {
                if (taskIdEss) {
                  nsiStore.cancelTask().then(() => {
                    nsiStore.getLastTask();
                  });
                }
              }}
              dataTest="upload-nsi-from-file-modal.cancel-button"
            />
          </ButtonsContainer>
        </Content>
      ) : (
        <Content height={modalHeight - 100}>
          <TitleConfirmModal>Вы действительно хотите опубликовать данные ?</TitleConfirmModal>
          <ButtonsContainer>
            <ButtonStyled
              onClick={() => {
                setIsModalConfirm(false);
                setIsModalInputDC(true);
              }}
              title="Сохранить"
            />
            <ButtonStyled type="secondary" onClick={() => setIsModalConfirm(false)} title="Отменена" />
          </ButtonsContainer>
        </Content>
      )}
    </ModalStyled>
  );
});
