import { Modal } from "components/Modal";
import styled, { css } from "styled-components";
import { Button } from "components/Button";

export const LoaderContainer = styled.div`
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  margin-top: 5px;
`;

export const Content = styled.div<{ height?: number }>`
  width: 100%;
  //height: 100%;
  height: ${(p) => p.height}px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-direction: column;
`;

export const Row = styled.div<{ status?: string; justify?: string; isError?: boolean }>`
  width: 100%;
  height: 30px;
  //margin: 6px 0;
  display: flex;
  flex-direction: row;
  &:last-child {
    justify-content: center;
  }
  ${(p) =>
    p.justify &&
    css`
      justify-content: ${p.justify};
    `}
  ${(p) =>
    p.status === "DONE" &&
    css`
      color: ${(p) => p.theme.greenActiveSupport};
    `}
  ${(p) =>
    p.status === "FAILED" &&
    css`
      color: ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.isError &&
    css`
      margin: 0;
    `}
`;

export const RowCell = styled.div<{ status?: string; justify?: string }>`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  white-space: nowrap;
  //justify-content: center;
  &:last-child {
    //justify-content: center;
    margin-left: 20px;
  }
  ${(p) =>
    p.justify &&
    css`
      justify-content: ${p.justify};
    `}
  ${(p) =>
    p.status === "LOADED" &&
    css`
      color: ${(p) => p.theme.blueActiveSupport};
    `}
  ${(p) =>
    p.status === "CANCELED" &&
    css`
      color: ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.status === "PUBLISHED" &&
    css`
      color: ${(p) => p.theme.greenActiveSupport};
    `}
`;

export const ButtonsContainer = styled.div`
  width: 100%;
  display: flex;
  //margin-top: 10px;
  height: 60px;
  align-items: center;
  padding-top: 10px;
  justify-content: center;
`;

export const ButtonStyled = styled(Button)<{ isLoading?: boolean }>`
  width: 150px;
  height: 50px;
  font-size: 14px;
  margin: 0 5px;
  ${(p) =>
    p.isLoading &&
    css`
      background-color: ${(p) => p.theme.backgroundColorSecondary};
      cursor: no-drop;
      border: solid 1px ${(p) => p.theme.lightGray};
      &:hover {
        background-color: ${(p) => p.theme.backgroundColorSecondary};
      }
    `}
`;

export const ModalStyled = styled(Modal)`
  //width: 580px;
  ////height: 440px;
  //height: 540px;
`;

export const TitleConfirmModal = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
  height: 150px;
`;

export const ButtonDownload = styled(ButtonStyled)`
  height: 38px;
`;
