import { Modal } from "components/Modal";
import styled, { css } from "styled-components";
import { Button } from "components/Button";
import { Icon } from "components/Icon";

export const LoaderContainer = styled.div`
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  margin-top: 5px;
`;

export const Content = styled.div<{ height?: number }>`
  width: 100%;
  //height: 100%;
  //height: 700px;
  height: ${(p) => p.height}px;
  //padding-top: 25px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-direction: column;
`;

export const Row = styled.div<{ status?: string; justify?: string; isError?: boolean }>`
  width: 100%;
  height: 25px;
  //margin: 6px 0;
  display: flex;
  flex-direction: row;
  &:last-child {
    justify-content: center;
  }
  ${(p) =>
    p.justify &&
    css`
      justify-content: ${p.justify};
    `}
  ${(p) =>
    p.status === "DONE" &&
    css`
      color: ${(p) => p.theme.greenActiveSupport};
    `}
  ${(p) =>
    p.status === "FAILED" &&
    css`
      color: ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.isError &&
    css`
      margin: 0;
    `}
`;

export const RowCell = styled.div<{ status?: string; isDate?: boolean; isButton?: boolean }>`
  width: 35%;
  height: 100%;
  display: flex;
  align-items: center;
  &:last-child {
    justify-content: flex-start;
    margin-left: 20px;
  }
  ${(p) =>
    p.status === "LOADED" &&
    css`
      color: ${(p) => p.theme.blueActiveSupport};
    `}
  ${(p) =>
    p.status === "CANCELED" &&
    css`
      color: ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.status === "PUBLISHED" &&
    css`
      color: ${(p) => p.theme.greenActiveSupport};
    `}
  ${(p) =>
    p.isDate &&
    css`
      width: 50%;
      justify-content: flex-end;
    `}
  ${(p) =>
    p.isButton &&
    css`
      width: 49%;
      justify-content: flex-end;
    `}
`;

export const ButtonsContainer = styled.div`
  width: 100%;
  display: flex;
  //margin-top: 40px;
  height: 60px;
  align-items: center;
  padding-top: 10px;
  justify-content: center;
`;

export const ButtonStyled = styled(Button)<{ isLoading?: boolean }>`
  width: 150px;
  height: 50px;
  font-size: 14px;
  margin: 0 5px;
  ${(p) =>
    p.isLoading &&
    css`
      background-color: ${(p) => p.theme.backgroundColorSecondary};
      cursor: no-drop;
      border: solid 1px ${(p) => p.theme.lightGray};
      &:hover {
        background-color: ${(p) => p.theme.backgroundColorSecondary};
      }
    `}
`;

export const ModalStyled = styled(Modal)`
  //width: 600px;
  //height: 460px;
`;

export const TitleConfirmModal = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
`;

export const StatusText = styled.div`
  // color: ${(p) => p.theme.textColor};
  display: flex;
  align-items: center;
  white-space: nowrap;
`;

export const IconStyled = styled(Icon)`
  //margin: 0 30px;
  margin-right: 10px;
`;

export const LabelStatusTitle = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  width: 100%;
`;
