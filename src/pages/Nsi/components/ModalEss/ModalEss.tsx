import React, { FC, useEffect, useState } from "react";
import { Loader } from "components/Loader";
import { useStores } from "stores/useStore";
import { observer } from "mobx-react";
import {
  Content,
  Row,
  RowCell,
  ButtonsContainer,
  ButtonStyled,
  ModalStyled,
  TitleConfirmModal,
  StatusText,
  IconStyled,
  LoaderContainer,
  LabelStatusTitle,
} from "./ModalEss.style";
import { ModalEssProps } from "./ModalEss.types";
import { FilterDatePickerStyled, LabelStatus } from "../../Nsi.style";
import { DEFAULT_DAY, DEFAULT_MONTH, DEFAULT_YEAR, prepareDate } from "helpers/DateUtils";
import { ModalInputDc } from "../ModalInputDc";
import { ButtonStyled as SmallButton } from "components/FilterDatePicker/FilterDatePicker.style";
import { useNavigate } from "react-router-dom";

export const ModalEss: FC<ModalEssProps> = observer(({ onCancel, onViewError, onProtocolChanges, onTransition }) => {
  const { nsiStore, liveTimerStore } = useStores();
  const { essStatus, isLoadingEss, isLoadingPublication, mainStatusLoading, lastTaskIdNsi, distributedInfo, downloadInfo } = nsiStore;

  const isLoading = downloadInfo.isDownloaded || distributedInfo.isDistributed; //isLoadingEss

  const [taskIdEss, setTaskIdEss] = useState(null);

  useEffect(() => {
    const initTaskId: any = lastTaskIdNsi ?? null;
    setTaskIdEss(initTaskId);
  }, [isLoadingEss, lastTaskIdNsi]);

  useEffect(() => {
    nsiStore.startLoadingEss();
    nsiStore.initModalEss();
    return () => {
      nsiStore.stopLoadingEss();
      nsiStore.stopModalEss();
    };
  }, []);

  const dateCurrent = new Date(`${DEFAULT_YEAR}-${DEFAULT_MONTH}, ${DEFAULT_DAY}`);
  dateCurrent.setDate(dateCurrent.getDate() + 1);
  const [DEF_YEAR, DEF_MONTH, DEF_DAY] = prepareDate(String(dateCurrent.getFullYear()), String(dateCurrent.getMonth() + 1), String(dateCurrent.getDate())).split("-");

  const initDate = { day: Number(DEF_DAY), month: Number(DEF_MONTH), year: Number(DEF_YEAR) };
  const [date, setDate] = useState<any>(initDate);
  const [isDist, setIsDist] = useState(false);

  useEffect(() => {
    liveTimerStore.getTimeServer().then(({ dateTime }: any) => {
      const [serverDate] = dateTime.split("T");
      const [year, month, day] = serverDate.split("-");
      const dateCurrent = new Date(`${year}-${month}, ${day}`);
      dateCurrent.setDate(dateCurrent.getDate() + 1);
      const [DEF_YEAR, DEF_MONTH, DEF_DAY] = prepareDate(String(dateCurrent.getFullYear()), String(dateCurrent.getMonth() + 1), String(dateCurrent.getDate())).split("-");
      setDate({ year: Number(DEF_YEAR), day: Number(DEF_DAY), month: Number(DEF_MONTH) });
    });
    return () => {
      nsiStore.stopModalEss();
      nsiStore.resetPublication();
      setIsDist(false);
    };
  }, []);

  const [isModalConfirm, setIsModalConfirm] = useState(false);

  const isDisabledButtons =
    isLoading || (essStatus.status && (essStatus.status === "CANCELED" || essStatus.status === "PUBLISHED" || essStatus.status === "FAILED")) || isLoadingPublication;

  const [isModalInputDC, setIsModalInputDC] = useState(false);

  const [selected, setSelected] = useState([]);

  const handleUpdateDate = (values: { day: number; month: number; year: number }) => {
    setDate(values);
    localStorage.setItem("date-loaded", JSON.stringify(values));
  };

  const getTitleLoading = () => {
    if (downloadInfo.isDownloaded) {
      const availableFrom = downloadInfo?.availableFrom ? downloadInfo?.availableFrom.split("-").reverse().join(".") : "";
      return `Пользователь ${downloadInfo?.initiator?.initiatorName} загружает НСИ на ${availableFrom}`;
    }
    if (distributedInfo.isDistributed) {
      const availableFrom = distributedInfo?.availableFrom ? distributedInfo?.availableFrom.split("-").reverse().join("-") : "";
      return `Пользователь ${distributedInfo?.initiator?.initiatorName} распространяет НСИ на ${availableFrom}`;
    }
    return "Идет подготовка к распространению ...";
  };

  const history = useNavigate();

  useEffect(() => {
    if (essStatus.status === "PUBLISHED" && isDist) {
      const [day, month, year] = essStatus.availableFrom.split(".");
      setIsDist(false);
      history(`?year=${Number(year)}&month=${Number(month)}&day=${Number(day)}&viewPage=distribution`);
      onCancel();
    }
  }, [essStatus]);

  const [modalHeight, setModalHeight] = useState(400);

  return (
    <ModalStyled
      isOverLay
      width={600}
      height={400}
      setModalHeight={setModalHeight}
      onCancel={() => {
        onCancel();
        localStorage.removeItem("date-loaded");
      }}
      title="Загрузка НСИ из ЕСС"
      dataTestContainer="upload-nsi-from-ess-modal.container"
    >
      {isModalInputDC && (
        <ModalInputDc
          selected={selected}
          setSelected={setSelected}
          onCancel={() => setIsModalInputDC(false)}
          onConfirm={() => {
            setIsModalInputDC(false);
            setIsDist(true);
            nsiStore
              .publication(selected)
              .then(() => {
                nsiStore.stopLoadingEss();
                nsiStore.stopModalEss();
              })
              .then(() => {
                nsiStore.startLoadingEss();
                nsiStore.initModalEss();
              });
          }}
        />
      )}
      {!isModalConfirm ? (
        <Content height={modalHeight - 100}>
          <Row>
            <LabelStatusTitle>
              {essStatus.availableFrom && essStatus.loadedAt && (
                <>
                  НСИ на {essStatus.availableFrom} загружена {essStatus.loadedAt}
                </>
              )}
            </LabelStatusTitle>
          </Row>
          <Row justify="space-around">
            <RowCell isDate={true}>
              <FilterDatePickerStyled
                day={date.day}
                month={date.month}
                year={date.year}
                hiddenChooseButton
                prevDisabled
                onClick={handleUpdateDate}
                disabled={isLoading || isLoadingPublication}
                dataTest="upload-nsi-from-ess-modal.date-field"
              />
            </RowCell>
            <RowCell isButton={true}>
              <SmallButton
                disabled={isLoading || isLoadingPublication}
                message={isLoading || isLoadingPublication ? getTitleLoading() : "Загрузить"}
                title="Загрузить"
                onClick={() => {
                  nsiStore
                    .createTask(date)
                    .then(() => {
                      nsiStore.stopLoadingEss();
                      nsiStore.stopModalEss();
                    })
                    .then(async () => {
                      nsiStore.startLoadingEss();
                      await nsiStore.initModalEss();
                    });
                }}
                dataTest="upload-nsi-from-ess-modal.upload-button"
              />
            </RowCell>
          </Row>
          <Row justify="center">
            <RowCell>
              <LabelStatus style={{ justifyContent: "flex-end" }}>Статус задачи :</LabelStatus>
            </RowCell>
            {isLoading ? (
              <RowCell status="LOADED" data-test="upload-nsi-from-ess-modal.status">
                <LoaderContainer>
                  <Loader spinnerSize={20} />
                </LoaderContainer>
                <StatusText>Загрузка</StatusText>
              </RowCell>
            ) : (
              <RowCell status={taskIdEss ? essStatus.status : null} data-test="upload-nsi-from-ess-modal.status">
                {essStatus.status === "LOADED" && taskIdEss && (
                  <>
                    <IconStyled width={20} name="success" />
                    <StatusText>Успешно загружена</StatusText>
                  </>
                )}
                {essStatus.status === "CANCELED" && taskIdEss && (
                  <>
                    <IconStyled width={20} name="close" />
                    <StatusText>Отменена</StatusText>
                  </>
                )}
                {essStatus.status === "PUBLISHED" && taskIdEss && (
                  <>
                    <IconStyled width={20} name="save" />
                    <StatusText>Сохранена</StatusText>
                  </>
                )}
                {(essStatus.status === "FAILED" || !taskIdEss) && <StatusText>Недоступен</StatusText>}
              </RowCell>
            )}
          </Row>
          <Row justify="center">
            <RowCell>
              <LabelStatus style={{ justifyContent: "flex-end" }}>Время загрузки НСИ :</LabelStatus>
            </RowCell>
            <RowCell data-test="upload-nsi-from-ess-modal.upload-date">{essStatus.loadedAtCurrent ? essStatus.loadedAtCurrent : "Недоступно"}</RowCell>
          </Row>
          <Row justify="center">
            <RowCell>
              <LabelStatus style={{ justifyContent: "flex-end" }}>Начало действия НСИ :</LabelStatus>
            </RowCell>
            <RowCell data-test="upload-nsi-from-ess-modal.beginning-nsi-action">{essStatus.availableFromCurrent ? essStatus.availableFromCurrent : "Недоступно"}</RowCell>
          </Row>
          <Row justify="center">
            <RowCell>
              <LabelStatus style={{ justifyContent: "flex-end" }}>Инициатор загрузки НСИ :</LabelStatus>
            </RowCell>
            <RowCell data-test="upload-nsi-from-ess-modal.user">{essStatus.status === "FAILED" || essStatus.status === "CANCELED" || !taskIdEss ? "Недоступен" : mainStatusLoading}</RowCell>
          </Row>
          {essStatus.error ? (
            <Row isError status={essStatus.status}>
              {essStatus.error}
            </Row>
          ) : (
            <Row isError />
          )}
          <ButtonsContainer>
            <ButtonStyled
              title="Протокол ошибок"
              disabled={isDisabledButtons}
              onClick={() => {
                onTransition && onTransition();
                onViewError && onViewError();
              }}
              dataTest="upload-nsi-from-ess-modal.protocol-errors-button"
            />
            <ButtonStyled
              title="Протокол изменений"
              disabled={isDisabledButtons}
              onClick={() => {
                onTransition && onTransition();
                onProtocolChanges && onProtocolChanges();
              }}
              dataTest="upload-nsi-from-ess-modal.protocol-changes-button"
            />
            <ButtonStyled
              title={isLoadingPublication || distributedInfo.isDistributed ? <Loader /> : <>Сохранить</>}
              isLoading={isLoadingPublication}
              // message="Сохранить"
              message={isLoading || isLoadingPublication ? getTitleLoading() : "Сохранить"}
              disabled={(isDisabledButtons && essStatus.isPossibleSave) || essStatus.status !== "LOADED" || isLoading}
              onClick={() => {
                if (!isLoadingPublication) {
                  if (essStatus.error) {
                    setIsModalConfirm(true);
                  } else {
                    setIsModalInputDC(true);
                  }
                }
              }}
              dataTest="upload-nsi-from-ess-modal.save-button"
            />
            <ButtonStyled
              title="Отменить загрузку"
              disabled={isDisabledButtons}
              onClick={() => {
                nsiStore.cancelTask().then(() => {
                  nsiStore.getLastTask();
                });
              }}
              dataTest="upload-nsi-from-ess-modal.cancel-button"
            />
          </ButtonsContainer>
        </Content>
      ) : (
        <Content>
          <TitleConfirmModal>Вы действительно хотите сохранить данные ?</TitleConfirmModal>
          <ButtonsContainer>
            <ButtonStyled
              onClick={() => {
                setIsModalConfirm(false);
                setIsModalInputDC(true);
              }}
              title="Сохранить"
            />
            <ButtonStyled type="secondary" onClick={() => setIsModalConfirm(false)} title="Отменить" />
          </ButtonsContainer>
        </Content>
      )}
    </ModalStyled>
  );
});
