import styled, { css } from "styled-components";
import { SegmentedPicker } from "components/SegmentedPicker";
import { Button } from "components/Button";
import { Icon } from "../../../../components/Icon";

export const ButtonMap = styled(Icon)`
  width: 16px;
  height: 16px;
`;

export const MapContainer = styled.div`
  width: 16px;
  height: 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  user-select: none;
  display: flex;
  flex-direction: row;
  justify-content: center;
  background: transparent;
  color: ${(props) => props.theme.buttonSecondaryColor};

  &:hover {
    background-color: ${(props) => props.theme.lightGray};
    border-color: ${(props) => props.theme.primaryColorHover};
  }

  &:active {
    color: ${(props) => props.theme.buttonSecondaryColorActive};
    border-color: ${(props) => props.theme.buttonSecondaryColorActive};
  }
`;

export const TableContainer = styled.div`
  //height: 845px;
  //max-height: 845px;
  //min-height: 845px;
  height: 100%;
`;

export const ButtonChoose = styled(Button)`
  width: 20px;
  height: 20px;
  padding: 0;
  margin-left: 10px;
`;

export const ComboboxContainer = styled.div`
  margin-left: auto;
  margin-right: 20px;
`;

export const SegmentedPickerStyled = styled(SegmentedPicker)`
  margin-right: 5px;
  width: 260px;
`;

export const SubMenu = styled.div`
  width: 100%;
  height: 65px;
  border-bottom: solid 1px ${(p) => p.theme.lightGray};
  padding: 15px 10px;
  border-radius: 6px;
`;

export const SupportMenuLeft = styled.div`
  width: 175px;
  height: 50px;
  box-shadow: 0 1px 2px rgb(0 0 0 / 30%), 0 2px 6px 2px rgb(0 0 0 / 15%);
  border-radius: 4px;
  top: 50px;
  z-index: 5;
  position: absolute;
  background-color: ${(p) => p.theme.lightGray};
`;

export const TreeContainer = styled.div`
  display: flex;
  //height: 94%;
  height: 900px;
  //border: solid 1px green;
  width: 100%;
  color: ${(p) => p.theme.textColor};
  @media (min-height: 1000px) and (max-height: 1080px) {
    height: 1010px;
  }
`;

export const TreeItem = styled.div`
  width: 50%;
  //height: 100%;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  //box-sizing: border-box;
  //box-shadow: 0 12px 16px -4px rgba(16, 24, 40, 0.1), 0px 4px 6px -2px rgba(16, 24, 40, 0.05);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  &:nth-child(2) {
    margin: 0 0 0 5px;
  }
  height: calc(100vh - 42px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 80px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 76px);
  }
`;

//  height: 900px;
//   @media (min-height: 400px) and (max-height: 429px) {
//     height: 370px;
//   }
//   @media (min-height: 430px) and (max-height: 459px) {
//     height: 400px;
//   }
//   @media (min-height: 460px) and (max-height: 499px) {
//     height: 430px;
//   }
//   @media (min-height: 500px) and (max-height: 599px) {
//     height: 470px;
//   }
//   @media (min-height: 600px) and (max-height: 619px) {
//     height: 500px;
//   }
//   @media (min-height: 620px) and (max-height: 639px) {
//     height: 540px;
//   }
//   @media (min-height: 640px) and (max-height: 699px) {
//     height: 550px;
//   }
//   @media (min-height: 700px) and (max-height: 739px) {
//     height: 620px;
//   }
//   @media (min-height: 740px) and (max-height: 767px) {
//     height: 660px;
//   }
//   @media (min-height: 768px) and (max-height: 779px) {
//     height: 670px;
//   }
//   @media (min-height: 780px) and (max-height: 799px) {
//     height: 690px;
//   }
//   @media (min-height: 800px) and (max-height: 819px) {
//     height: 700px;
//   }
//   @media (min-height: 820px) and (max-height: 839px) {
//     height: 720px;
//   }
//   @media (min-height: 840px) and (max-height: 859px) {
//     height: 740px;
//   }
//   @media (min-height: 860px) and (max-height: 869px) {
//     height: 780px;
//   }
//   @media (min-height: 870px) and (max-height: 899px) {
//     height: 790px;
//   }
//   @media (min-height: 900px) and (max-height: 929px) {
//     height: 860px;
//   }
//   @media (min-height: 930px) and (max-height: 999px) {
//     height: 890px;
//   }
//   @media (min-height: 1000px) and (max-height: 1023px) {
//     height: 940px;
//   }
//   @media (min-height: 1024px) and (max-height: 1049px) {
//     height: 970px;
//   }
//   @media (min-height: 1050px) and (max-height: 1079px) {
//     height: 1000px;
//   }
//   @media (min-height: 1080px) and (max-height: 1099px) {
//     height: 1030px;
//   }
//   @media (min-height: 1100px) and (max-height: 1129px) {
//     height: 1050px;
//   }
//   @media (min-height: 1130px) and (max-height: 1159px) {
//     height: 1080px;
//   }
//   @media (min-height: 1160px) and (max-height: 1199px) {
//     height: 1120px;
//   }
//   // new
//   @media (min-height: 1200px) and (max-height: 1229px) {
//     height: 1160px;
//   }
//   @media (min-height: 1230px) and (max-height: 1259px) {
//     height: 1090px;
//   }
//   @media (min-height: 1260px) and (max-height: 1299px) {
//     height: 1220px;
//   }
//   @media (min-height: 1300px) and (max-height: 1329px) {
//     height: 1260px;
//   }
//   @media (min-height: 1330px) and (max-height: 1359px) {
//     height: 1290px;
//   }
//   @media (min-height: 1360px) and (max-height: 1399px) {
//     height: 1320px;
//   }
//   @media (min-height: 1400px) and (max-height: 1429px) {
//     height: 1360px;
//   }
//   @media (min-height: 1430px) and (max-height: 1459px) {
//     height: 1390px;
//   }
//   @media (min-height: 1460px) and (max-height: 1499px) {
//     height: 1420px;
//   }
//   @media (min-height: 1500px) and (max-height: 1599px) {
//     height: 1460px;
//   }
//   @media (min-height: 1600px) and (max-height: 1699px) {
//     height: 1560px;
//   }
//   @media (min-height: 1700px) and (max-height: 1799px) {
//     height: 1660px;
//   }
//   @media (min-height: 1800px) and (max-height: 1899px) {
//     height: 1760px;
//   }
//   @media (min-height: 1900px) and (max-height: 1999px) {
//     height: 1860px;
//   }
//   @media (min-height: 2000px) and (max-height: 2099px) {
//     height: 1960px;
//   }
//   @media (min-height: 2100px) and (max-height: 2199px) {
//     height: 2060px;
//   }
//   @media (min-height: 2200px) and (max-height: 2299px) {
//     height: 2160px;
//   }
//   @media (min-height: 2300px) and (max-height: 2399px) {
//     height: 2260px;
//   }
//   @media (min-height: 2400px) and (max-height: 2499px) {
//     height: 2360px;
//   }

export const NameContainer = styled.div`
  display: flex;
  align-items: center;
  min-width: 0;
`;

export const NameIconContainer = styled.div<{ level?: number }>`
  margin-left: ${(p) => (p.level ?? 0) * 8}px;
  margin-right: 4px;
  width: 18px;
  height: 18px;
`;

export const LabelContainer = styled.div`
  flex: 1;
  min-width: 0;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
`;

export const Code = styled.div<{ level?: number }>`
  //margin-left: 10px;
  //font-size: 14px;
  //font-weight: bold;
  padding: 1px 4px; //2 4
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;

  ${(p) =>
    p.level === 0 &&
    css`
      background-color: ${(props) => props.theme.blueActiveSupport};
      color: ${(props) => props.theme.white};
    `}

  ${(p) =>
    p.level === 1 &&
    css`
      background-color: ${(props) => props.theme.greenActiveSupport};
      color: ${(props) => props.theme.white};
    `}

  ${(p) =>
    p.level === 2 &&
    css`
      background-color: ${(props) => props.theme.orangeActiveSupport};
      color: ${(props) => props.theme.white};
    `}

  ${(p) =>
    p.level === 3 &&
    css`
      background-color: ${(props) => props.theme.redActiveSupport};
      color: ${(props) => props.theme.white};
    `}

  ${(p) =>
    p.level === 4 &&
    css`
      background-color: ${(props) => props.theme.purpleActiveSupport};
      color: ${(props) => props.theme.white};
    `}
`;

export const CustomCell = styled.div<{ width?: number }>`
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: auto;
  ${(p) =>
    p.width &&
    css`
      width: ${p.width};
    `}
`;

export const PaginationNoData = styled.div`
  width: 100%;
  height: 70px;
`;

export const SRPGContainer = styled.div`
  height: calc(100vh - 42px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 80px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 76px);
  }
`;
