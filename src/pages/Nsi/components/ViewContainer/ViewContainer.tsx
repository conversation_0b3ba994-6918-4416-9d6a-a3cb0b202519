import React, { FC, useEffect, useMemo, useState } from "react";
import {
  SegmentedPickerStyled,
  TreeContainer,
  TreeItem,
  ComboboxContainer,
  Code,
  CustomCell,
  SRPGContainer,
  TableContainer,
  ButtonMap,
  MapContainer,
  NameContainer,
  NameIconContainer,
  LabelContainer,
} from "./ViewContainer.style";
import { DEFAULT_DAY, DEFAULT_MONTH, DEFAULT_YEAR, prepareDate } from "helpers/DateUtils";
import { useStores } from "stores/useStore";
import queryString from "query-string";
import { useNavigate } from "react-router-dom";
import { Combobox } from "components/Combobox";
import { observer } from "mobx-react";
import { typesSRPG } from "../../Nsi";
import { Table } from "components/Table";
import { segmentedList } from "./ViewContainer.constants";
import { prepareDataTable } from "utils/prepareDataTable";
import { ModalMap } from "./components/ModalMap";
import { getWidthModal } from "helpers/adaptive";
import { deleteParams, idb, saveParams } from "../../../../utils/indexDB";
import { Icon } from "../../../../components/Icon";
import { IconNameProps } from "../../../../components/Icon/Icon.type";

type Row = {
  tabId: string;
  objId: string;
  id: number;
  type: string;
  name: string;
  level: number;
  hasChildren: boolean;
  isOpen: boolean;
  parentId: string;
};

type SearchButton = { columnName: string; operation: string; value: string };

export const ViewContainer: FC<{ isCenter?: boolean }> = observer(({ isCenter }) => {
  const { nsiStore, tableStore } = useStores();
  const history = useNavigate();
  const { department, isLoadingLeftTable, isLoadingRightTable, srpgs, departmentOriginal } = nsiStore;
  const { day = DEFAULT_DAY, month = DEFAULT_MONTH, year = DEFAULT_YEAR, selectedSegment = "goy", srpg } = queryString.parse(location.search);

  const [selectDc, setSelectDc] = useState([]);
  const [columnsDC, setColumnsDC] = useState<any[]>([]);
  const [columns2, setColumns2] = useState<any[]>([]);
  const [columns3, setColumns3] = useState<any[]>([]);

  useMemo(() => {
    nsiStore.initLeft({ day, month, year });
    return () => {
      localStorage.removeItem(`table-sort-4_v5`);
      localStorage.removeItem(`table-sort-3_v4`);
      localStorage.removeItem(`table-sort-2_v5`);
    };
  }, [day, month, year, isCenter]);

  useMemo(() => {
    if (selectedSegment === "srpg" && department.length > 0) {
      nsiStore.initRight({
        day,
        month,
        year,
        selectedSegment: selectedSegment ?? "goy",
        selectItem: selectDc[0] ?? null,
      });
    }
    localStorage.removeItem(`tableOpened-2`);
    localStorage.removeItem(`tableOpened-3`);
  }, [day, month, year, selectedSegment, selectDc, isCenter, department]);

  const getDefaultColumns = () => {
    if (screenWidth >= 650 && screenWidth < 700) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 120, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 70, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 700 && screenWidth < 800) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 130, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 70, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 800 && screenWidth < 900) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 110, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 50, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 900 && screenWidth < 1024) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 110, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 100, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 1024 && screenWidth < 1152) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 170, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 100, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 1152 && screenWidth < 1280) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 230, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 100, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 1280 && screenWidth < 1400) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 300, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 100, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 1400 && screenWidth < 1600) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 360, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 100, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 1600 && screenWidth < 1700) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 460, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 100, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 1700 && screenWidth < 1800) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 510, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 100, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 1800 && screenWidth < 1900) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 560, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 100, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 1900 && screenWidth < 2000) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 610, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 100, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 2000 && screenWidth < 2100) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 660, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 100, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 2100 && screenWidth < 2200) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 710, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 100, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 2200 && screenWidth < 2560) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 760, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 100, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 2560 && screenWidth < 3000) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 940, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 100, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 3000 && screenWidth < 3840) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 1160, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 100, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    if (screenWidth >= 3840 && screenWidth < 4000) {
      return [
        { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 1580, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
        { name: "id", title: "ID", width: 100, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
        { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
      ];
    }
    return [
      { name: "child", title: " ", width: 170, isSearch: true, isSort: "alphabet" },
      { name: "name", title: "Название", width: 460, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
      { name: "id", title: "ID", width: 220, isSearch: true, position: "left", isSort: "number", searchLength: 1 },
      { name: "viewMap", title: " ", width: 60, isSearch: false, position: "center", isSort: "number", searchLength: 1 },
    ];
  };

  let defaultColumns = getDefaultColumns();

  const [initScreenWidth, setInitScreenWidth] = useState<any>(getWidthModal(document.documentElement.clientWidth));
  const [screenWidth, setScreenWidth] = useState<any>(initScreenWidth);

  const [isEditWidth, setEditWidth] = useState(false);

  const [columnOrderDC, setColumnOrderDC] = useState<any>([]);
  const [columnOrder2, setColumnOrder2] = useState<any>([]);
  const [columnOrder3, setColumnOrder3] = useState<any>([]);

  const setDefaultColumns = () => {
    setColumnsDC(() => {
      return columnOrderDC.map((el: any) => {
        return getDefaultColumns().find((item) => item.name === el);
      });
    });
    setColumns2(() => {
      return columnOrder2.map((el: any) => {
        return getDefaultColumns().find((item) => item.name === el);
      });
    });
    setColumns3(() => {
      return columnOrder3.map((el: any) => {
        return getDefaultColumns().find((item) => item.name === el);
      });
    });
  };

  useEffect(() => {
    if (initScreenWidth !== screenWidth && !isEditWidth) {
      setEditWidth(true);
      setDefaultColumns();
    } else if (initScreenWidth !== screenWidth && isEditWidth) {
      setDefaultColumns();
    } else if (initScreenWidth === screenWidth && isEditWidth) {
      setDefaultColumns();
    }
  }, [screenWidth]);

  const resizeWeb = () => {
    setScreenWidth(getWidthModal(document.documentElement.clientWidth));
  };

  useEffect(() => {
    window.addEventListener("resize", resizeWeb);
    return () => {
      window.removeEventListener("resize", resizeWeb);
    };
  }, []);

  useEffect(() => {
    tableStore.getTableParams("4_v5").then((data: any) => {
      const isWidth = data?.some((el: any) => !Number(el.width));
      if (data && !isWidth) {
        setColumnsDC(
          data
            .filter((el: any) => el.name !== "viewMap")
            .filter((el: any) => el.name !== "child")
            .map((el: any) => {
              if (el.name === "id") {
                return { ...el, title: "КПО" };
              }
              return el;
            })
        );
      } else {
        setColumnsDC(
          defaultColumns
            .filter((el: any) => el.name !== "viewMap")
            .filter((el: any) => el.name !== "child")
            .map((el: any) => {
              if (el.name === "id") {
                return { ...el, title: "КПО" };
              }
              return el;
            })
        );
      }
    });
    tableStore.getTableParams("2_v5").then((data: any) => {
      const isWidth = data?.some((el: any) => !Number(el.width));
      if (data && !isWidth) {
        setColumns2(data);
      } else {
        setColumns2(defaultColumns);
      }
    });
    tableStore.getTableParams("3_v4").then((data: any) => {
      const isWidth = data?.some((el: any) => !Number(el.width));
      if (data && !isWidth) {
        setColumns3(data);
      } else {
        setColumns3(defaultColumns);
      }
    });
  }, [selectedSegment]);

  const [objectMap, setObjectMap] = useState<any>(null);

  const getIconName = (type: string): IconNameProps | null => {
    if (type === "GOU") {
      return "gou";
    }
    if (type === "GEN") {
      return "gen";
    }
    if (type === "SALDO") {
      return "saldo";
    }
    if (type === "POTR") {
      return "adderPotr";
    }
    if (type === "VETV") {
      return "vetv";
    }
    if (type === "ARCHM_GROUP") {
      return null; //maybe
    }
    if (type === "AREA_2") {
      return "ter";
    }
    if (type === "TER") {
      return "ter";
    }
    if (type === "AREA") {
      return "electric";
    }
    if (type === "CONSUMER_2") {
      return "consumption";
    }
    if (type === "N_BLOCK") {
      return "ego";
    }
    if (type === "N_GROUP") {
      return "ng";
    }
    if (type === "POWER_SYSTEM") {
      return "ps";
    }
    if (type === "GENERATOR") {
      return "rge";
    }
    if (type === "RGE") {
      return "rge";
    }
    if (type === "SECHEN") {
      return "sech";
    }
    if (type === "SECH") {
      return "sech";
    }
    if (type === "W_SUM") {
      return "integral";
    }
    return null;
  };

  const customCells = [
    {
      name: "name",
      render: (value: string, row: Row) => {
        const icon = getIconName(row.type);
        return (
          <NameContainer data-test="registry-types-table.name-cell">
            <NameIconContainer level={row.level}>{icon && <Icon width={18} name={icon} />}</NameIconContainer>
            <LabelContainer>{value}</LabelContainer>
          </NameContainer>
        );
      },
    },
    {
      name: "id",
      render: (_value: string | number, row: Row, _level: number, searchesButton: SearchButton[]) => {
        const getCellName = (value: string | number, level: number) => {
          const isFind = searchesButton?.some((el) => el.value === value);
          if (isFind) {
            return <mark>{value}</mark>;
          }
          return <Code level={level}>{value === "undefined" ? "" : value}</Code>;
        };
        return <CustomCell>{getCellName(String(row.id), row.level ?? 0)}</CustomCell>;
      },
    },
    {
      name: "viewMap",
      render: (_value: undefined, row: Row, _level: number) => {
        return (
          <MapContainer
            onClick={() => {
              const date = prepareDate(String(year), String(month), String(day));
              setObjectMap({ date, id: row.id, type: row.type, name: row.name, segment: selectedSegment });
            }}
            title="Карты ведения"
          >
            <ButtonMap width={20} name="viewPassword" />
          </MapContainer>
        );
      },
    },
  ];

  const ROOT_ID = "";
  const [loading, setLoading] = useState(false);
  const initExpandedRowIdsLeft = JSON.parse(localStorage.getItem("expandedRowIds-4_v5") as string) ?? [];
  const [expandedRowIdsLeft, setExpandedRowIdsLeft] = useState(initExpandedRowIdsLeft);
  const initExpandedRowIdsRight = JSON.parse(localStorage.getItem("expandedRowIds-2_v5") as string) ?? [];
  const [expandedRowIds, setExpandedRowIds] = useState(initExpandedRowIdsRight);
  const [isLoadDataDB, setIsLoadDataDB] = useState(false);
  //database
  const getInitData = () => {
    const dbPromise = idb.open("table-db", 1);
    dbPromise.onsuccess = () => {
      const db = dbPromise.result;
      const tx = db.transaction("userData", "readonly");
      const userData = tx.objectStore("userData");
      const tableData = userData.getAll();
      tableData.onsuccess = (query) => {
        // @ts-ignore
        const buf = query?.srcElement?.result?.find((el: any) => el.id === "2_v5")?.data ?? [];
        if (buf.length > 0) {
          setData(buf);
        } else {
          loadData().then();
        }
        setIsLoadDataDB(true);
      };

      tableData.onerror = () => {
        setIsLoadDataDB(true);
        setData([]);
      };
    };
  };
  //database
  const [data, setData] = useState<any[]>([]);
  useEffect(() => {
    getInitData();
  }, []);
  const [dataOriginal, setDataOriginal] = useState<any[]>([]);
  const [isSearchMode, setIsSearchMode] = useState(false);
  const [hasMoreElements, setHasMoreElements] = useState(false);
  const [filters, setFilters] = useState([]);

  const prepareDepartment = prepareDataTable(department) ?? [];

  const loadData = async () => {
    const dc = selectDc[0] ?? null;
    const rowIdsWithNotLoadedChilds = [ROOT_ID, ...expandedRowIds].filter((rowId) => data.findIndex((row) => row?.parentId === rowId) === -1);
    if (rowIdsWithNotLoadedChilds.length) {
      if (loading) return;
      setLoading(true);
      Promise.all(
        rowIdsWithNotLoadedChilds.map((rowId) => {
          return nsiStore.getTablePrepareNsiRight(dc, rowId, selectedSegment, prepareDate(String(year), String(month), String(day))).then((res: any) => {
            return res;
          });
        })
      )
        .then((loadedData) => {
          if (data.length === 0) {
            setDataOriginal(data.concat(...loadedData).map((el) => ({ ...el, parentId: el?.parentId ?? "" })));
          }
          setData(data.concat(...loadedData).map((el) => ({ ...el, parentId: el?.parentId ?? "" })));
          //database
          saveParams(
            "2_v5",
            data.concat(...loadedData).map((el) => ({ ...el, parentId: el?.parentId ?? "" }))
          );
          //database
          setLoading(false);
        })
        .catch(() => setLoading(false));
    }
  };

  const [filtersSrpg, setFiltersSrpg] = useState([]);
  const [filtersDC, setFiltersDC] = useState([]);

  const [isInit, setIsInit] = useState(false);

  useEffect(() => {
    if (isInit) {
      setExpandedRowIdsLeft([]);
      setExpandedRowIds([]);
      setData([]);
      deleteParams("2_v5");
      setFilters([]);
      setFiltersSrpg([]);
      setFiltersDC([]);
      localStorage.removeItem("expandedRowIds-4_v5");
      localStorage.removeItem("expandedRowIds-2_v5");
      localStorage.removeItem("expandedRowIds-3_v4");
    } else {
      setIsInit(true);
    }
  }, [isCenter, selectedSegment, day, month, year]);

  useEffect(() => {
    // Этот эффект сработает только при изменении selectDc,
    // очищая данные для правой таблицы и инициируя их перезагрузку.
    if (isInit) {
      setData([]);
      setDataOriginal([]); // Также очищаем резервную копию данных
      setExpandedRowIds([]);
      localStorage.removeItem("expandedRowIds-2_v5");
      deleteParams("2_v5");
      setFilters([]);
    }
  }, [selectDc]);

  useMemo(() => {
    if (!loading && !isSearchMode && selectedSegment !== "srpg" && selectedSegment && isLoadDataDB) {
      //&& prepareDepartment.length > 0
      loadData().then();
    }
  }, [expandedRowIds, selectDc, selectedSegment, isCenter]); //prepareDepartment

  const [initSorting4, setInitSorting4] = useState<any>(null); //[{ columnName: "name", direction: "asc" }]
  const [initSorting2, setInitSorting2] = useState<any>(null);
  const [initSorting3, setInitSorting3] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("4_v5").then((data: any) => {
      if (data) {
        setInitSorting4(data);
      } else {
        setInitSorting4([{ columnName: "name", direction: "asc" }]);
      }
    });
    tableStore.getSortParams("2_v5").then((data: any) => {
      if (data) {
        setInitSorting2(data);
      } else {
        setInitSorting2([{ columnName: "name", direction: "asc" }]);
      }
    });
    tableStore.getSortParams("3_v4").then((data: any) => {
      if (data) {
        setInitSorting3(data);
      } else {
        setInitSorting3([{ columnName: "name", direction: "asc" }]);
      }
    });
    return () => {
      setExpandedRowIdsLeft([]);
      setExpandedRowIds([]);
      setData([]);
      deleteParams("2_v5");
      setFilters([]);
      setFiltersSrpg([]);
      setFiltersDC([]);
      localStorage.removeItem("expandedRowIds-4_v5");
      localStorage.removeItem("expandedRowIds-2_v5");
      localStorage.removeItem("expandedRowIds-3_v4");
    };
  }, []);

  const initExpandedRowIdsSrpg = JSON.parse(localStorage.getItem("expandedRowIds-3_v4") as string) ?? [];

  const [expandedRowIdsSrpg, setExpandedRowIdsSrpg] = useState(initExpandedRowIdsSrpg);

  const selectedItems = selectedSegment ?? "goy";

  return (
    <TreeContainer>
      {objectMap && (
        <ModalMap
          objectMap={objectMap}
          onCancel={() => {
            setObjectMap(null);
          }}
        />
      )}
      <TreeItem>
        <Table
          columns={columnsDC}
          setColumns={setColumnsDC}
          tableData={prepareDepartment}
          originalTableData={departmentOriginal}
          isLoading={isLoadingLeftTable} //&& columnsDC.length === 0
          title=" Реестр ДЦ"
          tableKey="4_v5"
          defaultColumns={defaultColumns} //.filter((el: any) => el.name !== "viewMap")
          customCells={customCells}
          selectedMode="one"
          selected={selectDc}
          setSelected={setSelectDc}
          showChildrenWhenSearching={true}
          childrenKey="name"
          columnOrder={columnOrderDC}
          setColumnOrder={setColumnOrderDC}
          initSorting={initSorting4}
          filters={filtersDC}
          setFilters={setFiltersDC}
          expandedRowIds={expandedRowIdsLeft}
          setExpandedRowIds={(e: any) => {
            localStorage.setItem("expandedRowIds-4_v5", JSON.stringify(e));
            setExpandedRowIdsLeft(e);
          }}
          dataTest="registry-dc.table"
          dataTestRows="registry-dc-table.row"
        />
      </TreeItem>
      <TreeItem>
        {selectedSegment !== "srpg" && (
          <TableContainer>
            <Table
              columns={columns2}
              setColumns={setColumns2}
              tableData={data}
              isLoading={loading}
              title="Реестры"
              tableKey={"2_v5"}
              defaultColumns={defaultColumns}
              customCells={customCells}
              showChildrenWhenSearching={true}
              childrenKey="child"
              disabledSearches={["viewMap", "child"]}
              networkMode={true}
              filters={filters}
              isEnter={true}
              setFilters={setFilters}
              hasMoreElements={hasMoreElements}
              columnOrder={columnOrder2}
              setColumnOrder={setColumnOrder2}
              dataTest="registry-types.table"
              dataTestRows="registry-types-table.row"
              setSearchParams={async (e: any) => {
                setFilters(e);
                if (e.length > 0) {
                  setLoading(true);
                  setHasMoreElements(false);
                  const id = e.find((el: any) => el.columnName === "id")?.value ?? "";
                  const name = e.find((el: any) => el.columnName === "name")?.value ?? "";
                  setLoading(true);
                  nsiStore
                    .getTableNsiSearch(selectDc[0] ?? null, selectedSegment, id, name, prepareDate(String(year), String(month), String(day)))
                    .then(({ items, hasMoreElements }: any) => {
                      const prepareItems = items.map((el: any) => ({ ...el, parentId: el?.parentId ?? "" }));
                      setHasMoreElements(hasMoreElements);
                      setData(prepareItems);
                      // saveParams("2_v5", prepareItems);
                      setIsSearchMode(true);
                      const arr = prepareItems
                        .map((el: any) => {
                          if (el.isOpen) {
                            return el.tabId;
                          } else {
                            return null;
                          }
                        })
                        .filter((el: any) => el);
                      setExpandedRowIds(arr);
                      setLoading(false);
                      setIsSearchMode(false);
                    })
                    .catch(() => {
                      setLoading(false);
                      setIsSearchMode(false);
                    });
                } else {
                  setData(dataOriginal);
                  // saveParams("2_v5", dataOriginal);
                  setExpandedRowIds([]);
                  localStorage.removeItem("expandedRowIds-2_v5");
                  localStorage.removeItem("expandedRowIds-3_v4");
                  setHasMoreElements(false);
                }
              }}
              expandedRowIds={expandedRowIds}
              initSorting={initSorting2}
              setExpandedRowIds={(e: any) => {
                localStorage.setItem("expandedRowIds-2_v5", JSON.stringify(e));
                setExpandedRowIds(e);
              }}
              headerComponents={
                <>
                  <SegmentedPickerStyled
                    items={segmentedList}
                    selectedItems={selectedItems}
                    onChange={({ value }: any) => {
                      if (value !== selectedItems) {
                        setExpandedRowIdsLeft([]);
                        setExpandedRowIds([]);
                        localStorage.removeItem("expandedRowIds-4_v5");
                        localStorage.removeItem("expandedRowIds-2_v5");
                        localStorage.removeItem("expandedRowIds-3_v4");
                        setData([]);
                        deleteParams("2_v5");
                        history(
                          `?year=${year ?? DEFAULT_YEAR}&month=${month ?? DEFAULT_MONTH}&day=${day ?? DEFAULT_DAY}&selectedSegment=${value}&srpg=${
                            srpg ?? typesSRPG[0].value
                          }`
                        );
                      }
                    }}
                  />
                </>
              }
            />
          </TableContainer>
        )}
        {selectedSegment === "srpg" && (
          <SRPGContainer>
            <TableContainer>
              <Table
                columns={columns3}
                tableData={prepareDataTable(srpgs[String(srpg)])}
                isLoading={isLoadingRightTable}
                title="Реестры"
                tableKey={"3_v4"}
                setColumns={setColumns3}
                defaultColumns={defaultColumns}
                customCells={customCells}
                showChildrenWhenSearching={true}
                columnOrder={columnOrder3}
                setColumnOrder={setColumnOrder3}
                childrenKey="child"
                disabledSearches={["viewMap", "child"]}
                filters={filtersSrpg}
                initSorting={initSorting3}
                expandedRowIds={expandedRowIdsSrpg}
                setExpandedRowIds={setExpandedRowIdsSrpg}
                headerComponents={
                  <>
                    <SegmentedPickerStyled
                      items={segmentedList}
                      selectedItems={selectedItems}
                      onChange={({ value }: any) => {
                        if (value !== selectedItems) {
                          nsiStore.resetRight();
                          history(
                            `?year=${year ?? DEFAULT_YEAR}&month=${month ?? DEFAULT_MONTH}&day=${day ?? DEFAULT_DAY}&selectedSegment=${value}&srpg=${
                              srpg ?? typesSRPG[0].value
                            }`
                          );
                        }
                      }}
                    />
                    <ComboboxContainer>
                      <Combobox
                        items={typesSRPG}
                        selectedValue={srpg ?? typesSRPG[0].value}
                        onChange={({ value }: { value: string | number }) => {
                          history(
                            `?year=${year ?? DEFAULT_YEAR}&month=${month ?? DEFAULT_MONTH}&day=${day ?? DEFAULT_DAY}&selectedSegment=${selectedSegment}&srpg=${value}`
                          );
                        }}
                        placeholder="Тип СРПГ"
                        maxHeightList={320}
                      />
                    </ComboboxContainer>
                  </>
                }
              />
            </TableContainer>
          </SRPGContainer>
        )}
      </TreeItem>
    </TreeContainer>
  );
});
