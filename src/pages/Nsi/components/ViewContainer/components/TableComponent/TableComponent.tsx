import React, { FC, useState } from "react";
import { BodyCell, IconContainer, PaperStyled, Container } from "./TableComponent.style";
import { CustomTreeData, IntegratedFiltering, SearchState, TreeDataState } from "@devexpress/dx-react-grid";
import {
  Grid,
  VirtualTable,
  Table,
  TableHeaderRow,
  TableColumnResizing,
  DragDropProvider,
  TableColumnReordering,
  TableTreeColumn,
  Toolbar,
  SearchPanel,
} from "@devexpress/dx-react-grid-material-ui";
import { Icon } from "components/Icon";
import { Loader } from "components/Loader";

const TableCell = ({ ...restProps }) => {
  const { column, value } = restProps;

  return (
    // @ts-ignore
    <Table.Cell tabIndex={0} {...restProps}>
      <BodyCell
        // @ts-ignore
        hoverable={true}
      >
        {value ?? "-"}
      </BodyCell>
    </Table.Cell>
  );
};

const getChildRows = (row: any, rootRows: any) => {
  return row ? row.items : rootRows;
};

interface TableComponentsProps {
  tableData?: any[];
}

export const TableComponent: FC<TableComponentsProps> = (props) => {
  const { tableData = [] } = props;

  const defaultColumnWidths = [{ columnName: "name", align: "center" }];

  const defaultColumnOrder = ["name"];

  const [columns] = useState([{ name: "name", title: "Название" }]);

  const [tableColumnExtensions] = useState([{ columnName: "name", align: "center" }]);

  const [columnWidths, setColumnWidths] = useState(defaultColumnWidths);
  const [columnOrder, setColumnOrder] = useState(defaultColumnOrder);

  const [searchValue, setSearchValue] = useState("");

  const hasData = tableData.length > 0;
  return (
    <Container>
      <PaperStyled>
        <Grid rows={hasData ? tableData : []} columns={columns}>
          <SearchState value={searchValue} onValueChange={setSearchValue} />
          <IntegratedFiltering />
          <DragDropProvider />
          <TreeDataState />
          <CustomTreeData getChildRows={getChildRows} />
          <VirtualTable
            cellComponent={(props) => <TableCell {...props} />}
            // @ts-ignore
            columnExtensions={tableColumnExtensions}
            messages={{ noData: "Нет данных" }}
          />
          {/*<TableColumnResizing columnWidths={columnWidths} onColumnWidthsChange={(array: any[]) => setColumnWidths(array)} />*/}
          <TableColumnReordering order={columnOrder} onOrderChange={setColumnOrder} />
          {/*<TableHeaderRow />*/}
          <TableTreeColumn for="name" />
          <Toolbar />
          <SearchPanel />
        </Grid>
      </PaperStyled>
    </Container>
  );
};
