import { useEffect, useState } from "react";
import styled from "styled-components";
import { Modal } from "components/Modal";
import { observer } from "mobx-react";
import { Table } from "components/Table";
import { useStores } from "stores/useStore";
import { Button } from "components/Button";

export const ButtonReset = styled(Button)`
  width: 20px;
  height: 20px;
`;

export const ModalMap = observer((props: any) => {
  const { objectMap, onCancel } = props;
  const { nsiStore, tableStore } = useStores();
  const { mapData } = nsiStore;
  const defaultColumns: any = [
    { name: "name", title: "Название", width: 300, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
    { name: "kpo", title: "КПО", width: 300, isSearch: true, position: "center", isSort: "alphabet", searchLength: 3 },
    { name: "key", title: " ", width: 300, isSearch: true, position: "left", isSort: "alphabet", searchLength: 3 },
  ];
  const [columns, setColumns] = useState(defaultColumns);

  useEffect(() => {
    setColumns(defaultColumns);
  }, []);

  useEffect(() => {
    nsiStore.getModalMapData(objectMap);
  }, [objectMap.id]);

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("map_v2").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "priority", direction: "asc" }]);
      }
    });
    return () => {
      localStorage.removeItem("expandedRowIds-map");
    };
  }, []);

  const [modalHeight, setModalHeight] = useState(400);

  return (
    <Modal title={`Карты ведения - ${objectMap.name}`} onCancel={onCancel} width={700} height={400} setModalHeight={setModalHeight}>
      <Table
        isLoading={false}
        tableKey={`map_v2`}
        height={modalHeight - 120}
        tableHeight={modalHeight - 120}
        columns={columns.filter((el: any) => el.name !== "key")}
        setColumns={setColumns}
        defaultColumns={defaultColumns}
        tableData={mapData}
        initSorting={initSorting}
        headerComponents={
          <>
            <ButtonReset
              icon="reset"
              type="secondary"
              message="Сбросить сортировку по приоритету"
              onClick={() => {
                setInitSorting([{ columnName: "priority", direction: "asc" }]);
                tableStore.setSortParams("map_v2", [{ columnName: "priority", direction: "asc" }]);
              }}
            />
          </>
        }
      />
    </Modal>
  );
});
