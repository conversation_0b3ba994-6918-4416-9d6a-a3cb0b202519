import React, { FC, useEffect, useState } from "react";
import { Modal } from "components/Modal";
import { Table } from "components/Table";
import styled, { css } from "styled-components";
import { useStores } from "stores/useStore";
import { observer } from "mobx-react";
import { Button } from "components/Button";
import { prepareDataTable } from "../../../../../../utils";
import { EssNameContainer, LoaderContainer, ViewDate } from "../MultipleConnections";
import { Loader } from "../../../../../../components/Loader";

export const ModalStyled = styled(Modal)`
  width: 1650px;
  height: 800px;
`;

export const WordContainer = styled.div<{ width?: number }>`
  height: 20px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  //position: relative;
  ${(p) =>
    p.theme.width &&
    css`
      width: ${p.width}px;
    `}
`;

export const SearchContainer = styled.div`
  position: absolute;
  top: -6px;
  left: auto;
  right: -4px;
`;

export const SearchButton = styled(Button)`
  width: 20px;
  height: 20px;
  opacity: 0.5;
  transition: all 0.3s;
  &:hover {
    background-color: transparent;
    opacity: 1;
  }
`;

export const CloseButton = styled(Button)`
  width: 20px;
  height: 20px;
  opacity: 0.5;
  transition: all 0.3s;
  &:hover {
    background-color: transparent;
    opacity: 1;
  }
`;

export const TableContainer = styled.div`
  width: 100%;
  //height: 95%;
  height: 700px;
`;

export interface ViewDuplicateModalProps {
  onClose: any;
  uuid: any;
  onConfirm: any;
  date: any;
  // connections: any;
}

export const ViewDuplicateModal: FC<ViewDuplicateModalProps> = observer((props) => {
  const { onClose, uuid, onConfirm, date } = props;
  const { nsiStore, tableStore } = useStores();
  const { duplicateListById } = nsiStore;
  const [isLoadingMultiple, setIsLoadingMultiple] = useState(null);

  const searchNameById = async (row: any) => {
    const date = row.name;
    const essId = row.tabId;
    const essTypeCode = row.type.code;
    setIsLoadingMultiple(row.tabId);
    await nsiStore.duplicateSearchNameById(date, essId, essTypeCode).then(() => {
      setIsLoadingMultiple(null);
    });
  };

  const defaultColumns: any[] = [
    { name: "nameCk11", title: "Название объекта ОИК СК-11", width: 300 },
    { name: "uuid", title: "UID объекта ОИК СК-11", width: 250 },
    { name: "parents", title: "Родительский объект СРПГ", width: 400 },
    { name: "nameSRPG", title: "Название объекта СРПГ", width: 250 },
    { name: "type", title: "Тип Объекта СРПГ", width: 200 },
    { name: "actions", title: "Действия", width: 150 },
  ];
  const [columns, setColumns] = useState(defaultColumns);

  const prepareDate = () => {
    const day = date.day > 9 ? `${date.day}` : `0${date.day}`;
    const month = date.month > 9 ? `${date.month}` : `0${date.month}`;
    const year = date.year;
    return `${year}-${month}-${day}`;
  };

  useEffect(() => {
    const dt = prepareDate();
    nsiStore.getDuplicateById(uuid, dt);
    return () => {
      nsiStore.resetDuplicate();
    };
  }, []);

  const [unLinks, setUnLinks] = useState<any>([]);

  const customCells = [
    {
      name: "nameCk11",
      render: (value: any) => {
        return <div>{value ?? "-"}</div>;
      },
    },
    {
      name: "uuid",
      render: () => {
        return <>{uuid}</>;
      },
    },
    {
      name: "type",
      render: (value: any) => {
        return <>{value?.name}</>;
      },
    },
    // {
    //   name: "nameSRPG",
    //   render: (value: any, row: any) => {
    //     const width = columns.find((el) => el.name === "nameSRPG")?.width ?? 200;
    //     return (
    //       <WordContainer width={width}>
    //         {row.name}
    //         {isLoadingMultiple === row.tabId ? (
    //           <LoaderContainer>
    //             <Loader spinnerSize={16} />
    //           </LoaderContainer>
    //         ) : (
    //           <> {row.isDate && <ViewDate type="secondary" icon="viewPassword" widthIcon={16} onClick={() => searchNameById(row)} />}</>
    //         )}
    //       </WordContainer>
    //     );
    //   },
    {
      name: "nameSRPG",
      render: (value: any, row: any) => {
        return (
          <EssNameContainer isAfter={row?.isAfter}>
            {row.name}
            {isLoadingMultiple === row.tabId ? (
              <LoaderContainer>
                <Loader spinnerSize={16} />
              </LoaderContainer>
            ) : (
              <> {row.isDate && <ViewDate type="secondary" icon="viewPassword" widthIcon={16} onClick={() => searchNameById(row)} />}</>
            )}
          </EssNameContainer>
        );
      },
    },
    {
      name: "actions",
      render: (_: any, row: any) => {
        const isFind = unLinks.some((el: any) => el === row.tabId);
        if (row.isRemovePossible) {
          return (
            <>
              {isFind ? (
                <CloseButton
                  icon="add"
                  type="secondary"
                  widthIcon={24}
                  message="Привязать"
                  onClick={() => {
                    setUnLinks((prev: any) => {
                      return prev.filter((el: any) => el !== row.tabId);
                    });
                  }}
                />
              ) : (
                <CloseButton
                  icon="close"
                  type="secondary"
                  widthIcon={12}
                  message="Отвязать"
                  onClick={() => {
                    setUnLinks((prev: any) => {
                      return [...prev, row.tabId];
                    });
                  }}
                />
              )}
            </>
          );
        }
      },
    },
  ];

  const isDisabled = unLinks.length === 0;

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("duplicate").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "id", direction: "asc" }]);
      }
    });
  }, []);

  const [modalHeight, setModalHeight] = useState(800);

  return (
    <ModalStyled
      width={1650}
      height={800}
      setModalHeight={setModalHeight}
      isOverLay
      onCancel={() => onClose()}
      title="Просмотр связи объекта ОИК СК-11 с объектом СРПГ"
      onConfirm={() => {
        const finalArray = unLinks.map((el: any) => {
          const type = duplicateListById.find((item: any) => String(item.tabId) === String(el))?.type?.code ?? null;
          return { id11: null, id: el, type };
        });
        const isAll = prepareDataTable(duplicateListById).length === finalArray.length;
        onConfirm(finalArray, isAll);
      }}
      confirmText="Сохранить"
      cancelText="Отменить"
      isDisabledConfirm={isDisabled}
      // isDisabledCancel={isDisabled}
    >
      <Table
        columns={columns}
        setColumns={setColumns}
        defaultColumns={defaultColumns}
        tableData={prepareDataTable(duplicateListById)}
        customCells={customCells}
        disabledSearches={["actions"]}
        tableKey={`duplicate`}
        tableHeight={modalHeight - 160}
        initSorting={initSorting}
      />
    </ModalStyled>
  );
});
