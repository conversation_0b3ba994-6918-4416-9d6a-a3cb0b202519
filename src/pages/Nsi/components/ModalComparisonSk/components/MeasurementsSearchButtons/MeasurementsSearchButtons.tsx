import React, { FC } from "react";
import { observer } from "mobx-react";
import { useStores } from "stores/useStore";
import { ButtonStyled } from "../../ModalComparisonSk.style";
import { IconNameProps } from "components/Icon/Icon.type";

interface MeasurementsSearchButtonsProps {
  connectionObjects: any[];
  searchForMeasurements: () => void;
  getIconMeasurements: (status: "DONE" | "CREATED" | "FAILED") => IconNameProps;
  getIconSize: (status: "DONE" | "CREATED" | "FAILED") => number;
}

export const MeasurementsSearchButtons: FC<MeasurementsSearchButtonsProps> = observer(
  ({ connectionObjects, searchForMeasurements, getIconMeasurements, getIconSize }) => {
    const { nsiStore, liveTimerStore } = useStores();
    const { statusMeasurements, lastTaskMeasurements } = nsiStore;

    return (
      <>
        <ButtonStyled
          icon={getIconMeasurements(statusMeasurements.status)}
          widthIcon={getIconSize(statusMeasurements.status)}
          type="secondary"
          title="Поиск измерений в ОИК СК-11"
          onClick={() => {
            searchForMeasurements();
          }}
          disabled={connectionObjects.length !== 0}
          message={statusMeasurements.error}
          dataTest="comparison-sk-11-header.search-for-measurements-button"
          dataTestIcon="comparison-sk-11-header.search-for-measurements-icon"
        />
        <ButtonStyled
          type="secondary"
          title="Экспорт XML"
          onClick={() => {
            liveTimerStore.getTimeServer().then(({ dateTime }: any) => {
              const date = new Date(dateTime);
              nsiStore.exportXMLMeasurements(date);
            });
          }}
          disabled={connectionObjects.length !== 0 || !!!lastTaskMeasurements || statusMeasurements?.status === "CREATED"}
          dataTest="comparison-sk-11-header.export-xml-measurements-button"
        />
      </>
    );
  }
);
