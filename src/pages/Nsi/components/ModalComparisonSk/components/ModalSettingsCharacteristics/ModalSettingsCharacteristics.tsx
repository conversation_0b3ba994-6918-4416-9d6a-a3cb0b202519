import React, { FC, useEffect, useState } from "react";
import styled, { css } from "styled-components";
import { Modal } from "components/Modal";
import { useStores } from "../../../../../../stores/useStore";
import { observer } from "mobx-react";
import { Table } from "components/Table";
import { Button } from "../../../../../../components/Button";
import { SegmentedPicker } from "components/SegmentedPicker";
import { Icon } from "../../../../../../components/Icon";
import { ModalEdit } from "./components/ModalEdit";
import { toJS } from "mobx";
import { Checkbox } from "components/Checkbox";
import { prepareDataTable } from "../../../../../../utils";

export const ModalStyled = styled(Modal)`
  width: 900px;
  height: 700px;
`;

export interface ModalSettingsCharacteristicsProps {
  onClose?: any;
  paramType?: any;
  isEngineer?: any;
  objectCharacter: { type: any; id: any; name: any; isEditSubs: any };
  dataTestContainer?: string;
}

export const TableContainer = styled.div`
  height: 640px;
`;

export const Buttons = styled.div`
  margin-left: auto;
  margin-right: 10px;
  display: flex;
`;

export const ButtonStyled = styled(Button)`
  margin: 0 10px;
`;

export const Code = styled.div<{ level?: number }>`
  //margin-left: 10px;
  //font-size: 14px;
  //font-weight: bold;
  padding: 1px 2px; //2 4
  border-radius: 4px;
  background-color: ${(props) => props.theme.greenActiveSupport};
  color: ${(props) => props.theme.white};
  margin: 0 10px;
`;

export const Name = styled.div`
  width: 100%;
  display: flex;
  position: relative;
  color: ${(p) => p.theme.textColor};
`;

export const IconStyled = styled.div`
  left: auto;
  right: 10px;
  top: 0;
  position: absolute;
  opacity: 0.7;
  transition: all 0.3s;
  &:hover {
    opacity: 1;
  }
`;

export const CheckboxContainer = styled.div`
  margin: 0 18px;
`;

export const ModalSettingsCharacteristics: FC<ModalSettingsCharacteristicsProps> = observer((props) => {
  const { onClose, objectCharacter, isEngineer, dataTestContainer } = props;
  const { nsiStore, tableStore } = useStores();
  const { objectModalSettingsCharacteristics, isLoadingCharacters, isChangeCharacteristics } = nsiStore;

  const subParams = objectModalSettingsCharacteristics.subParams;

  const [selectedButton, setSelectedButton] = useState<any>("pbr");

  useEffect(() => {
    nsiStore.initModalSettingsCharacteristics(objectCharacter.type, objectCharacter.id);
  }, [objectCharacter.id]);

  const defaultColumns = [{ name: "name", width: 700, title: "Название", isSearch: true }];

  const [objectEdit, setObjectEdit] = useState<any>(null);
  // const [selected, setSelected] = useState<any>([]); // TODO complete

  const customCells = [
    {
      name: "name",
      render: (value: any, row: any) => {
        const selectedSub = subParams?.find((el: any) => el?.code?.toUpperCase() === row?.tabId?.toUpperCase())?.selected ?? null;
        return (
          <Name>
            {isEngineer && (
              <CheckboxContainer>
                <Checkbox status={row?.isActive} disabled />
              </CheckboxContainer>
            )}
            {value}
            <Code>{row.opamCode}</Code>
            {selectedSub && <> ( {selectedSub?.name} )</>}
          </Name>
        );
      },
    },
  ];

  const [columns, setColumns] = useState(defaultColumns);

  const saveCharacteristics = async () => {
    await nsiStore.saveCharacteristics(objectCharacter.type, objectCharacter.id).then((isComplete: boolean) => {
      if (isComplete) {
        nsiStore.initModalSettingsCharacteristics(objectCharacter.type, objectCharacter.id);
      }
    });
  };

  const segmentedList = [
    { value: "pbr", label: "ПБР" },
    { value: "pdg", label: "ПДГ" },
    { value: "per", label: "ПЭР" },
    { value: "ppbr", label: "ППБР" },
  ];

  const selected = objectModalSettingsCharacteristics?.params[selectedButton].filter((el: any) => el.isUsed).map((el: any) => el.tabId) ?? [];

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("19").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "name", direction: "asc" }]);
      }
    });
  }, []);

  const [modalHeight, setModalHeight] = useState(700);

  return (
    <ModalStyled
      width={900}
      height={700}
      setModalHeight={setModalHeight}
      title={`${objectModalSettingsCharacteristics?.name} (${objectModalSettingsCharacteristics?.code}) - ${objectCharacter.name}` ?? ""}
      isOverLay
      onCancel={onClose}
      isLoading={isLoadingCharacters}
      dataTestContainer={dataTestContainer}
    >
      {objectEdit && (
        <ModalEdit
          objectEdit={objectEdit}
          onCancel={() => setObjectEdit(null)}
          subs={subParams}
          onConfirm={async (selectedSub: any) => {
            const subId = selectedSub;
            const id = objectModalSettingsCharacteristics?.id;
            const type = objectModalSettingsCharacteristics?.code;
            await nsiStore.saveSub(subId, type, id).then((isComplete: boolean) => {
              if (isComplete) {
                setObjectEdit(null);
                nsiStore.initModalSettingsCharacteristics(objectCharacter.type, objectCharacter.id);
              }
            });
          }}
        />
      )}
      <Table
        columns={columns}
        setColumns={setColumns}
        tableData={objectModalSettingsCharacteristics?.params[selectedButton] ? prepareDataTable(objectModalSettingsCharacteristics?.params[selectedButton]) : []}
        defaultColumns={defaultColumns}
        initSorting={initSorting}
        tableKey={19}
        selected={selected}
        setSelected={(value: any) => {
          const rows = prepareDataTable(objectModalSettingsCharacteristics?.params[selectedButton]);
          const isChange = rows.some((el) => !el.isDisableChecked);
          if (isChange) {
            nsiStore.changeCharacteristics(selectedButton, value);
          }
        }}
        selectedMode={isEngineer ? null : "many"}
        customCells={customCells}
        tableHeight={modalHeight - 110}
        heightContainer={modalHeight - 110}
        dataTestDefaultCells="comparison-sk-params-modal.name-param-cells"
        dataTestCheckboxCells="comparison-sk-params-modal.checkbox-cells"
        dataTestRows="comparison-sk-params-modal.rows"
        headerComponents={
          <>
            <SegmentedPicker
              items={segmentedList}
              selectedItems={selectedButton}
              onChange={({ value }: any) => {
                setSelectedButton(value);
              }}
            />
            {!isEngineer && (
              <Buttons>
                <ButtonStyled title="Сохранить" onClick={() => saveCharacteristics()} disabled={!isChangeCharacteristics} dataTest="comparison-sk-params-modal.save-button" />
                <ButtonStyled
                  title="Отменить"
                  onClick={() => {
                    nsiStore.resetCharacteristics();
                    nsiStore.initModalSettingsCharacteristics(objectCharacter.type, objectCharacter.id);
                  }}
                  type="secondary"
                  disabled={!isChangeCharacteristics}
                />
              </Buttons>
            )}
          </>
        }
      />
    </ModalStyled>
  );
});
