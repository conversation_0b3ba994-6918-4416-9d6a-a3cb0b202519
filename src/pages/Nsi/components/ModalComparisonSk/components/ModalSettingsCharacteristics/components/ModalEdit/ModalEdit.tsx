import React, { FC, useEffect, useState } from "react";
import { Modal } from "components/Modal";
import { observer } from "mobx-react";
import { Combobox } from "components/Combobox";
import { useStores } from "stores/useStore";
import styled from "styled-components";

export const ComboboxContainer = styled.div`
  margin-top: 50px;
`;

export interface ModalEditProps {
  onCancel?: any;
  objectEdit?: any;
  subs?: any;
  onConfirm?: any;
}

export const ModalEdit: FC<ModalEditProps> = observer((props) => {
  const { onCancel, objectEdit, subs, onConfirm } = props;
  const [selectedSub, setSelectedSub] = useState<any>(objectEdit.selectedSub ? objectEdit.selectedSub : null);

  const subsParams = subs.find((el: any) => el.code.toUpperCase() === objectEdit.tabId.toUpperCase())?.subs?.map((el: any) => ({ value: el.id, label: el.name })) ?? []; //id name

  return (
    <Modal
      isOverLay
      onCancel={onCancel}
      title={`Добавление типа характеристики ${objectEdit.name}`}
      confirmText="Применить"
      cancelText="Отменить"
      onConfirm={() => onConfirm(selectedSub)}
      isDisabledConfirm={selectedSub === objectEdit.selectedSub}
    >
      <ComboboxContainer>
        <Combobox
          items={[{ value: "NO", label: "Отсутствует" }, ...subsParams]}
          width={440}
          selectedValue={selectedSub ? selectedSub : "NO"}
          onChange={({ value }) => {
            setSelectedSub(value !== "NO" ? value : null);
          }}
        />
      </ComboboxContainer>
    </Modal>
  );
});
