import React, { FC, useEffect, useState } from "react";
import styled, { css } from "styled-components";
import { Modal } from "components/Modal";
import { useStores } from "stores/useStore";
import { observer } from "mobx-react";
import { Table } from "components/Table";
import { CloseButton } from "../ViewDuplicateModal";
import { Icon } from "components/Icon";
import { prepareDataTable } from "utils";
import { prepareDate } from "helpers/DateUtils";
import { Loader } from "components/Loader";
import { Button } from "components/Button";
import { deleteParams } from "../../../../../../utils/indexDB";

export const ModalStyled = styled(Modal)`
  width: 1650px;
  height: 800px;
`;

export const TableContainer = styled.div`
  width: 100%;
  height: 700px;
`;

export const GroupCell = styled.div<{ color?: any }>`
  width: 20px;
  height: 20px;
  min-width: 20px;
  min-height: 20px;
  border-radius: 50%;
  border: solid 1px ${(p) => p.theme.gray};
  ${(p) =>
    p.color &&
    css`
      background-color: ${p.color};
    `}
`;

export const BottomLeftPanel = styled.div`
  position: absolute;
  //bottom: 32px;
  bottom: -38px;
  left: 40px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 4px;
  user-select: none;
  transition: all 0.3s;
  text-align: center;
  z-index: 9999;
`;

export const ButtonPanel = styled.div`
  margin: 0 5px;
  width: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  color: ${(p) => p.theme.primaryColor};
  &:hover {
    background-color: ${(p) => p.theme.lightGray};
  }
  &:active {
    background-color: ${(p) => p.theme.gray};
  }
`;

export const ButtonPanelLabel = styled.div`
  margin-left: 4px;
`;

export const Excel = styled(ButtonPanel)`
  color: ${(p) => p.theme.colorExcel};
`;

export const StatusTry = styled.div`
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const EssNameContainer = styled.div<{ isAfter?: any }>`
  display: flex;
  align-items: center;
  justify-content: center;
  ${(p) =>
    p.isAfter === true &&
    css`
      color: ${(p) => p.theme.greenActiveSupport};
    `}
  ${(p) =>
    p.isAfter === false &&
    css`
      color: ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.isAfter === null &&
    css`
      color: ${(p) => p.theme.textColor};
    `}
`;

export const ViewDate = styled(Button)`
  width: 20px;
  height: 20px;
  margin-left: 4px;
`;

export const LoaderContainer = styled.div`
  width: 20px;
  height: 20px;
  margin-left: 4px;
`;

export interface MultipleConnectionsProps {
  onCancel: any;
  onConfirm: any;
  isEngineer: any;
  date: any;
}

export const MultipleConnections: FC<MultipleConnectionsProps> = observer((props) => {
  const { onCancel, onConfirm, isEngineer, date } = props;

  const finalDate = prepareDate(date.year, date.month, date.day);

  const { nsiStore, tableStore } = useStores();
  const { multipleConnections, isLoadingMultipleConnections } = nsiStore;

  useEffect(() => {
    nsiStore.getMultipleConnections(finalDate);
    return () => {
      nsiStore.stopMultipleConnections();
    };
  }, []);

  const defaultColumns: any[] = isEngineer
    ? [
        { name: "group", title: " ", width: 40 },
        { name: "essParents", title: "Родительский объект СРПГ", width: 260 },
        { name: "essName", title: "Название объекта СРПГ", width: 230 },
        { name: "type", title: "Тип объекта СРПГ ", width: 190 },
        { name: "ck11Parents", title: "Родительский объект ОИК СК-11 ", width: 290 },
        { name: "ck11Name", title: "Название объекта ОИК СК-11 ", width: 260 },
        { name: "uuid", title: "UID", width: 160 },
      ]
    : [
        { name: "group", title: " ", width: 40 },
        { name: "essParents", title: "Родительский объект СРПГ", width: 260 },
        { name: "essName", title: "Название объекта СРПГ", width: 230 },
        { name: "type", title: "Тип объекта СРПГ ", width: 190 },
        { name: "ck11Parents", title: "Родительский объект ОИК СК-11 ", width: 290 },
        { name: "ck11Name", title: "Название объекта ОИК СК-11 ", width: 260 },
        { name: "uuid", title: "UID", width: 160 },
        { name: "actions", title: "Действия", width: 120 },
      ];

  const [columns, setColumns] = useState(defaultColumns);

  const [unLinks, setUnLinks] = useState<any>([]);

  const [isLoadingMultiple, setIsLoadingMultiple] = useState(null);

  const searchNameById = async (row: any) => {
    const date = row.essName;
    const essId = row.id; //tabId
    const essTypeCode = row.typeCode;
    setIsLoadingMultiple(row.tabId);
    await nsiStore.multipleSearchNameById(date, essId, essTypeCode, row.tabId).then(() => {
      setIsLoadingMultiple(null);
    });
  };

  const customCells: any[] = [
    {
      name: "actions",
      render: (_: any, row: any) => {
        const isFind = unLinks.some((el: any) => el === row.tabId);
        if (row.isRemovePossible && !isEngineer) {
          return (
            <>
              {isFind ? (
                <CloseButton
                  icon="add"
                  type="secondary"
                  widthIcon={24}
                  message="Привязать"
                  onClick={() => {
                    setUnLinks((prev: any) => {
                      return prev.filter((el: any) => el !== row.tabId);
                    });
                  }}
                />
              ) : (
                <CloseButton
                  icon="close"
                  type="secondary"
                  widthIcon={12}
                  message="Отвязать"
                  onClick={() => {
                    setUnLinks((prev: any) => {
                      return [...prev, row.tabId];
                    });
                  }}
                />
              )}
            </>
          );
        }
      },
    },
    {
      name: "group",
      render: (_: any, row: any) => {
        return <GroupCell color={row.color} />;
      },
    },
    {
      name: "essName",
      render: (value: any, row: any) => {
        return (
          <EssNameContainer isAfter={row?.isAfter}>
            {value}
            {isLoadingMultiple === row.tabId ? (
              <LoaderContainer>
                <Loader spinnerSize={16} />
              </LoaderContainer>
            ) : (
              <> {row.isDate && <ViewDate type="secondary" icon="viewPassword" widthIcon={16} onClick={() => searchNameById(row)} />}</>
            )}
          </EssNameContainer>
        );
      },
    },
  ];

  const isDisabled = unLinks.length === 0;

  const [exportTable, setExportTable] = useState<any>(() => {});

  const customizeCell = (cell: any, row: any, column: any) => {
    if (row?.isAfter === true && column.name === "essName") {
      cell.font = { color: { argb: "34B53A" } };
    }
    if (row?.isAfter === false && column.name === "essName") {
      cell.font = { color: { argb: "FF3A29" } };
    }
    if (column.name === "group") {
      cell.value = "";
      const color = row.color
        .split("")
        .filter((el: any) => el !== "#")
        .join("");
      cell.fill = { type: "pattern", pattern: "solid", fgColor: { argb: color } };
    }
  };

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("1111").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "name", direction: "asc" }]);
      }
    });
  }, []);

  const [modalHeight, setModalHeight] = useState(800);

  return (
    <ModalStyled
      width={1650}
      height={800}
      setModalHeight={setModalHeight}
      onCancel={() => onCancel()}
      title="Множественные связи"
      isOverLay
      onConfirm={
        isEngineer
          ? undefined
          : () => {
              const finalArray = unLinks.map((el: any) => {
                const type = multipleConnections.find((item: any) => item.tabId === el)?.typeCode ?? null;
                const id = multipleConnections.find((item: any) => item.tabId === el)?.id ?? null;
                // return { id11: null, id: el, type };
                return { id11: null, id, type };
              });
              onConfirm(finalArray);
            }
      }
      confirmText="Сохранить"
      cancelText="Отменить"
      isDisabledConfirm={isDisabled}
    >
      <Table
        disabledSearches={["group", "actions"]}
        isLoading={isLoadingMultipleConnections}
        columns={columns}
        setColumns={setColumns}
        defaultColumns={defaultColumns}
        tableData={prepareDataTable(multipleConnections)}
        customCells={customCells}
        tableHeight={modalHeight - 170}
        heightContainer={modalHeight - 170}
        tableKey={`1111`}
        titleXLS="Множественные связи"
        initSorting={initSorting}
        customizeCellExport={customizeCell}
        setExportTable={(prev: any) => {
          setExportTable(prev);
        }}
      />
      <BottomLeftPanel key={1}>
        <Excel
          onClick={() => {
            exportTable?.exportGrid();
          }}
        >
          <Icon width={18} name="excel" />
          <ButtonPanelLabel>Выгрузка в EXCEL</ButtonPanelLabel>
        </Excel>
        <ButtonPanel
          onClick={() => {
            nsiStore.multipleConnectionsTry(finalDate);
          }}
        >
          <StatusTry>{nsiStore.infoMultipleConnections?.status === "CREATED" ? <Loader spinnerSize={18} /> : <Icon width={18} name="sync" />}</StatusTry>
          <ButtonPanelLabel>Проверить корректность</ButtonPanelLabel>
        </ButtonPanel>
      </BottomLeftPanel>
    </ModalStyled>
  );
});
