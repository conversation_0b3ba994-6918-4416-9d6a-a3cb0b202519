import styled, { css } from "styled-components";
import { Button } from "components/Button";
import { Modal } from "components/Modal";

export const Container = styled.div<{ height?: number }>`
  width: 100%;
  height: ${(p) => p.height}px;
  display: flex;
  flex-direction: column;
`;

export const ErrorMessage = styled.div`
  color: ${(p) => p.theme.redActiveSupport};
`;

export const TableContainer = styled.div<{ isError?: boolean }>`
  height: 50%;
  border: solid 1px ${(p) => p.theme.lightGray};
  &:last-child {
    border-top: solid 1px transparent;
  }
  ${(p) =>
    p.isError &&
    css`
      border: solid 1px ${(p) => p.theme.redActiveSupport};
    `}
`;

export const ModalStyled = styled(Modal)`
  width: 1200px;
  height: 800px;
`;

export const EssContainer = styled.div`
  width: 100%;
  height: 80px;
  border: solid 1px ${(p) => p.theme.lightGray}
  border-radius: 4px;
  overflow: auto;
`;

export const Row = styled.div`
  border: solid 1px ${(p) => p.theme.lightGray};
  border-top: none;
  height: 20px;
  display: flex;
  align-items: center;
  padding: 0 10px;
`;

export const Closer = styled(Button)`
  width: 20px;
  height: 20px;
  padding-bottom: 5px;
  opacity: 0.7;
  &:hover {
    background-color: transparent;
    opacity: 1;
  }
`;
