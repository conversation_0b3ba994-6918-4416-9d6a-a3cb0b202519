import React, { FC, useEffect, useState } from "react";
import { observer } from "mobx-react";
import { useStores } from "stores/useStore";
import { Table } from "components/Table";
import { flatArray } from "../../ModalComparisonSk";
import { Container, ErrorMessage, TableContainer, ModalStyled, EssContainer, Row, Closer } from "./ModalOverWrite.style";
import { ModalOverWriteProps } from "./ModalOverWrite.constants";
import { prepareDataTable } from "../../../../../../utils";

export const ModalOverWrite: FC<ModalOverWriteProps> = observer((props) => {
  const { connectionObjects, registry, type, dateObject, paramsFilter, prevParams, setSelect, setConnectionObjects, onCancel } = props;
  const { nsiStore, tableStore } = useStores();
  const { infoDuplicates, objectPakEss } = nsiStore;

  const [canceled, setCanceled] = useState<any[]>([]);

  const [dataTop, setDataTop] = useState<any[]>([]);

  const [isError, setIsError] = useState(false);

  useEffect(() => {
    if (infoDuplicates?.duplicatesFromRequest) {
      let result: any[] = [];
      infoDuplicates?.duplicatesFromRequest.map((el: any) => {
        if (el?.ess.length > 0) {
          el?.ess?.map((item: any) => {
            result.push({ type: item.type, id: item.id, uid: el.id11, name: item.name ?? "" });
          });
        }
      });
      setDataTop(result);
    }
  }, [infoDuplicates?.duplicatesFromRequest]);

  const onSave = () => {
    setIsError(false);
    const uniqDataTop = [...new Set(dataTop.map((el) => el.uid))];
    const isValid = uniqDataTop.length === dataTop.length;

    if (isValid) {
      const objectPakEssFlat = flatArray(objectPakEss);
      const finalConnectionsObjects = connectionObjects
        .map((el: any) => {
          const type = objectPakEssFlat.find((item) => item.id === el.id)?.type ?? null;
          return { ...el, type };
        })
        .filter((el: any) => {
          return !canceled.some((item) => item === el.id);
        });
      if (finalConnectionsObjects.length > 0) {
        const operation = canceled.length === infoDuplicates?.duplicatesFromRequest ? null : "REPLACE";
        nsiStore.saveModalComparison(finalConnectionsObjects, registry, type, operation).then(() => {
          nsiStore.initModalComparison({
            day: dateObject.day,
            month: dateObject.month,
            year: dateObject.year,
            filter: paramsFilter,
            registry,
            type: prevParams.type,
          });
          setSelect([]);
          setConnectionObjects([]);
          onCancel();
        });
      } else {
        onCancel();
      }
      setConnectionObjects(finalConnectionsObjects);
    } else {
      setIsError(true);
    }
  };

  const defaultColumns = [
    { name: "name", width: 600, title: "Название объекта", position: "left" },
    { name: "uid", width: 400, title: "UID" },
    { name: "action", width: 115, title: "Действие" },
  ];

  const defaultColumnsBottom = [
    { name: "name", width: 450, title: "Наименование объекта" },
    { name: "id11", width: 300, title: "UID" },
    { name: "ess", width: 450, title: "Объекты" },
  ];

  const [columns, setColumns] = useState(defaultColumns);
  const [columnsBottom, setColumnsBottom] = useState(defaultColumnsBottom);

  const customCell: any[] = [
    {
      name: "name",
      render: (value: any, row: any) => {
        return (
          <>
            {value}-{row.type}-{row.id}
          </>
        );
      },
    },
    {
      name: "action",
      render: (value: any, row: any) => {
        return (
          <Closer
            icon="close"
            type="secondary"
            onClick={() => {
              setCanceled((prev) => [...prev, row.id]);
              setDataTop((prev) => {
                return prev.filter((el) => el.id !== row.id);
              });
              setIsError(false);
            }}
          />
        );
      },
    },
  ];

  const customCellBottom: any[] = [
    {
      name: "ess",
      render: (value: any, row: any) => {
        return (
          <EssContainer>
            {value.map((el: any, index: number) => {
              return (
                <Row key={`ess-row-${index}`}>
                  {el.name}-{el.type}-{el.id}
                </Row>
              );
            })}
          </EssContainer>
        );
      },
    },
  ];

  const [initSorting, setInitSorting] = useState<any>(null);
  const [initSorting2, setInitSorting2] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("40").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "name", direction: "asc" }]);
      }
    });
    tableStore.getSortParams("40_v2").then((data: any) => {
      if (data) {
        setInitSorting2(data);
      } else {
        setInitSorting2([{ columnName: "name", direction: "asc" }]);
      }
    });
    return () => {
      localStorage.removeItem("expandedRowIds-40");
    };
  }, []);

  const [modalHeight, setModalHeight] = useState(800);

  return (
    <ModalStyled
      isOverLay
      width={1200}
      height={800}
      setModalHeight={setModalHeight}
      title={`Дублирование`}
      onCancel={() => {
        onCancel();
      }}
      onConfirm={() => {
        onSave();
      }}
      confirmText="Убрать дубли и сохранить"
      cancelText="Отменить"
      widthButtons={200}
    >
      <Container height={modalHeight - 100}>
        <TableContainer isError={isError}>
          <Table
            columns={columns}
            setColumns={setColumns}
            tableData={prepareDataTable(dataTop)}
            isLoading={false}
            height={330}
            tableKey={40}
            defaultColumns={defaultColumns}
            customCells={customCell}
            initSorting={initSorting}
            headerComponents={isError ? <ErrorMessage>Должны быть только уникальные элементы</ErrorMessage> : <></>}
          />
        </TableContainer>
        <TableContainer>
          <Table
            columns={columnsBottom}
            setColumns={setColumnsBottom}
            tableData={infoDuplicates?.duplicates ? prepareDataTable(infoDuplicates?.duplicates) : []}
            isLoading={false}
            height={330}
            tableKey={`40_v2`}
            defaultColumns={defaultColumnsBottom}
            rowHeight={80}
            customCells={customCellBottom}
            initSorting={initSorting2}
          />
        </TableContainer>
      </Container>
    </ModalStyled>
  );
});
