import React, { FC, useEffect, useMemo, useRef, useState } from "react";
import { FilterDatePickerStyled } from "../../Nsi.style";
import { Combobox } from "components/Combobox";
import { Table } from "components/Table";
import { isModeCenter } from "utils/getMode";
import { prepareDateTable } from "helpers/DateUtils";
import { useStores } from "stores/useStore";
import { observer } from "mobx-react";
import { Loader } from "components/Loader";
import { useOnClickOutside } from "hooks/useOnClickOutside";
import Tippy from "@tippyjs/react/headless";
import { Icon } from "components/Icon";
import { ModalSettingsCharacteristics } from "./components/ModalSettingsCharacteristics";
import { AccessControl } from "components/AccessControl";
import { MeasurementsSearchButtons } from "./components/MeasurementsSearchButtons";
import { ModalOverWrite } from "./components/ModalOverWrite";
import { ViewDuplicateModal } from "./components/ViewDuplicateModal";
import { MultipleConnections } from "./components/MultipleConnections";
import {
  TypeContainer,
  Action,
  ProtocolContainer,
  TableContainer,
  NameContainer,
  SearchUID,
  ModalStyled,
  ActionBar,
  ButtonStyled,
  Circle,
  CopyCell,
  TooltipContent,
  RowTooltipContainer,
  BodyTooltipContent,
  ComboboxStyled,
  Content,
  Left,
  Right,
  Connect,
  SettingUpCharacteristics,
  NameLabel,
  ButtonsGroupStyled,
  HeaderTooltipContent,
  RightActionMenu,
  CopyContent,
  ButtonsGroupStyledSk11,
  StatusGenerics,
  LoaderContainer,
  LabelLoader,
  ButtonLoadSK11,
  IconInformation,
  ContentSK11,
  StatusSK11,
  IconContainer,
  UIDLabel,
} from "./ModalComparisonSk.style";
import { Tooltip } from "components/Tooltip";
import styled from "styled-components";
import { deleteParams, idb, saveParams } from "utils/indexDB";

interface ModalСomparisonSkProps {
  isCenter?: boolean;
}

export const getIconMeasurements = (status: "DONE" | "CREATED" | "FAILED") => {
  if (status === "DONE") {
    return "success";
  }
  if (status === "CREATED") {
    return "wait";
  }
  if (status === "FAILED") {
    return "error";
  }
};

export const getIconSize = (status: "DONE" | "CREATED" | "FAILED") => {
  if (status === "DONE") {
    return 12;
  }
  if (status === "CREATED") {
    return 18;
  }
  if (status === "FAILED") {
    return 18;
  }
};

export const flatArray = (array: any[]) => {
  let result: any[] = [];
  array.map((el) => {
    if (el.childs && el.childs.length > 0) {
      const childs = flatArray(el.childs);
      childs.map((item) => {
        result.push(item);
      });
    }
    result.push(el);
  });
  return result;
};

export const OpenAllIcon = styled.div`
  //position: absolute;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  //transition: all 0.3s;
  border-radius: 50%;
  background-color: ${(p) => p.theme.orangeActiveSupport};
  width: 18px;
  height: 18px;
  max-width: 18px;
  max-height: 18px;
  min-width: 18px;
  min-height: 18px;
`;

const ROOT_ID = "";

export const getStatusSK11 = (statusSK11: any) => {
  let time = "";
  if (statusSK11.activeLoadedAt && statusSK11.activeModelVersion) {
    time = `Дата последней загрузки : ${prepareDateTable(statusSK11.activeLoadedAt)}. Номер версии модели : ${statusSK11.activeModelVersion}`;
  } else {
    time = "Данные отсутствуют .";
  }
  if (statusSK11.status === "ACTIVE") {
    return `Объекты загружены . ${time}`;
  }
  if (statusSK11.status === "CREATED") {
    return `Объекты загружаются . ${time}`;
  }
  if (statusSK11.status === "FAILED") {
    return `Ошибка загрузки объектов . ${time}`;
  }
  if (statusSK11.status === "TO_DELETE") {
    return `Нет активных версий объектов . ${time}`;
  }
  return `Нет активных версий объектов . ${time}`;
};

export const getStatusDistribution = (status: string) => {
  if (status === "DONE") {
    return <>Связи ГОУ успешно распространены</>;
  }
  if (status === "ERROR") {
    return <>Связи ГОУ не распространены ни в один ДЦ</>;
  }
  if (status === "NOT_FULLY_SENT") {
    return <>Связи ГОУ успешно распространены во все ДЦ, кроме...</>;
  }
};

export const SettingContainer = styled.div`
  height: 20px;
  width: 100%;
  display: flex;
  align-items: center;
`;

export const ModalComparisonSk: FC<ModalСomparisonSkProps> = observer((props) => {
  const { nsiStore, notificationStore, tableStore, authStore, liveTimerStore } = useStores();
  const { userInfo } = authStore;
  const { isCenter } = props;
  const { protocolDistributionInfo, isStatusDistribution, statusGenericSK11, loadedDayNsi, isLoadingCheckSK11, subs, statusSK11, initPrev, dateObject } = nsiStore;

  const isEngineer = userInfo.roles.length > 0 ? (userInfo.roles.length === 1 && userInfo.roles.some((el: any) => el === "engineer") ? true : false) : false;
  const tooltipRef = useRef(null);

  const [filtersSK11, setFiltersSK11] = useState<any[]>([]);
  const [filtersLeft, setFiltersLeft] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const [dataOriginal, setDataOriginal] = useState<any[]>([]);
  const initExpandedRowIdsRight: any = [];
  const [expandedRowIds, setExpandedRowIds] = useState<any>(initExpandedRowIdsRight);
  const [isSearchMode, setIsSearchMode] = useState(false);
  const [hasMoreElements, setHasMoreElements] = useState(false);
  const [isMultipleConnections, setIsMultipleConnections] = useState(false);
  const [objectCharacter, setObjectCharacter] = useState<any>(null);
  const [selectedParams, setSelectedParams] = useState<any>(null);
  const initRge = localStorage.getItem("selectedRge") ?? "RGE_WITH_GOU";
  const [selectedRge, setSelectedRge] = useState(initRge);
  const [typeTableSk11, setTypeTableSk11] = useState("sk11");
  const initTaskIdGeneric = localStorage.getItem("taskIdGeneric") ?? null;
  const [taskIdGeneric, setTaskIdGeneric] = useState(initTaskIdGeneric);
  const initConnectionObjects = JSON.parse(localStorage.getItem("connectionObjects") as string) ?? [];
  const [connectionObjects, setConnectionObjects] = useState<any[]>(initConnectionObjects);
  const [isModalDuplicate, setIsModalDuplicate] = useState(false);
  const [disputedDuplicateObject, setDisputedDuplicateObject] = useState(null);
  const initParamsFilter = localStorage.getItem("paramsFilter") ?? "all";
  const [paramsFilter, setParamsFilter] = useState(initParamsFilter);
  const initRegistry = initPrev ? initPrev.registry : "gou";
  const initSubRegistry = initPrev ? initPrev.subRegistry : "GOU";
  const [registry, setRegistry] = useState<any>(initRegistry);
  const [subRegistry, setSubRegistry] = useState<any>(initSubRegistry);
  const [search, setSearch] = useState<any>([]);
  const [columns, setColumns] = useState<any>([]);
  const selectDefault = JSON.parse(localStorage.getItem("table-checked-items-87") as string) ?? [];
  const [select, setSelect] = useState(selectDefault);
  const [isTooltip, setIsTooltip] = useState(false);
  const [prevParams, setPrevParams] = useState<any>({ subRegistry, registry, date: dateObject });
  const TABLE_KEY_LEFT = `871`;
  const TABLE_KEY_RIGHT = `88`;

  const [isLoadDataDB, setIsLoadDataDB] = useState(false);

  const getInitData = (): any => {
    const dbPromise = idb.open("table-db", 1);
    dbPromise.onsuccess = () => {
      const db = dbPromise.result;
      const tx = db.transaction("userData", "readonly");
      const userData = tx.objectStore("userData");
      const tableData = userData.getAll();
      tableData.onsuccess = (query) => {
        // @ts-ignore
        const buf = query?.srcElement?.result?.find((el: any) => el.id === TABLE_KEY_RIGHT)?.data ?? [];
        if (buf.length > 0) {
          setData(buf);
        } else {
          loadData().then();
        }
        setIsLoadDataDB(true);
      };

      tableData.onerror = () => {
        setIsLoadDataDB(true);
        setData([]);
      };
    };
  };

  const [isLoadDataDBLeft, setIsLoadDataDBLeft] = useState(false);

  const getInitDataLeft = (): any => {
    const dbPromise = idb.open("table-db", 1);
    dbPromise.onsuccess = () => {
      const db = dbPromise.result;
      const tx = db.transaction("userData", "readonly");
      const userData = tx.objectStore("userData");
      const tableData = userData.getAll();
      tableData.onsuccess = (query) => {
        loadDataLeft().then();
        setIsLoadDataDBLeft(true);
      };

      tableData.onerror = () => {
        setIsLoadDataDBLeft(true);
        setDataLeft([]);
      };
    };
  };

  useEffect(() => {
    getInitDataLeft();
    return () => {
      localStorage.removeItem("prevParams");
      localStorage.removeItem("selectedRge");
      localStorage.removeItem("paramsFilter");
      nsiStore.resetInitPrev();
      nsiStore.resetInitDate();
    };
  }, []);

  const initExpandedRowIds: any = [];
  const [expandedRowIdsLeft, setExpandedRowIdsLeft] = useState<any>(initExpandedRowIds);

  const defaultColumnsGou: any[] = [
    { name: "connection", width: 80, title: " ", isSearch: true, searchLength: 3 },
    { name: "name", width: 420, title: "Название объекта СК-11", isSearch: true, searchLength: 3 },
    { name: "isEntry", width: 60, title: " ", isSearch: true, searchLength: 3 },
    { name: "id11", width: 374, title: "UID объекта СК-11", isSearch: true, searchLength: 3 },
  ];

  const [columnsGou, setColumnsGou] = useState<any>([]);

  useEffect(() => {
    tableStore.getTableParams(TABLE_KEY_RIGHT).then((data: any) => {
      const isWidth = data?.some((el: any) => !Number(el.width));
      if (data && !isWidth) {
        setColumnsGou(data);
      } else {
        setColumnsGou(defaultColumnsGou);
      }
    });
  }, []);

  useOnClickOutside(tooltipRef, () => setIsTooltip(false));

  const typeObjects: any = {
    gou:
      isModeCenter && isCenter
        ? [{ value: "GOU", label: "ГОУ" }]
        : [
            { value: "GOU", label: "ГОУ" },
            { value: "RGE", label: "РГЕ" },
            { value: "N_BLOCK", label: "ЕГО" },
          ],
    srpg: [
      { value: "RGE", label: "РГЕ" },
      { value: "N_BLOCK", label: "ЕГО" },
      { value: "LINE", label: "Ветвь" },
      { value: "SECHEN", label: "Сечение" },
      { value: "CONSUMER_2", label: "Потребитель" },
      { value: "AREA_2", label: "Территория" },
      { value: "AREA", label: "Район электрической сети" },
      { value: "W_SUM", label: "Интегральное ограничение" },
      { value: "POWER_SYSTEM", label: "Объединенная энергосистема" },
      { value: "N_GROUP", label: "Нагрузочная группа" },
    ],
    adder: [
      { value: "GEN", label: "Генерации" },
      { value: "POTR", label: "Потребления" },
      { value: "SALDO", label: "Сальдо" },
    ],
  };

  const registries =
    isModeCenter && isCenter
      ? [{ value: "gou", label: "ГОУ" }]
      : [
          { value: "gou", label: "ГОУ" },
          { value: "srpg", label: "СРПГ" },
          { value: "adder", label: "Сумматоры" },
        ];

  const isDisabledLoadData =
    prevParams.subRegistry === subRegistry &&
    prevParams.registry === registry &&
    prevParams.date.day === dateObject.day &&
    prevParams.date.month === dateObject.month &&
    prevParams.date.year === dateObject.year;

  const defaultColumns: any[] = [
    { name: "setting", width: 40, title: " ", isSearch: true, searchLength: 4 },
    { name: "name", width: 300, title: "Название объекта ЕСС", isSearch: true, searchLength: 4 },
    { name: "id", width: 160, title: "ID объекта ЕСС", isSearch: true, searchLength: 2 },
    { name: "paramType", width: 150, title: "Типы", isSearch: true, searchLength: 2 },
    { name: "id11", width: 270, title: "UID объекта СК-11", isSearch: true, searchLength: 4 },
  ];

  useEffect(() => {
    tableStore.getTableParams(TABLE_KEY_LEFT).then((data: any) => {
      if (data) {
        setColumns(data);
      } else {
        setColumns(defaultColumns);
      }
    });

    setSearch([
      { isOpen: false, searchName: "", key: "name" },
      { isOpen: false, searchName: "", key: "id11" },
    ]);
  }, [TABLE_KEY_LEFT]);

  useEffect(() => {
    if (isModeCenter && isCenter && !isEngineer) {
      nsiStore.stopProtocolDistribution();
      nsiStore.getStatusProtocolDistribution().then(() => {
        nsiStore.startProtocolDistribution();
      });
    }
    return () => {
      localStorage.removeItem("tableOpened-87");
      localStorage.removeItem(`tableOpened-${TABLE_KEY_RIGHT}`);
      localStorage.removeItem(`table-sort-${TABLE_KEY_LEFT}`);
      localStorage.removeItem(`table-checked-items-${TABLE_KEY_LEFT}`);
      localStorage.removeItem(`table-sort-${TABLE_KEY_RIGHT}`);
      nsiStore.stopProtocolDistribution();
    };
  }, [paramsFilter, prevParams, isCenter]);

  const onSave = (res: any) => {
    const finalConnectionsObjects = connectionObjects.map((el) => {
      const find = res.find((item: any) => item.tabId === el.id);
      return { ...el, type: find.type, id: Number(find.id) };
    });
    // Обновляем полные (исходные) данные
    const finDataLeftOriginal = dataLeftOriginal.map((el: any) => {
      const find = connectionObjects.find((item) => item.id === el.tabId);
      if (find) {
        return { ...el, id11: find.id11, isBound: !!find.id11 };
      }
      return el;
    });
    setDataLeftOriginal(finDataLeftOriginal);
    saveParams(TABLE_KEY_LEFT, finDataLeftOriginal);

    const finDataOriginal = dataOriginal.map((el) => {
      const findInLeft = finDataLeftOriginal.some((item: any) => item.id11 === el.id11);
      const findInFinalConnectionsObjects = finalConnectionsObjects.some((item: any) => item.id11 === el.id11);
      const findOriginalId = finalConnectionsObjects.some((item: any) => item.originalId11 === el.id11);
      const isFind = !findInLeft && !findInFinalConnectionsObjects ? (findOriginalId ? false : el.isBound) : true;
      return { ...el, isBound: isFind };
    });
    setDataOriginal(finDataOriginal);
    saveParams(TABLE_KEY_RIGHT, finDataOriginal);

    // Обновляем текущие (отфильтрованные) данные, чтобы сохранить состояние дерева
    setDataLeft((currentData) =>
      currentData.map((el: any) => {
        const find = connectionObjects.find((item) => item.id === el.tabId);
        if (find) {
          return { ...el, id11: find.id11, isBound: !!find.id11 };
        }
        return el;
      })
    );

    setData((currentData) =>
      currentData.map((el) => {
        const findInLeft = finDataLeftOriginal.some((item: any) => item.id11 === el.id11);
        const findInFinalConnectionsObjects = finalConnectionsObjects.some((item: any) => item.id11 === el.id11);
        const findOriginalId = finalConnectionsObjects.some((item: any) => item.originalId11 === el.id11);
        const isFind = !findInLeft && !findInFinalConnectionsObjects ? (findOriginalId ? false : el.isBound) : true;
        return { ...el, isBound: isFind };
      })
    );

    setConnectionObjects([]);
    localStorage.removeItem("connectionObjects");
    nsiStore.saveModalComparison(finalConnectionsObjects, null, paramsFilter).then((isComplete: boolean) => {
      if (isComplete) {
        setSelect([]);
        setConnectionObjects([]);
        localStorage.removeItem("connectionObjects");
        localStorage.removeItem("table-checked-items-87");
      }
    });
  };

  const [disputedDuplicateId, setDisputedDuplicateId] = useState(null);
  const customGou = [
    {
      name: "connection",
      render: (value: any, row: any, level: any) => {
        const checkTable = (array: any[]): boolean => {
          return array.some((el) => {
            let childs = el?.childs ?? [];
            let isChilds = false;
            if (childs.length > 0) {
              isChilds = checkTable(childs);
            }
            return el.id11 === row.id11 || isChilds;
          });
        };
        const isDuplicate = connectionObjects.some((el) => el.id11 === row.id11);
        const isPrevId11 = connectionObjects.some((el) => el.originalId11 === row.id11);
        const isDisabled = isDuplicate ? true : isPrevId11 ? false : row.isBound;

        const disabled = select.length === 0;
        return (
          <NameLabel>
            {row.isTarget && (
              <>
                <Connect
                  disabled={disabled}
                  isChoose={isDisabled}
                  title="Установить связь"
                  icon={"system"}
                  onClick={() => {
                    if (!disabled) {
                      //Чистка двух таблиц от привязки
                      const findConnection = connectionObjects.find((el) => el.id === select[0]);
                      const prevId11 = findConnection ? findConnection?.id11 ?? null : dataLeft.find((el: any) => el.tabId === select[0])?.id11 ?? null;

                      if (prevId11) {
                        const resConnetions = connectionObjects.filter((el) => el.id !== select[0]);
                        setConnectionObjects(resConnetions);
                        setData((prev) => {
                          return prev.map((el: any) => {
                            if (el.tabId === prevId11) {
                              const isFind = dataLeft.some((item: any) => item.id11 === prevId11) || resConnetions.some((item: any) => item.id11 === prevId11);
                              return { ...el, isBound: isFind };
                            }
                            return el;
                          });
                        });
                      }

                      const id11Left = row.id11;
                      if (id11Left) {
                        setData((prev: any) => {
                          return prev.map((el: any) => {
                            if (el.tabId === id11Left) {
                              return { ...el, isBound: false };
                            }
                            return el;
                          });
                        });
                      }
                      //Чистка двух таблиц от привязки
                      setConnectionObjects((prev) => {
                        let result = prev;
                        const isFind = connectionObjects.some((el) => el.id === select[0]);
                        if (isFind) {
                          result = prev.filter((el) => el.id !== select[0]);
                        }
                        const findLeft = dataLeft.find((el: any) => el.tabId === select[0]);
                        const res = [...result, { id: select[0], id11: row.id11, originalId11: findLeft.id11 }];
                        localStorage.setItem("connectionObjects", JSON.stringify(res));
                        return res;
                      });
                      setSelect([]);
                    }
                  }}
                  data-test="oik-sk-11-table.connect-button"
                >
                  <Icon width={14} name="system" />
                </Connect>
                <Connect
                  disabled={disabled || isDuplicate}
                  isChoose={false}
                  title="Просмотр связей"
                  icon={"viewPassword"}
                  onClick={() => {
                    if (!disabled && !isDuplicate) {
                      setDisputedDuplicateObject(row.id11);
                      setDisputedDuplicateId(row.tabId);
                    }
                  }}
                >
                  <Icon width={14} name="viewPassword" />
                </Connect>
              </>
            )}
          </NameLabel>
        );
      },
    },
    {
      name: "id11",
      render: (value: any, row: any, _: any, searchesButton: any) => {
        const getCellName = (value: any) => {
          const isFind = searchesButton.some((el: any) => el.value === value);
          if (isFind) {
            return (
              <>
                <mark>{value}</mark>
              </>
            );
          }
          return <>{value === "undefined" || value === "null" ? "" : value}</>;
        };

        const copy = (text: any) => {
          navigator.clipboard.writeText(text);
          notificationStore.addNotification({
            title: "Копирование",
            description: `UID ${text} скопирован !`,
            icon: "success",
            type: "done",
          });
        };
        return (
          <Tippy interactive={true} placement="top" appendTo={document.body} delay={[200, null]} render={() => <CopyContent>Копировать</CopyContent>}>
            <CopyCell onClick={() => copy(value)}>{getCellName(String(value))}</CopyCell>
          </Tippy>
        );
      },
    },
    {
      name: "isEntry",
      render: (value: any, row: any) => {
        return (
          <>
            {row?.isEntry && row?.hasChildren && (
              <OpenAllIcon
                title="Имеются дочерние объекты"
                onClick={async () => {
                  setLoading(true);
                  const loadType = typeTableSk11 === "sk11" ? (prevParams.subRegistry === "RGE" ? selectedRge : prevParams.subRegistry) : "GENERIC";
                  return nsiStore.getTablePrepareSK11(row.tabId, loadType, typeTableSk11).then((result: any) => {
                    const filterData = data
                      .filter((el) => el.parentId !== row.tabId)
                      .map((el) => {
                        if (el.tabId === row.tabId) {
                          return { ...el, isEntry: null };
                        }
                        return el;
                      });
                    setData(filterData.concat(...result).map((el) => ({ ...el, tabId: el.id11, parentId: el.parentId ? el.parentId : "" })));
                    setLoading(false);
                    setExpandedRowIds((prev: any) => {
                      let res: any[];
                      const isFind = prev.some((item: any) => item === row.tabId);
                      if (isFind) {
                        res = prev;
                      } else {
                        res = [...prev, row.tabId];
                      }
                      localStorage.setItem(`expandedRowIds-${TABLE_KEY_RIGHT}`, JSON.stringify(res));
                      return res;
                    });
                  });
                }}
              >
                <Icon name="openAll" width={12} />
              </OpenAllIcon>
            )}
          </>
        );
      },
    },
  ];

  const startDistribution = () => {
    nsiStore.startDistribution().then(() => {
      nsiStore.getStatusProtocolDistribution().then(() => {
        nsiStore.startProtocolDistribution();
      });
    });
  };

  useEffect(() => {
    if (taskIdGeneric) {
      nsiStore.getLastStatusGeneric();
    }
    return () => {
      localStorage.removeItem("taskIdGeneric");
      nsiStore.stopStatusGeneric();
    };
  }, [taskIdGeneric]);

  useEffect(() => {
    if (selectedParams) {
      nsiStore.getListSubs(selectedParams.type, selectedParams.id);
    }
  }, [selectedParams]);

  const mode = isCenter && isModeCenter;

  const customCells = [
    {
      name: "paramType",
      render: (value: any, row: any) => {
        if (selectedParams?.id === row?.id) {
          return (
            <TypeContainer>
              <div
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
              >
                <Combobox
                  items={subs.length > 0 ? subs : [{ value, label: value }]}
                  selectedValue={selectedParams?.selectedId ?? value}
                  width={114}
                  onChange={({ value }) => {
                    nsiStore
                      .saveSelectedSub(selectedParams.type, selectedParams.id, value)
                      .then(() => {
                        setDataLeft((prev: any) => {
                          const res = prev.map((el: any) => {
                            if (el.id === selectedParams.id && el.type === selectedParams.type) {
                              return { ...el, paramType: value };
                            }
                            return el;
                          });
                          saveParams(TABLE_KEY_LEFT, res);
                          return res;
                        });
                        setDataLeftOriginal((prev: any) => {
                          return prev.map((el: any) => {
                            if (el.id === selectedParams.id && el.type === selectedParams.type) {
                              return { ...el, paramType: value };
                            }
                            return el;
                          });
                        });
                      })
                      .then(() => {
                        setSelectedParams(null);
                      });
                  }}
                />
              </div>
            </TypeContainer>
          );
        }

        return (
          <TypeContainer>
            <>{value}</>
            {row.type === "gen" && ( //gen
              <AccessControl rules={["nsi_admin"]}>
                <Action
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setSelectedParams({ id: row.id, type: row.type, selectedId: null });
                  }}
                >
                  <Icon name="pencil" width={18} />
                </Action>
              </AccessControl>
            )}
          </TypeContainer>
        );
      },
    },
    {
      name: "setting",
      render: (value: any, row: any, _: any, searchesButton: any) => {
        const isFind: any = connectionObjects.find((item: any) => item.id === row.tabId);
        const isClose = isFind ? !!isFind?.id11 : !!row.id11;

        return (
          <SettingContainer>
            {isClose && !mode && (
              <AccessControl rules={["nsi_admin", "engineer"]}>
                <Connect
                  icon={"settings"}
                  title="Характеристики"
                  data-test="pak-ess-table.settings-button"
                  onClick={() => {
                    setObjectCharacter({ type: row.type, id: row.id, name: row.name, isEditSubs: subRegistry === "GEN" });
                  }}
                >
                  <Icon width={14} name="settings" />
                </Connect>
              </AccessControl>
            )}
            {isClose && row.isTarget && (
              <AccessControl rules={["nsi_admin"]}>
                <Connect
                  title="Отвязать"
                  isClose={true}
                  icon={"close"}
                  data-test="pak-ess-table.unbound-button"
                  onClick={() => {
                    const findFlat = dataLeft.find((item: any) => item.tabId === row.tabId)?.id11 ?? null;
                    setConnectionObjects((prev) => {
                      let res: any[];
                      const result = prev.filter((el) => el.id !== row.tabId);
                      if (findFlat) {
                        res = [...result, { id: row.tabId, id11: null, originalId11: row.id11 }];
                      } else {
                        res = [...result];
                      }
                      localStorage.setItem("connectionObjects", JSON.stringify(res));
                      return res;
                    });
                  }}
                >
                  <Icon width={10} name="close" />
                </Connect>
              </AccessControl>
            )}
          </SettingContainer>
        );
      },
    },
    {
      name: "id11",
      render: (value: any, row: any, _: any, searchesButton: any) => {
        const isFind: any = connectionObjects.some((item: any) => item.id === row.tabId);
        const isChildsIdentity = row.isOpen;
        const isClose = isFind ? isFind?.id11 : row.id11;

        const getCellName = (value: any) => {
          const isFind = searchesButton.some((el: any) => el.value === value);
          if (isFind) {
            return (
              <>
                <mark>{value}</mark>
              </>
            );
          }
          return <>{value === "undefined" || value === "null" ? "" : value}</>;
        };

        if (isFind && !isChildsIdentity) {
          const id11 = connectionObjects.find((item: any) => item.id === row.tabId)?.id11 ?? null;
          return (
            <>
              <>{getCellName(String(id11))}</>
              {id11 && (
                <div
                  onClick={() => {
                    const findFlat = dataLeft.find((item: any) => item.tabId === row.tabId)?.id11 ?? null;
                    setConnectionObjects((prev) => {
                      let res: any[];
                      const result = prev.filter((el) => el.id !== row.tabId);
                      if (findFlat) {
                        res = [...result, { id: row.tabId, id11: null, originalId11: row.id11 }];
                      } else {
                        res = [...result];
                      }
                      localStorage.setItem("connectionObjects", JSON.stringify(res));
                      return res;
                    });
                  }}
                >
                  <SearchUID name="search" width={14} />
                </div>
              )}
              {!(isCenter && isModeCenter) && !isChildsIdentity && (
                <AccessControl rules={["nsi_admin"]}>
                  {isClose && (
                    <SettingUpCharacteristics
                      type="secondary"
                      icon="settings"
                      message="Характеристики"
                      widthIcon={14}
                      onClick={() => {
                        setObjectCharacter({ type: row.type, id: row.id, name: row.name, isEditSubs: subRegistry === "GEN" });
                      }}
                    />
                  )}
                </AccessControl>
              )}
            </>
          );
        } else {
          return (
            <>
              {value ? (
                <>
                  <UIDLabel>{getCellName(String(value))}</UIDLabel>
                  <div
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setFiltersSK11((prev) => {
                        const final = prev.filter((item) => item.columnName !== "id11");
                        return [...final, { columnName: "id11", value: row.id11, operation: "contains" }];
                      });
                      nsiStore.changeInputFilter(TABLE_KEY_RIGHT, "id11", row.id11); // Добавляет значение в инпут TextFieldStyled "UID объекта СК-11" для таблицы "Объекты ОИК СК-11"
                    }}
                  >
                    <SearchUID name="search" width={14} />
                  </div>
                </>
              ) : (
                <>{getCellName(String(value))}</>
              )}
            </>
          );
        }
      },
    },
    {
      name: "name",
      render: (value: any, row: any) => {
        const isFind = connectionObjects.find((item: any) => item.id === row.tabId);
        const isClose = isFind ? isFind?.id11 : row.id11;
        return (
          <NameLabel>
            {isClose && row.isTarget && (
              <Connect
                title="Отвязать"
                isClose={true}
                icon={"close"}
                onClick={() => {
                  const findFlat = dataLeft.find((item: any) => item.tabId === row.tabId)?.id11 ?? null;
                  setConnectionObjects((prev) => {
                    let res: any[];
                    const result = prev.filter((el) => el.id !== row.tabId);
                    if (findFlat) {
                      res = [...result, { id: row.tabId, id11: null, originalId11: row.id11 }];
                    } else {
                      res = [...result];
                    }
                    localStorage.setItem("connectionObjects", JSON.stringify(res));
                    return res;
                  });
                }}
              >
                <Icon width={10} name="close" />
              </Connect>
            )}
            <NameContainer>{value}</NameContainer>
          </NameLabel>
        );
      },
    },
  ];

  const reportKindGet = () => {
    if (paramsFilter === "all") {
      return "ALL";
    }
    if (paramsFilter === "matched") {
      return "WITH_ID11";
    }
    if (paramsFilter === "notMatched") {
      return "NO_ID11";
    }
  };

  const exportCSV = (type: any) => {
    const reportKind = reportKindGet();
    liveTimerStore.getTimeServer().then(({ dateTime }: any) => {
      nsiStore.exportCSV(dateObject.year, dateObject.month, dateObject.day, type, dateTime, prevParams.registry, prevParams.subRegistry, reportKind);
    });
  };

  const searchForMeasurements = () => {
    nsiStore.searchForMeasurements();
  };

  useEffect(() => {
    if (!isEngineer && userInfo.roles.length > 0) {
      nsiStore.getSearchForMeasurements();
      return () => {
        nsiStore.stopCheckStatusGeneric();
        nsiStore.stopCheckStatusSK11();
        setPrevParams({ subRegistry, registry, date: dateObject });
      };
    }
  }, [userInfo]);

  useEffect(() => {
    const loadType = typeTableSk11 === "sk11" ? (prevParams.subRegistry === "RGE" ? selectedRge : prevParams.subRegistry) : "GENERIC";
    nsiStore.checkStatusSK11(loadType);
  }, [prevParams, typeTableSk11]);

  const status = typeTableSk11 === "sk11" ? statusSK11 : statusGenericSK11;

  useEffect(() => {
    if (statusSK11?.status === "ACTIVE") {
      const version = localStorage.getItem("status-sk11");
      if (!isLoadDataDB && version !== "GENERIC") {
        getInitData();
      } else {
        setLoading(true);
        const loadType = typeTableSk11 === "sk11" ? (prevParams.subRegistry === "RGE" ? selectedRge : prevParams.subRegistry) : "GENERIC";
        nsiStore
          .getTablePrepareSK11(null, loadType, "sk11")
          .then((res: any) => {
            setData(res.map((el: any) => ({ ...el, tabId: el.id11, parentId: el.parentId ? el.parentId : "" })));
            setDataOriginal(
              res.map((el: any) => ({
                ...el,
                tabId: el.id11,
                parentId: el.parentId ? el.parentId : "",
              }))
            );
            setLoading(false);
          })
          .catch(() => setLoading(false));
        deleteParams(TABLE_KEY_RIGHT);
        localStorage.removeItem(`expandedRowIds-${TABLE_KEY_RIGHT}`);
      }
    }
  }, [statusSK11.status]);

  const startTaskSk11 = async () => {
    setIsLoadDataDB(true);
    const loadType = typeTableSk11 === "sk11" ? (prevParams.subRegistry === "RGE" ? selectedRge : prevParams.subRegistry) : "GENERIC";
    localStorage.setItem("status-sk11", loadType);
    await nsiStore.startSK11(loadType).then((res: any) => {
      nsiStore.stopCheckStatusSK11();
      if (res) {
        nsiStore.checkStatusSK11(loadType);
      }
    });
  };

  const loadData = async () => {
    const rowIdsWithNotLoadedChilds = [ROOT_ID, ...expandedRowIds].filter((rowId) => data.findIndex((row) => row.parentId === rowId) === -1);
    if (rowIdsWithNotLoadedChilds.length) {
      if (loading) return;
      setLoading(true);
      Promise.all(
        rowIdsWithNotLoadedChilds.map((rowId) => {
          const loadType = typeTableSk11 === "sk11" ? (prevParams.subRegistry === "RGE" ? selectedRge : prevParams.subRegistry) : "GENERIC";
          return nsiStore.getTablePrepareSK11(rowId, loadType, typeTableSk11).then((res: any) => {
            return res;
          });
        })
      )
        .then((loadedData) => {
          if (dataOriginal.length === 0) {
            setDataOriginal(data.concat(...loadedData).map((el) => ({ ...el, tabId: el.id11, parentId: el.parentId ? el.parentId : "" })));
          }
          setData(data.concat(...loadedData).map((el) => ({ ...el, tabId: el.id11, parentId: el.parentId ? el.parentId : "" })));
          const searchMode = filtersSK11.some((el) => el?.value?.trim()?.length > 0);
          if (!searchMode) {
            saveParams(
              TABLE_KEY_RIGHT,
              data.concat(...loadedData).map((el) => ({
                ...el,
                tabId: el.id11,
                parentId: el.parentId ? el.parentId : "",
              }))
            );
          }
          setLoading(false);
        })
        .catch(() => setLoading(false));
    }
  };

  const [hiddenColumnNames, setHiddenColumnNames] = useState<any>(null);

  useMemo(() => {
    if (prevParams.subRegistry !== "GEN") {
      setHiddenColumnNames(["paramType"]);
    } else {
      setHiddenColumnNames(null);
    }
    if (!loading && !isSearchMode && isLoadDataDB) {
      loadData().then();
    }
  }, [expandedRowIds, prevParams, typeTableSk11]);

  const prepareDate = () => {
    const year = dateObject.year;
    const day = dateObject.day < 10 ? `0${dateObject.day}` : dateObject.day;
    const month = dateObject.month < 10 ? `0${dateObject.month}` : dateObject.month;
    return `${year}-${month}-${day}`;
  };

  const dateLeft = prepareDate();
  const [dataLeft, setDataLeft] = useState<any>([]);
  const [dataLeftOriginal, setDataLeftOriginal] = useState<any>([]);
  const [loadingLeft, setLoadingLeft] = useState(false);

  const loadDataLeft = async () => {
    const rowIdsWithNotLoadedChilds = [ROOT_ID, ...expandedRowIdsLeft].filter((rowId) => dataLeft.findIndex((row: any) => row.parentId === rowId) === -1);
    if (rowIdsWithNotLoadedChilds.length) {
      if (loading) return;
      setLoadingLeft(true);
      Promise.all(
        rowIdsWithNotLoadedChilds.map((rowId) => {
          return nsiStore.getTableLeftSK11(prevParams.registry, paramsFilter, prevParams.subRegistry, rowId, dateLeft).then((res: any) => {
            return res;
          });
        })
      )
        .then((loadedData) => {
          if (dataLeftOriginal.length === 0) {
            setDataLeftOriginal(
              dataLeft.concat(...loadedData).map((el: any) => ({ ...el, parentId: el?.parentId ?? "", isDisableChecked: !el.isTarget, idConnect: `${el.id}-${el.type}` }))
            );
          }
          setDataLeft(
            dataLeft.concat(...loadedData).map((el: any) => ({ ...el, parentId: el?.parentId ?? "", isDisableChecked: !el.isTarget, idConnect: `${el.id}-${el.type}` }))
          );
          saveParams(
            TABLE_KEY_LEFT,
            dataLeft.concat(...loadedData).map((el: any) => ({ ...el, parentId: el?.parentId ?? "", isDisableChecked: !el.isTarget, idConnect: `${el.id}-${el.type}` }))
          );
          setLoadingLeft(false);
        })
        .catch(() => setLoadingLeft(false));
    }
  };
  const [isSearchModeLeft, setIsSearchModeLeft] = useState(false);
  const [hasMoreElementsLeft, setHasMoreElementsLeft] = useState(false);

  useMemo(() => {
    if (!loadingLeft && !isSearchModeLeft && isLoadDataDBLeft) {
      loadDataLeft().then();
    }
  }, [expandedRowIdsLeft]);

  const [initSortingLeft, setInitSortingLeft] = useState<any>(null);
  const [initSortingRight, setInitSortingRight] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams(TABLE_KEY_LEFT).then((data: any) => {
      if (data) {
        setInitSortingLeft(data);
      } else {
        setInitSortingLeft([{ columnName: "name", direction: "asc" }]);
      }
    });
    tableStore.getSortParams(TABLE_KEY_RIGHT).then((data: any) => {
      if (data) {
        setInitSortingRight(data);
      } else {
        setInitSortingRight([{ columnName: "name", direction: "asc" }]);
      }
    });
    return () => {
      localStorage.removeItem("connectionObjects");
      localStorage.removeItem(`expandedRowIdsLeft-${TABLE_KEY_LEFT}`);
      localStorage.removeItem(`expandedRowIds-${TABLE_KEY_RIGHT}`);
      deleteParams(TABLE_KEY_RIGHT);
      deleteParams(TABLE_KEY_LEFT);
      localStorage.removeItem("status-sk11");
    };
  }, []);

  return (
    <ModalStyled>
      {disputedDuplicateObject && (
        <ViewDuplicateModal
          date={prevParams.date}
          uuid={disputedDuplicateObject}
          onClose={() => {
            setDisputedDuplicateObject(null);
          }}
          onConfirm={async (array: any[], isAll: boolean) => {
            if (isAll) {
              setData((prev) => {
                const res = prev.map((el) => {
                  if (disputedDuplicateId === el.tabId) {
                    return { ...el, isBound: false };
                  }
                  return el;
                });
                saveParams(TABLE_KEY_RIGHT, res);
                return res;
              });
            }
            await nsiStore.saveModalComparison(array, null, paramsFilter).then((isComplete: boolean) => {
              if (isComplete) {
                const isRge = prevParams.subRegistry === "RGE" || (prevParams.registry === "srpg" && prevParams.subRegistry === "RGE");
                nsiStore.unLinkMultiConnection(array, disputedDuplicateObject, isRge, selectedRge);
              }
            });
            setDisputedDuplicateObject(null);
          }}
        />
      )}
      {isMultipleConnections && (
        <MultipleConnections
          date={prevParams.date}
          isEngineer={isEngineer}
          onCancel={() => setIsMultipleConnections(false)}
          onConfirm={async (array: any[]) => {
            setDisputedDuplicateObject(null);
            await nsiStore.saveModalComparison(array, null, paramsFilter).then((isComplete: boolean) => {
              if (isComplete) {
                const isRge = prevParams.subRegistry === "RGE" || (prevParams.registry === "srpg" && prevParams.subRegistry === "generator");
                setIsMultipleConnections(false);
                nsiStore.unLinkMultipleConnections(array, isRge, selectedRge);
              }
            });
          }}
        />
      )}
      {objectCharacter && (
        <ModalSettingsCharacteristics
          isEngineer={isEngineer}
          onClose={() => setObjectCharacter(null)}
          objectCharacter={objectCharacter}
          dataTestContainer="comparison-sk-params-modal.container"
        />
      )}
      {isModalDuplicate && (
        <ModalOverWrite
          connectionObjects={connectionObjects}
          registry={registry}
          type={subRegistry}
          dateObject={dateObject}
          paramsFilter={paramsFilter}
          prevParams={prevParams}
          setSelect={setSelect}
          setConnectionObjects={setConnectionObjects}
          onCancel={() => setIsModalDuplicate(false)}
        />
      )}
      <ActionBar>
        <FilterDatePickerStyled
          day={dateObject.day}
          month={dateObject.month}
          year={dateObject.year}
          onClick={({ day, month, year }) => {
            nsiStore.setDateObject({ day, month, year });
          }}
          hiddenChooseButton
          loadDay={loadedDayNsi}
        />
        <ComboboxStyled
          items={registries}
          selectedValue={registry}
          onChange={({ value }) => {
            setRegistry(value);
            setSubRegistry(() => {
              switch (value) {
                case "gou":
                  return "GOU";
                case "srpg":
                  return "LINE";
                case "adder":
                  return "GEN";
                default:
                  return "GOU";
              }
            });
          }}
          placeholder="Реестр"
          width={160}
          disabled={connectionObjects.length !== 0}
          dataTest="comparison-sk-11-header.type-registry-combobox"
        />
        <ComboboxStyled
          items={typeObjects[registry]}
          selectedValue={subRegistry}
          onChange={({ value }) => {
            setSubRegistry(value);
          }}
          placeholder="Тип объекта"
          width={230}
          maxHeightList={320}
          disabled={connectionObjects.length !== 0}
          dataTest="comparison-sk-11-header.type-object-combobox"
        />
        <ButtonStyled
          title="Выбрать"
          onClick={() => {
            setLoadingLeft(true);
            setDataLeft([]);
            setDataLeftOriginal([]);
            setExpandedRowIdsLeft([]);
            localStorage.setItem(`expandedRowIdsLeft-${TABLE_KEY_LEFT}`, JSON.stringify([]));
            nsiStore
              .getTableLeftSK11(registry, "all", subRegistry, "", dateLeft)
              .then((res: any) => {
                setDataLeft(res.map((el: any) => ({ ...el, parentId: el?.parentId ?? "", isDisableChecked: !el.isTarget, idConnect: `${el.id}-${el.type}` })));
                saveParams(
                  TABLE_KEY_LEFT,
                  res.map((el: any) => ({ ...el, parentId: el?.parentId ?? "", isDisableChecked: !el.isTarget, idConnect: `${el.id}-${el.type}` }))
                );
                setDataLeftOriginal(res.map((el: any) => ({ ...el, parentId: el?.parentId ?? "", isDisableChecked: !el.isTarget, idConnect: `${el.id}-${el.type}` })));
                setLoadingLeft(false);
              })
              .catch(() => setLoadingLeft(false));
            setLoading(true);
            const loadType = subRegistry === "RGE" ? selectedRge : subRegistry;
            nsiStore
              .getTablePrepareSK11(null, loadType, "sk11")
              .then((res: any) => {
                setData(res.map((el: any) => ({ ...el, tabId: el.id11, parentId: el.parentId ? el.parentId : "" })));
                setDataOriginal(res.map((el: any) => ({ ...el, tabId: el.id11, parentId: el.parentId ? el.parentId : "" })));
                setLoading(false);
              })
              .catch(() => setLoading(false));
            setTypeTableSk11("sk11");
            setPrevParams({ subRegistry, registry, date: dateObject });
            setSearch([
              { isOpen: false, searchName: "", key: "name" },
              { isOpen: false, searchName: "", key: "id11" },
            ]);
            setFiltersLeft([]);
            setFiltersSK11([]);
            localStorage.removeItem(`expandedRowIdsLeft-${TABLE_KEY_LEFT}`);
            localStorage.removeItem(`expandedRowIds-88`);
            deleteParams("88");
            deleteParams(TABLE_KEY_LEFT);
            setExpandedRowIds([]);
            localStorage.removeItem("connectionObjects");
            setConnectionObjects([]);
            setSelect([]);
            localStorage.removeItem("table-checked-items-87");
            localStorage.setItem("prevParams", JSON.stringify({ registry, subRegistry, date: dateObject }));
            localStorage.setItem("status-sk11", loadType);
            setParamsFilter("all");
          }}
          disabled={isDisabledLoadData || connectionObjects.length !== 0}
          dataTest="comparison-sk-11-header.choose-button"
        />
        <ButtonStyled
          title="Сохранить"
          onClick={async () => {
            await nsiStore.prepareDataSaveConnection(connectionObjects.map((el: any) => el.id)).then((res: any) => {
              onSave(res);
            });
          }}
          disabled={connectionObjects.length === 0}
          dataTest="comparison-sk-11-header.save-button"
        />
        <ButtonStyled
          title="Отменить"
          disabled={connectionObjects.length === 0}
          onClick={() => {
            localStorage.removeItem("connectionObjects");
            setConnectionObjects([]);
          }}
        />
        <ButtonStyled type="secondary" title="Множественные связи" onClick={() => setIsMultipleConnections(true)} />
        {!isEngineer && (
          <RightActionMenu>
            {isModeCenter && isCenter ? (
              <>
                <ButtonStyled
                  type="secondary"
                  title={
                    protocolDistributionInfo.status === "SENDING" || isStatusDistribution ? (
                      <LoaderContainer>
                        <Loader style={{ width: "auto", margin: "0 10px" }} />
                        <LabelLoader>Связи ГОУ распространяются</LabelLoader>
                      </LoaderContainer>
                    ) : (
                      "Распространить связи ГОУ в ДЦ"
                    )
                  }
                  message={protocolDistributionInfo.status === "SENDING" || isStatusDistribution ? "Связи ГОУ распространяются" : "Распространить связи ГОУ в ДЦ"}
                  onClick={() => startDistribution()}
                  disabled={connectionObjects.length > 0 || protocolDistributionInfo.status === "SENDING" || isStatusDistribution}
                  isDistribution={true}
                  dataTest={
                    protocolDistributionInfo.status === "SENDING" || isStatusDistribution
                      ? "comparison-sk-11-header.distributing-button"
                      : "comparison-sk-11-header.distribute-button"
                  }
                />
                <ButtonStyled
                  type="secondary"
                  title={
                    <ProtocolContainer>
                      {protocolDistributionInfo.status && (
                        <>
                          {protocolDistributionInfo.status === "SENDING" || isStatusDistribution ? (
                            <Loader style={{ width: "auto", margin: "0 10px" }} />
                          ) : (
                            <Circle status={protocolDistributionInfo.status} />
                          )}
                        </>
                      )}
                      <>Протокол</>
                      {isTooltip && (
                        <TooltipContent ref={tooltipRef}>
                          <HeaderTooltipContent>
                            <Circle status={protocolDistributionInfo.status} />
                            Статус распространения : {getStatusDistribution(protocolDistributionInfo.status)}
                          </HeaderTooltipContent>
                          <BodyTooltipContent>
                            {protocolDistributionInfo.notSentTo.map((item: any, index: number) => {
                              return (
                                <RowTooltipContainer key={index} data-test="comparison-sk-11-protocol-list.row">
                                  <Circle status={protocolDistributionInfo.status} />
                                  {item}
                                </RowTooltipContainer>
                              );
                            })}
                          </BodyTooltipContent>
                        </TooltipContent>
                      )}
                    </ProtocolContainer>
                  }
                  message={protocolDistributionInfo.status === "SENDING" || isStatusDistribution ? "Связи ГОУ распространяются" : "Протокол"}
                  onClick={() => setIsTooltip(true)}
                  disabled={!protocolDistributionInfo.status || protocolDistributionInfo.status === "SENDING" || isStatusDistribution}
                  isProtocol={true}
                  isActive={isTooltip}
                  dataTest="comparison-sk-11-header.protocol-button"
                />
              </>
            ) : (
              <MeasurementsSearchButtons
                connectionObjects={connectionObjects}
                searchForMeasurements={searchForMeasurements}
                getIconMeasurements={getIconMeasurements}
                getIconSize={getIconSize}
              />
            )}
          </RightActionMenu>
        )}
      </ActionBar>
      <Content>
        <Left>
          <TableContainer>
            <Table
              columns={columns}
              setColumns={setColumns}
              networkMode={true}
              expandedRowIds={expandedRowIdsLeft}
              setExpandedRowIds={(e: any) => {
                localStorage.setItem(`expandedRowIdsLeft-${TABLE_KEY_LEFT}`, JSON.stringify(e));
                setExpandedRowIdsLeft(e);
              }}
              tableData={dataLeft}
              isLoading={loadingLeft}
              title="Объекты ПАК ЕСС :"
              tableKey={TABLE_KEY_LEFT}
              isEnter={true}
              initSorting={initSortingLeft}
              filters={filtersLeft}
              hasMoreElements={hasMoreElementsLeft}
              setFilters={setFiltersLeft}
              setSearchParams={async (e: any) => {
                const fs = e.filter((el: any) => el.value.trim().length > 0);
                if (fs.length > 0) {
                  setLoadingLeft(true);
                  setHasMoreElementsLeft(false);
                  const uid11 = e.find((el: any) => el.columnName === "id11")?.value ?? null;
                  const name = e.find((el: any) => el.columnName === "name")?.value ?? null;
                  const id = e.find((el: any) => el.columnName === "id")?.value ?? null;
                  const typeSearch = e.find((el: any) => el.columnName === "paramType")?.value ?? null;
                  setLoadingLeft(true);
                  nsiStore
                    .getTableLeftSearch(name, id, prevParams.registry, dateLeft, prevParams.subRegistry, paramsFilter, uid11, typeSearch)
                    .then(({ items, hasMoreElements }: any) => {
                      setHasMoreElementsLeft(hasMoreElements);
                      setDataLeft(items.map((el: any) => ({ ...el, parentId: el?.parentId ?? "", isDisableChecked: !el.isTarget, idConnect: `${el.id}-${el.type}` })));
                      saveParams(
                        TABLE_KEY_LEFT,
                        items.map((el: any) => ({ ...el, parentId: el?.parentId ?? "", isDisableChecked: !el.isTarget, idConnect: `${el.id}-${el.type}` }))
                      );
                      setIsSearchModeLeft(true);
                      const arr = items
                        .map((el: any) => {
                          if (el.isOpen > 0) {
                            return el.tabId;
                          } else {
                            return null;
                          }
                        })
                        .filter((el: any) => el);

                      setExpandedRowIdsLeft(arr);
                      localStorage.setItem(`expandedRowIdsLeft-${TABLE_KEY_LEFT}`, JSON.stringify(arr));
                      setLoadingLeft(false);
                      setIsSearchModeLeft(false);
                    })
                    .catch(() => {
                      setLoadingLeft(false);
                      setIsSearchModeLeft(false);
                    });
                } else {
                  setDataLeft(dataLeftOriginal);
                  setExpandedRowIdsLeft([]);
                  localStorage.setItem(`expandedRowIdsLeft-${TABLE_KEY_LEFT}`, JSON.stringify([]));
                  setHasMoreElementsLeft(false);
                }
              }}
              isSearchForChilds={true}
              disabledSearches={["setting"]}
              defaultColumns={defaultColumns}
              selectedMode={isEngineer ? null : "one"}
              selected={select}
              setSelected={setSelect}
              customCells={customCells}
              hiddenColumnNames={hiddenColumnNames}
              childrenKey="name"
              isOverFlowHidden={false}
              onClickRow={(path: string, onClickOpenAndClose: any, closeAllChild: any, clearSearch: any) => {
                clearSearch();
                closeAllChild();
                const paths = path.split("/").filter((el) => el.length > 0);
                paths.map((el) => {
                  onClickOpenAndClose({ tabId: el, isSearchMode: false });
                });
              }}
              dataTest="pak-ess.table"
              dataTestDefaultCells="pak-ess-table.cells"
              dataTestRows="pak-ess-table.rows"
              dataTestSearch="pak-ess-table.search-button"
              headerComponents={
                <>
                  <ButtonsGroupStyled
                    items={[
                      { value: "all", label: "Все" },
                      { value: "matched", label: "Сопоставленные", dataTest: "pak-ess-header.matched-button" },
                      { value: "notMatched", label: "Несопоставленные", dataTest: "pak-ess-header.not-matched-button" },
                    ]}
                    selectedValue={paramsFilter}
                    onClick={(value) => {
                      localStorage.setItem("paramsFilter", value);
                      setExpandedRowIdsLeft([]);
                      localStorage.setItem(`expandedRowIdsLeft-${TABLE_KEY_LEFT}`, JSON.stringify([]));
                      setLoadingLeft(true);
                      nsiStore
                        .getTableLeftSK11(prevParams.registry, value, prevParams.subRegistry, "", dateLeft)
                        .then((res: any) => {
                          setDataLeft(res.map((el: any) => ({ ...el, parentId: el?.parentId ?? "", isDisableChecked: !el.isTarget, idConnect: `${el.id}-${el.type}` })));
                          saveParams(
                            TABLE_KEY_LEFT,
                            res.map((el: any) => ({ ...el, parentId: el?.parentId ?? "", isDisableChecked: !el.isTarget, idConnect: `${el.id}-${el.type}` }))
                          );
                          setDataLeftOriginal(
                            res.map((el: any) => ({ ...el, parentId: el?.parentId ?? "", isDisableChecked: !el.isTarget, idConnect: `${el.id}-${el.type}` }))
                          );
                          setLoadingLeft(false);
                        })
                        .catch(() => setLoadingLeft(false));
                      setTypeTableSk11("sk11");
                      setSearch([
                        { isOpen: false, searchName: "", key: "name" },
                        { isOpen: false, searchName: "", key: "id11" },
                      ]);
                      setFiltersLeft([]);
                      setParamsFilter(value);
                      localStorage.removeItem(`expandedRowIdsLeft-${TABLE_KEY_LEFT}`);
                      deleteParams(TABLE_KEY_LEFT);
                      setHasMoreElementsLeft(false);
                    }}
                  />
                  <Combobox
                    items={[
                      { value: "ALL", label: "Все " },
                      { value: "WITH_ID11", label: "Сопоставленные " },
                      { value: "NO_ID11", label: "Несопоставленные" },
                      { value: "filter", label: "По критериям фильтрации" },
                    ]}
                    selectedValue={null}
                    placeholder="Экспорт CSV"
                    icon="csv"
                    onChange={({ value }) => exportCSV(value)}
                    width={186}
                    isTextColorPlaceHolder={true}
                    dataTest="pak-ess-header.export-combobox"
                    isLoading={nsiStore.isExportingCSV}
                  />
                </>
              }
            />
          </TableContainer>
        </Left>
        <Right>
          <TableContainer>
            <Table
              columns={columnsGou}
              setColumns={setColumnsGou}
              tableData={status?.status === "ACTIVE" ? data : []}
              expandedRowIds={expandedRowIds}
              setExpandedRowIds={(e: any) => {
                localStorage.setItem(`expandedRowIds-88`, JSON.stringify(e));
                setExpandedRowIds(e);
              }}
              isLoading={loading}
              disabledSearches={["connection", "isEntry"]}
              title="Объекты ОИК СК-11 :"
              tableKey={TABLE_KEY_RIGHT}
              networkMode={true}
              initSorting={initSortingRight}
              isEnter={true}
              filters={filtersSK11}
              setFilters={setFiltersSK11}
              search={search}
              childrenKey="name"
              defaultColumns={defaultColumnsGou}
              customCells={customGou}
              hasMoreElements={hasMoreElements}
              setSearchParams={async (e: any) => {
                if (
                  e.length > 0 &&
                  ((typeTableSk11 === "sk11" && statusSK11.status && statusSK11.status === "ACTIVE") ||
                    (typeTableSk11 === "generic" && statusGenericSK11.status && statusGenericSK11.status === "ACTIVE"))
                ) {
                  setLoading(true);
                  setHasMoreElements(false);
                  const id11 = e.find((el: any) => el.columnName === "id11")?.value ?? "";
                  const name = e.find((el: any) => el.columnName === "name")?.value ?? "";
                  setLoading(true);
                  const loadType = typeTableSk11 === "sk11" ? (prevParams.subRegistry === "RGE" ? selectedRge : prevParams.subRegistry) : "GENERIC";
                  nsiStore
                    .getTableSK11Search(statusSK11.activeTaskId, id11, name, loadType, typeTableSk11)
                    .then(({ items, hasMoreElements }: any) => {
                      setHasMoreElements(hasMoreElements);
                      setData(items);
                      setIsSearchMode(true);
                      const arr = items
                        .map((el: any) => {
                          if (el.childs.length > 0) {
                            return el.tabId;
                          } else {
                            return null;
                          }
                        })
                        .filter((el: any) => el);

                      setExpandedRowIds(arr);
                      setLoading(false);
                      setIsSearchMode(false);
                    })
                    .catch(() => {
                      setLoading(false);
                      setIsSearchMode(false);
                      setHasMoreElements(false);
                    });
                } else {
                  setData(dataOriginal);
                  setExpandedRowIds([]);
                  localStorage.setItem(`expandedRowIds-88`, JSON.stringify([]));
                }
              }}
              dataTest="oik-sk-11.table"
              dataTestDefaultCells="oik-sk-11-table.cells"
              dataTestRows="oik-sk-11-table.rows"
              headerComponents={
                <>
                  {prevParams.subRegistry === "RGE" ? (
                    <>
                      <ButtonLoadSK11 disabled={statusSK11.status === "CREATED"} title="Загрузить объекты" type="secondary" onClick={() => startTaskSk11()} />
                      <Tooltip
                        content={
                          <ContentSK11>
                            <StatusSK11 status={statusSK11?.status} />
                            <>{isLoadingCheckSK11 ? "Идет подгрузка данных с сервера" : getStatusSK11(statusSK11)}</>
                          </ContentSK11>
                        }
                      >
                        <IconContainer>
                          {isLoadingCheckSK11 ? (
                            <Loader spinnerSize={20} />
                          ) : (
                            <IconInformation status={statusSK11?.status}>
                              <Icon width={20} name="information" />
                            </IconInformation>
                          )}
                        </IconContainer>
                      </Tooltip>
                      <ComboboxStyled
                        items={[
                          { value: "RGE_WITH_GOU", label: "РГЕ с ГОУ" },
                          { value: "RGE_WITHOUT_GOU", label: "РГЕ без ГОУ" },
                        ]}
                        selectedValue={selectedRge}
                        onChange={({ value }) => {
                          const prev = localStorage.getItem("selectedRge");
                          if (prev !== value) {
                            setData([]);
                            setExpandedRowIds([]);
                            localStorage.setItem(`expandedRowIds-88`, JSON.stringify([]));
                            localStorage.setItem("selectedRge", value);
                            setSelectedRge(value);
                            localStorage.setItem("status-sk11", value);
                            nsiStore.checkStatusSK11(value);
                            setLoading(true);
                            nsiStore
                              .getTablePrepareSK11(null, value, "sk11")
                              .then((res: any) => {
                                setData(
                                  res.map((el: any) => ({
                                    ...el,
                                    tabId: el.id11,
                                    parentId: el.parentId ? el.parentId : "",
                                  }))
                                );
                                setDataOriginal(
                                  res.map((el: any) => ({
                                    ...el,
                                    tabId: el.id11,
                                    parentId: el.parentId ? el.parentId : "",
                                  }))
                                );
                                setLoading(false);
                              })
                              .catch(() => setLoading(false));
                            deleteParams("88");
                            localStorage.removeItem(`expandedRowIds-88`);
                          }
                        }}
                        width={200}
                      />
                    </>
                  ) : (
                    <>
                      {typeTableSk11 === "sk11" && (
                        <>
                          <ButtonLoadSK11 disabled={statusSK11.status === "CREATED"} title="Загрузить объекты" type="secondary" onClick={() => startTaskSk11()} />
                          <Tooltip
                            content={
                              <ContentSK11>
                                <StatusSK11 status={statusSK11?.status} />
                                <>{isLoadingCheckSK11 ? "Идет подгрузка данных с сервера" : getStatusSK11(statusSK11)}</>
                              </ContentSK11>
                            }
                          >
                            <IconContainer>
                              {isLoadingCheckSK11 ? (
                                <Loader spinnerSize={20} />
                              ) : (
                                <IconInformation status={statusSK11?.status}>
                                  <Icon width={20} name="information" />
                                </IconInformation>
                              )}
                            </IconContainer>
                          </Tooltip>
                        </>
                      )}
                      {(prevParams?.subRegistry?.toUpperCase() === "N_BLOCK" ||
                        prevParams?.subRegistry?.toUpperCase() === "CONSUMER_2" ||
                        prevParams?.subRegistry?.toUpperCase() === "AREA" ||
                        prevParams?.subRegistry?.toUpperCase() === "SECHEN" ||
                        prevParams?.subRegistry?.toUpperCase() === "AREA_2" ||
                        prevParams?.subRegistry?.toUpperCase() === "POWER_SYSTEM" ||
                        prevParams.registry === "adder") && (
                        <ButtonsGroupStyledSk11
                          items={[
                            { value: "sk11", label: "Другие классы" },
                            {
                              value: "generic",
                              label: (
                                <>
                                  GenericPSR:
                                  {statusGenericSK11.status ? (
                                    statusGenericSK11?.status === "CREATED" ? (
                                      <>
                                        <>Загрузка</>
                                        <StatusGenerics status="WAIT">
                                          <Icon name={"wait"} width={16} />
                                        </StatusGenerics>
                                      </>
                                    ) : statusGenericSK11?.status === "FAILED" ? (
                                      <>
                                        <>Ошибка</>
                                        <StatusGenerics status="ERROR">
                                          <Icon name={"error"} width={16} />
                                        </StatusGenerics>
                                      </>
                                    ) : (
                                      <>
                                        <>Перейти</>
                                        <StatusGenerics status="DONE">
                                          <Icon name={"success"} width={14} />
                                        </StatusGenerics>
                                      </>
                                    )
                                  ) : (
                                    <>
                                      <>Получить</>
                                      <StatusGenerics>
                                        <Icon name={"unavailable"} width={16} />
                                      </StatusGenerics>
                                    </>
                                  )}
                                </>
                              ),
                            },
                          ]}
                          selectedValue={typeTableSk11}
                          onClick={async (value) => {
                            nsiStore.resetSearch();
                            nsiStore.resetSearchSK11();
                            if (value === "sk11") {
                              setExpandedRowIds([]);
                              localStorage.setItem(`expandedRowIds-88`, JSON.stringify([]));
                              setData([]);
                              setDataOriginal([]);
                              setTypeTableSk11(value);
                              setFiltersSK11([]);
                            } else {
                              if (statusGenericSK11?.status === "ACTIVE") {
                                setIsLoadDataDB(true);
                                localStorage.setItem("status-sk11", "GENERIC");
                                setTypeTableSk11(value);
                                setExpandedRowIds([]);
                                setData([]);
                                setDataOriginal([]);
                                setFiltersSK11([]);
                                localStorage.setItem(`expandedRowIds-88`, JSON.stringify([]));
                              } else {
                                await nsiStore.startGeneric().then((taskId: string) => {
                                  setTaskIdGeneric(taskId);
                                });
                              }
                            }
                          }}
                          widthButton={200}
                        />
                      )}
                    </>
                  )}
                </>
              }
            />
          </TableContainer>
        </Right>
      </Content>
    </ModalStyled>
  );
});
