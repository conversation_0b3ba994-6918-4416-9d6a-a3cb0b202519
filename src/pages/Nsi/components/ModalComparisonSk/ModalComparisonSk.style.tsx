import styled, { css } from "styled-components";
import { ButtonsGroup } from "components/ButtonsGroup";
import { Button } from "components/Button";
import { Combobox } from "components/Combobox";
import { Icon } from "components/Icon";

export const UIDLabel = styled.div`
  color: ${(p) => p.theme.textColor};
  width: 220px;
`;

export const IconContainer = styled.div`
  margin-left: 4px;
`;

export const ContentSK11 = styled.div`
  padding: 6px;
  display: flex;
  align-items: center;
  min-width: 200px;
  //max-width: 300px;
  //justify-content: center;
`;

export const StatusSK11 = styled.div<{ status?: string }>`
  width: 14px;
  height: 14px;
  min-width: 14px;
  min-height: 14px;
  border-radius: 50%;
  background-color: ${(p) => p.theme.gray};
  margin-right: 4px;
  ${(p) =>
    p.status === "ACTIVE" &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport} !important;
    `}
  ${(p) =>
    p.status === "CREATED" &&
    css`
      background-color: ${(p) => p.theme.orangeActiveSupport} !important;
    `}
  ${(p) =>
    p.status === "FAILED" &&
    css`
      background-color: ${(p) => p.theme.redActiveSupport} !important;
    `}
  ${(p) =>
    p.status === "TO_DELETE" &&
    css`
      background-color: ${(p) => p.theme.gray} !important;
    `}
`;

export const IconInformation = styled.div<{ status?: string }>`
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 2px;
  cursor: pointer;
  margin-left: 4px;
  transition: all 0.3s;
  // color: ${(p) => p.theme.primaryColor};
  color: ${(p) => p.theme.gray} !important;
  &:hover {
    transform: scale(1.4);
  }
  ${(p) =>
    p.status === "ACTIVE" &&
    css`
      color: ${(p) => p.theme.greenActiveSupport} !important;
    `}
  ${(p) =>
    p.status === "CREATED" &&
    css`
      color: ${(p) => p.theme.orangeActiveSupport} !important;
    `}
  ${(p) =>
    p.status === "FAILED" &&
    css`
      color: ${(p) => p.theme.redActiveSupport} !important;
    `}
  ${(p) =>
    p.status === "TO_DELETE" &&
    css`
      color: ${(p) => p.theme.gray} !important;
    `}
`;

export const ButtonLoadSK11 = styled(Button)`
  border: solid 1px ${(p) => p.theme.lightGray};
  transition: all 0.3s;

  &:hover {
    background-color: ${(props) => props.theme.primaryColor};
    color: #ffffff;
    border: solid 1px ${(p) => p.theme.lightGray};
  }
`;

export const TypeContainer = styled.div`
  width: 100%;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${(p) => p.theme.textColor};
`;

export const Action = styled.div`
  height: 20px;
  width: 20px;
  margin: 5px 4px 0 4px;
  transition: all 0.3s;
  &:hover {
    color: ${(props) => props.theme.primaryColor};
  }

  &:active {
    color: ${(props) => props.theme.primaryColorActive};
  }
`;

export const MenuIconContainer = styled.div`
  margin: 0 10px;
`;

export const ProtocolContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const TableContainer = styled.div`
  height: calc(100vh - 68px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 106px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 102px);
  }
`;

export const NameContainer = styled.div<{ width?: number }>`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  //width: 410px;
  ${(p) =>
    p.width &&
    css`
      width: ${p.width}px;
    `}
`;

export const SearchUID = styled(Icon)`
  margin-left: 10px;
  opacity: 0.7;
  cursor: pointer;
  color: ${(p) => p.theme.textColor};
  &:hover {
    opacity: 1;
  }
`;

export const ButtonCSV = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: ${(p) => p.theme.blueActiveSupport};
  transition: all 0.3s;
  padding: 0 6px;
  border-radius: 4px;
  user-select: none;
  &:hover {
    background-color: ${(p) => p.theme.lightGray};
  }
  &:active {
    background-color: ${(p) => p.theme.gray};
  }
`;

export const ModalStyled = styled.div`
  //width: 1600px;
  //height: 900px;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  width: 100%;
  height: 87%;
  //height: 836px;
  //@media (min-height: 1000px) and (max-height: 1080px) {
  //  height: 956px;
  //}
`;

export const ActionBar = styled.div`
  width: 100%;
  height: 24px; //50
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  border-radius: 6px;
  display: flex;
  align-items: center;
  //padding: 0 20px;
`;

export const ButtonStyled = styled(Button)<{ isDistribution?: boolean; isProtocol?: boolean; isActive?: boolean }>`
  margin: 0 5px;
  //height: 28px; //40px
  width: auto;
  ${(p) =>
    p.isDistribution &&
    css`
      min-width: 300px;
    `}
  ${(p) =>
    p.isProtocol &&
    css`
      min-width: 136px;
      position: relative;
      border: solid 1px transparent;
      &:hover {
        background-color: transparent;
        border: solid 1px ${(p) => p.theme.lightGray};
      }
      &:active {
        color: ${(p) => p.theme.primaryColor};
      }
    `}
  ${(p) =>
    p.isActive &&
    css`
      border: solid 1px ${(p) => p.theme.lightGray};
    `}
`;

export const Circle = styled.div<{ status?: string }>`
  width: 16px;
  height: 16px;
  border-radius: 50%;
  //position: absolute;
  //left: 5px;
  //top: 5px;
  display: flex;
  margin: 0 10px;
  ${(p) =>
    p.status === "NOT_FULLY_SENT" &&
    css`
      background-color: ${(p) => p.theme.orangeActiveSupport};
    `}
  ${(p) =>
    p.status === "ERROR" &&
    css`
      background-color: ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.status === "DONE" &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport};
    `}
`;

export const CopyCell = styled.div`
  cursor: pointer;
  opacity: 0.7;
  color: ${(p) => p.theme.textColor};
  &:hover {
    opacity: 1;
  }
`;

export const TooltipContent = styled.div`
  width: 600px;
  min-height: 30px;
  max-height: 400px;
  overflow: auto;
  z-index: 1000;
  position: absolute;
  top: 30px;
  right: 0;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  border: solid 1px ${(p) => p.theme.lightGray};
  box-shadow: 0 1px 10px rgb(0 0 0 / 5%), 0 2px 4px rgb(0 0 0 / 10%);
  border-radius: 8px;
  color: ${(p) => p.theme.textColor};
`;

export const RowTooltipContainer = styled.div`
  width: 100%;
  height: 20px;
  display: flex;
  //padding: 0 10px;
  border-bottom: solid 1px ${(p) => p.theme.lightGray};
}
  &:hover {
    background-color: ${(p) => p.theme.lightGray};
  }
`;

export const BodyTooltipContent = styled.div`
  margin-top: 30px;
`;

export const ComboboxStyled = styled(Combobox)`
  margin-left: 10px;
`;

export const Content = styled.div`
  width: 100%;
  //height: 720px;
  //height: 100%;
  height: 100%;
  display: flex;
`;

export const Left = styled.div`
  border-right: solid 1px ${(p) => p.theme.lightGray};
  width: 50%;
  //padding: 5px;
`;

export const Right = styled.div`
  width: 50%;
  //padding: 5px;
`;

export const Connect = styled.div<{ isClose?: boolean; isChoose?: boolean; icon?: any; disabled?: any; isError?: any }>`
  width: 20px;
  height: 20px;
  //margin: 0 2px;
  //min-height: 20px;
  //transition: all 0.3s;
  //width: 16px;
  //height: 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  user-select: none;
  display: flex;
  flex-direction: row;
  justify-content: center;
  background: transparent;
  //color: ${(props) => props.theme.buttonSecondaryColor};

  &:hover {
    background-color: ${(props) => props.theme.lightGray};
    border-color: ${(props) => props.theme.primaryColorHover};
  }

  &:active {
    color: ${(props) => props.theme.buttonSecondaryColorActive};
    border-color: ${(props) => props.theme.buttonSecondaryColorActive};
  }

  ${(p) =>
    p.isError &&
    css`
      background-color: ${(p) => p.theme.redActiveSupport} !important;
      color: ${(p) => p.theme.white} !important;
    `}
  color: ${(p) => p.theme.primaryColor} !important;
  ${(p) =>
    p.isClose &&
    css`
      // color: ${(p) => p.theme.gray} !important;
      color: ${(p) => p.theme.textColor};
      &:hover {
        background-color: transparent;
        color: ${(p) => p.theme.primaryColor} !important;
      }
      &:active {
        color: ${(p) => p.theme.gray} !important;
      }
    `}
  ${(p) =>
    p.isChoose &&
    css`
      color: ${(p) => p.theme.greenActiveSupport} !important;
    `}
  ${(p) =>
    p.icon === "settings" &&
    css`
      padding-top: 3px !important;
    `}
  ${(p) =>
    p.icon === "system" &&
    css`
      padding-top: 3px !important;
    `}
  ${(p) =>
    p.icon === "viewPassword" &&
    css`
      padding-top: 2px !important;
    `}
  ${(p) =>
    p.disabled &&
    css`
      background-color: ${(p) => p.theme.lightGray} !important;
      color: ${(p) => p.theme.textColor} !important;
      cursor: no-drop !important;
    `}
  margin: 0 4px;
`;

// ${(p) =>
//     p.icon === "close" &&
//     css`
//       margin-top: 2px !important;
//     `}

export const ConnectionMenu = styled.div`
  position: absolute;
  left: 5px;
  right: 0;
  top: 20px;
  border: solid 1px ${(p) => p.theme.lightGray};
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  width: 200px;
`;

export const ConnectionMenuRow = styled.div`
  width: 100%;
  height: 20px;
  cursor: pointer;
  padding: 0 2px;
  display: flex;
  align-items: center;
  &:hover {
    background-color: ${(p) => p.theme.blueActiveSupport};
    color: ${(p) => p.theme.white};
  }
`;

export const SettingUpCharacteristics = styled(Button)<{ isClose?: boolean }>`
  width: 20px;
  height: 20px;
  //margin-left: 10px;
  //margin-right: auto;
  //position: absolute;
  //left: 27px;
  //right: 20px;
  //top: -3px;
  color: ${(p) => p.theme.gray};
  transition: all 0.3s;
  &:hover {
    background-color: transparent;
    color: ${(p) => p.theme.primaryColor};
  }
  &:active {
    color: ${(p) => p.theme.gray};
  }
`;

export const NameLabel = styled.div`
  position: relative;
  width: 100%;
  height: 14px;
  display: flex;
  align-items: center;
`;

export const ButtonsGroupStyled = styled(ButtonsGroup)`
  margin-left: auto;
  margin-right: 10px;
`;

export const HeaderTooltipContent = styled.div`
  width: 588px;
  height: 30px;
  display: flex;
  align-items: center;
  border-bottom: solid 1px ${(p) => p.theme.lightGray};
  border-top: solid 1px transparent;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  position: fixed;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
`;

export const RightActionMenu = styled.div`
  margin-left: auto;
  margin-right: 10px;
  display: flex;
`;

export const CopyContent = styled.div`
  width: 160px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${(p) => p.theme.primaryColor};
  color: ${(p) => p.theme.white};
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  border: solid 1px ${(p) => p.theme.lightGray};
`;

export const ButtonsGroupStyledSk11 = styled(ButtonsGroup)`
  margin-left: auto;
  margin-right: 10px;
`;

export const StatusGenerics = styled.div<{ status?: "DONE" | "ERROR" | "WAIT" }>`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  padding-top: 2px;
  ${(p) =>
    p.status === "DONE" &&
    css`
      color: ${(p) => p.theme.greenActiveSupport};
    `}
  ${(p) =>
    p.status === "ERROR" &&
    css`
      color: ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.status === "WAIT" &&
    css`
      color: ${(p) => p.theme.orangeActiveSupport};
    `}
`;

export const SpinnerContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const LoaderContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const LabelLoader = styled.div`
  margin-left: 10px;
`;
