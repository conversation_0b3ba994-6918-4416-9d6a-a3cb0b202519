import React, { useEffect, useState } from "react";
import styled from "styled-components";
import { Modal } from "../../../../components/Modal";
import { useStores } from "../../../../stores/useStore";
import { observer } from "mobx-react";
import { Loader } from "../../../../components/Loader";
import { Table } from "components/Table";
import { toJS } from "mobx";
import { prepareDataTable } from "../../../../utils";

export const ModalStyled = styled(Modal)`
  //width: 1300px;
  //height: 800px;
`;
export const Container = styled.div`
  width: 100%;
  height: 750px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  border: 1px solid ${(p) => p.theme.lightGray};
  border-radius: 8px;
`;

export const InfoItem = styled.div`
  width: 100%;
  height: 38px;
  min-height: 38px;
  border-bottom: 1px solid ${(p) => p.theme.lightGray};
  padding: 0 10px;
  display: flex;
  align-items: center;

  &:hover {
    background-color: ${(p) => p.theme.backgroundColor};
  }
`;

export const LoaderContainer = styled.div`
  width: 100%;
  height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const NameText = styled.div`
  color: ${(p) => p.theme.textColor};
`;

export const ModalSync = observer((props: any) => {
  const { onCancel } = props;
  const { nsiStore, tableStore } = useStores();
  const { isLoadingModalSync, infoModalSync } = nsiStore;

  useEffect(() => {
    nsiStore.initModalSync();
  }, []);

  const defaultColumns: any[] = [
    { name: "child", width: 100, title: " " },
    { name: "date", width: 900, title: "Действие", align: "left" },
    { name: "initiator", width: 200, title: "Инициатор", align: "left" },
  ];

  const [columns, setColumns] = useState<any>([]);

  useEffect(() => {
    tableStore.getTableParams("modalSync_v2").then((data: any) => {
      const isWidth = data?.some((el: any) => !Number(el.width));
      if (data && !isWidth) {
        setColumns(data);
      } else {
        setColumns(defaultColumns);
      }
    });
  }, []);

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("modalSync_v2").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "date", direction: "desc" }]);
      }
    });
    return () => {
      localStorage.removeItem("expandedRowIds-modalSync");
    };
  }, []);

  return (
    <ModalStyled isOverLay title="История загрузки НСИ" onCancel={() => onCancel()} width={1300} height={800} dataTestContainer="download-history-nsi-modal.container">
      <Table
        isLoading={isLoadingModalSync}
        childrenKey="child"
        tableKey={`modalSync_v2`}
        columns={columns}
        setColumns={setColumns}
        defaultColumns={defaultColumns}
        tableData={prepareDataTable(infoModalSync)}
        heightContainer={680}
        tableHeight={680}
        disabledSearches={["child"]}
        initSorting={initSorting}
        dataTestRows="download-history-nsi-modal-table.item-row"
        customCells={[
          {
            name: "date",
            render: (value: any, row: any) => {
              return <NameText data-test="download-history-nsi-modal-table.date-cell">{row.name}</NameText>;
            },
            tooltip: (value: any, row: any) => {
              return row.name ?? "";
            },
          },
          {
            name: "initiator",
            render: (value: any) => {
              return <NameText data-test="download-history-nsi-modal-table.initiator-cell">{value ? value : ""}</NameText>;
            },
            tooltip: (value: any, row: any) => {
              return row.name ?? "";
            },
          },
        ]}
      />
    </ModalStyled>
  );
});
