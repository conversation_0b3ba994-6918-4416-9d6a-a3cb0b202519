import React, { FC, useEffect, useRef, useState } from "react";
import { Modal } from "../../../../components/Modal";
import styled from "styled-components";
import { Table } from "../../../../components/Table";
import { observer } from "mobx-react";
import { useStores } from "../../../../stores/useStore";
import { NameLabel, NameText } from "../DistributionContainer/DistributionContainer.constant";
import { prepareDataTable } from "../../../../utils";

export const ModalStyled = styled(Modal)`
  //width: 700px;
  //height: 700px;
`;
export const Content = styled.div<{ height?: number }>`
  //height: 570px; //700
  height: ${(p) => p.height}px;
  padding: 10px 0;
`;

export const customCells = [
  {
    name: "name",
    render: (value: any, row: any) => {
      const getLevel = (type: any) => {
        if (type === "CDU") {
          return 0;
        }
        if (type === "ODU") {
          return 1;
        }
        if (type === "RDU") {
          return 2;
        }
      };
      const level = getLevel(row.type);
      return (
        <NameLabel>
          {/*<ElementTree level={level} />*/}
          <NameText>{value}</NameText>
        </NameLabel>
      );
    },
  },
];
interface ModalInputDcProps {
  onCancel: any;
  selected: any;
  setSelected: any;
  onConfirm: any;
}

export const ModalInputDc: FC<ModalInputDcProps> = observer((props) => {
  const { onCancel, selected, setSelected, onConfirm } = props;
  const { nsiStore } = useStores();

  const { dcListAtTask, isLoadingModalInputDC } = nsiStore;

  useEffect(() => {
    nsiStore.initModalInputDC();
  }, []);

  const defaultColumns: any[] = [{ name: "name", title: "Название ДЦ", width: 600, isSearch: true }];
  const [columns, setColumns] = useState(defaultColumns);

  const [expandedRowIds, setExpandedRowIds] = useState<any>([]);

  const prepareRows = (data: any, parentId: any) => {
    let result: any[] = [];
    data.map((el: any) => {
      let childs = [];
      if (el?.childs?.length > 0) {
        childs = prepareRows(el?.childs, el.tabId);
      }
      result.push({ ...el, parentId });
      if (childs?.length > 0) {
        childs.map((item) => {
          result.push({ ...item });
        });
      }
    });
    return result;
  };

  const rows = prepareRows(dcListAtTask, 0);

  useEffect(() => {
    const newState = rows.map((el) => el.tabId);
    setExpandedRowIds(newState);
  }, [dcListAtTask]);

  const refModal = useRef(null);

  const heightContent = 570; //TODO RESIZE

  const [modalHeight, setModalHeight] = useState(700);

  return (
    <ModalStyled
      title="Распространение НСИ"
      onCancel={onCancel}
      cancelText="Отмена"
      confirmText="Отправить"
      onConfirm={onConfirm}
      description="Выберите ДЦ из списка"
      isDisabledConfirm={selected.length === 0}
      width={700}
      height={700}
      setModalHeight={setModalHeight}
      dataTestContainer="distribution-nsi-modal.container"
      dataTestConfirmButton="distribution-nsi-modal.distribute-button"
    >
      <Content height={modalHeight - 130}>
        <Table
          columns={columns}
          setColumns={setColumns}
          tableData={prepareDataTable(dcListAtTask)}
          isLoading={isLoadingModalInputDC}
          tableKey={89}
          defaultColumns={defaultColumns}
          selected={selected}
          setSelected={setSelected}
          selectedMode="many"
          customCells={customCells}
          childrenKey="name"
          expandedRowIds={expandedRowIds}
          setExpandedRowIds={setExpandedRowIds}
          dataTestRows="distribution-nsi-modal-table.row"
          dataTestCheckboxCells="distribution-nsi-modal-table.checkbox-cell"
        />
      </Content>
    </ModalStyled>
  );
});
