import React, { useEffect, useState } from "react";
import styled from "styled-components";
import { Table } from "components/Table";
import { observer } from "mobx-react";
import { useStores } from "stores/useStore";
import { Checkbox } from "components/Checkbox";
import { Button } from "components/Button";
import { isModeCenter } from "../../../../../../utils/getMode";
import { prepareDataTable } from "../../../../../../utils";
import { getWidthModal } from "../../../../../../helpers/adaptive";

export const Buttons = styled.div`
  //width: 100%;
  display: flex;
  //justify-content: flex-end;
  margin-left: auto;
  margin-right: 10px;
`;

export const ButtonStyled = styled(Button)`
  margin: 0 4px;
  width: 160px;
  //height: 28px;
`;

export const Container = styled.div`
  width: 100%;
  height: 100%;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
`;

export const TableContainer = styled.div`
  height: calc(100vh - 40px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 78px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 74px);
  }
`;

export const RecordEg = observer(() => {
  let defaultColumns: any[];

  const [columnOrder, setColumnOrder] = useState<any>([]);

  const setDefaultColumns = () => {
    setColumns(() => {
      return columnOrder.map((el: any) => {
        return getDefaultColumns().find((item) => item.name === el);
      });
    });
  };
  const [initScreenWidth, setInitScreenWidth] = useState<any>(getWidthModal(document.documentElement.clientWidth));
  const [screenWidth, setScreenWidth] = useState<any>(initScreenWidth);
  const [isEditWidth, setEditWidth] = useState(false);

  const resizeWeb = () => {
    setScreenWidth(getWidthModal(document.documentElement.clientWidth));
  };

  useEffect(() => {
    window.addEventListener("resize", resizeWeb);
    return () => {
      window.removeEventListener("resize", resizeWeb);
    };
  }, []);

  useEffect(() => {
    if (initScreenWidth !== screenWidth && !isEditWidth) {
      setEditWidth(true);
      setDefaultColumns();
    } else if (initScreenWidth !== screenWidth && isEditWidth) {
      setDefaultColumns();
    } else if (initScreenWidth === screenWidth && isEditWidth) {
      setDefaultColumns();
    }
  }, [screenWidth]);

  const getDefaultColumns = () => {
    if (screenWidth >= 650 && screenWidth < 700) {
      return [
        { name: "name", title: "Название", width: 210, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 210 },
        { name: "accepted", title: "Акцептован", width: 210 },
      ];
    }
    if (screenWidth >= 700 && screenWidth < 800) {
      return [
        { name: "name", title: "Название", width: 230, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 230 },
        { name: "accepted", title: "Акцептован", width: 230 },
      ];
    }
    if (screenWidth >= 800 && screenWidth < 900) {
      return [
        { name: "name", title: "Название", width: 265, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 265 },
        { name: "accepted", title: "Акцептован", width: 265 },
      ];
    }
    if (screenWidth >= 900 && screenWidth < 1024) {
      return [
        { name: "name", title: "Название", width: 298, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 298 },
        { name: "accepted", title: "Акцептован", width: 298 },
      ];
    }
    if (screenWidth >= 1024 && screenWidth < 1152) {
      return [
        { name: "name", title: "Название", width: 340, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 340 },
        { name: "accepted", title: "Акцептован", width: 340 },
      ];
    }
    if (screenWidth >= 1152 && screenWidth < 1280) {
      return [
        { name: "name", title: "Название", width: 382, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 382 },
        { name: "accepted", title: "Акцептован", width: 382 },
      ];
    }
    if (screenWidth >= 1280 && screenWidth < 1400) {
      return [
        { name: "name", title: "Название", width: 424, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 424 },
        { name: "accepted", title: "Акцептован", width: 424 },
      ];
    }
    if (screenWidth >= 1400 && screenWidth < 1600) {
      return [
        { name: "name", title: "Название", width: 460, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 460 },
        { name: "accepted", title: "Акцептован", width: 460 },
      ];
    }
    if (screenWidth >= 1600 && screenWidth < 1700) {
      return [
        { name: "name", title: "Название", width: 530, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 530 },
        { name: "accepted", title: "Акцептован", width: 530 },
      ];
    }
    if (screenWidth >= 1700 && screenWidth < 1800) {
      return [
        { name: "name", title: "Название", width: 560, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 560 },
        { name: "accepted", title: "Акцептован", width: 560 },
      ];
    }
    if (screenWidth >= 1800 && screenWidth < 1900) {
      return [
        { name: "name", title: "Название", width: 600, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 600 },
        { name: "accepted", title: "Акцептован", width: 600 },
      ];
    }
    if (screenWidth >= 1900 && screenWidth < 2000) {
      return [
        { name: "name", title: "Название", width: 630, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 630 },
        { name: "accepted", title: "Акцептован", width: 630 },
      ];
    }
    if (screenWidth >= 2000 && screenWidth < 2100) {
      return [
        { name: "name", title: "Название", width: 660, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 660 },
        { name: "accepted", title: "Акцептован", width: 660 },
      ];
    }
    if (screenWidth >= 2100 && screenWidth < 2200) {
      return [
        { name: "name", title: "Название", width: 698, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 698 },
        { name: "accepted", title: "Акцептован", width: 698 },
      ];
    }
    if (screenWidth >= 2200 && screenWidth < 2560) {
      return [
        { name: "name", title: "Название", width: 730, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 730 },
        { name: "accepted", title: "Акцептован", width: 730 },
      ];
    }
    if (screenWidth >= 2560 && screenWidth < 3000) {
      return [
        { name: "name", title: "Название", width: 850, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 850 },
        { name: "accepted", title: "Акцептован", width: 850 },
      ];
    }
    if (screenWidth >= 3000 && screenWidth < 3840) {
      return [
        { name: "name", title: "Название", width: 998, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 998 },
        { name: "accepted", title: "Акцептован", width: 998 },
      ];
    }
    if (screenWidth >= 3840 && screenWidth < 4000) {
      return [
        { name: "name", title: "Название", width: 1280, isSearch: true, isSort: "alphabet" },
        { name: "received", title: "Получен", width: 1280 },
        { name: "accepted", title: "Акцептован", width: 1280 },
      ];
    }
    return [
      { name: "name", title: "Название", width: 630, isSearch: true, isSort: "alphabet" },
      { name: "received", title: "Получен", width: 630 },
      { name: "accepted", title: "Акцептован", width: 630 },
    ];
  };

  defaultColumns = getDefaultColumns();

  const { settingsStore, tableStore } = useStores();
  const { recordsEG, recordsEGOriginal } = settingsStore;

  useEffect(() => {
    settingsStore.initRecordEG();
  }, []);

  const [receiveds, setReceiveds] = useState<any[]>([]);
  const [accepteds, setAccepted] = useState<any[]>([]);

  const isDisable = receiveds.length === 0 && accepteds.length === 0;

  const customCells = [
    {
      name: "received",
      render: (value: any, row: any) => {
        const find = receiveds.find((el) => el.tabId === row.tabId) ?? null;
        return (
          <>
            <Checkbox
              status={find ? find.status : value}
              dataTest="eg3-table.received-checkbox"
              // disabled={!isModeCenter}
              onChange={() => {
                setReceiveds((prev) => {
                  const isFind = prev.some((el) => el.tabId === row.tabId);
                  if (isFind) {
                    return prev.filter((el) => el.tabId !== row.tabId);
                  } else {
                  }
                  return [...prev, { tabId: row.tabId, status: !value }];
                });
              }}
            />
          </>
        );
      },
    },
    {
      name: "accepted",
      render: (value: any, row: any) => {
        const find = accepteds.find((el) => el.tabId === row.tabId) ?? null;
        return (
          <>
            <Checkbox
              status={find ? find.status : value}
              dataTest="eg3-table.accepted-checkbox"
              // disabled={!isModeCenter}
              onChange={() => {
                setAccepted((prev) => {
                  const isFind = prev.some((el) => el.tabId === row.tabId);
                  if (isFind) {
                    return prev.filter((el) => el.tabId !== row.tabId);
                  } else {
                  }
                  return [...prev, { tabId: row.tabId, status: !value }];
                });
              }}
            />
          </>
        );
      },
    },
  ];

  const [columns, setColumns] = useState<any[]>([]);

  useEffect(() => {
    tableStore.getTableParams("96").then((data: any) => {
      if (data) {
        setColumns(data);
      } else {
        setColumns(defaultColumns);
      }
    });
  }, []);

  const save = () => {
    const res = recordsEG.map((el: any) => {
      const accepted = accepteds.find((item) => item.tabId === el.tabId);
      const received = receiveds.find((item) => item.tabId === el.tabId);
      return { ...el, accepted: accepted ? accepted?.status : el.accepted, received: received ? received?.status : el.received };
    });

    settingsStore.saveRecordEG(res).then((isSave: boolean) => {
      if (isSave) {
        settingsStore.initRecordEG().then((isSave: boolean) => {
          if (isSave) {
            setReceiveds([]);
            setAccepted([]);
          }
        });
      }
    });
  };

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("96").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "name", direction: "asc" }]);
      }
    });
  }, []);

  return (
    <Container>
      <TableContainer>
        <Table
          tableKey="96"
          columns={columns}
          setColumns={setColumns}
          columnOrder={columnOrder}
          setColumnOrder={setColumnOrder}
          defaultColumns={defaultColumns}
          tableData={prepareDataTable(recordsEG)}
          originalTableData={recordsEGOriginal}
          initSorting={initSorting}
          customCells={customCells}
          dataTest="eg3.table"
          dataTestRows="eg3-table.row"
          headerComponents={
            <Buttons>
              <ButtonStyled title="Сохранить" onClick={() => save()} disabled={isDisable} dataTest="eg3-table-header.save-button" />
              <ButtonStyled
                title="Отменить"
                onClick={() => {
                  setReceiveds([]);
                  setAccepted([]);
                }}
                // type="secondary"
                disabled={isDisable}
              />
            </Buttons>
          }
        />
      </TableContainer>
    </Container>
  );
});
