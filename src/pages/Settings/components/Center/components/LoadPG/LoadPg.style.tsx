import styled, { css } from "styled-components";
import { TextField } from "components/TextField";
import { Button } from "components/Button";
import { Combobox } from "components/Combobox";

export const TableContainer = styled.div`
  height: calc(100vh - 40px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 78px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 74px);
  }
`;

export const Container = styled.div`
  width: 100%;
  height: 100%;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
`;

export const CellAccept = styled.div`
  border: solid 1px transparent;
  padding: 6px 10px;
  border-radius: 8px;
  cursor: pointer;
  width: 100%;
  color: ${(p) => p.theme.textColor} !important;
`;

export const ComboboxCell = styled.div<{ isError?: boolean }>`
  display: flex;
  align-items: center;
  border: solid 1px transparent;
  border-radius: 8px;
  position: relative;
  ${(p) =>
    p.isError &&
    css`
      border: solid 1px ${(p) => p.theme.redActiveSupport};
    `}
`;

export const CloseContainer = styled.div`
  margin: 0 10px;
`;

export const TextFieldStyled = styled(TextField)`
  width: 120px;
  height: 18px;
`;

export const ComboboxStyled = styled(Combobox)`
  width: 245px;
`;

export const Buttons = styled.div`
  //width: 100%;
  display: flex;
  //justify-content: flex-end;
  margin-left: auto;
  margin-right: 10px;
`;

export const ButtonStyled = styled(Button)`
  margin: 0 4px;
  width: 160px;
  //height: 28px;
`;

export const IconContainer = styled.div`
  opacity: 0;
  position: absolute;
  top: 7px;
  right: 16px;
`;

export const TypeContainer = styled.div`
  font-weight: bold;
  width: 281px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: solid 1px transparent;
  padding: 6px 10px;
  border-radius: 8px;
  position: relative;

  &:hover {
    border: solid 1px ${(p) => p.theme.blueActiveSupport};
  }

  &:hover ${IconContainer} {
    opacity: 1;
  }
`;

export const TimeContainer = styled.div<{ isError?: boolean }>`
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border: solid 1px transparent;
  padding: 6px 10px;
  border-radius: 8px;
  position: relative;
  &:hover {
    border: solid 1px ${(p) => p.theme.blueActiveSupport};
  }
  &:hover ${IconContainer} {
    opacity: 1;
  }
  ${(p) =>
    p.isError &&
    css`
      border: solid 1px ${(p) => p.theme.redActiveSupport};
      &:hover {
        border: solid 1px ${(p) => p.theme.redActiveSupport};
      }
    `}
`;

export const ErrorContainer = styled.div`
  color: red;
  position: absolute;
  top: 2px;
  right: 3px;
  z-index: 10;
`;

export const TooltipContainer = styled.div`
  padding: 10px;
  width: 360px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const SecondLabel = styled.div`
  margin-left: 10px;
`;
