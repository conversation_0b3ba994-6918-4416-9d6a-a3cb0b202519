import React, { FC, useEffect, useState } from "react";
import { Checkbox } from "components/Checkbox";
import { Table } from "components/Table";
import { useStores } from "stores/useStore";
import { observer } from "mobx-react";
import { Icon } from "components/Icon";
import { Tooltip } from "components/Tooltip";
import { SegmentedPicker } from "components/SegmentedPicker";
import {
  Container,
  CellAccept,
  ComboboxCell,
  CloseContainer,
  TextFieldStyled,
  ComboboxStyled,
  Buttons,
  ButtonStyled,
  IconContainer,
  TypeContainer,
  TimeContainer,
  ErrorContainer,
  TooltipContainer,
  SecondLabel,
  TableContainer,
} from "./LoadPg.style";
import { prepareDataTable } from "../../../../../../utils";
import { getWidthModal } from "../../../../../../helpers/adaptive";

export const offsetList = [
  { value: 0, label: "0" },
  { value: -1, label: "-1" },
  { value: -2, label: "-2" },
];

export const LoadPg: FC<{ innerHeight?: number }> = observer(() => {
  const { settingsStore, tableStore } = useStores();
  const { pgListForLoadPf, typeAccept, isLoadingLoadPG, pgListForLoadPfOriginal } = settingsStore;
  useEffect(() => {
    settingsStore.initLoadPG();
  }, []);

  const [focus, setFocus] = useState<any>({ row: null, cell: null });

  let defaultColumns: any;

  const [columnOrder, setColumnOrder] = useState<any>([]);

  const setDefaultColumns = () => {
    setColumns(() => {
      return columnOrder.map((el: any) => {
        return getDefaultColumns().find((item) => item.name === el);
      });
    });
  };
  const [initScreenWidth, setInitScreenWidth] = useState<any>(getWidthModal(document.documentElement.clientWidth));
  const [screenWidth, setScreenWidth] = useState<any>(initScreenWidth);
  const [isEditWidth, setEditWidth] = useState(false);

  const resizeWeb = () => {
    setScreenWidth(getWidthModal(document.documentElement.clientWidth));
  };

  useEffect(() => {
    window.addEventListener("resize", resizeWeb);
    return () => {
      window.removeEventListener("resize", resizeWeb);
    };
  }, []);

  useEffect(() => {
    if (initScreenWidth !== screenWidth && !isEditWidth) {
      setEditWidth(true);
      setDefaultColumns();
    } else if (initScreenWidth !== screenWidth && isEditWidth) {
      setDefaultColumns();
    } else if (initScreenWidth === screenWidth && isEditWidth) {
      setDefaultColumns();
    }
  }, [screenWidth]);

  const getDefaultColumns = () => {
    if (screenWidth >= 650 && screenWidth < 700) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 120, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 150, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 620, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 700 && screenWidth < 800) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 120, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 150, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 620, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 800 && screenWidth < 900) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 120, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 150, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 620, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 900 && screenWidth < 1024) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 120, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 150, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 620, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 1024 && screenWidth < 1152) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 120, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 150, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 620, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 1152 && screenWidth < 1280) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 120, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 150, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 680, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 1280 && screenWidth < 1400) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 180, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 210, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 680, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 1400 && screenWidth < 1600) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 240, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 270, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 680, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 1600 && screenWidth < 1700) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 340, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 370, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 680, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 1700 && screenWidth < 1800) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 390, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 420, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 680, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 1800 && screenWidth < 1900) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 440, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 470, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 680, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 1900 && screenWidth < 2000) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 490, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 520, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 680, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 2000 && screenWidth < 2100) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 540, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 570, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 680, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 2100 && screenWidth < 2200) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 590, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 620, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 680, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 2200 && screenWidth < 2560) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 640, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 670, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 680, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 2560 && screenWidth < 3000) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 820, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 850, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 680, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 3000 && screenWidth < 3840) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 1040, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 1070, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 680, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    if (screenWidth >= 3840 && screenWidth < 4000) {
      return [
        { name: "pgType", title: "Тип ПГ", width: 1460, isSearch: true, isSort: "alphabet" },
        { name: "loadOffsetInDays", title: "Смещение", width: 1490, isSearch: true, isSort: "alphabet" },
        { name: "accept", title: "Способ акцепта", width: 680, isSearch: true },
        { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
      ];
    }
    return [
      { name: "pgType", title: "Тип ПГ", width: 490, isSearch: true, isSort: "alphabet" },
      { name: "loadOffsetInDays", title: "Смещение", width: 520, isSearch: true, isSort: "alphabet" },
      { name: "accept", title: "Способ акцепта", width: 680, isSearch: true },
      { name: "addersCalculation", title: "Расчет сумматоров", width: 200 },
    ];
  };

  defaultColumns = getDefaultColumns();

  const [columns, setColumns] = useState<any>([]);

  useEffect(() => {
    tableStore.getTableParams("92").then((data: any) => {
      if (data) {
        setColumns(data);
      } else {
        setColumns(defaultColumns);
      }
    });
    return () => {
      localStorage.removeItem(`rowEdit`);
    };
  }, []);

  const [editTypes, setEditTypes] = useState<any[]>([]);
  const [editTypesOriginal, setEditTypesOriginal] = useState<any[]>([]);
  const [editTimes, setEditTimes] = useState<any[]>([]);
  const [editTimesOriginal, setEditTimesOriginal] = useState<any[]>([]);
  const [editChecked, setEditChecked] = useState<number[]>([]);
  const [editOffset, setEditOffset] = useState<any[]>([]);
  const [focusHeader, setFocusHeader] = useState<any>(null);
  const [isTimeChanged, setIsTimeChanged] = useState(false);

  const [isDisabled, setIsDisabled] = useState(true);

  useEffect(() => {
    setEditTypes(pgListForLoadPf.map((el: any) => ({ value: el.acceptValue, id: el.tabId })));
    setEditTypesOriginal(pgListForLoadPf.map((el: any) => ({ value: el.acceptValue, id: el.tabId })));
    setEditTimes(pgListForLoadPf.map((el: any) => ({ value: el.acceptTime ? String(el.acceptTime) : null, id: el.tabId })));
    setEditTimesOriginal(pgListForLoadPf.map((el: any) => ({ value: el.acceptTime ? String(el.acceptTime) : null, id: el.tabId })));
  }, [pgListForLoadPf]);
  const [originalChecked, setOriginalChecked] = useState<number[]>([]);

  useEffect(() => {
    const initChecked: number[] = pgListForLoadPf.filter((el: any) => el.addersCalculation === true).map((el: any) => el.tabId);
    setOriginalChecked(initChecked);
    setEditChecked(initChecked);
  }, [pgListForLoadPf]);
  const [errors, setErrors] = useState<any[]>([]);

  const customCell = [
    {
      name: "accept",
      render: (value: any, row: any) => {
        const type = editTypes.find((el) => el.id === row.tabId)?.value ?? null;
        const time = editTimes.find((el) => el.id === row.tabId)?.value ?? null;
        const isError = errors.some((el) => el === row.tabId);
        // const localCur = JSON.parse(localStorage.getItem(`rowEdit`) as string) ?? null;
        // const initCurrent = localCur && localCur?.key === row.tabId ? localCur?.value : time ?? "";
        const localStorageArr = JSON.parse(localStorage.getItem(`rowEdit`) as string) ?? [];
        const localCur = localStorageArr && localStorageArr?.length > 0 ? localStorageArr?.find((el: any) => el.key === `${row.tabId}-time`) ?? null : null;
        const initCurrent = localCur ? localCur?.value : time ?? "";
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [current, setCurrent] = useState(initCurrent);
        const initFocus = focus.row === row.tabId && "accept" === focus.cell && focusHeader === null;
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [isFocus, setIsFocus] = useState(initFocus);
        const originalValue = time ? time : "";
        const isChange = current !== originalValue;
        return (
          <>
            <CellAccept
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                if (!type) {
                  setEditTypes((prev) => {
                    return [...prev, { id: row.tabId, value: row.acceptValue }];
                  });
                }
                setErrors([]);
              }}
            >
              {type ? (
                <ComboboxCell>
                  <SegmentedPicker
                    items={typeAccept}
                    selectedItems={type}
                    dataTest="load-pg-table.type-accept-item"
                    onChange={({ value }: { value: any }) => {
                      setEditTypes((prev) => {
                        return prev.map((el) => {
                          if (el.id === row.tabId) {
                            return { ...el, value };
                          }
                          return el;
                        });
                      });
                      setIsDisabled(false);
                    }}
                  />
                </ComboboxCell>
              ) : (
                <TypeContainer>
                  {row.acceptType}
                  <IconContainer>
                    <Icon width={20} name="pencil" />
                  </IconContainer>
                </TypeContainer>
              )}
            </CellAccept>
            {type === "AUTO" && (
              <>
                <CellAccept>{type === "AUTO" ? "через (секунд)" : "согласование через (секунд)"}</CellAccept>
                <CellAccept
                  onFocus={() => {
                    if (focus.row === null && focus.cell === null) {
                      setFocus({ row: row.tabId, cell: "accept" });
                    }
                    setIsFocus(true);
                  }}
                  onBlur={() => {
                    setIsFocus(false);
                    // setFocus({ row: null, cell: null });
                  }}
                >
                  <TextFieldStyled
                    value={current}
                    focus={isFocus}
                    data-time={row.tabId}
                    errors={isError ? ["Диапазон должен быть от 5 до 180 секунд"] : null}
                    isChange={isChange}
                    onChange={(e) => {
                      const value = e.target.value
                        .split("")
                        .filter((el) => el !== "." && el !== "," && el !== "/")
                        .join("");
                      setCurrent(value);
                      const finalArr = [...localStorageArr?.filter((el: any) => el.key !== `${row.tabId}-time`), { key: `${row.tabId}-time`, value: value }];
                      localStorage.setItem(`rowEdit`, JSON.stringify(finalArr));
                      setIsTimeChanged(true);
                    }}
                  />
                </CellAccept>
              </>
            )}
          </>
        );
      },
      tooltip: (value: any, row: any) => {
        const type = editTypes.find((el) => el.id === row.tabId)?.value ?? null;
        return type ? (type === "AUTO" ? "Автоматический" : "Ручной") : row?.acceptType ?? null;
      },
    },
    {
      name: "addersCalculation",
      render: (value: any, row: any) => {
        const status = editChecked.some((el) => el === row.tabId);
        return (
          <Checkbox
            readonly
            status={status}
            dataTest="load-pg-table.adders-calculation-checkbox"
            onChange={() => {
              setEditChecked((prev) => {
                const isFind = prev.find((el) => el === row.tabId);
                if (isFind || isFind === 0) {
                  return prev.filter((el) => el !== row.tabId);
                } else {
                  return [...prev, row.tabId];
                }
              });
              setIsDisabled(false);
            }}
          />
        );
      },
    },
    {
      name: "loadOffsetInDays",
      render: (valueCell: any, row: any) => {
        const selectedValue = editOffset.find((el) => el.id === row.tabId)?.value ?? valueCell;
        return (
          <>
            <SegmentedPicker
              items={offsetList}
              selectedItems={selectedValue}
              dataTest="load-pg-table.offset-item"
              onChange={({ value }: any) => {
                setEditOffset((prev: any) => {
                  const result = prev.filter((el: any) => el.id !== row.tabId);
                  if (value !== valueCell) {
                    return [...result, { id: row.tabId, value: value }];
                  } else {
                    return [...result];
                  }
                });
                setIsDisabled(false);
              }}
            />
          </>
        );
      },
    },
  ];

  const isEditTypes =
    editTypes.length == editTypesOriginal.length &&
    editTypes.every(function (element: any, index: any) {
      return element.value === editTypesOriginal[index].value;
    });

  const isEditChecked = editChecked.length === originalChecked.length && [...editChecked].sort().every((value, index) => value === [...originalChecked].sort()[index]);

  const getDisabled = () => {
    return isEditTypes && isEditChecked && editOffset.length === 0 && !isTimeChanged;
  };

  const disabled = getDisabled();

  const saveLoadPG = async () => {
    const row = focus?.row ?? null;
    const cell = focus.cell ?? null;
    setFocus({ row: null, cell: null });
    let times: any = [];
    const collectionOfElements = document.querySelectorAll("[data-time]");
    for (let i = 0; i < collectionOfElements.length; i++) {
      // @ts-ignore
      const key = collectionOfElements[i].getAttribute("data-time");
      // @ts-ignore
      times[i] = { key, value: collectionOfElements[i]?.value ?? null };
    }
    const timeRes = editTimes.map((el) => {
      const find = times.find((item: any) => item.key === el.id);
      if (find) {
        return { ...el, value: find.value };
      }
      return el;
    });
    setEditTimes(timeRes);
    const isValid = validate(timeRes);
    if (!disabled && !isValid) {
      await settingsStore
        .saveLoadPG(editTypes, timeRes, editChecked, editOffset)
        .then(() => {
          localStorage.removeItem(`rowEdit`);
          setEditOffset([]);
          setIsTimeChanged(false);
        })
        .catch(() => {
          setFocus({ row, cell });
        });
    } else {
      setFocus({ row, cell });
    }
  };

  const unique = (arr: any[]) => {
    let result: any[] = [];

    for (let str of arr) {
      if (!result.includes(str)) {
        result.push(str);
      }
    }

    return result;
  };

  function isNumeric(str: number) {
    if (typeof str != "string") return false; // we only process strings!
    return (
      !isNaN(str) && // use type coercion to parse the _entirety_ of the string (`parseFloat` alone does not do this)...
      !isNaN(parseFloat(str))
    ); // ...and ensure strings of whitespace fail
  }

  const validate = (editTimes: any) => {
    setErrors([]);
    let result: any[] = [];
    const filteredEditTypes = editTypes.filter((el) => el.value === "AUTO");
    if (filteredEditTypes.length > 0) {
      filteredEditTypes.forEach((el) => {
        const isFind = editTimes.find((item: any) => item.id === el.id);
        if (!!isFind.value) {
          if (Number(isFind.value) < 5 || Number(isFind.value) > 180 || !isNumeric(isFind.value)) {
            result = [...result, el.id];
          }
        } else {
          result = [...result, el.id];
        }
      });
    }
    result = unique(result);
    setErrors(result);
    return result.length > 0;
  };

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("92").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "name", direction: "asc" }]);
      }
    });
  }, []);

  return (
    <Container>
      <TableContainer>
        <Table
          columns={columns}
          setColumns={setColumns}
          tableData={prepareDataTable(pgListForLoadPf)}
          originalTableData={pgListForLoadPfOriginal}
          isLoading={isLoadingLoadPG}
          tableKey="92"
          initSorting={initSorting}
          defaultColumns={defaultColumns}
          customCells={customCell}
          focusHeader={focusHeader}
          setFocusHeader={setFocusHeader}
          columnOrder={columnOrder}
          setColumnOrder={setColumnOrder}
          isFocusHeader={true}
          // searchCustomFilter={searchCustomFilter}
          dataTest="load-pg.table"
          dataTestRows="load-pg-table.rows"
          headerComponents={
            <Buttons>
              <ButtonStyled
                disabled={disabled}
                title="Сохранить"
                isError={errors.length > 0}
                dataTest="load-pg-table-header.save-button"
                onClick={() => {
                  saveLoadPG();
                }}
              />
              <ButtonStyled
                // type="secondary"
                disabled={disabled}
                title="Отменить"
                onClick={() => {
                  setEditChecked(originalChecked);
                  setIsDisabled(true);
                  setEditOffset([]);
                  setErrors([]);
                  setEditTypes(editTypesOriginal);
                  setEditTimes(editTimesOriginal);
                  setFocus({ row: null, cell: null });
                  localStorage.removeItem(`rowEdit`);
                  setIsTimeChanged(false);
                }}
              />
            </Buttons>
          }
        />
      </TableContainer>
    </Container>
  );
});
