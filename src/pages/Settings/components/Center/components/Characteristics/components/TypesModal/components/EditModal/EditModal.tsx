import React, { FC, useEffect, useState } from "react";
import styled from "styled-components";
import { Modal } from "components/Modal";
import { TextField } from "components/TextField";
import { Combobox } from "components/Combobox";
import { observer } from "mobx-react";
import { isUUID } from "../../../../../../../../../../helpers/GenerationUUID";

export interface AddTypeModalProps {
  onCancel?: any;
  onConfirm?: any;
}

export const TextFieldStyled = styled(TextField)`
  width: 500px;
`;

export const Container = styled.div`
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
`;

export interface EditModalProps {
  onCancel?: any;
  selectedValue?: any;
  listParams?: any;
  onConfirm?: any;
}

export const EditModal: FC<EditModalProps> = observer((props) => {
  const { onCancel, selectedValue, listParams = [], onConfirm } = props;
  const [editObject, setEditObject] = useState<any>({});

  useEffect(() => {
    const [first] = listParams;
    setEditObject({
      name: first?.value ?? null,
      uuidCk11: selectedValue?.uuidCk11 ?? "",
    });
  }, []);

  const name = listParams.find((el: any) => el?.value === editObject?.name)?.label ?? "";

  const [errors, setErrors] = useState<any>([]);

  return (
    <Modal
      width={600}
      height={300}
      isOverLay
      onCancel={onCancel}
      title={selectedValue?.name ?? ""}
      confirmText="Применить"
      cancelText="Отменить"
      scrollableContent // Прижимает кнопки к низу
      onConfirm={() => {
        if (editObject?.uuidCk11.length === 0) {
          onConfirm({ ...editObject, parentId: selectedValue.id, name });
        } else {
          const isValid = isUUID(editObject?.uuidCk11);
          if (isValid) {
            onConfirm({
              ...editObject,
              parentId: selectedValue.id,
              name,
              code: editObject.name,
            });
          } else {
            setErrors(["Если вы хотите установить значение UID CK-11 то он должен быть в формате XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX."]);
          }
        }
      }}
      isDisabledConfirm={editObject?.name === null || editObject?.uuidCk11?.length !== 36}
      colorScheme={errors.length > 0 ? "red" : "default"}
    >
      <Container>
        <Combobox
          items={listParams}
          width={500}
          selectedValue={editObject?.name ?? null}
          onChange={({ value }) => {
            setErrors([]);
            setEditObject((prev: any) => ({ ...prev, name: value }));
          }}
          maxHeightList={96}
        />
        <TextFieldStyled
          placeholder="UID CK-11"
          value={editObject?.uuidCk11 ?? ""}
          onChange={(e) => {
            setErrors([]);
            setEditObject((prev: any) => ({
              ...prev,
              uuidCk11: e.target.value,
            }));
          }}
          errors={errors}
        />
      </Container>
    </Modal>
  );
});
