import React, { <PERSON> } from "react";
import styled from "styled-components";
import { Modal } from "components/Modal";

export interface DeleteModalProps {
  selectedValue?: any;
  onConfirm?: any;
  onCancel?: any;
}

export const DeleteModal: FC<DeleteModalProps> = (props) => {
  const { selectedValue, onConfirm, onCancel } = props;
  return (
    <Modal
      width={500}
      height={200}
      isOverLay
      title={`Вы действительно хотите удалить ${selectedValue.name} ?`}
      onConfirm={onConfirm}
      onCancel={onCancel}
      cancelText="Отменить"
      confirmText="Удалить"
      colorScheme="red"
    />
  );
};
