import React, { <PERSON> } from "react";
import { Modal } from "components/Modal";
import styled from "styled-components";

export interface DeleteModalTypeProps {
  selectedValue?: any;
  onConfirm?: any;
  onCancel?: any;
}

export const DeleteModalType: FC<DeleteModalTypeProps> = (props) => {
  const { selectedValue, onConfirm, onCancel } = props;
  return (
    <Modal
      width={500}
      height={200}
      isOverLay
      title={`Вы действительно хотите удалить ${selectedValue.name} ?`}
      onConfirm={onConfirm}
      onCancel={onCancel}
      cancelText="Отменить"
      confirmText="Удалить"
      colorScheme="red"
    />
  );
};
