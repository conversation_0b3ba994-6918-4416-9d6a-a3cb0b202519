import React, { FC, useEffect, useState } from "react";
import styled, { css } from "styled-components";
import { Modal } from "components/Modal";
import { Table } from "components/Table";
import { observer } from "mobx-react";
import { useStores } from "stores/useStore";
import { Icon } from "components/Icon";
import { AddModal } from "./components/AddModal";
import { EditModal } from "./components/EditModal";
import { DeleteModal } from "./components/DeleteModal";
import { Button } from "components/Button";
import { AddTypeModal } from "./components/AddTypeModal";
import { EditModalType } from "./components/EditModalType";
import { DeleteModalType } from "./components/DeleteModalType";
import { Tooltip } from "components/Tooltip";
import { generateUUID } from "../../../../../../../../helpers/GenerationUUID";
import { isEast, isModeCenter } from "../../../../../../../../utils/getMode";
import { prepareDataTable } from "../../../../../../../../utils";
import { toJS } from "mobx";

export const ModalStyled = styled(Modal)`
  //width: 900px;
  //height: 530px;
`;

export const NameContainer = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
`;

export const LabelName = styled.div``;

export const Message = styled.div`
  width: 20px;
  height: 20px;
  color: ${(p) => p.theme.redActiveSupport};
  margin-top: 4px;
`;

export const MessageContent = styled.div`
  width: 200px;
  padding: 4px;
`;

export const Container = styled.div<{ height?: number }>`
  //height: 420px;
  height: ${(p) => p.height}px;
`;

export const ErrorsContainer = styled.div`
  color: ${(p) => p.theme.redActiveSupport};
  margin-top: 20px;
`;

export const Actions = styled.div`
  width: 100%;
  height: 20px;
  display: flex;
  justify-content: space-around;
  align-items: center;
`;

export const Empty = styled.div`
  width: 20px;
  height: 20px;
`;

export const Action = styled(Icon)<{ isEdit?: boolean; isTrash?: boolean }>`
  margin-top: 3px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  color: ${(props) => props.theme.textColor};

  &:hover {
    color: ${(props) => props.theme.primaryColorHover};
  }

  &:active {
    color: ${(props) => props.theme.primaryColorActive};
  }

  ${(p) =>
    p.isEdit &&
    css`
      margin-top: 6px;
    `}
  ${(p) =>
    p.isTrash &&
    css`
      margin-top: 4px;
      margin-bottom: 3px;
    `}
`;

export const Buttons = styled.div`
  width: 100%;
  display: flex;
  justify-content: flex-end;
`;

export const UUIDContainer = styled.div`
  color: ${(p) => p.theme.textColor};
`;

export interface TypesModalProps {
  onCancel?: any;
  type?: any;
  onConfirm?: any;
}

export const TypesModal: FC<TypesModalProps> = observer((props) => {
  const { nsiStore, tableStore } = useStores();
  const { listParams } = nsiStore;
  const { onCancel, type = "ADDER_GEN", onConfirm } = props;
  const defaultColumns: any[] =
    isEast || !isModeCenter
      ? [
          { name: "name", title: "Название", width: 410, position: "left" },
          { name: "uuidCk11", title: "UID CK-11", width: 400 },
        ]
      : [
          { name: "name", title: "Название", width: 310, position: "left" },
          { name: "uuidCk11", title: "UID CK-11", width: 300 },
          { name: "actions", title: "Действия", width: 200 },
        ];
  const [columns, setColumns] = useState(defaultColumns);
  const { typesForModal } = nsiStore;

  const [tableData, setTableData] = useState<any>([]);

  useEffect(() => {
    setTableData(typesForModal);
  }, [typesForModal]);

  useEffect(() => {
    nsiStore.initTypesModal(type);
  }, []);

  const [addId, setAddId] = useState<any>(null);
  const [addIdType, setAddIdType] = useState<any>(false);
  const [editId, setEditId] = useState<any>(null);
  const [editIdType, setEditIdType] = useState<any>(null);
  const [deleteId, setDeleteId] = useState<any>(null);
  const [deleteIdType, setDeleteIdType] = useState<any>(null);

  const [errors, setErrors] = useState<any>([]);

  const customCells = [
    {
      name: "actions",
      render: (_: any, row: any) => {
        const isAdd = row?.childs ?? false;
        return (
          <Actions>
            {isAdd ? (
              <Action
                width={20}
                name="add"
                onClick={() => {
                  setAddId({ id: row.tabId, name: row.name, type: row.type, childs: row.childs, code: row.code });
                }}
              />
            ) : (
              <Empty />
            )}
            <Action
              width={18}
              name="pencil"
              isEdit={true}
              onClick={() => {
                if (row.type === "params") {
                  setEditIdType(row.name);
                  setOldEditIdType(row.tabId);
                } else {
                  setEditId({ id: row.tabId, type: row.type, name: row.name, parentId: row.parentId, childs: row.childs, code: row.code, uuidCk11: row.uuidCk11 });
                }
              }}
            />
            <Action
              isTrash={true}
              width={14}
              name="trash"
              onClick={() => {
                if (row.type === "params") {
                  setDeleteIdType({ id: row.tabId, name: row.name, oldValue: row.tabId });
                } else {
                  setDeleteId({ id: row.tabId, type: row.type, name: row.name, parentId: row.parentId });
                }
              }}
            />
          </Actions>
        );
      },
    },
    {
      name: "name",
      render: (value: any, row: any) => {
        const isErrors = errors.some((el: any) => el === row.tabId);
        return (
          <NameContainer>
            <Message>
              {isErrors && (
                <Tooltip type="error" content={<MessageContent>Пожалуйста добавьте хотя бы одну характеристику для данного типа и повторите сохранение.</MessageContent>}>
                  <Icon name="warning" width={18} />
                </Tooltip>
              )}
            </Message>
            <LabelName>{value}</LabelName>
          </NameContainer>
        );
      },
    },
    {
      name: "uuidCk11",
      render: (value: any) => {
        return <UUIDContainer>{value ? value : ""}</UUIDContainer>;
      },
    },
  ];

  const [isEditing, setIsEditing] = useState(false);
  const [oldEditIdType, setOldEditIdType] = useState(null);

  const editValueTable = (type: any, value: any, oldValue: any) => {
    setIsEditing(true);
    if (type === "ADD_TYPE") {
      setTableData((prev: any) => {
        return [...prev, { tabId: generateUUID(), name: value, type: "params", childs: [] }];
      });
    }
    if (type === "ADD") {
      setTableData((prev: any) => {
        return prev.map((el: any) => {
          const name = listParams.find((par: any) => par.code === value.name);
          if (el.tabId === value.parentId) {
            return {
              ...el,
              childs: [...el.childs, { code: name.code, tabId: name.code, name: name.name, uuidCk11: value?.uuidCk11 ?? "", type: "subParams", parentId: el.tabId }],
            };
          }
          return el;
        });
      });
    }
    if (type === "DELETE") {
      setTableData((prev: any) => {
        return prev.map((el: any) => {
          if (el.tabId === value.parentId) {
            return { ...el, childs: el.childs.filter((item: any) => item?.tabId?.toUpperCase() !== value?.id?.toUpperCase()) };
          }
          return el;
        });
      });
    }
    if (type === "DELETE_TYPE") {
      setTableData((prev: any) => {
        return prev.filter((el: any) => el.tabId !== value.id);
      });
    }
    if (type === "EDIT_TYPE") {
      setTableData((prev: any) => {
        return prev.map((el: any) => {
          if (el.tabId === oldEditIdType) {
            return { ...el, tabId: el.tabId, name: value };
          }
          return el;
        });
      });
    }
    if (type === "EDIT") {
      setTableData((prev: any) => {
        return prev.map((el: any) => {
          if (el.tabId === oldValue.parentId) {
            const childs = el.childs.map((item: any) => {
              if (item.code === oldValue.code) {
                return { ...item, uuidCk11: value.uuidCk11, name: value.name, code: value.code };
              }
              return item;
            });
            return { ...el, childs };
          }
          return el;
        });
      });
    }
  };

  const getParams = (type: any) => {
    if (type === "ADD") {
      return listParams.filter((item: any) => {
        return !addId.childs.some((el: any) => el?.code?.toUpperCase() === item?.code?.toUpperCase());
      });
    }
    if (type === "EDIT") {
      const params = tableData.find((el: any) => el.tabId === editId.parentId)?.childs ?? [];

      const selectedParams = listParams.find((el: any) => el?.code?.toUpperCase() === editId?.code?.toUpperCase());

      return [
        selectedParams,
        ...listParams.filter((item: any) => {
          return !params.some((el: any) => el?.code?.toUpperCase() === item?.code?.toUpperCase() && item?.code?.toUpperCase() === editId?.code?.toUpperCase());
        }),
      ];
    }
  };

  const validate = () => {
    return !tableData.some((el: any) => el.childs.length === 0);
  };

  const confirm = () => {
    const isValidate = validate();
    if (isValidate) {
      onConfirm(tableData);
    } else {
      setErrors(() => {
        return tableData.filter((el: any) => el.childs.length === 0).map((el: any) => el.tabId);
      });
    }
  };

  const cancel = () => {
    onCancel();
    setIsEditing(false);
  };

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("123").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "name", direction: "asc" }]);
      }
    });
  }, []);

  const [modalHeight, setModalHeight] = useState(550);

  return (
    <ModalStyled
      isOverLay
      title="Типы сумматоров агрегированной плановой генерации"
      colorScheme={errors.length > 0 ? "red" : "default"}
      onCancel={cancel}
      onConfirm={isModeCenter && !isEast ? confirm : undefined}
      isDisabledConfirm={!isEditing}
      cancelText={isModeCenter && !isEast ? "Отменить" : undefined}
      confirmText={isModeCenter && !isEast ? "Сохранить" : undefined}
      width={900}
      height={550}
      setModalHeight={setModalHeight}
    >
      {addIdType && (
        <AddTypeModal
          onConfirm={(name: any) => {
            editValueTable("ADD_TYPE", name, null);
            setAddIdType(false);
          }}
          onCancel={() => setAddIdType(false)}
          tableData={tableData}
        />
      )}
      {addId && (
        <AddModal
          listParams={getParams("ADD")}
          selectedValue={addId}
          onConfirm={(addObject: any) => {
            editValueTable("ADD", addObject, null);
            setAddId(null);
          }}
          onCancel={() => setAddId(null)}
        />
      )}
      {editIdType && (
        <EditModalType
          selectedValue={editIdType}
          onCancel={() => setEditIdType(null)}
          onConfirm={(name: any) => {
            editValueTable("EDIT_TYPE", name, editIdType);
            setEditIdType(null);
          }}
          tableData={tableData}
        />
      )}
      {editId && (
        <EditModal
          onCancel={() => setEditId(null)}
          selectedValue={editId}
          listParams={getParams("EDIT")}
          onConfirm={(editObject: any) => {
            editValueTable("EDIT", editObject, editId);
            setEditId(null);
          }}
        />
      )}
      {deleteId && (
        <DeleteModal
          onCancel={() => setDeleteId(null)}
          selectedValue={deleteId}
          onConfirm={() => {
            editValueTable("DELETE", deleteId, null);
            setDeleteId(null);
          }}
        />
      )}
      {deleteIdType && (
        <DeleteModalType
          selectedValue={deleteIdType}
          onCancel={() => setDeleteIdType(null)}
          onConfirm={() => {
            editValueTable("DELETE_TYPE", deleteIdType, null);
            setDeleteIdType(null);
          }}
        />
      )}
      <Container height={modalHeight - 130}>
        <Table
          columns={columns}
          setColumns={setColumns}
          tableData={prepareDataTable(tableData)}
          defaultColumns={defaultColumns}
          customCells={customCells}
          childrenKey="name"
          tableHeight={300}
          initSorting={initSorting}
          headerComponents={
            isModeCenter && !isEast ? (
              <Buttons>
                <Button title="Добавить" onClick={() => setAddIdType(true)} />
              </Buttons>
            ) : undefined
          }
          tableKey={123}
        />
      </Container>
    </ModalStyled>
  );
});
