import React, { <PERSON> } from "react";
import { Modal } from "components/Modal";
import styled from "styled-components";

export interface ModalDeleteProps {
  row?: any;
  onCancel?: any;
  onConfirm?: any;
}

export const ModalStyled = styled(Modal)`
  height: 140px;
`;

export const ModalDelete: FC<ModalDeleteProps> = (props) => {
  const { row, onCancel, onConfirm } = props;

  return (
    <ModalStyled
      width={300}
      height={140}
      isOverLay
      title={`Вы действительно хотите удалить ${row.name} ?`}
      cancelText="Отменить"
      confirmText="Удалить"
      colorScheme="red"
      onCancel={onCancel}
      onConfirm={onConfirm}
    />
  );
};
