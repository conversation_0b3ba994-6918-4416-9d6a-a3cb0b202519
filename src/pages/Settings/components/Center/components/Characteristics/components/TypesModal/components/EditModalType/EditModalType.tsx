import React, { FC, useEffect, useState } from "react";
import { Modal } from "components/Modal";
import styled from "styled-components";
import { TextField } from "components/TextField";

export const TextFieldStyled = styled(TextField)`
  margin-top: 50px;
`;

export interface EditModalTypeProps {
  onCancel?: any;
  onConfirm?: any;
  selectedValue?: any;
  tableData?: any;
}

export const EditModalType: FC<EditModalTypeProps> = (props) => {
  const { onCancel, onConfirm, selectedValue, tableData } = props;

  const [name, setName] = useState("");

  useEffect(() => {
    setName(selectedValue);
  }, []);

  const isDisabled = tableData.some((el: any) => el.name.toUpperCase() === name.toUpperCase()) || name.length === 0;

  return (
    <Modal
      isOverLay
      onCancel={onCancel}
      title="Изменение типа сумматора"
      confirmText="Применить"
      cancelText="Отменить"
      onConfirm={() => onConfirm(name)}
      isDisabledConfirm={isDisabled}
      width={500}
      height={200}
    >
      <TextFieldStyled placeholder="Название" onChange={(e) => setName(e.target.value)} value={name} />
    </Modal>
  );
};
