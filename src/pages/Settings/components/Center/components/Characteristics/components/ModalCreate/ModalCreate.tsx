import React, { FC, useEffect, useState } from "react";
import styled from "styled-components";
import { Modal } from "components/Modal";
import { useStores } from "stores/useStore";
import { Combobox } from "components/Combobox";
import { observer } from "mobx-react";
import { TextField } from "components/TextField";

export interface ModalCreateProps {
  onClose?: any;
  object?: any;
}

export const Container = styled.div`
  width: 100%;
  height: 130px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-direction: column;
`;

export const ComboboxStyled = styled(Combobox)`
  margin: 4px 0;
`;

export const TextFieldStyled = styled(TextField)`
  margin: 4px 0;
  width: 438px;
`;

export const ModalStyled = styled(Modal)`
  height: 250px;
`;

export const ModalCreate: FC<ModalCreateProps> = observer((props) => {
  const { onClose, object } = props;
  const { nsiStore } = useStores();
  const { unrelated, isLoadingUnrelated, characteristics } = nsiStore;

  const characteristicsList = characteristics.find((el: any) => el.tabId === object.type)?.childs;

  const finalUnrelated = unrelated
    // .filter((el: any) => {
    //   return !characteristicsList.some((item: any) => item.opamCode === el.value);
    // })
    .filter((el: any) => !(el.value === "id" || el.value === "hour"));

  useEffect(() => {
    nsiStore.getUnrelated(object.type);
  }, []);

  const [objectSave, setObjectSave] = useState({ opamCode: null, name: "" });

  const modes = [
    { value: "AUTO", label: "Из ОпАМ" },
    { value: "MANUAL", label: "Ручной" },
  ];

  const [mode, setMode] = useState("AUTO");

  return (
    <ModalStyled
      width={500}
      height={130}
      isOverLay
      title={`Добавление характеристики для ${object.name}`}
      cancelText="Отменить"
      confirmText="Добавить"
      onCancel={onClose}
      onConfirm={() => {
        nsiStore.saveUnrelated({ opamCode: objectSave.opamCode, name: objectSave.name, type: object.type }).then((isComplete: any) => {
          if (isComplete) {
            nsiStore.initCharacteristics();
            onClose();
          }
        });
      }}
      isLoading={isLoadingUnrelated}
      isDisabledConfirm={objectSave.opamCode === null || objectSave?.name?.trim().length === 0}
    >
      <Container>
        <ComboboxStyled
          items={modes}
          selectedValue={mode}
          width={438}
          placeholder="Ввод"
          onChange={({ value }) => {
            setMode(value);
            setObjectSave((prev: any) => {
              return { ...prev, opamCode: value === "AUTO" ? null : "" };
            });
          }}
        />
        {mode === "AUTO" ? (
          <ComboboxStyled
            items={finalUnrelated}
            selectedValue={objectSave.opamCode}
            width={438}
            placeholder={'Характеристики из ПАК "ОпАМ"'}
            onChange={({ value }) => {
              setObjectSave((prev) => {
                return { ...prev, opamCode: value };
              });
            }}
          />
        ) : (
          <TextFieldStyled
            value={String(objectSave.opamCode)}
            onChange={(e) => {
              setObjectSave((prev: any) => {
                return { ...prev, opamCode: e.target.value };
              });
            }}
            placeholder="Характеристика"
          />
        )}
        <TextFieldStyled
          value={objectSave.name}
          onChange={(e) => {
            setObjectSave((prev) => {
              return { ...prev, name: e.target.value };
            });
          }}
          placeholder="Название характеристики"
        />
      </Container>
    </ModalStyled>
  );
});
