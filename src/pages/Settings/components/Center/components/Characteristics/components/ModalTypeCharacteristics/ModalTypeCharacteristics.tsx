import React, { FC, useState } from "react";
import { observer } from "mobx-react";
import { Modal } from "components/Modal";
import styled from "styled-components";
import { TextField } from "components/TextField";

export const ModalStyled = styled(Modal)`
  height: 250px;
`;

export const Container = styled.div<{ height?: number }>`
  width: 100%;
  height: ${(p) => p.height}px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-direction: column;
`;

export const TextFieldStyled = styled(TextField)`
  margin: 4px 0;
`;

export interface ModalTypeCharacteristicsProps {
  onClose: any;
  object: any;
  setObjectCharacteristics: any;
  onConfirm: any;
}

export const ModalTypeCharacteristics: FC<ModalTypeCharacteristicsProps> = observer((props) => {
  const { onClose, object, setObjectCharacteristics, onConfirm } = props;
  const [modalHeight, setModalHeight] = useState(250);
  return (
    <ModalStyled
      width={300}
      height={250}
      setModalHeight={setModalHeight}
      onCancel={onClose}
      title={!!object.id ? `Редактирование типа характеристики` : `Добавление типа характеристики для ${object.parent} ${object.name}`}
      isOverLay
      cancelText="Отменить"
      confirmText={!!object.id ? "Сохранить" : "Добавить"}
      isDisabledConfirm={object?.typeCharacteristics?.length === 0} //&& object?.uuidCk11?.length === 0
      onConfirm={onConfirm}
    >
      <Container height={modalHeight - 120}>
        <TextFieldStyled
          value={object.typeCharacteristics}
          onChange={(e) => {
            setObjectCharacteristics((prev: any) => {
              return { ...prev, typeCharacteristics: e.target.value };
            });
          }}
          placeholder="Название"
        />
        <TextFieldStyled
          value={object.uuidCk11}
          onChange={(e) => {
            setObjectCharacteristics((prev: any) => {
              return { ...prev, uuidCk11: e.target.value };
            });
          }}
          placeholder="UID СК-11"
        />
      </Container>
    </ModalStyled>
  );
});
