import React, { FC, useState } from "react";
import { Modal } from "components/Modal";
import styled from "styled-components";
import { TextField } from "components/TextField";

export interface AddTypeModalProps {
  onCancel?: any;
  onConfirm?: any;
  tableData?: any;
}

export const TextFieldStyled = styled(TextField)`
  margin-top: 50px;
`;

export const AddTypeModal: FC<AddTypeModalProps> = (props) => {
  const { onCancel, onConfirm, tableData } = props;

  const [name, setName] = useState("");

  const isDisabled = tableData.some((el: any) => el.name.toUpperCase() === name.toUpperCase()) || name.length === 0;

  return (
    <Modal
      width={500}
      height={200}
      isOverLay
      onCancel={onCancel}
      title="Добавление типа сумматора"
      confirmText="Добавить"
      cancelText="Отменить"
      onConfirm={() => onConfirm(name)}
      isDisabledConfirm={isDisabled}
    >
      <TextFieldStyled placeholder="Название" onChange={(e) => setName(e.target.value)} value={name} />
    </Modal>
  );
};
