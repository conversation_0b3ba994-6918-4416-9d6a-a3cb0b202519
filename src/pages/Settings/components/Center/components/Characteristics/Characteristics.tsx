import React, { useEffect, useRef, useState } from "react";
import styled, { css } from "styled-components";
import { Table } from "components/Table";
import { Checkbox } from "components/Checkbox";
import { Button } from "components/Button";
import { observer } from "mobx-react";
import { useStores } from "stores/useStore";
import { Icon } from "components/Icon";
import { TextField } from "components/TextField";
import { ModalCreate } from "./components/ModalCreate";
import { Tooltip } from "components/Tooltip";
import { useOnClickOutside } from "hooks/useOnClickOutside";
import { isEast, isModeCenter } from "utils/getMode";
import { ModalDelete } from "./components/ModalDelete";
import { ModalTypeCharacteristics } from "./components/ModalTypeCharacteristics";
import { TypesModal } from "./components/TypesModal";
import { Label } from "components/Label";
import { prepareDataTable } from "../../../../../../utils";
import { getWidthModal } from "../../../../../../helpers/adaptive";

export const LabelStyled = styled(Label)``;

export const DeleteButton = styled.div`
  width: 14px;
  height: 14px;
  margin-bottom: 2px;
  margin-right: 5px;
  cursor: pointer;
  transition: all 0.3s;
  color: ${(p) => p.theme.textColor};
  &:hover {
    color: ${(p) => p.theme.blueActiveSupport};
  }
`;

export const TextFieldStyled = styled(TextField)`
  height: 18px;
`;

export const Container = styled.div`
  //height: 900px; //95
  position: relative;
  width: 100%;
  height: 100%;
`;

export const MainDoubleContainer = styled.div`
  width: 100%;
  height: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
`;

export const PartDoubleContainer = styled.div`
  //width: 50%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const CheckboxStyled = styled(Checkbox)`
  margin-right: 10px;
`;

export const CodeLabel = styled.div<{ level?: number; value?: boolean }>`
  padding: 2px;
  border-radius: 4px;
  ${(p) =>
    p.level === 0 &&
    css`
      background-color: ${(p) => p.theme.blueActiveSupport};
      color: ${(p) => p.theme.white};
    `}
  ${(p) =>
    p.level === 1 &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport};
      color: ${(p) => p.theme.white};
    `}
`;
export const ButtonStyled = styled(Button)`
  width: auto;
  margin: 0 4px;
`;

export const ProtocolButtonContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const Action = styled(Button)`
  width: 160px;
  margin: 0 4px;
`;

export const ButtonsRight = styled.div`
  margin-left: auto;
  margin-right: 10px;
  display: flex;
  //width: 435px;
  justify-content: space-between;
`;

export const IconStyled = styled(Icon)<{ name?: string }>`
  margin-left: auto;
  margin-right: 4px;
  margin-top: 3px;
  cursor: pointer;
  color: ${(p) => p.theme.textColor};
  &:hover {
    color: ${(p) => p.theme.primaryColor};
    transition: all 0.3s;
  }
  ${(p) =>
    p.name === "close" &&
    css`
      margin-top: 1px;
    `}
`;

export const IconStyledChilds = styled.div`
  display: flex;
  margin-top: 3px;
  margin-left: auto;
  margin-right: 4px;
  color: ${(p) => p.theme.textColor};
`;

export const IconStyledChild = styled(Icon)<{ name?: string; isTypes?: boolean }>`
  //margin-left: 10px;
  //margin-top: 3px;
  cursor: pointer;
  color: ${(p) => p.theme.textColor};
  &:hover {
    color: ${(p) => p.theme.primaryColor};
    transition: all 0.3s;
  }
  ${(p) =>
    p.name === "close" &&
    css`
      margin-top: 1px;
    `}

  ${(p) =>
    p.isTypes &&
    css`
      margin-top: 2.6px;
      margin-left: 2px;
    `}
`;

export const UIDLabel = styled.div`
  //width: 250px;
  color: ${(p) => p.theme.textColor};
`;

export const ErrorContainer = styled.div`
  cursor: pointer;
  color: ${(p) => p.theme.redActiveSupport};
`;

export const ErrorContent = styled.div`
  width: 200px;
  height: 20px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const ErrorsMainContainer = styled.div<{ isError?: boolean }>`
  width: 100%;
  height: 20px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  ${(p) =>
    p.isError &&
    css`
      border: solid 1px red;
    `}
`;

export const TableContainer = styled.div`
  height: calc(100vh - 40px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 78px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 74px);
  }
`;

export const Circle = styled.div<{ status?: "SENDING" | "NOT_FULLY_SENT" | "ERROR" | "DONE" }>`
  width: 12px;
  height: 12px;
  background-color: ${(p) => p.theme.gray};
  border-radius: 50%;
  margin-right: 10px;
  ${(p) =>
    (p.status === "SENDING" || p.status === "NOT_FULLY_SENT") &&
    css`
      background-color: ${(p) => p.theme.orangeActiveSupport};
    `}
  ${(p) =>
    p.status === "ERROR" &&
    css`
      background-color: ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.status === "DONE" &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport};
    `}
`;

export const TooltipContent = styled.div`
  width: 200px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999999;
`;

export const ProtocolContainer = styled.div`
  position: absolute;
  width: 400px;
  height: auto;
  min-height: 30px;
  max-height: 300px;
  border: solid 1px ${(p) => p.theme.lightGray};
  background-color: ${(p) => p.theme.white};
  z-index: 1001;
  left: 110px;
  top: 30px;
  border-radius: 6px;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  overflow-x: hidden;
`;

export const RowProtocol = styled.div`
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: solid 1px ${(p) => p.theme.lightGray};
  cursor: default;
  &:hover {
    background-color: ${(p) => p.theme.blueActiveSupport};
    color: ${(p) => p.theme.white};
  }
`;

export const EmptyRowProtocol = styled.div`
  width: 100%;
  min-height: 30px;
`;

export const TextContainer = styled.div`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: ${(p) => p.theme.textColor};
`;

export const Characteristics = observer(() => {
  const { nsiStore, authStore, tableStore } = useStores();
  const { isCenter } = authStore;
  const { characteristics, isLoadingCharacteristics, characteristicsFlat, protocolCharacteristics, characteristicsOriginal } = nsiStore;
  const [idsEditChecked, setIdsEditChecked] = useState<any[]>([]);
  const [focus, setFocus] = useState<any>({ row: null, cell: null });

  const isDisabled = idsEditChecked.length === 0 && !Object.values(focus).some((el) => el);

  const initErrors = characteristicsFlat.map((el: any) => {
    return { id: el.tabId, name: "", uuidCk11: "", megaPointCode: "" };
  });
  const [errors, setErrors] = useState<any[]>([]);

  const [isTypesModal, setIsTypesModal] = useState(null);

  useEffect(() => {
    nsiStore.initCharacteristics();
    if (isCenter && isModeCenter) {
      nsiStore.startCheckCharacteristicsProtocol();
    }
    return () => {
      localStorage.removeItem(`rowEdit`);
    };
  }, []);

  let defaultColumns: any[];

  const [columnOrder, setColumnOrder] = useState<any>([]);

  const setDefaultColumns = () => {
    setColumns(() => {
      return columnOrder.map((el: any) => {
        return getDefaultColumns().find((item) => item.name === el);
      });
    });
  };
  const [initScreenWidth, setInitScreenWidth] = useState<any>(getWidthModal(document.documentElement.clientWidth));
  const [screenWidth, setScreenWidth] = useState<any>(initScreenWidth);
  const [isEditWidth, setEditWidth] = useState(false);

  const resizeWeb = () => {
    setScreenWidth(getWidthModal(document.documentElement.clientWidth));
  };

  useEffect(() => {
    window.addEventListener("resize", resizeWeb);
    return () => {
      window.removeEventListener("resize", resizeWeb);
    };
  }, []);

  useEffect(() => {
    if (initScreenWidth !== screenWidth && !isEditWidth) {
      setEditWidth(true);
      setDefaultColumns();
    } else if (initScreenWidth !== screenWidth && isEditWidth) {
      setDefaultColumns();
    } else if (initScreenWidth === screenWidth && isEditWidth) {
      setDefaultColumns();
    }
  }, [screenWidth]);

  const getDefaultColumns = () => {
    if (screenWidth >= 650 && screenWidth < 700) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 130, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 186, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 140, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 124 },
        { name: "megatochka", title: "Мегаточка", width: 150 },
      ];
    }
    if (screenWidth >= 700 && screenWidth < 800) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 130, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 186, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 140, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 124 },
        { name: "megatochka", title: "Мегаточка", width: 150 },
      ];
    }
    if (screenWidth >= 800 && screenWidth < 900) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 130, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 186, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 140, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 124 },
        { name: "megatochka", title: "Мегаточка", width: 150 },
      ];
    }
    if (screenWidth >= 900 && screenWidth < 1024) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 130, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 186, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 140, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 124 },
        { name: "megatochka", title: "Мегаточка", width: 150 },
      ];
    }
    if (screenWidth >= 1024 && screenWidth < 1152) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 186, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 186, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 140, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 124 },
        { name: "megatochka", title: "Мегаточка", width: 150 },
      ];
    }
    if (screenWidth >= 1152 && screenWidth < 1280) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 316, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 186, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 140, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 124 },
        { name: "megatochka", title: "Мегаточка", width: 150 },
      ];
    }
    if (screenWidth >= 1280 && screenWidth < 1400) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 340, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 200, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 230, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 140 },
        { name: "megatochka", title: "Мегаточка", width: 140 },
      ];
    }
    if (screenWidth >= 1400 && screenWidth < 1600) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 340, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 220, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 300, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 150 },
        { name: "megatochka", title: "Мегаточка", width: 150 },
      ];
    }
    if (screenWidth >= 1600 && screenWidth < 1700) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 340, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 220, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 300, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 250 },
        { name: "megatochka", title: "Мегаточка", width: 250 },
      ];
    }
    if (screenWidth >= 1700 && screenWidth < 1800) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 340, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 220, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 300, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 300 },
        { name: "megatochka", title: "Мегаточка", width: 300 },
      ];
    }
    if (screenWidth >= 1800 && screenWidth < 1900) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 340, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 220, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 300, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 350 },
        { name: "megatochka", title: "Мегаточка", width: 350 },
      ];
    }
    if (screenWidth >= 1900 && screenWidth < 2000) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 340, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 220, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 400, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 350 },
        { name: "megatochka", title: "Мегаточка", width: 350 },
      ];
    }
    if (screenWidth >= 2000 && screenWidth < 2100) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 340, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 220, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 400, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 400 },
        { name: "megatochka", title: "Мегаточка", width: 400 },
      ];
    }
    if (screenWidth >= 2100 && screenWidth < 2200) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 340, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 220, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 500, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 400 },
        { name: "megatochka", title: "Мегаточка", width: 400 },
      ];
    }
    if (screenWidth >= 2200 && screenWidth < 2560) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 340, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 220, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 500, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 450 },
        { name: "megatochka", title: "Мегаточка", width: 450 },
      ];
    }
    if (screenWidth >= 2560 && screenWidth < 3000) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 340, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 220, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 500, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 630 },
        { name: "megatochka", title: "Мегаточка", width: 630 },
      ];
    }
    if (screenWidth >= 3000 && screenWidth < 3840) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 340, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 220, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 740, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 730 },
        { name: "megatochka", title: "Мегаточка", width: 730 },
      ];
    }
    if (screenWidth >= 3840 && screenWidth < 4000) {
      return [
        { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
        { name: "name", title: "Название", width: 340, isSearch: true, isSort: "alphabet" },
        { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "megaPointCode", title: "Атрибут мегаточки", width: 220, isSort: "alphabet" },
        { name: "uuidCk11", title: "UID СК-11", width: 1180, isSearch: true, isSort: "alphabet" },
        { name: "opam", title: "ОпАМ", width: 930 },
        { name: "megatochka", title: "Мегаточка", width: 930 },
      ];
    }
    return [
      { name: "child", title: " ", width: 70, isSearch: true, isSort: "alphabet" },
      { name: "name", title: "Название", width: 340, isSearch: true, isSort: "alphabet" },
      { name: "opamCode", title: "Значение ОпАМ", width: 160, isSearch: true, isSort: "alphabet" },
      { name: "megaPointCode", title: "Атрибут мегаточки", width: 220, isSort: "alphabet" },
      { name: "uuidCk11", title: "UID СК-11", width: 400, isSearch: true, isSort: "alphabet" },
      { name: "opam", title: "ОпАМ", width: 350 },
      { name: "megatochka", title: "Мегаточка", width: 350 },
    ];
  };

  defaultColumns = getDefaultColumns();

  const [columns, setColumns] = useState<any>([]);

  useEffect(() => {
    tableStore.getTableParams("90").then((data: any) => {
      if (data) {
        setColumns(data);
      } else {
        setColumns(defaultColumns);
      }
    });
  }, []);

  const [typeCreate, setTypeCreate] = useState<any>(null);

  const [deleteType, setDeleteType] = useState<any>(null);

  const isDisabledEdit = !(isModeCenter && isCenter);

  const [objectCharacteristics, setObjectCharacteristics] = useState<any>(null);

  const [focusHeader, setFocusHeader] = useState<any>(null);

  const customCells = [
    {
      name: "opam",
      render: (value: any, row: any) => {
        const find = idsEditChecked.find((el) => el.id === row.tabId);
        if (row.level !== 0) {
          return (
            <MainDoubleContainer>
              {!row.isSubs && (
                <PartDoubleContainer>
                  <CheckboxStyled
                    status={find ? find.isPpbr : row.isPpbr}
                    disabled={isEast || isDisabledEdit}
                    onChange={({ status }) => {
                      setIdsEditChecked((prev) => {
                        const find = prev.find((el) => el.id === row.tabId);
                        if (find) {
                          const element = {
                            id: row.tabId,
                            isPpbr: !find.isPpbr,
                            isPbr: find.isPbr,
                            isPdg: find.isPdg,
                            isPer: find.isPer,
                          };
                          let final = prev.filter((el) => el.id !== row.tabId);
                          return [...final, element];
                        }
                        return [
                          ...prev,
                          {
                            id: row.tabId,
                            isPpbr: !row.isPpbr,
                            isPbr: row.isPbr,
                            isPdg: row.isPdg,
                            isPer: row.isPer,
                          },
                        ];
                      });
                      setFocus({ row: null, cell: null });
                    }}
                  />
                  <LabelStyled>ППБР</LabelStyled>
                </PartDoubleContainer>
              )}
              {!row.isSubs && (
                <PartDoubleContainer>
                  <CheckboxStyled
                    status={find ? find.isPbr : row.isPbr}
                    disabled={isEast || isDisabledEdit}
                    onChange={({ status }) => {
                      setIdsEditChecked((prev) => {
                        const find = prev.find((el) => el.id === row.tabId);
                        if (find) {
                          const element = {
                            id: row.tabId,
                            isPpbr: find.isPpbr,
                            isPbr: !find.isPbr,
                            isPdg: find.isPdg,
                            isPer: find.isPer,
                          };
                          let final = prev.filter((el) => el.id !== row.tabId);
                          return [...final, element];
                        }
                        return [
                          ...prev,
                          {
                            id: row.tabId,
                            isPpbr: row.isPpbr,
                            isPbr: !row.isPbr,
                            isPdg: row.isPdg,
                            isPer: row.isPer,
                          },
                        ];
                      });
                      setFocus({ row: null, cell: null });
                    }}
                  />
                  <LabelStyled>ПБР</LabelStyled>
                </PartDoubleContainer>
              )}
            </MainDoubleContainer>
          );
        }
        return <></>;
      },
    },
    {
      name: "megatochka",
      render: (value: any, row: any) => {
        const find = idsEditChecked.find((el) => el.id === row.tabId);
        if (row.level !== 0) {
          return (
            <MainDoubleContainer>
              {!row.isSubs && (
                <PartDoubleContainer>
                  <CheckboxStyled
                    status={find ? find.isPdg : row.isPdg}
                    disabled={isEast || isDisabledEdit}
                    onChange={({ status }) => {
                      setIdsEditChecked((prev) => {
                        const find = prev.find((el) => el.id === row.tabId);
                        if (find) {
                          const element = { id: row.tabId, isPpbr: find.isPpbr, isPbr: find.isPbr, isPdg: !find.isPdg, isPer: find.isPer };
                          let final = prev.filter((el) => el.id !== row.tabId);
                          return [...final, element];
                        }
                        return [...prev, { id: row.tabId, isPpbr: row.isPpbr, isPbr: row.isPbr, isPdg: !row.isPdg, isPer: row.isPer }];
                      });
                      setFocus({ row: null, cell: null });
                    }}
                  />
                  <LabelStyled>ПДГ</LabelStyled>
                </PartDoubleContainer>
              )}
              {!row.isSubs && (
                <PartDoubleContainer>
                  <CheckboxStyled
                    status={find ? find.isPer : row.isPer}
                    disabled={isEast || isDisabledEdit}
                    onChange={({ status }) => {
                      setIdsEditChecked((prev) => {
                        const find = prev.find((el) => el.id === row.tabId);
                        if (find) {
                          const element = { id: row.tabId, isPpbr: find.isPpbr, isPbr: find.isPbr, isPdg: find.isPdg, isPer: !find.isPer };
                          let final = prev.filter((el) => el.id !== row.tabId);
                          return [...final, element];
                        }
                        return [...prev, { id: row.tabId, isPpbr: row.isPpbr, isPbr: row.isPbr, isPdg: row.isPdg, isPer: !row.isPer }];
                      });
                      setFocus({ row: null, cell: null });
                    }}
                  />
                  <LabelStyled>ПЭР</LabelStyled>
                </PartDoubleContainer>
              )}
            </MainDoubleContainer>
          );
        }
        return <></>;
      },
    },
    {
      name: "opamCode",
      render: (value: any, row: any) => {
        if (value) {
          return <CodeLabel level={row.level}>{value}</CodeLabel>;
        }
        return <></>;
      },
    },
    {
      name: "megaPointCode",
      render: (value: any, row: any) => {
        const findErrorElement = errors.find((el) => el.id === row.tabId);
        const localStorageArr = JSON.parse(localStorage.getItem(`rowEdit`) as string) ?? [];
        const localCur = localStorageArr && localStorageArr?.length > 0 ? localStorageArr?.find((el: any) => el.key === `${row.tabId}-megaPointCode`) ?? null : null;
        const initCurrent = localCur ? localCur?.value : value ?? "";
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [current, setCurrent] = useState(initCurrent);
        const initFocus = focus.row === row.tabId && "megaPointCode" === focus.cell && focusHeader === null;
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [isFocus, setIsFocus] = useState(initFocus);
        const originalValue = value ? value : "";
        const isChange = current !== originalValue;
        if (row.level !== 0) {
          return (
            <ErrorsMainContainer
              onFocus={() => {
                if (focus.row === null && focus.cell === null) {
                  setFocus({ row: row.tabId, cell: "megaPointCode" });
                }
                setIsFocus(true);
              }}
              onBlur={() => {
                setIsFocus(false);
                localStorage.removeItem(row.tabId);
              }}
            >
              <TextFieldStyled
                errors={findErrorElement?.megaPointCode ? [findErrorElement?.megaPointCode] : null}
                value={current}
                focus={isFocus}
                disabled={isEast || isDisabledEdit}
                isChange={isChange}
                data-megapointcode={row.tabId}
                onChange={(e) => {
                  setCurrent(e.target.value);
                  const finalArr = [
                    ...localStorageArr?.filter((el: any) => el.key !== `${row.tabId}-megaPointCode`),
                    { key: `${row.tabId}-megaPointCode`, value: e.target.value },
                  ];
                  localStorage.setItem(`rowEdit`, JSON.stringify(finalArr));
                }}
              />
            </ErrorsMainContainer>
          );
        } else {
          return <>{value ?? ""}</>;
        }
      },
    },
    {
      name: "name",
      render: (value: any, row: any) => {
        const isAdderGen = row?.parent === "ADDER_GEN";
        const findErrorElement = errors.find((el) => el.id === row.tabId);
        const isManyCharacteristics = row.tabId === "ADDER_GEN";

        const parent = characteristicsFlat.find((el: any) => el.code === row?.parent)?.name?.toLowerCase() ?? " ";

        const localStorageArr = JSON.parse(localStorage.getItem(`rowEdit`) as string) ?? [];
        const localCur = localStorageArr && localStorageArr?.length > 0 ? localStorageArr?.find((el: any) => el.key === `${row.tabId}-name`) ?? null : null;
        const initCurrent = localCur ? localCur?.value : value ?? "";
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [current, setCurrent] = useState(initCurrent);
        const initFocus = focus.row === row.tabId && "name" === focus.cell && focusHeader === null;
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [isFocus, setIsFocus] = useState(initFocus);
        const originalValue = value ? value : "";
        const isChange = current !== originalValue;

        if (row.level === 0) {
          return (
            <ErrorsMainContainer isError={findErrorElement?.name}>
              <TextContainer data-test="characteristircs-table.name-cells">{value}</TextContainer>
              {!isEast && !isDisabledEdit && (
                <IconStyled
                  width={18}
                  name="add"
                  onClick={() => {
                    setTypeCreate({ type: row.tabId, name: row.name });
                  }}
                />
              )}
              {isManyCharacteristics && (
                <UIDLabel title="Типы">
                  <IconStyledChild
                    width={16}
                    name="types"
                    isTypes={true}
                    onClick={() => {
                      setIsTypesModal(row.tabId);
                    }}
                  />
                </UIDLabel>
              )}
              {findErrorElement && findErrorElement?.name?.trim().length > 0 && (
                <Tooltip content={<ErrorContent>{findErrorElement?.name}</ErrorContent>} type="error" placement="right">
                  <ErrorContainer>
                    <Icon width={18} name="error" />
                  </ErrorContainer>
                </Tooltip>
              )}
            </ErrorsMainContainer>
          );
        } else {
          return (
            <ErrorsMainContainer
              onFocus={() => {
                if (focus.row === null && focus.cell === null) {
                  setFocus({ row: row.tabId, cell: "name" });
                }
                setIsFocus(true);
              }}
              onBlur={() => {
                setIsFocus(false);
                localStorage.removeItem(row.tabId);
              }}
            >
              {!isEast && !isDisabledEdit && (
                <DeleteButton onClick={() => setDeleteType(row)}>
                  <Icon width={14} name="trash" />
                </DeleteButton>
              )}
              <TextFieldStyled
                value={current}
                errors={findErrorElement?.name ? [findErrorElement?.name] : null}
                disabled={isEast || isDisabledEdit}
                focus={isFocus}
                data-name={row.tabId}
                isChange={isChange}
                onChange={(e) => {
                  setCurrent(e.target.value);
                  const finalArr = [...localStorageArr?.filter((el: any) => el.key !== `${row.tabId}-name`), { key: `${row.tabId}-name`, value: e.target.value }];
                  localStorage.setItem(`rowEdit`, JSON.stringify(finalArr));
                }}
              />
            </ErrorsMainContainer>
          );
        }
      },
    },
    {
      name: "uuidCk11",
      render: (value: any, row: any) => {
        const findErrorElement = errors.find((el) => el.id === row.tabId);
        const localStorageArr = JSON.parse(localStorage.getItem(`rowEdit`) as string) ?? [];
        const localCur = localStorageArr && localStorageArr?.length > 0 ? localStorageArr?.find((el: any) => el.key === `${row.tabId}-uuidCk11`) ?? null : null;
        const initCurrent = localCur ? localCur?.value : value ?? "";
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [current, setCurrent] = useState(initCurrent);
        const initFocus = focus.row === row.tabId && "uuidCk11" === focus.cell && focusHeader === null;
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [isFocus, setIsFocus] = useState(initFocus);
        const originalValue = value ? value : "";
        const isChange = current !== originalValue;
        if (row.level === 0) {
          return <>{value ?? ""}</>;
        } else {
          return (
            <ErrorsMainContainer
              // isError={findErrorElement?.uuidCk11}
              onFocus={() => {
                if (focus.row === null && focus.cell === null) {
                  setFocus({ row: row.tabId, cell: "uuidCk11" });
                }
                setIsFocus(true);
              }}
              onBlur={() => {
                setIsFocus(false);
                localStorage.removeItem(row.tabId);
              }}
            >
              <TextFieldStyled
                value={current}
                errors={findErrorElement?.uuidCk11 ? [findErrorElement?.uuidCk11] : null}
                disabled={isEast || isDisabledEdit}
                focus={isFocus}
                maxLength={36}
                data-uuidck11={row.tabId}
                isChange={isChange}
                dataTest="characteristics-table.uid-cells"
                onChange={(e) => {
                  setCurrent(e.target.value);
                  const finalArr = [...localStorageArr?.filter((el: any) => el.key !== `${row.tabId}-uuidCk11`), { key: `${row.tabId}-uuidCk11`, value: e.target.value }];
                  localStorage.setItem(`rowEdit`, JSON.stringify(finalArr));
                }}
              />
            </ErrorsMainContainer>
          );
        }
      },
    },
  ];

  const validate = (array: any) => {
    let isError = false;
    array.map((el: any) => {
      if (el.name.trim().length === 0) {
        setErrors((prev) => {
          return prev.map((item) => {
            if (item.id === el.tabId) {
              return { ...item, name: "Поле не должно быть пустым" };
            }
            return item;
          });
        });
        isError = true;
      }

      if (el.isPbr || el.isPdg || el.isPer || el.isPpbr) {
        if (!!!el.uuidCk11 || el?.uuidCk11?.trim().length === 0) {
          setErrors((prev) => {
            return prev.map((item) => {
              if (item.id === el.tabId) {
                return { ...item, uuidCk11: "Поле не должно быть пустым" };
              }
              return item;
            });
          });
          isError = true;
        }
        if (!!!el.uuidCk11 || el?.uuidCk11?.trim().length !== 36) {
          setErrors((prev) => {
            return prev.map((item) => {
              if (item.id === el.tabId) {
                return { ...item, uuidCk11: "Поле должно содержать 36 символов в формате UID" };
              }
              return item;
            });
          });
          isError = true;
        }
      }

      if (el.isPdg || el.isPer) {
        if (el?.megaPointCode?.trim().length === 0 || !!!el.megaPointCode) {
          setErrors((prev) => {
            return prev.map((item) => {
              if (item.id === el.tabId) {
                return { ...item, megaPointCode: "Поле не должно быть пустым" };
              }
              return item;
            });
          });
          isError = true;
        }
      }
    });

    setErrors((prev) => {
      return prev.filter((el: any) => el.name.trim().length !== 0 || el.uuidCk11.trim().length !== 0 || el.megaPointCode.trim().length !== 0);
    });

    return isError;
  };

  const tableData = prepareDataTable(characteristics);

  const onSave = () => {
    setErrors([]);
    let megaPointCodes: any = [];
    const collectionOfElementsMegaPointCodes: any = document.querySelectorAll("[data-megapointcode]");
    for (let i = 0; i < collectionOfElementsMegaPointCodes.length; i++) {
      // @ts-ignore
      const key = collectionOfElementsMegaPointCodes[i].getAttribute("data-megapointcode");
      // @ts-ignore
      megaPointCodes[i] = { key, value: collectionOfElementsMegaPointCodes[i]?.value };
    }

    // megaPointCodes = megaPointCodes.filter((el: any) => el.isEdit).map((el: any) => ({ key: el.key, value: el.value }));

    let names: any = [];
    const collectionOfElementsName: any = document.querySelectorAll("[data-name]");
    for (let i = 0; i < collectionOfElementsName.length; i++) {
      // @ts-ignore
      const key = collectionOfElementsName[i].getAttribute("data-name");
      // @ts-ignore
      names[i] = { key, value: collectionOfElementsName[i]?.value };
    }

    names = names.filter((el: any) => el.key !== "checkbox");

    let uuidCk11s: any = [];
    const collectionOfElementsUuidCk11s: any = document.querySelectorAll("[data-uuidck11]");
    for (let i = 0; i < collectionOfElementsUuidCk11s.length; i++) {
      // @ts-ignore
      const key = collectionOfElementsUuidCk11s[i].getAttribute("data-uuidck11");
      // @ts-ignore
      uuidCk11s[i] = { key, value: collectionOfElementsUuidCk11s[i]?.value };
    }

    setErrors(initErrors);
    const items = characteristicsFlat
      .map((el: any) => {
        let element = {};

        const findName = names.find((item: any) => item.key === el.tabId);
        if (findName) {
          element = { ...element, name: findName?.value ?? "" };
        }

        const findCK11 = uuidCk11s.find((item: any) => item.key === el.tabId);
        if (findCK11) {
          element = { ...element, uuidCk11: findCK11?.value ?? "" };
        }

        const findMegaPointCode = megaPointCodes.find((item: any) => item.key === el.tabId);
        if (findMegaPointCode) {
          element = { ...element, megaPointCode: findMegaPointCode?.value ?? null }; //maybe
        }

        const idsChecked = idsEditChecked.some((item) => item.id.toUpperCase() === el.tabId.toUpperCase());
        if (idsChecked) {
          const checked = idsEditChecked.find((item) => item.id === el.tabId);
          element = {
            ...element,
            isPpbr: checked.isPpbr,
            isPbr: checked.isPbr,
            isPdg: checked.isPdg,
            isPer: checked.isPer,
          };
        }
        if (Object.keys(element).length > 0) {
          return { ...el, ...element };
        } else {
          return null;
        }
      })
      .filter((el: any) => el !== null);

    const rowEditStorage = JSON.parse(localStorage.getItem("rowEdit") as string) ?? [];

    const rowEdit =
      rowEditStorage.map((el: any) => {
        const key = el.key
          .split("-")
          .filter((item: any) => item !== "uuidCk11" && item !== "megaPointCode")
          .join("-");
        return { ...el, key };
      }) ?? [];

    const finItems = items.filter((el: any) => {
      return rowEdit.some((item: any) => item.key === el.tabId) || idsEditChecked.some((item: any) => item.id.toUpperCase() === el.tabId.toUpperCase());
    });

    const isValidate = validate(finItems);

    if (!isValidate) {
      const items = finItems.map((el: any) => {
        const megaPointCode = el?.megaPointCode?.trim().length === 0 ? null : el?.megaPointCode;
        return { ...el, megaPointCode };
      });
      nsiStore.editCharacters(items).then(async (isComplete: boolean) => {
        if (isComplete) {
          nsiStore.initCharacteristics();
          setIdsEditChecked([]);
          setErrors([]);
          setFocus({ row: null, cell: null });
          localStorage.removeItem(`rowEdit`);
        }
      });
    }
  };

  const startDistribution = () => {
    nsiStore.startDistributionCharacteristics().then(() => {
      nsiStore.startCheckCharacteristicsProtocol();
    });
  };

  const getTextTooltip = (status: "SENDING" | "NOT_FULLY_SENT" | "ERROR" | "DONE") => {
    if (status === "SENDING") {
      return "Характеристики распространяются";
    }
    if (status === "NOT_FULLY_SENT") {
      return "Характеристики успешно распространены во все ДЦ, кроме...";
    }
    if (status === "ERROR") {
      return "Характеристики не распространены ни в один ДЦ";
    }
    if (status === "DONE") {
      return "Характеристики успешно распространены";
    }
  };

  const [isOpenProtocol, setIsOpenProtocol] = useState(false);

  const protocolRef = useRef<any>(null);

  useOnClickOutside(protocolRef, () => {
    setIsOpenProtocol(false);
  });

  const saveTypesModal = (data: any) => {
    nsiStore.saveTypesModal(data, "ADDER_GEN").then((isComplete: boolean) => {
      if (isComplete) {
        setIsTypesModal(null);
        localStorage.removeItem("tableOpened-123");
      }
    });
  };

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("90").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "priority", direction: "asc" }]);
      }
    });
    return () => {
      localStorage.removeItem("expandedRowIds-90");
    };
  }, []);

  const initExpandedRowIds = JSON.parse(localStorage.getItem("expandedRowIds-90") as string) ?? [];

  const [expandedRowIds, setExpandedRowIds] = useState(initExpandedRowIds);

  return (
    <Container>
      {isTypesModal && (
        <TypesModal
          onCancel={() => setIsTypesModal(null)}
          onConfirm={(data: any) => {
            saveTypesModal(data);
          }}
        />
      )}
      {objectCharacteristics && (
        <ModalTypeCharacteristics
          onClose={() => setObjectCharacteristics(null)}
          object={objectCharacteristics}
          setObjectCharacteristics={setObjectCharacteristics}
          onConfirm={async () => {
            const value = objectCharacteristics.typeCharacteristics;
            const [paramType, paramCode] = objectCharacteristics.type.split("-");
            await nsiStore
              .saveTypeCharacteristics(value, paramCode.toUpperCase(), paramType.toUpperCase(), objectCharacteristics.id, objectCharacteristics?.uuidCk11)
              .then((isComplete: boolean) => {
                if (isComplete) {
                  setObjectCharacteristics(null);
                  nsiStore.initCharacteristics();
                }
              });
          }}
        />
      )}
      {deleteType && (
        <ModalDelete
          row={deleteType}
          onCancel={() => setDeleteType(null)}
          onConfirm={() => {
            if (deleteType.id) {
              nsiStore.deleteSubs(deleteType).then(() => {
                setDeleteType(null);
                nsiStore.initCharacteristics();
              });
            } else {
              nsiStore.deleteCharacteristics(deleteType).then(() => {
                setDeleteType(null);
                nsiStore.initCharacteristics();
              });
            }
          }}
        />
      )}
      {isOpenProtocol && (
        <ProtocolContainer ref={protocolRef}>
          {/*<EmptyRowProtocol />*/}
          {protocolCharacteristics?.status === "ERROR" ? (
            <>{protocolCharacteristics.error}</>
          ) : (
            protocolCharacteristics.notSentTo.map((el: any, index: number) => {
              return <RowProtocol key={`row-protocol-${index}`}>{el}</RowProtocol>;
            })
          )}
        </ProtocolContainer>
      )}
      {typeCreate && <ModalCreate onClose={() => setTypeCreate(null)} object={typeCreate} />}
      <TableContainer>
        <Table
          columns={columns}
          setColumns={setColumns}
          isVirtualTable={false}
          focusHeader={focusHeader}
          setFocusHeader={setFocusHeader}
          isFocusHeader={true}
          defaultColumns={defaultColumns}
          columnOrder={columnOrder}
          setColumnOrder={setColumnOrder}
          tableData={tableData}
          isLoading={isLoadingCharacteristics}
          tableKey="90"
          expandedRowIds={expandedRowIds}
          setExpandedRowIds={(e: any) => {
            localStorage.setItem("expandedRowIds-90", JSON.stringify(e));
            setExpandedRowIds(e);
          }}
          childrenKey="child"
          disabledSearches={["child", "opam", "megatochka"]} // отключаем поиск и сортировку для колонок "child", "opam" и "megatochka"
          initSorting={initSorting}
          dataTest="characteristics.table"
          dataTestRows="characteristics-table.rows"
          headerComponents={
            isModeCenter && !isEast ? (
              <>
                <ButtonStyled
                  title="Распространить настройки СРПГ в ДЦ"
                  message="Распространить настройки СРПГ в ДЦ (Номер ПБР [1СЗ], Номер ПБР [2СЗ], Характеристики, Отсутствие данных, MODES)"
                  // type="secondary"
                  onClick={() => startDistribution()}
                  disabled={protocolCharacteristics?.status === "SENDING"}
                />
                <Tooltip content={<TooltipContent>{getTextTooltip(protocolCharacteristics?.status)}</TooltipContent>} placement="bottom">
                  <ButtonStyled
                    title={
                      <ProtocolButtonContent>
                        <Circle status={protocolCharacteristics?.status} />
                        <>{protocolCharacteristics?.status === "SENDING" ? "Загрузка" : "Протокол"}</>
                      </ProtocolButtonContent>
                    }
                    // type="secondary"
                    message={" "}
                    disabled={!!!protocolCharacteristics?.status || protocolCharacteristics?.status === "SENDING" || protocolCharacteristics?.notSentTo?.length === 0}
                    onClick={() => {
                      setIsOpenProtocol(true);
                    }}
                  />
                </Tooltip>

                <ButtonsRight>
                  <Action title="Сохранить" disabled={isDisabled} onClick={() => onSave()} dataTest="characteristics-header.save-button" />
                  <Action
                    title="Отменить"
                    // type="secondary"
                    disabled={isDisabled}
                    onClick={() => {
                      setIdsEditChecked([]);
                      setErrors([]);
                      setFocus({ row: null, cell: null });
                      localStorage.removeItem(`rowEdit`);
                    }}
                    dataTest="characteristics-header.cancel-button"
                  />
                </ButtonsRight>
              </>
            ) : undefined
          }
          customCells={customCells}
        />
      </TableContainer>
    </Container>
  );
});
