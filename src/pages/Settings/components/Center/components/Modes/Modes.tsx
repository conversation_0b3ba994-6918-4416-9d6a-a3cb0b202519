import React, { useEffect, useMemo, useRef, useState } from "react";
import styled, { css } from "styled-components";
import { useStores } from "../../../../../../stores/useStore";
import { Table } from "../../../../../../components/Table";
import { observer } from "mobx-react";
import { Checkbox } from "../../../../../../components/Checkbox";
import { TextField } from "components/TextField";
import { Button } from "components/Button";
import { Circle, RowProtocol, TooltipContent } from "../Characteristics";
import { useOnClickOutside } from "../../../../../../hooks/useOnClickOutside";
import { Tooltip } from "components/Tooltip";
import { isEast, isModeCenter } from "utils/getMode";
import { getWidthModal } from "../../../../../../helpers/adaptive";
import { toJS } from "mobx";

export const ProtocolButton = styled(Button)`
  width: auto;
  margin: 0 4px;
`;

export const Container = styled.div`
  //height: 900px;
  width: 100%;
  height: 100%;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  position: relative;
`;

export const SRPG = styled.div<{ level: number }>`
  background-color: ${(p) => p.theme.blueActiveSupport};
  color: ${(p) => p.theme.white};
  padding: 2px;
  border-radius: 4px;
  ${(p) =>
    p.level === 1 &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport};
    `}
`;

export const ButtonStyled = styled(Button)`
  margin: 0 4px;
  width: 160px;
`;

export const Buttons = styled.div`
  margin-left: auto;
  margin-right: 10px;
  display: flex;
`;

export const ButtonDistribution = styled(Button)`
  width: auto;
  margin: 0 4px;
`;

export const ProtocolContainer = styled.div`
  position: absolute;
  width: 400px;
  height: auto;
  min-height: 30px;
  max-height: 300px;
  border: solid 1px ${(p) => p.theme.lightGray};
  background-color: ${(p) => p.theme.white};
  z-index: 1001;
  left: 110px;
  top: 30px;
  border-radius: 6px;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  overflow-x: hidden;
`;

export const InputStyled = styled(TextField)<{ isChange?: boolean }>`
  height: 19px;
`;

export const ProtocolButtonContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const TableContainer = styled.div`
  height: calc(100vh - 40px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 78px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 74px);
  }
`;

const getTextTooltip = (status: "SENDING" | "NOT_FULLY_SENT" | "ERROR" | "DONE") => {
  if (status === "SENDING") {
    return "Настройки распространяются";
  }
  if (status === "NOT_FULLY_SENT") {
    return "Настройки успешно распространены во все ДЦ, кроме...";
  }
  if (status === "ERROR") {
    return "Настройки не распространены ни в один ДЦ";
  }
  if (status === "DONE") {
    return "Настройки успешно распространены";
  }
};

const InputContainer = styled.div`
  width: 100%;
  height: 100%;
`;

export const Modes = observer(() => {
  const { settingsStore, notificationStore, tableStore, authStore, nsiStore } = useStores();
  const { modes } = settingsStore;
  const { isCenter } = authStore;
  const { protocolCharacteristics } = nsiStore;
  const [columns, setColumns] = useState<any>([]);
  const [pbr, setPbr] = useState<any>([]);
  const [ppbr, setPpbr] = useState<any>([]);
  const [modesCode, setModesCode] = useState<any>([]);
  const [errorsIds, setErrorsIds] = useState<any[]>([]);
  const [focus, setFocus] = useState<any>({ row: null, cell: null });
  const [isOpenProtocol, setIsOpenProtocol] = useState(false);

  let defaultColumns: any[];

  const [columnOrder, setColumnOrder] = useState<any>([]);

  const setDefaultColumns = () => {
    setColumns(() => {
      return columnOrder.map((el: any) => {
        return getDefaultColumns().find((item) => item.name === el);
      });
    });
  };
  const [initScreenWidth, setInitScreenWidth] = useState<any>(getWidthModal(document.documentElement.clientWidth));
  const [screenWidth, setScreenWidth] = useState<any>(initScreenWidth);
  const [isEditWidth, setEditWidth] = useState(false);

  const resizeWeb = () => {
    setScreenWidth(getWidthModal(document.documentElement.clientWidth));
  };

  useEffect(() => {
    window.addEventListener("resize", resizeWeb);
    return () => {
      window.removeEventListener("resize", resizeWeb);
    };
  }, []);

  useEffect(() => {
    if (initScreenWidth !== screenWidth && !isEditWidth) {
      setEditWidth(true);
      setDefaultColumns();
    } else if (initScreenWidth !== screenWidth && isEditWidth) {
      setDefaultColumns();
    } else if (initScreenWidth === screenWidth && isEditWidth) {
      setDefaultColumns();
    }
  }, [screenWidth]);

  const getDefaultColumns = () => {
    if (screenWidth >= 650 && screenWidth < 700) {
      return [
        { name: "name", title: "Название", width: 200, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 160, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 180 },
        { name: "isPpbr", title: "ППБР", width: 110 },
        { name: "isPbr", title: "ПБР", width: 110 },
      ];
    }
    if (screenWidth >= 700 && screenWidth < 800) {
      return [
        { name: "name", title: "Название", width: 200, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 160, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 180 },
        { name: "isPpbr", title: "ППБР", width: 110 },
        { name: "isPbr", title: "ПБР", width: 110 },
      ];
    }
    if (screenWidth >= 800 && screenWidth < 900) {
      return [
        { name: "name", title: "Название", width: 200, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 160, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 180 },
        { name: "isPpbr", title: "ППБР", width: 110 },
        { name: "isPbr", title: "ПБР", width: 110 },
      ];
    }
    if (screenWidth >= 900 && screenWidth < 1024) {
      return [
        { name: "name", title: "Название", width: 300, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 190, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 180 },
        { name: "isPpbr", title: "ППБР", width: 110 },
        { name: "isPbr", title: "ПБР", width: 110 },
      ];
    }
    if (screenWidth >= 1024 && screenWidth < 1152) {
      return [
        { name: "name", title: "Название", width: 300, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 200, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 300 },
        { name: "isPpbr", title: "ППБР", width: 110 },
        { name: "isPbr", title: "ПБР", width: 110 },
      ];
    }
    if (screenWidth >= 1152 && screenWidth < 1280) {
      return [
        { name: "name", title: "Название", width: 300, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 230, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 400 },
        { name: "isPpbr", title: "ППБР", width: 110 },
        { name: "isPbr", title: "ПБР", width: 110 },
      ];
    }
    if (screenWidth >= 1280 && screenWidth < 1400) {
      return [
        { name: "name", title: "Название", width: 350, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 300, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 400 },
        { name: "isPpbr", title: "ППБР", width: 110 },
        { name: "isPbr", title: "ПБР", width: 110 },
      ];
    }
    if (screenWidth >= 1400 && screenWidth < 1600) {
      return [
        { name: "name", title: "Название", width: 400, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 370, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 400 },
        { name: "isPpbr", title: "ППБР", width: 110 },
        { name: "isPbr", title: "ПБР", width: 110 },
      ];
    }
    if (screenWidth >= 1600 && screenWidth < 1700) {
      return [
        { name: "name", title: "Название", width: 400, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 370, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 400 },
        { name: "isPpbr", title: "ППБР", width: 210 },
        { name: "isPbr", title: "ПБР", width: 210 },
      ];
    }
    if (screenWidth >= 1700 && screenWidth < 1800) {
      return [
        { name: "name", title: "Название", width: 400, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 370, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 400 },
        { name: "isPpbr", title: "ППБР", width: 260 },
        { name: "isPbr", title: "ПБР", width: 260 },
      ];
    }
    if (screenWidth >= 1800 && screenWidth < 1900) {
      return [
        { name: "name", title: "Название", width: 400, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 370, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 400 },
        { name: "isPpbr", title: "ППБР", width: 310 },
        { name: "isPbr", title: "ПБР", width: 310 },
      ];
    }
    if (screenWidth >= 1900 && screenWidth < 2000) {
      return [
        { name: "name", title: "Название", width: 400, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 370, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 400 },
        { name: "isPpbr", title: "ППБР", width: 360 },
        { name: "isPbr", title: "ПБР", width: 360 },
      ];
    }
    if (screenWidth >= 2000 && screenWidth < 2100) {
      return [
        { name: "name", title: "Название", width: 400, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 370, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 400 },
        { name: "isPpbr", title: "ППБР", width: 410 },
        { name: "isPbr", title: "ПБР", width: 410 },
      ];
    }
    if (screenWidth >= 2100 && screenWidth < 2200) {
      return [
        { name: "name", title: "Название", width: 500, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 370, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 400 },
        { name: "isPpbr", title: "ППБР", width: 410 },
        { name: "isPbr", title: "ПБР", width: 410 },
      ];
    }
    if (screenWidth >= 2200 && screenWidth < 2560) {
      return [
        { name: "name", title: "Название", width: 500, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 470, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 400 },
        { name: "isPpbr", title: "ППБР", width: 410 },
        { name: "isPbr", title: "ПБР", width: 410 },
      ];
    }
    if (screenWidth >= 2560 && screenWidth < 3000) {
      return [
        { name: "name", title: "Название", width: 660, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 570, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 500 },
        { name: "isPpbr", title: "ППБР", width: 410 },
        { name: "isPbr", title: "ПБР", width: 410 },
      ];
    }
    if (screenWidth >= 3000 && screenWidth < 3840) {
      return [
        { name: "name", title: "Название", width: 660, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 570, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 640 },
        { name: "isPpbr", title: "ППБР", width: 510 },
        { name: "isPbr", title: "ПБР", width: 510 },
      ];
    }
    if (screenWidth >= 3840 && screenWidth < 4000) {
      return [
        { name: "name", title: "Название", width: 900, isSort: "alphabet" },
        { name: "srpg", title: "Значение СРПГ", width: 670, isSort: "alphabet" },
        { name: "modesCode", title: "Значение MODES", width: 740 },
        { name: "isPpbr", title: "ППБР", width: 710 },
        { name: "isPbr", title: "ПБР", width: 710 },
      ];
    }
    return [
      { name: "name", title: "Название", width: 400, isSort: "alphabet" },
      { name: "srpg", title: "Значение СРПГ", width: 370, isSort: "alphabet" },
      { name: "modesCode", title: "Значение MODES", width: 400 },
      { name: "isPpbr", title: "ППБР", width: 360 },
      { name: "isPbr", title: "ПБР", width: 360 },
    ];
  };

  defaultColumns = getDefaultColumns();

  useMemo(() => {
    setPbr(modes.map((el: any) => ({ id: el.tabId, value: el.isPbr ?? false, originalValue: el.isPbr ?? false })));
    setPpbr(modes.map((el: any) => ({ id: el.tabId, value: el.isPpbr ?? false, originalValue: el.isPpbr ?? false })));
    setModesCode(modes.map((el: any) => ({ id: el.tabId, value: el.modesCode ?? "", originalValue: el.modesCode ?? "" })));
  }, [modes]);

  useEffect(() => {
    settingsStore.initModes(isModeCenter);
    if (isCenter && isModeCenter) {
      nsiStore.startCheckCharacteristicsProtocol();
    }
    tableStore.getTableParams("93").then((data: any) => {
      if (data) {
        setColumns(data);
      } else {
        setColumns(defaultColumns);
      }
    });
    return () => {
      localStorage.removeItem(`rowEdit`);
    };
  }, []);

  const [focusHeader, setFocusHeader] = useState<any>(null);

  const customCells = [
    {
      name: "srpg",
      render: (value: any, row: any) => {
        return <SRPG level={row.level}>{value}</SRPG>;
      },
    },
    {
      name: "isPpbr",
      render: (value: any, row: any) => {
        const findEl: any = ppbr.find((el: any) => el.id === row.tabId)?.value;
        if (row.level === 1) {
          return (
            <>
              <Checkbox
                status={findEl}
                disabled={isEast || !isModeCenter}
                onChange={({ status }) => {
                  setErrorsIds([]);
                  setPpbr((prev: any) => {
                    return prev.map((el: any) => {
                      if (el.id === row.tabId) {
                        return { ...el, value: status };
                      }
                      return el;
                    });
                  });
                }}
              />
            </>
          );
        }
      },
    },
    {
      name: "isPbr",
      render: (value: any, row: any) => {
        const findEl: any = pbr.find((el: any) => el.id === row.tabId)?.value;
        if (row.level === 1) {
          return (
            <>
              <Checkbox
                status={findEl}
                disabled={isEast || !isModeCenter}
                onChange={({ status }) => {
                  setErrorsIds([]);
                  setPbr((prev: any) => {
                    return prev.map((el: any) => {
                      if (el.id === row.tabId) {
                        return { ...el, value: status };
                      }
                      return el;
                    });
                  });
                }}
              />
            </>
          );
        }
      },
    },
    {
      name: "modesCode",
      render: (value: any, row: any) => {
        const findEl = modesCode.find((el: any) => el.id === row.tabId)?.value ?? value;
        const findEr = errorsIds.find((el: any) => el === row.tabId);
        const findPpbr = ppbr.find((el: any) => el.id === row.tabId)?.value;
        const findPbr = pbr.find((el: any) => el.id === row.tabId)?.value;
        const localStorageArr = JSON.parse(localStorage.getItem(`rowEdit`) as string) ?? [];
        const localCur = localStorageArr && localStorageArr?.length > 0 ? localStorageArr?.find((el: any) => el.key === `${row.tabId}-modesCode`) ?? null : null;
        const initCurrent = localCur ? localCur?.value : value ?? "";
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [current, setCurrent] = useState(initCurrent);
        const initFocus = focus.row === row.tabId && "modesCode" === focus.cell && focusHeader === null;
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [isFocus, setIsFocus] = useState(initFocus);
        const originalValue = value ? value : "";
        const isChange = current !== originalValue;
        const modesValid = findPpbr || findPbr ? current?.trim().length === 0 : false;
        return (
          <InputContainer
            onFocus={() => {
              if (focus.row === null && focus.cell === null) {
                setFocus({ row: row.tabId, cell: "modesCode" });
              }
              setIsFocus(true);
            }}
            onBlur={() => {
              setIsFocus(false);
              // localStorage.removeItem(row.tabId);
            }}
          >
            <InputStyled
              value={current}
              focus={isFocus}
              disabled={isEast || !isModeCenter}
              errors={findEr ? ["Значение должно быть уникальным"] : modesValid ? ["Поле не должно быть пустым"] : []}
              data-modescode={row.tabId}
              dataTest="modes-table.modes-value-cells"
              onChange={(e) => {
                setCurrent(e.target.value);
                const finalArr = [...localStorageArr?.filter((el: any) => el.key !== `${row.tabId}-modesCode`), { key: `${row.tabId}-modesCode`, value: e.target.value }];
                localStorage.setItem(`rowEdit`, JSON.stringify(finalArr));
              }}
              isChange={isChange}
            />
          </InputContainer>
        );
      },
    },
  ];

  const isDisabled = !(
    pbr.some((el: any) => el.value !== el.originalValue) ||
    ppbr.some((el: any) => el.value !== el.originalValue) ||
    modesCode.some((el: any) => el.value !== el.originalValue) ||
    Object.values(focus).some((el) => el)
  );

  const onReset = () => {
    setPbr((prev: any) => {
      return prev.map((el: any) => ({ ...el, value: el.originalValue }));
    });
    setPpbr((prev: any) => {
      return prev.map((el: any) => ({ ...el, value: el.originalValue }));
    });
    setModesCode((prev: any) => {
      return prev.map((el: any) => ({ ...el, value: el.originalValue }));
    });
    setFocus({ row: null, cell: null });
    localStorage.removeItem(`rowEdit`);
  };

  const onSave = () => {
    if (!isDisabled) {
      let dataModes: any = [];
      const collectionOfElements = document.querySelectorAll("[data-modescode]");
      for (let i = 0; i < collectionOfElements.length; i++) {
        // @ts-ignore
        const key = collectionOfElements[i].getAttribute("data-modescode");
        // @ts-ignore
        dataModes[i] = { key, value: collectionOfElements[i]?.value };
      }
      const codeModes = modesCode.map((el: any) => {
        const find = dataModes.find((item: any) => item.key === el.id);
        if (find) {
          return { ...el, value: find.value };
        }
        return el;
      });
      const types: any = [];
      const params: any = [];
      const finalPbr = pbr.filter((el: any) => el.value !== el.originalValue);
      const finalPpbr = ppbr.filter((el: any) => el.value !== el.originalValue);
      const finalModesCode = codeModes.filter((el: any) => el.value !== el.originalValue);
      const finalFlatModes = modes
        .map((el: any) => {
          let objectFinal = {};
          const isFindPbr = finalPbr.find((item: any) => item.id === el.tabId);
          if (isFindPbr) {
            objectFinal = { ...objectFinal, isPbr: isFindPbr.value };
          }
          const isFindPpbr = finalPpbr.find((item: any) => item.id === el.tabId);
          if (isFindPpbr) {
            objectFinal = { ...objectFinal, isPpbr: isFindPpbr.value };
          }
          const isFindModesCode = finalModesCode.find((item: any) => item.id === el.tabId);
          if (isFindModesCode) {
            objectFinal = { ...objectFinal, modesCode: isFindModesCode.value };
          }
          if (Object.keys(objectFinal).length > 0) {
            return { ...el, ...objectFinal };
          } else {
            return null;
          }
        })
        .filter((el: any) => el !== null);

      // check uniq modesCode
      let duplicates = [];

      const tempArray = [...finalFlatModes].sort();

      for (let i = 0; i < tempArray.length; i++) {
        if (
          tempArray[i + 1]?.modesCode?.toUpperCase() === tempArray[i]?.modesCode?.toUpperCase() &&
          tempArray[i + 1]?.type?.toUpperCase() === tempArray[i]?.type?.toUpperCase() &&
          tempArray[i + 1]?.modesCode && tempArray[i]?.type &&
          tempArray[i + 1]?.type?.trim()?.length >0 &&
          tempArray[i]?.type?.trim()?.length >0
        ) {
          duplicates.push(tempArray[i + 1]);
          duplicates.push(tempArray[i]);
        }
      }
      if (duplicates.length > 0) {
        setErrorsIds(duplicates.map((el) => el.tabId));
      } else {
        finalFlatModes.map((el: any) => {
          if (el.level === 0) {
            types.push(el);
          }
          if (el.level === 1) {
            params.push(el);
          }
        });

        const isValid = types.some((el: any) => (el.isPbr || el.isPpbr) && !!!el.modesCode) || params.some((el: any) => (el.isPbr || el.isPpbr) && !!!el.modesCode);
        if (!isValid) {
          settingsStore
            .saveModes(types, params)
            .then(() => {
              settingsStore.initModes(isModeCenter);
            })
            .then(() => {
              setFocus({ row: null, cell: null });
              localStorage.removeItem(`rowEdit`);
            });
        } else {
          notificationStore.addNotification({
            title: "Ошибка",
            description: "У вас есть не заполненые поля",
            icon: "error",
            type: "error",
          });
        }
      }
    }
    // check uniq modesCode
  };

  const startDistribution = () => {
    nsiStore.startDistributionCharacteristics().then(() => {
      nsiStore.startCheckCharacteristicsProtocol();
    });
  };

  const protocolRef = useRef<any>(null);

  useOnClickOutside(protocolRef, () => {
    setIsOpenProtocol(false);
  });

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("93").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "priority", direction: "asc" }]);
      }
    });
    return () => {
      localStorage.removeItem("expandedRowIds-93");
    };
  }, []);

  const initExpandedRowIds = JSON.parse(localStorage.getItem("expandedRowIds-93") as string) ?? [];

  const [expandedRowIds, setExpandedRowIds] = useState(initExpandedRowIds);

  return (
    <Container>
      {isOpenProtocol && (
        <ProtocolContainer ref={protocolRef}>
          {/*<EmptyRowProtocol />*/}
          {protocolCharacteristics?.status === "ERROR" ? (
            <>{protocolCharacteristics.error}</>
          ) : (
            protocolCharacteristics.notSentTo.map((el: any, index: number) => {
              return <RowProtocol key={`row-protocol-${index}`}>{el}</RowProtocol>;
            })
          )}
        </ProtocolContainer>
      )}
      <TableContainer>
        <Table
          columns={columns}
          setColumns={setColumns}
          tableData={modes}
          columnOrder={columnOrder}
          setColumnOrder={setColumnOrder}
          initSorting={initSorting}
          isLoading={settingsStore.isLoadingModes}
          defaultColumns={defaultColumns}
          tableKey="93"
          childrenKey="name"
          expandedRowIds={expandedRowIds}
          setExpandedRowIds={(e: any) => {
            localStorage.setItem("expandedRowIds-93", JSON.stringify(e));
            setExpandedRowIds(e);
          }}
          focusHeader={focusHeader}
          setFocusHeader={setFocusHeader}
          isFocusHeader={true}
          dataTest="modes.table"
          dataTestRows="modes-table.rows"
          headerComponents={
            isModeCenter && !isEast ? (
              <>
                <ButtonDistribution
                  title="Распространить настройки СРПГ в ДЦ"
                  message="Распространить настройки СРПГ в ДЦ (Номер ПБР [1СЗ], Номер ПБР [2СЗ], Характеристики, Отсутствие данных, MODES)"
                  // type="secondary"
                  onClick={() => startDistribution()}
                  disabled={protocolCharacteristics?.status === "SENDING"}
                />
                <Tooltip content={<TooltipContent>{getTextTooltip(protocolCharacteristics?.status)}</TooltipContent>} placement="top">
                  <ProtocolButton
                    title={
                      <ProtocolButtonContent>
                        <Circle status={protocolCharacteristics?.status} />
                        <>{protocolCharacteristics?.status === "SENDING" ? "Загрузка" : "Протокол"}</>
                      </ProtocolButtonContent>
                    }
                    // type="secondary"
                    message={" "}
                    disabled={!!!protocolCharacteristics?.status || protocolCharacteristics?.status === "SENDING" || protocolCharacteristics?.notSentTo?.length === 0}
                    onClick={() => {
                      setIsOpenProtocol(true);
                    }}
                  />
                </Tooltip>

                <Buttons>
                  <ButtonStyled title="Сохранить" disabled={isDisabled} onClick={onSave} dataTest="modes-header.save-button" />
                  <ButtonStyled
                    title="Отменить"
                    // type="secondary"
                    disabled={isDisabled}
                    onClick={onReset}
                    dataTest="modes-header.cancel-button"
                  />
                </Buttons>
              </>
            ) : undefined
          }
          customCells={customCells}
        />
      </TableContainer>
    </Container>
  );
});
