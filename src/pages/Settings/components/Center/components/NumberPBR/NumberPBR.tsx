import React, { FC, useEffect, useRef, useState } from "react";
import styled, { css } from "styled-components";
import { Button } from "components/Button";
import { Table } from "components/Table";
import { useStores } from "stores/useStore";
import { observer } from "mobx-react";
import { Combobox } from "components/Combobox";
import { Icon } from "components/Icon";
import { Checkbox } from "components/Checkbox";
import { Tooltip } from "components/Tooltip";
import { Circle, RowProtocol } from "../Characteristics";
import { ButtonDistribution, ProtocolButton, ProtocolButtonContent, ProtocolContainer } from "../Modes";
import { isEast, isModeCenter } from "../../../../../../utils/getMode";
import { useOnClickOutside } from "../../../../../../hooks/useOnClickOutside";
import { prepareDataTable } from "../../../../../../utils";
import { getWidthModal } from "../../../../../../helpers/adaptive";

const SegmentedButton = styled.div<{ selectedItems?: any; disabled?: any }>`
  width: 20px;
  height: 20px;
  border-radius: 6.93px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding-top: 1px;
  color: ${(p) => p.theme.textColor};
  ${(p) =>
    p.selectedItems &&
    css`
      background-color: ${(p) => p.theme.backgroundColorSecondary};
      border-radius: 10px;
      transition: 0.3s;
      box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.12), 0px 3px 1px rgba(0, 0, 0, 0.04);
      border: 0.5px solid rgba(0, 0, 0, 0.04);
    `};
  ${(p) =>
    p.disabled &&
    css`
      cursor: default;
    `}
`;

export const SegmentedContainer = styled.div`
  background-color: ${(p) => p.theme.lightGray};
  border-radius: 10px;
  display: flex;
  align-items: center;
  font-size: 10px;
  font-weight: 600;
  user-select: none;
  height: 20px;
  padding: 0 5px;
  color: ${(p) => p.theme.textColor};
`;

const SegmentedPickerLocal = (props: any) => {
  const { items, selectedItems, onChange, disabled } = props;

  const handleConfirmItem = (value: any, label: any) => {
    if (onChange && !disabled) {
      onChange({ value, label });
    }
  };

  return (
    <SegmentedContainer>
      {items.map((item: any, index: any) => {
        return (
          <SegmentedButton
            disabled={disabled}
            selectedItems={selectedItems === item.value}
            key={`segmented-${index}`}
            onClick={() => handleConfirmItem(item.value, item.label)}
          >
            {item.label}
          </SegmentedButton>
        );
      })}
    </SegmentedContainer>
  );
};

export const Container = styled.div`
  width: 100%;
  height: 100%;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  position: relative;
`;

export const Buttons = styled.div`
  //width: 100%;
  display: flex;
  //justify-content: flex-end;
  margin-left: auto;
  margin-right: 10px;
`;

export const ButtonStyled = styled(Button)`
  margin: 0 4px;
  width: 160px;
  //height: 28px;
`;

export const NameCell = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-right: 10px;
  color: ${(p) => p.theme.textColor};
`;

export const IconContainer = styled.div`
  cursor: pointer;
  margin-left: 10px;
  opacity: 0.6;
  transition: all 0.3s;
  margin-right: 10px;
  &:hover {
    opacity: 1;
  }
`;

export const CellTime = styled.div<{ isError?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  border: solid 1px transparent;
  padding: 0 20px;
  border-radius: 8px;

  ${(p) =>
    p.isError &&
    css`
      border: solid 1px ${(p) => p.theme.redActiveSupport};
    `}
`;

export const ErrorIcon = styled.div`
  color: ${(p) => p.theme.redActiveSupport};
  cursor: pointer;
  margin-top: 4px;
  //margin-left: 20px;
`;

export const TooltipContent = styled.div`
  width: 200px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const TableContainer = styled.div`
  height: calc(100vh - 40px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 78px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 74px);
  }
`;

export const NumberPbr: FC = observer(() => {
  const { settingsStore, tableStore, authStore } = useStores();
  const { pbrList, pbrListOriginal } = settingsStore;
  const { isCenter } = authStore;

  useEffect(() => {
    settingsStore.initNumberPBR(isModeCenter);
    if (isCenter && isModeCenter) {
      nsiStore.startCheckCharacteristicsProtocol();
    }
  }, []);

  let defaultColumns: any[];

  const [columnOrder, setColumnOrder] = useState<any>([]);

  const setDefaultColumns = () => {
    setColumns(() => {
      return columnOrder.map((el: any) => {
        return getDefaultColumns().find((item) => item.name === el);
      });
    });
  };

  const [initScreenWidth, setInitScreenWidth] = useState<any>(getWidthModal(document.documentElement.clientWidth));
  const [screenWidth, setScreenWidth] = useState<any>(initScreenWidth);
  const [isEditWidth, setEditWidth] = useState(false);

  const resizeWeb = () => {
    setScreenWidth(getWidthModal(document.documentElement.clientWidth));
  };

  useEffect(() => {
    window.addEventListener("resize", resizeWeb);
    return () => {
      window.removeEventListener("resize", resizeWeb);
    };
  }, []);

  useEffect(() => {
    if (initScreenWidth !== screenWidth && !isEditWidth) {
      setEditWidth(true);
      setDefaultColumns();
    } else if (initScreenWidth !== screenWidth && isEditWidth) {
      setDefaultColumns();
    } else if (initScreenWidth === screenWidth && isEditWidth) {
      setDefaultColumns();
    }
  }, [screenWidth]);

  const getDefaultColumns = () => {
    if (screenWidth >= 650 && screenWidth < 700) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 140, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 340, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 170 },
      ];
    }
    if (screenWidth >= 700 && screenWidth < 800) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 140, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 390, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 170 },
      ];
    }
    if (screenWidth >= 800 && screenWidth < 900) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 140, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 485, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 170 },
      ];
    }
    if (screenWidth >= 900 && screenWidth < 1024) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 150, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 570, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 180 },
      ];
    }
    if (screenWidth >= 1024 && screenWidth < 1152) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 170, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 640, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 200 },
      ];
    }
    if (screenWidth >= 1152 && screenWidth < 1280) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 190, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 734, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 220 },
      ];
    }
    if (screenWidth >= 1280 && screenWidth < 1400) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 210, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 820, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 240 },
      ];
    }
    if (screenWidth >= 1400 && screenWidth < 1600) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 230, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 900, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 260 },
      ];
    }
    if (screenWidth >= 1600 && screenWidth < 1700) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 280, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 1000, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 310 },
      ];
    }
    if (screenWidth >= 1700 && screenWidth < 1800) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 330, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 1000, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 360 },
      ];
    }
    if (screenWidth >= 1800 && screenWidth < 1900) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 380, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 1000, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 410 },
      ];
    }
    if (screenWidth >= 1900 && screenWidth < 2000) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 430, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 1000, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 460 },
      ];
    }
    if (screenWidth >= 2000 && screenWidth < 2100) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 430, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 1100, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 460 },
      ];
    }
    if (screenWidth >= 2100 && screenWidth < 2200) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 480, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 1100, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 510 },
      ];
    }
    if (screenWidth >= 2200 && screenWidth < 2560) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 480, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 1200, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 510 },
      ];
    }
    if (screenWidth >= 2560 && screenWidth < 3000) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 480, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 1450, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 510 },
      ];
    }
    if (screenWidth >= 3000 && screenWidth < 3840) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 580, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 1790, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 610 },
      ];
    }
    if (screenWidth >= 3840 && screenWidth < 4000) {
      return [
        { name: "name", title: "Номер ПБР [1СЗ]", width: 580, isSearch: true, isSort: "number" },
        { name: "time", title: "Час начала действия", width: 2640, isSort: "alphabet" },
        { name: "nextDay", title: "Следующий день", width: 610 },
      ];
    }
    return [
      { name: "name", title: "Номер ПБР [1СЗ]", width: 430, isSearch: true, isSort: "number" },
      { name: "time", title: "Час начала действия", width: 1000, isSort: "alphabet" },
      { name: "nextDay", title: "Следующий день", width: 460 },
    ];
  };

  defaultColumns = getDefaultColumns();

  const [elModes, setElModes] = useState<any[]>([]);

  const initChecked = pbrList.find((el: any) => el.nextDay)?.number;
  const [checked, setChecked] = useState(null);

  useEffect(() => {
    setChecked(initChecked);
  }, [initChecked]);

  const [errors, setErrors] = useState<any[]>([]);

  const validate = () => {
    setErrors([]);

    const newArray = pbrList.map((el: any) => {
      if (el.number === checked) {
        return { number: el.number, time: el.time, nextDay: true, name: el.name };
      }
      return { number: el.number, time: el.time, nextDay: false, name: el.name };
    });

    return { isError: false, newArray };
  };

  const customCell = [
    {
      name: "time",
      render: (value: any, row: any) => {
        const isErrorEl = errors.find((el) => el === row.number);
        return (
          <CellTime isError={isErrorEl} data-test="number-pbr-table.hour-beginning-action">
            <SegmentedPickerLocal
              items={new Array(24).fill(null).map((_: any, index) => ({ value: index, label: index }))}
              selectedItems={value}
              disabled={!isModeCenter}
              onChange={({ value }: any) => {
                settingsStore.changePpbr(row.number, value);
              }}
            />
          </CellTime>
        );
      },
    },
    {
      name: "nextDay",
      render: (_: any, row: any) => {
        return (
          <>
            <Checkbox
              status={row.number === checked}
              disabled={!isModeCenter}
              dataTest="number-pbr-table.next-day-checkbox"
              onChange={() => {
                if (isModeCenter) {
                  setChecked((prev) => {
                    if (prev === row.number) {
                      return null;
                    } else {
                      return row.number;
                    }
                  });
                }
              }}
            />
          </>
        );
      },
    },
    {
      name: "name",
      render: (value: any) => {
        return <NameCell>{value}</NameCell>;
      },
    },
  ];

  const [columns, setColumns] = useState<any>([]);

  useEffect(() => {
    tableStore.getTableParams("94").then((data: any) => {
      if (data) {
        setColumns(data);
      } else {
        setColumns(defaultColumns);
      }
    });
  }, []);

  const isDisabled =
    checked === initChecked &&
    pbrList.length == pbrListOriginal.length &&
    pbrList.every(function (element: any, index: number) {
      return element.nextDay === pbrListOriginal[index].nextDay && element.time === pbrListOriginal[index].time;
    });

  const saveEdit = (newArray: any[]) => {
    if (!isDisabled) {
      settingsStore.saveEdit(newArray).then((res: any) => {
        if (res) {
          settingsStore.initNumberPBR(isModeCenter);
        }
      });
    }
  };

  const { nsiStore } = useStores();
  const { protocolCharacteristics } = nsiStore;

  const startDistribution = () => {
    nsiStore.startDistributionCharacteristics().then(() => {
      nsiStore.startCheckCharacteristicsProtocol();
    });
  };

  const getTextTooltip = (status: "SENDING" | "NOT_FULLY_SENT" | "ERROR" | "DONE") => {
    if (status === "SENDING") {
      return "Настройки распространяются";
    }
    if (status === "NOT_FULLY_SENT") {
      return "Настройки успешно распространены во все ДЦ, кроме...";
    }
    if (status === "ERROR") {
      return "Настройки не распространены ни в один ДЦ";
    }
    if (status === "DONE") {
      return "Настройки успешно распространены";
    }
  };

  const [isOpenProtocol, setIsOpenProtocol] = useState(false);

  const protocolRef = useRef<any>(null);

  useOnClickOutside(protocolRef, () => {
    setIsOpenProtocol(false);
  });

  const isEditArray =
    pbrList.length == pbrListOriginal.length &&
    pbrList.every(function (element: any, index: any) {
      return element.time === pbrListOriginal[index].time;
    });

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("94").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "name", direction: "asc" }]);
      }
    });
  }, []);

  return (
    <Container>
      {isOpenProtocol && (
        <ProtocolContainer ref={protocolRef} data-test="load-pg-header.protocol-list-container">
          {protocolCharacteristics?.status === "ERROR" ? (
            <>{protocolCharacteristics.error}</>
          ) : (
            protocolCharacteristics.notSentTo.map((el: any, index: number) => {
              return (
                <RowProtocol key={`row-protocol-${index}`} data-test="load-pg-header.protocol-list-row">
                  {el}
                </RowProtocol>
              );
            })
          )}
        </ProtocolContainer>
      )}
      <TableContainer>
        <Table
          columns={columns}
          setColumns={setColumns}
          tableData={prepareDataTable(pbrList)}
          isLoading={false}
          columnOrder={columnOrder}
          setColumnOrder={setColumnOrder}
          tableKey="94"
          defaultColumns={defaultColumns}
          customCells={customCell}
          initSorting={initSorting}
          dataTest="number-pbr.table"
          dataTestRows="number-pbr-table.rows"
          headerComponents={
            !isEast && isModeCenter ? (
              <>
                <ButtonDistribution
                  title="Распространить настройки СРПГ в ДЦ"
                  message="Распространить настройки СРПГ в ДЦ (Номер ПБР [1СЗ], Номер ПБР [2СЗ], Характеристики, Отсутствие данных, MODES)"
                  // type="secondary"
                  onClick={() => startDistribution()}
                  disabled={protocolCharacteristics?.status === "SENDING"}
                  dataTest="load-pg-header.start-distribution-button"
                />
                <Tooltip content={<TooltipContent>{getTextTooltip(protocolCharacteristics?.status)}</TooltipContent>} placement="bottom">
                  <ProtocolButton
                    title={
                      <ProtocolButtonContent
                        data-test={protocolCharacteristics?.status === "SENDING" ? "load-pg-header.upload-button" : "load-pg-header.protocol-button"}
                      >
                        <Circle status={protocolCharacteristics?.status} />
                        <>{protocolCharacteristics?.status === "SENDING" ? "Загрузка" : "Протокол"}</>
                      </ProtocolButtonContent>
                    }
                    // type="secondary"
                    message={" "}
                    disabled={!!!protocolCharacteristics?.status || protocolCharacteristics?.status === "SENDING" || protocolCharacteristics?.notSentTo?.length === 0}
                    onClick={() => {
                      setIsOpenProtocol(true);
                    }}
                  />
                </Tooltip>
                <Buttons>
                  <ButtonStyled
                    disabled={isEditArray && checked === initChecked}
                    title="Сохранить"
                    isError={errors.length > 0}
                    dataTest="number-pbr-table-header.save-button"
                    onClick={() => {
                      const { isError, newArray } = validate();
                      if (!isError) {
                        saveEdit(newArray);
                      }
                    }}
                  />
                  <ButtonStyled
                    disabled={isEditArray && checked === initChecked}
                    title="Отменить"
                    onClick={() => {
                      setElModes([]);
                      setChecked(initChecked);
                      setErrors([]);
                      settingsStore.resetPbr();
                    }}
                    // type="secondary"
                  />
                </Buttons>
              </>
            ) : undefined
          }
        />
      </TableContainer>
    </Container>
  );
});
