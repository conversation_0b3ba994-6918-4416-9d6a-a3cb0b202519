import React, { useEffect, useRef, useState } from "react";
import styled from "styled-components";
import { NoData } from "components/NoData";
import { Table } from "../../../../../../components/Table";
import { useStores } from "../../../../../../stores/useStore";
import { Button } from "components/Button";
import { ModalCreate } from "./components/ModalCreate";
import { ModalDelete } from "./components/ModalDelete";
import { observer } from "mobx-react";
import { ButtonStyled, Circle, RowProtocol, TooltipContent } from "../Characteristics";
import { useOnClickOutside } from "../../../../../../hooks/useOnClickOutside";
import { Tooltip } from "components/Tooltip";
import { isEast, isModeCenter } from "../../../../../../utils/getMode";
import { prepareDataTable } from "../../../../../../utils";
import { getWidthModal } from "../../../../../../helpers/adaptive";

export const Container = styled.div`
  position: relative;
  height: 100%;
`;

export const ButtonDistribution = styled(Button)`
  width: auto;
  margin: 0 4px;
`;

export const Buttons = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-left: auto;
  margin-right: 10px;
`;

export const Actions = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
`;

export const Action = styled(Button)`
  width: 20px;
  height: 20px;
  margin-bottom: 6px;
`;

export const ProtocolContainer = styled.div`
  position: absolute;
  width: 400px;
  height: auto;
  min-height: 30px;
  max-height: 300px;
  border: solid 1px ${(p) => p.theme.lightGray};
  background-color: ${(p) => p.theme.white};
  z-index: 1001;
  left: 110px;
  top: 30px;
  border-radius: 6px;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  overflow-x: hidden;
`;

export const ProtocolButtonContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const TableContainer = styled.div`
  height: calc(100vh - 40px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 78px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 74px);
  }
`;

export const LackOfData = observer(() => {
  const { settingsStore, tableStore, authStore } = useStores();
  const { lackOfData, lackOfDataOriginal } = settingsStore;
  const { isCenter } = authStore;
  const isEditMode = isCenter && isModeCenter;
  let defaultColumns: any[];

  const [columnOrder, setColumnOrder] = useState<any>([]);

  const setDefaultColumns = () => {
    setColumns(() => {
      return columnOrder.map((el: any) => {
        // return getDefaultColumns().find((item: any) => item.name === el);
        return isEast ? getDefaultColumnsEast().find((item: any) => item.name === el) : getDefaultColumns().find((item: any) => item.name === el);
      });
    });
  };
  const [initScreenWidth, setInitScreenWidth] = useState<any>(getWidthModal(document.documentElement.clientWidth));
  const [screenWidth, setScreenWidth] = useState<any>(initScreenWidth);
  const [isEditWidth, setEditWidth] = useState(false);

  const resizeWeb = () => {
    setScreenWidth(getWidthModal(document.documentElement.clientWidth));
  };

  useEffect(() => {
    window.addEventListener("resize", resizeWeb);
    return () => {
      window.removeEventListener("resize", resizeWeb);
    };
  }, []);

  useEffect(() => {
    if (initScreenWidth !== screenWidth && !isEditWidth) {
      setEditWidth(true);
      setDefaultColumns();
    } else if (initScreenWidth !== screenWidth && isEditWidth) {
      setDefaultColumns();
    } else if (initScreenWidth === screenWidth && isEditWidth) {
      setDefaultColumns();
    }
  }, [screenWidth]);

  const getDefaultColumnsEast = () => {
    if (screenWidth >= 650 && screenWidth < 700) {
      return [
        { name: "name", title: "Тип объекта", width: 255, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 385, isSort: "number" },
      ];
    }
    if (screenWidth >= 700 && screenWidth < 800) {
      return [
        { name: "name", title: "Тип объекта", width: 305, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 385, isSort: "number" },
      ];
    }
    if (screenWidth >= 800 && screenWidth < 900) {
      return [
        { name: "name", title: "Тип объекта", width: 355, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 435, isSort: "number" },
      ];
    }
    if (screenWidth >= 900 && screenWidth < 1024) {
      return [
        { name: "name", title: "Тип объекта", width: 405, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 485, isSort: "number" },
      ];
    }
    if (screenWidth >= 1024 && screenWidth < 1152) {
      return [
        { name: "name", title: "Тип объекта", width: 505, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 505, isSort: "number" },
      ];
    }
    if (screenWidth >= 1152 && screenWidth < 1280) {
      return [
        { name: "name", title: "Тип объекта", width: 575, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 575, isSort: "number" },
      ];
    }
    if (screenWidth >= 1280 && screenWidth < 1400) {
      return [
        { name: "name", title: "Тип объекта", width: 635, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 635, isSort: "number" },
      ];
    }
    if (screenWidth >= 1400 && screenWidth < 1600) {
      return [
        { name: "name", title: "Тип объекта", width: 695, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 695, isSort: "number" },
      ];
    }
    if (screenWidth >= 1600 && screenWidth < 1700) {
      return [
        { name: "name", title: "Тип объекта", width: 795, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 795, isSort: "number" },
      ];
    }
    if (screenWidth >= 1700 && screenWidth < 1800) {
      return [
        { name: "name", title: "Тип объекта", width: 845, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 845, isSort: "number" },
      ];
    }
    if (screenWidth >= 1800 && screenWidth < 1900) {
      return [
        { name: "name", title: "Тип объекта", width: 895, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 895, isSort: "number" },
      ];
    }
    if (screenWidth >= 1900 && screenWidth < 2000) {
      return [
        { name: "name", title: "Тип объекта", width: 945, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 945, isSort: "number" },
      ];
    }
    if (screenWidth >= 2000 && screenWidth < 2100) {
      return [
        { name: "name", title: "Тип объекта", width: 995, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 995, isSort: "number" },
      ];
    }
    if (screenWidth >= 2100 && screenWidth < 2200) {
      return [
        { name: "name", title: "Тип объекта", width: 1045, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 1045, isSort: "number" },
      ];
    }
    if (screenWidth >= 2200 && screenWidth < 2560) {
      return [
        { name: "name", title: "Тип объекта", width: 1095, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 1095, isSort: "number" },
      ];
    }
    if (screenWidth >= 2560 && screenWidth < 3000) {
      return [
        { name: "name", title: "Тип объекта", width: 1275, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 1275, isSort: "number" },
      ];
    }
    if (screenWidth >= 3000 && screenWidth < 3840) {
      return [
        { name: "name", title: "Тип объекта", width: 1495, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 1495, isSort: "number" },
      ];
    }
    if (screenWidth >= 3840 && screenWidth < 4000) {
      return [
        { name: "name", title: "Тип объекта", width: 1915, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 1915, isSort: "number" },
      ];
    }
    return [
      { name: "name", title: "Тип объекта", width: 950, isSort: "alphabet" },
      { name: "value", title: "Значение", width: 950, isSort: "number" },
    ];
  };

  const getDefaultColumns = () => {
    if (screenWidth >= 650 && screenWidth < 700) {
      return [
        { name: "name", title: "Тип объекта", width: 200, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 330, isSort: "number" },
        { name: "actions", title: "Действия", width: 110 },
      ];
    }
    if (screenWidth >= 700 && screenWidth < 800) {
      return [
        { name: "name", title: "Тип объекта", width: 250, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 330, isSort: "number" },
        { name: "actions", title: "Действия", width: 110 },
      ];
    }
    if (screenWidth >= 800 && screenWidth < 900) {
      return [
        { name: "name", title: "Тип объекта", width: 300, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 380, isSort: "number" },
        { name: "actions", title: "Действия", width: 110 },
      ];
    }
    if (screenWidth >= 900 && screenWidth < 1024) {
      return [
        { name: "name", title: "Тип объекта", width: 300, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 380, isSort: "number" },
        { name: "actions", title: "Действия", width: 210 },
      ];
    }
    if (screenWidth >= 1024 && screenWidth < 1152) {
      return [
        { name: "name", title: "Тип объекта", width: 400, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 400, isSort: "number" },
        { name: "actions", title: "Действия", width: 210 },
      ];
    }
    if (screenWidth >= 1152 && screenWidth < 1280) {
      return [
        { name: "name", title: "Тип объекта", width: 470, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 470, isSort: "number" },
        { name: "actions", title: "Действия", width: 210 },
      ];
    }
    if (screenWidth >= 1280 && screenWidth < 1400) {
      return [
        { name: "name", title: "Тип объекта", width: 530, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 530, isSort: "number" },
        { name: "actions", title: "Действия", width: 210 },
      ];
    }
    if (screenWidth >= 1400 && screenWidth < 1600) {
      return [
        { name: "name", title: "Тип объекта", width: 590, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 590, isSort: "number" },
        { name: "actions", title: "Действия", width: 210 },
      ];
    }
    if (screenWidth >= 1600 && screenWidth < 1700) {
      return [
        { name: "name", title: "Тип объекта", width: 690, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 690, isSort: "number" },
        { name: "actions", title: "Действия", width: 210 },
      ];
    }
    if (screenWidth >= 1700 && screenWidth < 1800) {
      return [
        { name: "name", title: "Тип объекта", width: 740, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 740, isSort: "number" },
        { name: "actions", title: "Действия", width: 210 },
      ];
    }
    if (screenWidth >= 1800 && screenWidth < 1900) {
      return [
        { name: "name", title: "Тип объекта", width: 790, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 790, isSort: "number" },
        { name: "actions", title: "Действия", width: 210 },
      ];
    }
    if (screenWidth >= 1900 && screenWidth < 2000) {
      return [
        { name: "name", title: "Тип объекта", width: 840, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 840, isSort: "number" },
        { name: "actions", title: "Действия", width: 210 },
      ];
    }
    if (screenWidth >= 2000 && screenWidth < 2100) {
      return [
        { name: "name", title: "Тип объекта", width: 890, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 890, isSort: "number" },
        { name: "actions", title: "Действия", width: 210 },
      ];
    }
    if (screenWidth >= 2100 && screenWidth < 2200) {
      return [
        { name: "name", title: "Тип объекта", width: 940, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 940, isSort: "number" },
        { name: "actions", title: "Действия", width: 210 },
      ];
    }
    if (screenWidth >= 2200 && screenWidth < 2560) {
      return [
        { name: "name", title: "Тип объекта", width: 990, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 990, isSort: "number" },
        { name: "actions", title: "Действия", width: 210 },
      ];
    }
    if (screenWidth >= 2560 && screenWidth < 3000) {
      return [
        { name: "name", title: "Тип объекта", width: 1170, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 1170, isSort: "number" },
        { name: "actions", title: "Действия", width: 210 },
      ];
    }
    if (screenWidth >= 3000 && screenWidth < 3840) {
      return [
        { name: "name", title: "Тип объекта", width: 1390, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 1390, isSort: "number" },
        { name: "actions", title: "Действия", width: 210 },
      ];
    }
    if (screenWidth >= 3840 && screenWidth < 4000) {
      return [
        { name: "name", title: "Тип объекта", width: 1810, isSort: "alphabet" },
        { name: "value", title: "Значение", width: 1810, isSort: "number" },
        { name: "actions", title: "Действия", width: 210 },
      ];
    }
    return [
      { name: "name", title: "Тип объекта", width: 840, isSort: "alphabet" },
      { name: "value", title: "Значение", width: 840, isSort: "number" },
      { name: "actions", title: "Действия", width: 210 },
    ];
  };

  defaultColumns = isEast ? getDefaultColumnsEast() : getDefaultColumns();

  const [objectEdit, setObjectEdit] = useState<any>(null);
  const [objectDelete, setObjectDelete] = useState<any>(null);

  const customCells = !isEast
    ? [
        {
          name: "actions",
          render: (value: any, row: any) => {
            return (
              <Actions>
                <Action
                  icon="edit"
                  type="secondary"
                  widthIcon={14}
                  onClick={() => {
                    setObjectEdit({ code: row.tabId, value: row.value });
                    setIsModalCreate(true);
                  }}
                />
                <Action icon="trash" type="secondary" widthIcon={14} onClick={() => setObjectDelete({ code: row.tabId, value: row.value })} />
              </Actions>
            );
          },
        },
      ]
    : [];
  const [columns, setColumns] = useState<any>([]);

  useEffect(() => {
    tableStore.getTableParams("91").then((data: any) => {
      if (data) {
        setColumns(data);
      } else {
        setColumns(defaultColumns);
      }
    });
  }, []);

  useEffect(() => {
    settingsStore.initLackOfData();
    if (isCenter && isModeCenter) {
      nsiStore.startCheckCharacteristicsProtocol();
    }
  }, []);

  const [isModalCreate, setIsModalCreate] = useState(false);

  const { nsiStore } = useStores();
  const { protocolCharacteristics } = nsiStore;

  const startDistribution = () => {
    nsiStore.startDistributionCharacteristics().then(() => {
      nsiStore.startCheckCharacteristicsProtocol();
    });
  };

  const [isOpenProtocol, setIsOpenProtocol] = useState(false);

  const protocolRef = useRef<any>(null);

  useOnClickOutside(protocolRef, () => {
    setIsOpenProtocol(false);
  });

  const getTextTooltip = (status: "SENDING" | "NOT_FULLY_SENT" | "ERROR" | "DONE") => {
    if (status === "SENDING") {
      return "Настройки распространяются";
    }
    if (status === "NOT_FULLY_SENT") {
      return "Настройки успешно распространены во все ДЦ, кроме...";
    }
    if (status === "ERROR") {
      return "Настройки не распространены ни в один ДЦ";
    }
    if (status === "DONE") {
      return "Настройки успешно распространены";
    }
  };

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("91").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "name", direction: "asc" }]);
      }
    });
  }, []);

  return (
    <Container>
      {isOpenProtocol && (
        <ProtocolContainer ref={protocolRef}>
          {protocolCharacteristics?.status === "ERROR" ? (
            <>{protocolCharacteristics.error}</>
          ) : (
            protocolCharacteristics.notSentTo.map((el: any, index: number) => {
              return <RowProtocol key={`row-protocol-${index}`}>{el}</RowProtocol>;
            })
          )}
        </ProtocolContainer>
      )}
      {isModalCreate && (
        <ModalCreate
          onCancel={() => {
            setIsModalCreate(false);
            setObjectEdit(null);
          }}
          editObject={objectEdit}
        />
      )}
      {objectDelete && (
        <ModalDelete
          onCancel={() => setObjectDelete(null)}
          objectDelete={objectDelete}
          onConfirm={() => {
            settingsStore.deleteObject(objectDelete.code).then((isComplete: boolean) => {
              if (isComplete) {
                setObjectDelete(null);
                settingsStore.initLackOfData();
              }
            });
          }}
        />
      )}
      <TableContainer>
        <Table
          tableData={prepareDataTable(lackOfData)}
          defaultColumns={defaultColumns}
          columnOrder={columnOrder}
          setColumnOrder={setColumnOrder}
          columns={columns}
          hiddenColumnNames={!isEditMode || isEast ? ["actions"] : []}
          setColumns={setColumns}
          customCells={customCells}
          originalTableData={lackOfDataOriginal}
          tableKey="91"
          initSorting={initSorting}
          disabledSearches={["actions"]}
          dataTest="lack-data.table"
          dataTestDefaultCells="lack-data-table.cells"
          dataTestRows="lack-data-table.rows"
          headerComponents={
            !isEast && isEditMode ? (
              <>
                <ButtonDistribution
                  title="Распространить настройки СРПГ в ДЦ"
                  message="Распространить настройки СРПГ в ДЦ (Номер ПБР [1СЗ], Номер ПБР [2СЗ], Характеристики, Отсутствие данных, MODES)"
                  // type="secondary"
                  onClick={() => startDistribution()}
                  disabled={protocolCharacteristics?.status === "SENDING"}
                />
                <Tooltip content={<TooltipContent>{getTextTooltip(protocolCharacteristics?.status)}</TooltipContent>} placement="top">
                  <ButtonStyled
                    title={
                      <ProtocolButtonContent>
                        <Circle status={protocolCharacteristics?.status} />
                        <>{protocolCharacteristics?.status === "SENDING" ? "Загрузка" : "Протокол"}</>
                      </ProtocolButtonContent>
                    }
                    // type="secondary"
                    message={" "}
                    disabled={!!!protocolCharacteristics?.status || protocolCharacteristics?.status === "SENDING" || protocolCharacteristics?.notSentTo?.length === 0}
                    onClick={() => {
                      setIsOpenProtocol(true);
                    }}
                  />
                </Tooltip>
                <Buttons>
                  <Button
                    title="Добавить"
                    // type="secondary"
                    onClick={() => setIsModalCreate(true)}
                    dataTest="lack-data-header.add-button"
                  />
                </Buttons>
              </>
            ) : undefined
          }
        />
      </TableContainer>
    </Container>
  );
});
