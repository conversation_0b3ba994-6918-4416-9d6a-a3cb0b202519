import React, { <PERSON> } from "react";
import styled from "styled-components";
import { Modal } from "../../../../../../../../components/Modal";
import { observer } from "mobx-react";

interface ModalDeleteProps {
  onCancel?: any;
  objectDelete?: any;
  onConfirm?: any;
}

export const ModalDelete: FC<ModalDeleteProps> = observer((props) => {
  const { onCancel, objectDelete, onConfirm } = props;
  return (
    <Modal
      isOverLay
      colorScheme="red"
      title="Удаление"
      description={`Вы действительно хотите удалить объект ${objectDelete.code} ?`}
      confirmText="Удалить"
      cancelText="Отмена"
      onCancel={onCancel}
      onConfirm={onConfirm}
      width={400}
      height={160}
    />
  );
});
