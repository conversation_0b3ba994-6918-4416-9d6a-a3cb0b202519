import React, { FC, useEffect, useState } from "react";
import styled from "styled-components";
import { Modal } from "../../../../../../../../components/Modal";
import { Combobox } from "../../../../../../../../components/Combobox";
import { TextField } from "../../../../../../../../components/TextField";
import { useStores } from "../../../../../../../../stores/useStore";
import { observer } from "mobx-react";

interface ModalCreateProps {
  onCancel?: any;
  editObject?: any;
}

export const Row = styled.div`
  width: 100%;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 16px 0;
`;

export const Label = styled.div`
  background-color: ${(p) => p.theme.lightGray};
  border-radius: 4px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const ComboboxStyled = styled(Combobox)`
  width: 100%;
`;

export const ModalCreate: FC<ModalCreateProps> = observer((props) => {
  const { onCancel, editObject } = props;
  const { settingsStore } = useStores();
  const { lackOfDataCodes } = settingsStore;
  const [code, setCode] = useState(null);
  const [value, setValue] = useState("");

  useEffect(() => {
    if (editObject) {
      setCode(editObject.code);
      setValue(editObject.value);
    }
  }, []);

  const addObject = () => {
    const object = { code, noValue: value };
    settingsStore.addObject(object, editObject).then((isComplete: any) => {
      if (isComplete) {
        onCancel();
        settingsStore.initLackOfData();
      }
    });
  };

  const [errors, setErrors] = useState<any>([]);

  return (
    <Modal
      isOverLay
      onCancel={onCancel}
      title={editObject ? "Редактирование характеристики" : "Добавление характеристики"}
      cancelText="Отмена"
      confirmText={editObject ? "Сохранить" : "Добавить"}
      isDisabledConfirm={errors.length > 0}
      dataTestContainer={editObject ? "" : "lack-data-add-modal.container"}
      dataTestConfirmButton={editObject ? "" : "lack-data-add-modal.add-button"}
      onConfirm={() => {
        setErrors([]);
        const isValid = value.length <= 10;
        if (isValid) {
          addObject();
        } else {
          setErrors(["Значение превышает 10 символов"]);
        }
      }}
      width={600}
      height={240}
    >
      <Row>
        {editObject ? <Label>{code}</Label> : <ComboboxStyled items={lackOfDataCodes} selectedValue={code} width={438} onChange={({ value }) => setCode(value)} dataTest="lack-data-modal.type-parameter-combobox" />}
      </Row>
      <Row>
        <TextField
          errors={errors}
          value={value}
          onChange={(e) => {
            setErrors([]);
            setValue(e.target.value);
          }}
          maxLength={10}
          dataTest="lack-data-modal.value-parameter-input"
        />
      </Row>
    </Modal>
  );
});
