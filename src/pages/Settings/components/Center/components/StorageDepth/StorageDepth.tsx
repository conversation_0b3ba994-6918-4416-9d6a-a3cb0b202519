import React, { FC, useEffect, useState } from "react";
import { useStores } from "stores/useStore";
import styled, { css } from "styled-components";
import { Table } from "components/Table";
import { observer } from "mobx-react";
import { Button } from "components/Button";
import { Combobox } from "components/Combobox";
import { TextField } from "components/TextField";
import { prepareDataTable } from "utils";
import { getWidthModal } from "../../../../../../helpers/adaptive";

export const Container = styled.div`
  width: 100%;
`;

export const Buttons = styled.div`
  display: flex;
  margin-left: auto;
  margin-right: 10px;
`;

export const ButtonStyled = styled(Button)`
  margin: 0 10px;
  width: 160px;
`;

export const CellMonth = styled.div<{ errors?: any }>`
  width: 400px;
  height: 26px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  ${(p) =>
    p.errors &&
    css`
      border: solid 1px red;
    `}
`;

export const Label = styled.div`
  cursor: pointer;
`;

export const TableContainer = styled.div`
  height: calc(100vh - 40px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 78px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 74px);
  }
`;

export const MonthLabel = styled.div`
  margin-left: 4px;
  color: ${(p) => p.theme.textColor};
`;

export const StorageDepth: FC = observer((props) => {
  const { settingsStore, tableStore } = useStores();
  const [errors, setErrors] = useState([]);

  useEffect(() => {
    settingsStore.initStorageDepth();
  }, []);

  const { listStorageDepth, listStorageDepthOriginal } = settingsStore;

  let defaultColumns: any[];

  const [columnOrder, setColumnOrder] = useState<any>([]);

  const setDefaultColumns = () => {
    setColumns(() => {
      return columnOrder.map((el: any) => {
        return getDefaultColumns().find((item) => item.name === el);
      });
    });
  };
  const [initScreenWidth, setInitScreenWidth] = useState<any>(getWidthModal(document.documentElement.clientWidth));
  const [screenWidth, setScreenWidth] = useState<any>(initScreenWidth);
  const [isEditWidth, setEditWidth] = useState(false);

  const resizeWeb = () => {
    setScreenWidth(getWidthModal(document.documentElement.clientWidth));
  };

  useEffect(() => {
    window.addEventListener("resize", resizeWeb);
    return () => {
      window.removeEventListener("resize", resizeWeb);
    };
  }, []);

  useEffect(() => {
    if (initScreenWidth !== screenWidth && !isEditWidth) {
      setEditWidth(true);
      setDefaultColumns();
    } else if (initScreenWidth !== screenWidth && isEditWidth) {
      setDefaultColumns();
    } else if (initScreenWidth === screenWidth && isEditWidth) {
      setDefaultColumns();
    }
  }, [screenWidth]);

  const getDefaultColumns = () => {
    if (screenWidth >= 650 && screenWidth < 700) {
      return [
        { name: "name", title: "Данные", width: 320, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 320, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 700 && screenWidth < 800) {
      return [
        { name: "name", title: "Данные", width: 345, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 345, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 800 && screenWidth < 900) {
      return [
        { name: "name", title: "Данные", width: 395, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 395, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 900 && screenWidth < 1024) {
      return [
        { name: "name", title: "Данные", width: 445, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 445, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1024 && screenWidth < 1152) {
      return [
        { name: "name", title: "Данные", width: 510, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 510, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1152 && screenWidth < 1280) {
      return [
        { name: "name", title: "Данные", width: 570, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 570, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1280 && screenWidth < 1400) {
      return [
        { name: "name", title: "Данные", width: 630, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 630, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1400 && screenWidth < 1600) {
      return [
        { name: "name", title: "Данные", width: 690, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 690, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1600 && screenWidth < 1700) {
      return [
        { name: "name", title: "Данные", width: 790, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 790, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1700 && screenWidth < 1800) {
      return [
        { name: "name", title: "Данные", width: 840, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 840, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1800 && screenWidth < 1900) {
      return [
        { name: "name", title: "Данные", width: 890, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 890, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1900 && screenWidth < 2000) {
      return [
        { name: "name", title: "Данные", width: 940, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 940, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 2000 && screenWidth < 2100) {
      return [
        { name: "name", title: "Данные", width: 990, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 990, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 2100 && screenWidth < 2200) {
      return [
        { name: "name", title: "Данные", width: 1040, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 1040, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 2200 && screenWidth < 2560) {
      return [
        { name: "name", title: "Данные", width: 1090, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 1090, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 2560 && screenWidth < 3000) {
      return [
        { name: "name", title: "Данные", width: 1270, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 1270, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 3000 && screenWidth < 3840) {
      return [
        { name: "name", title: "Данные", width: 1490, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 1490, isSearch: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 3840 && screenWidth < 4000) {
      return [
        { name: "name", title: "Данные", width: 1910, isSearch: true, isSort: "alphabet" },
        { name: "months", title: "Глубина хранения", width: 1910, isSearch: true, isSort: "alphabet" },
      ];
    }
    return [
      { name: "name", title: "Данные", width: 940, isSearch: true, isSort: "alphabet" },
      { name: "months", title: "Глубина хранения", width: 940, isSearch: true, isSort: "alphabet" },
    ];
  };

  defaultColumns = getDefaultColumns();

  const [columns, setColumns] = useState<any[]>([]);

  useEffect(() => {
    tableStore.getTableParams("97").then((data: any) => {
      if (data) {
        setColumns(data);
      } else {
        setColumns(defaultColumns);
      }
    });
  }, []);

  // const getLabel = (value: number) => {
  //   if (value === 1) {
  //     return `${value} месяц`;
  //   }
  //   if (value > 1 && value < 5) {
  //     return `${value} месяца`;
  //   }
  //   if (value >= 5) {
  //     return `${value} месяцев`;
  //   }
  // };

  // const [editMonths, setEditMonths] = useState<any[]>([]);

  const getErrors = (res: any) => {
    return res
      .map((el: any) => {
        if (el.name === "Журналы системы" || el.name === "ПГ") {
          if (Number(el.months)) {
            if (Number(el.months) < 1 || Number(el.months) > 12) {
              return { name: el.name, value: "Значение должно быть задано числом от 1 до 12" };
            } else {
              return null;
            }
          } else {
            return { name: el.name, value: "Значение должно быть задано числом от 1 до 12" };
          }
        } else {
          if (Number(el.months)) {
            if (Number(el.months) < 1 || Number(el.months) > 120) {
              return { name: el.name, value: "Значение должно быть задано числом от 1 до 120" };
            } else {
              return null;
            }
          } else {
            return { name: el.name, value: "Значение должно быть задано числом от 1 до 120" };
          }
        }
      })
      .filter((el: any) => el);
  };

  const [focus, setFocus] = useState<any>({ row: null, cell: null });
  const [focusHeader, setFocusHeader] = useState<any>(null);

  const customCell = [
    {
      name: "months",
      render: (value: any, row: any) => {
        // @ts-ignore
        const error = errors.find((item: any) => item?.name === row?.name)?.value ?? null;
        const localCur = JSON.parse(localStorage.getItem(`rowEdit`) as string) ?? null;
        const initCurrent = localCur && localCur?.key === row.tabId ? localCur?.value : row?.months ?? "";
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [current, setCurrent] = useState(initCurrent);
        const initFocus = focus.row === row.tabId && "months" === focus.cell && focusHeader === null;
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [isFocus, setIsFocus] = useState(initFocus);
        const originalValue = value ? value : "";
        const isChange = current !== originalValue;
        return (
          <>
            <CellMonth
              errors={false}
              onFocus={() => {
                if (focus.row === null && focus.cell === null) {
                  setFocus({ row: row.tabId, cell: "months" });
                }
                setIsFocus(true);
              }}
              onBlur={() => {
                setIsFocus(false);
                localStorage.removeItem(row.tabId);
              }}
            >
              <TextField
                value={current}
                focus={isFocus}
                isChange={isChange}
                data-months={row.tabId}
                onChange={(e: any) => {
                  setCurrent(e.target.value);
                  localStorage.setItem(`rowEdit`, JSON.stringify({ key: row.tabId, value: e.target.value }));
                }}
                // onBlur={() => {
                //   settingsStore.changeStorageMonth(row.name, current);
                // }}
                errors={[error]}
                dataTest="storage-depth-table.count-months-input"
              />
              <MonthLabel>месяцев</MonthLabel>
            </CellMonth>
          </>
        );
      },
    },
  ];

  const onSave = () => {
    setErrors([]);

    let dataModes: any = [];
    const collectionOfElements = document.querySelectorAll("[data-months]");
    for (let i = 0; i < collectionOfElements.length; i++) {
      // @ts-ignore
      const key = collectionOfElements[i].getAttribute("data-months");
      // @ts-ignore
      dataModes[i] = { key, value: collectionOfElements[i]?.value };
    }
    const res = listStorageDepth.map((el: any) => {
      const find = dataModes.find((item: any) => item.key === el.tabId);
      if (find) {
        return { ...el, months: find.value };
      }
      return el;
    });
    const errorsMonth = getErrors(res);
    if (errorsMonth.length === 0) {
      settingsStore.saveStorageDepth(res).then(() => {
        settingsStore.initStorageDepth();
        setFocus({ row: null, cell: null });
        localStorage.removeItem(`rowEdit`);
      });
    } else {
      setErrors(errorsMonth);
    }
  };
  const isDisabledButton = !Object.values(focus).some((el) => el);

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("97").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "name", direction: "asc" }]);
      }
    });
  }, []);

  return (
    <Container>
      <TableContainer>
        <Table
          columns={columns}
          setColumns={setColumns}
          initSorting={initSorting}
          columnOrder={columnOrder}
          setColumnOrder={setColumnOrder}
          focusHeader={focusHeader}
          setFocusHeader={setFocusHeader}
          isFocusHeader={true}
          tableData={prepareDataTable(listStorageDepth)}
          originalTableData={listStorageDepthOriginal}
          isLoading={false}
          tableKey="97"
          defaultColumns={defaultColumns}
          customCells={customCell}
          dataTest="storage-depth.table"
          dataTestRows="storage-depth-table.rows"
          headerComponents={
            <Buttons>
              <ButtonStyled
                disabled={isDisabledButton}
                title="Сохранить"
                onClick={() => {
                  onSave();
                }}
                isError={errors.length > 0}
                dataTest="storage-depth-table-header.save-button"
              />
              <ButtonStyled
                disabled={isDisabledButton}
                title="Отменить"
                onClick={() => {
                  settingsStore.resetStorageMonths();
                  setFocus({ row: null, cell: null });
                  localStorage.removeItem(`rowEdit`);
                  setErrors([]);
                }}
                // type="secondary"
              />
            </Buttons>
          }
        />
      </TableContainer>
    </Container>
  );
});
