import React, { FC, useEffect, useState } from "react";
import styled, { css } from "styled-components";
import queryString from "query-string";
import { useLocation, useNavigate } from "react-router-dom";
import { useStores } from "stores/useStore";
import { Button } from "components/Button";
import { TextField } from "components/TextField";
import { Combobox } from "components/Combobox";
import { observer } from "mobx-react";
import { isModeCenter } from "utils/getMode";
import { Tabs } from "components/Tabs";
import { Loader } from "components/Loader";
import { Switch } from "components/Switch";

export const SecondsLabel = styled.div`
  width: 100px;
  height: 22px;
  border-radius: 4px;
  padding: 4px 10px;
  user-select: none;
`;

export const TextFieldInterval = styled(TextField)<{ type?: string }>`
  width: 350px;
  ${(p) =>
    p.type &&
    css`
      width: 350px;
    `}
`;

export const TextFieldStyled = styled(TextField)<{ type?: string }>`
  width: 450px;
  ${(p) =>
    p.type &&
    css`
      width: 410px;
    `}
`;

export const Container = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
`;

export const ButtonStyled = styled(Button)`
  height: 40px;
  margin: 10px 0;
`;

interface ExternalSystemProps {
  viewPage: string;
  modeSetting: string;
  generalView: string;
  externalSystemTabId: string;
}

export const TitleContainer = styled.div`
  height: 20px;
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0 10px;
  font-weight: bold;
  user-select: none;
`;

export const Content = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  border: solid 1px ${(p) => p.theme.lightGray};
`;

export const Row = styled.div`
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  //justify-content: center;
`;

export const Cell = styled.div<{ justify?: "start" | "end" | "center" }>`
  margin: 0 10px;
  width: 27%;
  display: flex;
  align-items: center;

  ${(p: any) =>
    p.justify == null &&
    css`
      justify-content: center;
    `}

  ${(p: any) =>
    p.justify != null &&
    css`
      justify-content: ${p.align};
    `}
`;

export const ScrollContainer = styled.div`
  width: 100%;
  height: 120px;
  overflow: auto;
  //border: solid 1px ${(p) => p.theme.lightGray};
`;

export const Action = styled(Button)`
  width: 20px;
  height: 20px;
  margin: 0 10px;
  padding-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const ExternalSystem: FC<ExternalSystemProps> = observer((props: any) => {
  const { viewPage, modeSetting, generalView } = props;
  const history = useNavigate();
  const location = useLocation();
  const { tab }: any = queryString.parse(location.search);

  const { settingsStore, authStore } = useStores();
  const { isCenter } = authStore;
  const { externalSystems, isTesting } = settingsStore;
  const { names, systems } = externalSystems;

  const [selectedButton, setSelectedButton] = useState<any>(null);

  useEffect(() => {
    const [first] = names;
    setSelectedButton(tab ? tab : first?.value);
  }, [names]);

  useEffect(() => {
    settingsStore.initExternalSystems(selectedButton);
  }, [isModeCenter, isCenter]);

  const [emailSrpg, setEmailSrpg] = useState<any>("");
  const [urls, setUrls] = useState<any>([]);
  const [login, setLogin] = useState("");
  const [password, setPassword] = useState("");
  const [retryNumber, setRetryNumber] = useState("");
  const [retryTimeout, setRetryTimeout] = useState("");
  const [retryTimeoutMes, setRetryTimeoutMes] = useState("");
  const [authActive, setAuthActive] = useState(false);

  const [pdgTemplate, setPdgTemplate] = useState("");
  const [perTemplate, setPerTemplate] = useState("");
  const [kind, setKind] = useState("");
  const [port, setPort] = useState("");
  const [protocol, setProtocol] = useState("");
  const [loginPath, setLoginPath] = useState("");
  const [isPasswordMode, setIsPasswordMode] = useState(true);

  const [originalData, setOriginalData] = useState<any>({});
  const [errors, setErrors] = useState<any>([]);
  const [isErrors, setIsErrors] = useState(false);

  const [errorsUrls, setErrorsUrls] = useState<any>([]);

  const initForm = () => {
    if (!!systems[selectedButton]?.url || !!systems[selectedButton]?.host) {
      if (systems[selectedButton]?.url) {
        setUrls([systems[selectedButton]?.url]);
      }
      if (systems[selectedButton]?.host) {
        setUrls([systems[selectedButton]?.host]);
      }
    } else {
      if (systems[selectedButton]?.urls?.length > 0) {
        setUrls(systems[selectedButton]?.urls);
      } else {
        setUrls([]);
      }
    }
    const authActive = systems[selectedButton]?.authActive !== undefined ? systems[selectedButton]?.authActive : false;
    const user = systems[selectedButton]?.user !== undefined ? systems[selectedButton]?.user : "";
    const password = systems[selectedButton]?.password !== undefined ? systems[selectedButton]?.password : "";
    const retryNumber = systems[selectedButton]?.retryNumber !== undefined ? systems[selectedButton]?.retryNumber : "";
    const retryTimeout = systems[selectedButton]?.retryTimeout !== undefined ? systems[selectedButton]?.retryTimeout : "";
    const retryTimeoutMes = systems[selectedButton]?.retryTimeoutMes !== undefined ? systems[selectedButton]?.retryTimeoutMes : "";
    const pdgTemplate = systems[selectedButton]?.pdgTemplate !== undefined ? systems[selectedButton]?.pdgTemplate : "";
    const perTemplate = systems[selectedButton]?.perTemplate !== undefined ? systems[selectedButton]?.perTemplate : "";
    const kind = systems[selectedButton]?.kind !== undefined ? systems[selectedButton]?.kind : "";
    const emailSrpg = systems[selectedButton]?.emailSrpg !== undefined ? String(systems[selectedButton]?.emailSrpg) : "";
    const port = systems[selectedButton]?.port !== undefined ? systems[selectedButton]?.port : "";
    const loginPath = systems[selectedButton]?.loginPath !== undefined ? String(systems[selectedButton]?.loginPath) : "";
    const protocol = systems[selectedButton]?.protocol !== undefined ? String(systems[selectedButton]?.protocol) : "";
    const urls = systems[selectedButton]?.urls !== undefined && systems[selectedButton]?.urls.length > 0 ? systems[selectedButton]?.urls : [];
    setAuthActive(authActive);
    setLogin(user);
    setPassword(password);
    setRetryNumber(retryNumber);
    setRetryTimeout(retryTimeout);
    setRetryTimeoutMes(retryTimeoutMes);
    setPdgTemplate(pdgTemplate);
    setPerTemplate(perTemplate);
    setKind(kind);
    setPort(port);
    setProtocol(protocol);
    setLoginPath(loginPath);
    setEmailSrpg(emailSrpg);
    setIsPasswordMode(true);
    // setOriginalData({ ...systems[selectedButton], emailSrpg: systems[selectedButton]?.emailSrpg ?? "", port: String(systems[selectedButton]?.port ?? "") });
    setOriginalData({
      authActive,
      user,
      password,
      retryNumber,
      retryTimeout,
      retryTimeoutMes,
      pdgTemplate,
      perTemplate,
      kind,
      emailSrpg,
      port,
      loginPath,
      protocol,
      urls,
    });

    const currentObject = systems[selectedButton];
    let tempErrors: any = {};

    for (let key in currentObject) {
      if (key === "urls" || key === "url" || key === "host") {
        tempErrors["urls"] = new Array(urls.length).fill([null]);
      } else {
        tempErrors[key] = [null];
      }
    }
    setErrors(tempErrors);
  };

  const [addUrlError, setAddUrlError] = useState<any>([]);

  const resetData = () => {
    if (!!systems[selectedButton]?.url || !!systems[selectedButton]?.host) {
      if (systems[selectedButton]?.url) {
        setUrls([systems[selectedButton]?.url]);
      }
      if (systems[selectedButton]?.host) {
        setUrls([systems[selectedButton]?.host]);
      }
    } else {
      if (systems[selectedButton]?.urls?.length > 0) {
        setUrls(systems[selectedButton]?.urls);
      } else {
        setUrls([]);
      }
    }
    if (systems[selectedButton]?.authActive != null) {
      setAuthActive(systems[selectedButton]?.authActive ?? "");
    }
    // setLogin(systems[selectedButton]?.user ?? "");
    // setPassword(systems[selectedButton]?.password ?? "");
    // setRetryNumber(systems[selectedButton]?.retryNumber ?? "");
    // setRetryTimeout(systems[selectedButton]?.retryTimeout ?? "");
    // setRetryTimeoutMes(systems[selectedButton]?.retryTimeoutMes ?? "");
    // setPdgTemplate(systems[selectedButton]?.pdgTemplate ?? "");
    // setPerTemplate(systems[selectedButton]?.perTemplate ?? "");
    // setPort(String(systems[selectedButton]?.port ?? ""));
    // setKind(systems[selectedButton]?.kind ?? "");
    // setProtocol(String(systems[selectedButton]?.protocol ?? ""));
    // setLoginPath(String(systems[selectedButton]?.loginPath ?? ""));
    // setEmailSrpg(systems[selectedButton]?.emailSrpg ? String(systems[selectedButton]?.emailSrpg) : "");
    const authActive = systems[selectedButton]?.authActive !== undefined ? systems[selectedButton]?.authActive : false;
    const user = systems[selectedButton]?.user !== undefined ? systems[selectedButton]?.user : "";
    const password = systems[selectedButton]?.password !== undefined ? systems[selectedButton]?.password : "";
    const retryNumber = systems[selectedButton]?.retryNumber !== undefined ? systems[selectedButton]?.retryNumber : "";
    const retryTimeout = systems[selectedButton]?.retryTimeout !== undefined ? systems[selectedButton]?.retryTimeout : "";
    const retryTimeoutMes = systems[selectedButton]?.retryTimeoutMes !== undefined ? systems[selectedButton]?.retryTimeoutMes : "";
    const pdgTemplate = systems[selectedButton]?.pdgTemplate !== undefined ? systems[selectedButton]?.pdgTemplate : "";
    const perTemplate = systems[selectedButton]?.perTemplate !== undefined ? systems[selectedButton]?.perTemplate : "";
    const kind = systems[selectedButton]?.kind !== undefined ? systems[selectedButton]?.kind : "";
    const emailSrpg = systems[selectedButton]?.emailSrpg !== undefined ? String(systems[selectedButton]?.emailSrpg) : "";
    const port = systems[selectedButton]?.port !== undefined ? systems[selectedButton]?.port : "";
    const loginPath = systems[selectedButton]?.loginPath !== undefined ? String(systems[selectedButton]?.loginPath) : "";
    const protocol = systems[selectedButton]?.protocol !== undefined ? String(systems[selectedButton]?.protocol) : "";
    setAuthActive(authActive);
    setLogin(user);
    setPassword(password);
    setRetryNumber(retryNumber);
    setRetryTimeout(retryTimeout);
    setRetryTimeoutMes(retryTimeoutMes);
    setPdgTemplate(pdgTemplate);
    setPerTemplate(perTemplate);
    setKind(kind);
    setPort(port);
    setProtocol(protocol);
    setLoginPath(loginPath);
    setEmailSrpg(emailSrpg);
    setIsPasswordMode(true);
    const currentObject = systems[selectedButton];
    let tempErrors: any = {};

    for (let key in currentObject) {
      if (key === "urls" || key === "url" || key === "host") {
        tempErrors["urls"] = new Array(urls.length).fill([null]);
      } else {
        tempErrors[key] = [null];
      }
    }
    setErrors(tempErrors);
    setIsErrors(false);
  };

  useEffect(() => {
    setAddUrlError([]);
    initForm();
    setIsErrors(false);
  }, [selectedButton, systems]);

  const isArray = () => {
    const originalUrls = originalData?.urls ?? [];
    if (systems[selectedButton]?.url || systems[selectedButton]?.host) {
      if (systems[selectedButton]?.url) {
        return systems[selectedButton]?.url === urls[0];
      }
      if (systems[selectedButton]?.host) {
        return systems[selectedButton]?.host === urls[0];
      }
    } else {
      if (systems[selectedButton]?.urls?.length > 0) {
        return (
          originalUrls?.length == urls?.length &&
          originalUrls?.every(function (element: any, index: number) {
            return element === urls[index];
          })
        );
      } else {
        return (
          originalUrls?.length == urls?.length &&
          originalUrls?.every(function (element: any, index: number) {
            return element === urls[index];
          })
        );
      }
    }
  };

  const getDisabled = () => {
    let res: string = ``;
    let text: string = ``;
    text = `originalData?.authActive == authActive`;
    res = res.length === 0 ? res + text : res + `&&` + text;
    text = `originalData?.user == login`;
    res = res.length === 0 ? res + text : res + `&&` + text;
    text = `originalData?.password == password`;
    res = res.length === 0 ? res + text : res + `&&` + text;
    text = `originalData?.retryNumber == retryNumber`;
    res = res.length === 0 ? res + text : res + `&&` + text;
    text = `originalData?.retryTimeout == retryTimeout`;
    res = res.length === 0 ? res + text : res + `&&` + text;
    text = `originalData?.retryTimeoutMes == retryTimeoutMes`;
    res = res.length === 0 ? res + text : res + `&&` + text;
    text = `originalData?.pdgTemplate == pdgTemplate`;
    res = res.length === 0 ? res + text : res + `&&` + text;
    text = `originalData?.perTemplate == perTemplate`;
    res = res.length === 0 ? res + text : res + `&&` + text;
    text = `originalData?.kind == kind`;
    res = res.length === 0 ? res + text : res + `&&` + text;
    text = `originalData?.emailSrpg == emailSrpg`;
    res = res.length === 0 ? res + text : res + `&&` + text;
    text = `String(originalData?.port) == port`;
    res = res.length === 0 ? res + text : res + `&&` + text;
    text = `String(originalData?.loginPath) == loginPath`;
    res = res.length === 0 ? res + text : res + `&&` + text;
    text = `String(originalData?.protocol) == protocol`;
    res = res.length === 0 ? res + text : res + `&&` + text;
    text = `isArray()`;
    res = res.length === 0 ? res + text : res + `&&` + text;
    return eval(res);
  };

  const isDisable = getDisabled();

  const [addUrl, setAddUrl] = useState("");

  const encodings = [
    { value: "ESS", label: "ПАК ЕСС" },
    { value: "CK11", label: "СК-11" },
  ];
  const protocols = [
    { value: "TLS", label: "TLS" },
    { value: "SSL", label: "SSL" },
    { value: "НЕТ", label: "Нет" },
  ];

  let isValidUrl = new RegExp("^(https?|ftp)://");

  const validPassword = (value: string) => {
    if (value.trim().length === 0) {
      setErrors((prev: any) => {
        const final = prev;
        final.password = ["Поле не должно быть пустым"];
        return final;
      });
      return false;
    } else {
      setErrors((prev: any) => {
        const final = prev;
        final.password = [];
        return final;
      });
      return true;
    }
  };

  const validRetryNumber = (value: string) => {
    if (String(value).trim().length === 0) {
      setErrors((prev: any) => {
        const final = prev;
        final.retryNumber = ["Поле не должно быть пустым"];
        return final;
      });
      return false;
    } else {
      setErrors((prev: any) => {
        const final = prev;
        final.retryNumber = [];
        return final;
      });
      return true;
    }
  };

  const validRetryTimeout = (value: string) => {
    if (String(value).trim().length === 0) {
      setErrors((prev: any) => {
        const final = prev;
        final.retryTimeout = ["Поле не должно быть пустым"];
        return final;
      });
      return false;
    } else {
      setErrors((prev: any) => {
        const final = prev;
        final.retryTimeout = [];
        return final;
      });
      return true;
    }
  };

  const validUser = (value: string) => {
    if (value.trim().length === 0) {
      setErrors((prev: any) => {
        const final = prev;
        final.user = ["Поле не должно быть пустым"];
        return final;
      });
      return false;
    } else {
      setErrors((prev: any) => {
        const final = prev;
        final.user = [];
        return final;
      });
      return true;
    }
  };

  const validLoginPath = (value: string) => {
    if (value?.trim()?.length === 0) {
      setErrors((prev: any) => {
        const final = prev;
        final.loginPath = ["Поле не должно быть пустым"];
        return final;
      });
      return false;
    } else {
      setErrors((prev: any) => {
        const final = prev;
        final.loginPath = [];
        return final;
      });
      return true;
    }
  };

  const validateUrls = () => {
    let finalIsValid = true;
    const res = urls.map((value: any, index: number) => {
      const isValid = isValidUrl.test(value);
      if (!isValid) {
        finalIsValid = false;
        return ["Не соотетствует формату http или https"];
      }
      return [null];
    });
    setErrorsUrls(res ?? []);
    return finalIsValid;
  };
  const validUrls = (selectedItem: number, value: any) => {
    let finalIsValid = true;
    setErrors((prev: any) => {
      const finalResult = prev;
      finalResult.urls = finalResult.urls.map((item: any, index: number) => {
        const isValid = isValidUrl.test(value);
        if (index === selectedItem && !isValid) {
          finalIsValid = false;
          return ["Не соотетствует формату http или https"];
        }
        return [null];
      });
      return finalResult;
    });
    return finalIsValid;
  };

  const validPort = (value: string) => {
    if (String(value).trim().length === 0) {
      setErrors((prev: any) => {
        const final = prev;
        final.port = ["Поле не должно быть пустым"];
        return final;
      });
      return false;
    } else {
      setErrors((prev: any) => {
        const final = prev;
        final.port = [];
        return final;
      });
      return true;
    }
  };

  const validPdgTemplate = (value: string) => {
    if (String(value).trim().length === 0) {
      setErrors((prev: any) => {
        const final = prev;
        final.pdgTemplate = ["Поле не должно быть пустым"];
        return final;
      });
      return false;
    } else {
      setErrors((prev: any) => {
        const final = prev;
        final.pdgTemplate = [];
        return final;
      });
      return true;
    }
  };

  const validPerTemplate = (value: string) => {
    if (String(value).trim().length === 0) {
      setErrors((prev: any) => {
        const final = prev;
        final.perTemplate = ["Поле не должно быть пустым"];
        return final;
      });
      return false;
    } else {
      setErrors((prev: any) => {
        const final = prev;
        final.perTemplate = [];
        return final;
      });
      return true;
    }
  };

  const checkErrors = () => {
    let isValid = true;
    for (let key in errors) {
      // if (key === "urls") {
      //   // const isError = errors[key].some((el: any) => {
      //   //   return el[0] !== null && el[0] !== undefined;
      //   // });
      //   // if (isError) {
      //   //   isValid = false;
      //   // }
      //   isValid = true;
      // } else {
      //   if (errors[key][0] !== null && errors[key][0] !== undefined) {
      //     isValid = false;
      //   }
      // }
      if (errors[key][0] !== null && errors[key][0] !== undefined) {
        isValid = false;
      }
    }
    return isValid;
  };

  const saveData = async () => {
    setIsErrors(false);
    let objectPOST: any = {};
    if (selectedButton === "CK_11") {
      objectPOST = {
        CK_11: {
          retryNumber,
          retryTimeout,
          retryTimeoutMes: "SEC",
          urls,
          user: login,
          password,
        },
      };
    }
    if (selectedButton === "MODES") {
      objectPOST = {
        MODES: {
          authActive,
          user: login,
          password,
          kind,
          retryNumber,
          retryTimeout,
          retryTimeoutMes: "SEC",
          url: urls.length === 0 || urls[0] === null ? "" : urls[0],
        },
      };
    }
    if (selectedButton === "SRDK") {
      objectPOST = {
        SRDK: {
          password,
          retryNumber,
          retryTimeout,
          retryTimeoutMes: "SEC",
          url: urls.length === 0 || urls[0] === null ? "" : urls[0],
          user: login,
          loginPath,
        },
      };
    }
    if (selectedButton === "EMAIL") {
      objectPOST = {
        EMAIL: {
          host: urls[0] === null ? "" : urls[0],
          password,
          port: String(port)?.length > 0 ? port : null,
          protocol: protocol === "NO" ? null : protocol,
          retryTimeout,
          retryTimeoutMes: "SEC",
          user: login,
          retryNumber,
          emailSrpg,
        },
      };
    }
    if (selectedButton === "MEGAPOINT") {
      objectPOST = {
        MEGAPOINT: {
          pdgTemplate,
          perTemplate,
          retryNumber,
          retryTimeout,
          retryTimeoutMes: "SEC",
          urls,
        },
      };
    }
    if (selectedButton === "MEGAPOINT_VOSTOK") {
      objectPOST = {
        MEGAPOINT_VOSTOK: {
          perTemplate,
          pdgTemplate,
          retryNumber,
          retryTimeout,
          retryTimeoutMes: "SEC",
          urls,
        },
      };
    }
    if (selectedButton === "ESS") {
      objectPOST = {
        ESS: {
          password,
          retryNumber,
          retryTimeout,
          retryTimeoutMes: "SEC",
          url: urls.length === 0 || urls[0] === null ? "" : urls[0],
          user: login,
        },
      };
    }
    if (selectedButton === "OPAM") {
      objectPOST = {
        OPAM: {
          password,
          retryNumber,
          retryTimeout,
          retryTimeoutMes: "SEC",
          url: urls.length === 0 || urls[0] === null ? "" : urls[0],
          user: login,
        },
      };
    }

    let validModes = true;

    if (selectedButton === "MODES") {
      if (authActive) {
        if (objectPOST.MODES.user.length === 0) {
          validModes = false;
          setErrors((prev: any) => ({ ...prev, user: ["Поле не должно быть пустым"] }));
        }
        if (objectPOST.MODES.password.length === 0) {
          validModes = false;
          setErrors((prev: any) => ({ ...prev, password: ["Поле не должно быть пустым"] }));
        }
      }
    }
    const isValid = selectedButton === "EMAIL" ? validModes : validateUrls() && validModes;

    if (isValid) {
      await settingsStore
        .saveExternalSystems(objectPOST)
        .then(() => {
          settingsStore.initExternalSystems(selectedButton);
        })
        .then(() => {
          setAddUrlError([]);
          initForm();
          setIsErrors(false);
        });
    }
  };

  const getContent = () => {
    if (selectedButton === "CK_11") {
      return (
        <>
          <ScrollContainer data-test="ck-11-tab.first-row">
            {urls.map((el: any, index: number) => {
              return (
                <Row key={`scroll-container-${index}`}>
                  {index === 0 ? <Cell>Адрес сервиса :</Cell> : <Cell />}
                  <Cell>
                    <TextFieldStyled
                      value={el ?? ""}
                      onChange={(e) => {
                        setUrls((prev: any) => {
                          return prev.map((item: any, indexx: number) => {
                            if (index === indexx) {
                              return e.target.value;
                            }
                            return item;
                          });
                        });
                        // validUrls(index, e.target.value);
                        setIsErrors(false);
                        setErrorsUrls([]);
                      }}
                      errors={errorsUrls?.length > 0 ? errorsUrls[index] : []}
                      data-test={`ck-11-tab.address-input-${index}`}
                    />
                  </Cell>
                  <Cell>
                    <Action
                      type="secondary"
                      icon="close"
                      onClick={() => {
                        setUrls((prev: any) => {
                          return prev.filter((el: any, indexx: number) => {
                            return indexx !== index;
                          });
                        });
                      }}
                    />
                  </Cell>
                </Row>
              );
            })}
          </ScrollContainer>
          <Row>
            <Cell />
            <Cell>
              <TextFieldStyled
                value={addUrl ?? ""}
                onChange={(e) => {
                  setAddUrlError([]);
                  setAddUrl(e.target.value);
                  setIsErrors(false);
                }}
                errors={addUrlError}
              />
            </Cell>
            <Cell>
              <Button
                title="Добавить"
                onClick={() => {
                  const isValid = isValidUrl.test(addUrl);
                  if (isValid) {
                    setUrls((prev: any) => {
                      return [...prev, addUrl];
                    });
                    setAddUrl("");
                  } else {
                    setAddUrlError(["Не соотетствует формату http или https"]);
                  }
                }}
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Логин :</Cell>
            <Cell>
              <TextFieldStyled
                value={login}
                onChange={(e) => {
                  // validUser(e.target.value);
                  setLogin(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.user}
                data-test="ck-11-tab.login-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Пароль :</Cell>
            <Cell>
              <TextFieldStyled
                type={isPasswordMode ? "password" : "text"}
                value={password}
                onChange={(e) => {
                  // validPassword(e.target.value);
                  setPassword(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.password}
                data-test="ck-11-tab.password-input"
              />
              <Action type="secondary" icon={isPasswordMode ? "viewPassword" : "notViewPassword"} onClick={() => setIsPasswordMode((prev) => !prev)} />
            </Cell>
          </Row>
          <Row>
            <Cell>Количество повторных попыток : </Cell>
            <Cell>
              <TextFieldStyled
                value={retryNumber}
                onChange={(e) => {
                  validRetryNumber(e.target.value);
                  setRetryNumber(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.retryNumber}
                data-test="ck-11-tab.count-tries-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Интервал повторных попыток : </Cell>
            <Cell>
              <TextFieldInterval
                value={retryTimeout}
                onChange={(e) => {
                  validRetryTimeout(e.target.value);
                  setRetryTimeout(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.retryTimeout}
                data-test="ck-11-tab.interval-tries-input"
              />
              <SecondsLabel>Секунды</SecondsLabel>
            </Cell>
          </Row>
        </>
      );
    }
    if (selectedButton === "MODES") {
      return (
        <>
          <Row data-test="modes-tab.first-row">
            <Cell>Активность авторизации :</Cell>
            <Cell justify="start">
              <Switch
                checked={authActive}
                onChange={(e: any) => {
                  setAuthActive(e.target.checked);
                  setIsErrors(false);
                }}
                data-test="modes-tab.auth-active "
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Логин :</Cell>
            <Cell>
              <TextFieldStyled
                disabled={!authActive}
                value={login}
                onChange={(e) => {
                  // validUser(e.target.value);
                  setLogin(e.target.value);
                  setIsErrors(false);
                  if (errors.user) {
                    setErrors((prev: any) => ({ ...prev, user: null }));
                  }
                }}
                errors={errors.user}
                data-test="modes-tab.login-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Пароль :</Cell>
            <Cell>
              <TextFieldStyled
                disabled={!authActive}
                type={isPasswordMode ? "password" : "text"}
                value={password}
                onChange={(e) => {
                  // validPassword(e.target.value);
                  setPassword(e.target.value);
                  setIsErrors(false);
                  if (errors.password) {
                    setErrors((prev: any) => ({ ...prev, password: null }));
                  }
                }}
                errors={errors.password}
                data-test="modes-tab.password-input"
              />
              <Action
                type="secondary"
                icon={isPasswordMode ? "viewPassword" : "notViewPassword"}
                onClick={() => setIsPasswordMode((prev) => !prev)}
                disabled={!authActive}
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Адрес сервиса :</Cell>
            <Cell>
              <TextFieldStyled
                value={urls ?? ""}
                onChange={(e) => {
                  setUrls([e.target.value]);
                  // validUrls(0, e.target.value);
                  setIsErrors(false);
                  setErrorsUrls([]);
                }}
                errors={errorsUrls?.length > 0 ? errorsUrls[0] : []}
                data-test="modes-tab.address-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Кодировка :</Cell>
            <Cell>
              <Combobox
                items={encodings}
                selectedValue={kind}
                onChange={({ value }) => {
                  setIsErrors(false);
                  setKind(value);
                }}
                width={450}
                dataTest="modes-tab.encoding-combobox"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Количество повторных попыток : </Cell>
            <Cell>
              <TextFieldStyled
                value={retryNumber}
                onChange={(e) => {
                  validRetryNumber(e.target.value);
                  setRetryNumber(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.retryNumber}
                data-test="modes-tab.count-tries-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Интервал повторных попыток : </Cell>
            <Cell>
              <TextFieldInterval
                value={retryTimeout}
                onChange={(e) => {
                  validRetryTimeout(e.target.value);
                  setRetryTimeout(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.retryTimeout}
                data-test="modes-tab.interval-tries-input"
              />
              <SecondsLabel>Секунды</SecondsLabel>
            </Cell>
          </Row>
        </>
      );
    }
    if (selectedButton === "SRDK") {
      return (
        <>
          <Row data-test="srdk-tab.first-row">
            <Cell>Адрес сервиса (Распределенная часть) :</Cell>
            <Cell>
              <TextFieldStyled
                value={urls[0] ?? ""}
                onChange={(e) => {
                  setUrls([e.target.value]);
                  setIsErrors(false);
                  // validUrls(0, e.target.value);
                  setErrorsUrls([]);
                }}
                errors={errorsUrls?.length > 0 ? errorsUrls[0] : []}
                data-test="srdk-tab.distributed-address-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Адрес сервиса (Авторизация) :</Cell>
            <Cell>
              <TextFieldStyled
                value={loginPath}
                onChange={(e) => {
                  setLoginPath(e.target.value ?? "");
                  setIsErrors(false);
                  // validLoginPath(e.target.value);
                }}
                errors={errors?.loginPath ? errors?.loginPath : []}
                data-test="srdk-tab.auth-address-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Логин :</Cell>
            <Cell>
              <TextFieldStyled
                value={login}
                onChange={(e) => {
                  // validUser(login);
                  setLogin(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.user}
                data-test="srdk-tab.login-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Пароль :</Cell>
            <Cell>
              <TextFieldStyled
                type={isPasswordMode ? "password" : "text"}
                value={password}
                onChange={(e) => {
                  // validPassword(e.target.value);
                  setPassword(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.password}
                data-test="srdk-tab.password-input"
              />
              <Action type="secondary" icon={isPasswordMode ? "viewPassword" : "notViewPassword"} onClick={() => setIsPasswordMode((prev) => !prev)} />
            </Cell>
          </Row>
          <Row>
            <Cell>Количество повторных попыток : </Cell>
            <Cell>
              <TextFieldStyled
                value={retryNumber}
                onChange={(e) => {
                  validRetryNumber(e.target.value);
                  setRetryNumber(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.retryNumber}
                data-test="srdk-tab.count-tries-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Интервал повторных попыток : </Cell>
            <Cell>
              <TextFieldInterval
                value={retryTimeout}
                onChange={(e) => {
                  validRetryTimeout(e.target.value);
                  setRetryTimeout(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.retryTimeout}
                data-test="srdk-tab.interval-tries-input"
              />
              <SecondsLabel>Секунды</SecondsLabel>
            </Cell>
          </Row>
        </>
      );
    }
    if (selectedButton === "EMAIL") {
      return (
        <>
          <Row data-test="email-tab.first-row">
            <Cell>Адрес сервиса :</Cell>
            <Cell>
              <TextFieldStyled
                value={urls[0] ?? ""}
                onChange={(e) => {
                  setUrls([e.target.value]);
                  setIsErrors(false);
                }}
                data-test="email-tab.address-input"
                errors={errorsUrls?.length > 0 ? errorsUrls[0] : []}
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Порт :</Cell>
            <Cell>
              <TextFieldStyled
                value={port}
                onChange={(e) => {
                  // validPort(e.target.value);
                  setPort(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.port}
                data-test="email-tab.port-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Логин :</Cell>
            <Cell>
              <TextFieldStyled
                value={login}
                onChange={(e) => {
                  // validUser(e.target.value);
                  setLogin(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.user}
                data-test="email-tab.login-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Пароль :</Cell>
            <Cell>
              <TextFieldStyled
                type={isPasswordMode ? "password" : "text"}
                value={password}
                onChange={(e) => {
                  // validPassword(e.target.value);
                  setPassword(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.password}
                data-test="email-tab.password-input"
              />
              <Action
                type="secondary"
                icon={isPasswordMode ? "viewPassword" : "notViewPassword"}
                onClick={() => {
                  setIsPasswordMode((prev) => !prev);
                  setIsErrors(false);
                }}
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Шифрование : </Cell>
            <Cell>
              <Combobox
                items={protocols}
                selectedValue={protocol}
                onChange={({ value }) => {
                  setProtocol(value);
                  setIsErrors(false);
                }}
                width={450}
                dataTest="email-tab.encoding-combobox"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>E-mail СРПГ :</Cell>
            <Cell>
              <TextFieldStyled
                value={emailSrpg}
                onChange={(e) => {
                  // validUser(e.target.value);
                  setEmailSrpg(e.target.value);
                  setIsErrors(false);
                }}
                // errors={errors.user}
                data-test="email-tab.email-srpg-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Количество повторных попыток : </Cell>
            <Cell>
              <TextFieldStyled
                value={retryNumber}
                onChange={(e) => {
                  validRetryNumber(e.target.value);
                  setRetryNumber(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.retryNumber}
                data-test="email-tab.count-tries-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Интервал повторных попыток : </Cell>
            <Cell>
              <TextFieldInterval
                value={retryTimeout}
                onChange={(e) => {
                  validRetryTimeout(e.target.value);
                  setRetryTimeout(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.retryTimeout}
                data-test="email-tab.interval-tries-input"
              />
              <SecondsLabel>Секунды</SecondsLabel>
            </Cell>
          </Row>
        </>
      );
    }
    if (selectedButton === "MEGAPOINT") {
      return (
        <>
          <ScrollContainer data-test="megapoint-tab.first-row">
            {urls.map((el: any, index: number) => {
              return (
                <Row key={`scroll-container-${index}`}>
                  {index === 0 ? <Cell>Адрес сервиса :</Cell> : <Cell />}
                  <Cell>
                    <TextFieldStyled
                      value={el ?? ""}
                      onChange={(e) => {
                        setUrls((prev: any) => {
                          return prev.map((item: any, indexx: number) => {
                            if (index === indexx) {
                              return e.target.value;
                            }
                            return item;
                          });
                        });
                        // validUrls(index, e.target.value);
                        setIsErrors(false);
                        setErrorsUrls([]);
                      }}
                      errors={errorsUrls?.length > 0 ? errorsUrls[index] : []}
                      data-test={`megapoint-tab.address-input-${index}`}
                    />
                  </Cell>
                  <Cell>
                    <Action
                      type="secondary"
                      icon="close"
                      onClick={() => {
                        setUrls((prev: any) => {
                          return prev.filter((el: any, indexx: number) => {
                            return indexx !== index;
                          });
                        });
                      }}
                      dataTest={`megapoint-tab.remove-address-button-${index}`}
                    />
                  </Cell>
                </Row>
              );
            })}
          </ScrollContainer>
          <Row>
            <Cell />
            <Cell>
              <TextFieldStyled
                value={addUrl}
                onChange={(e) => {
                  setAddUrlError([]);
                  setAddUrl(e.target.value);
                }}
                errors={addUrlError}
              />
            </Cell>
            <Cell>
              <Button
                title="Добавить"
                onClick={() => {
                  const isValid = isValidUrl.test(addUrl);
                  if (isValid) {
                    setUrls((prev: any) => {
                      return [...prev, addUrl];
                    });
                    setAddUrl("");
                  } else {
                    setAddUrlError(["Не соотетствует формату http или https"]);
                  }
                }}
              />
            </Cell>
          </Row>
          <Row>
            <Cell>ПДГ :</Cell>
            <Cell>
              <TextFieldStyled
                value={pdgTemplate}
                onChange={(e) => {
                  // validPdgTemplate(e.target.value);
                  setPdgTemplate(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.pdgTemplate}
                data-test="megapoint-tab.pdg-path-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>ПЭР :</Cell>
            <Cell>
              <TextFieldStyled
                value={perTemplate}
                onChange={(e) => {
                  // validPerTemplate(e.target.value);
                  setPerTemplate(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.perTemplate}
                data-test="megapoint-tab.per-path-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Количество повторных попыток : </Cell>
            <Cell>
              <TextFieldStyled
                value={retryNumber}
                onChange={(e) => {
                  validRetryNumber(e.target.value);
                  setRetryNumber(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.retryNumber}
                data-test="megapoint-tab.count-tries-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Интервал повторных попыток : </Cell>
            <Cell>
              <TextFieldInterval
                value={retryTimeout}
                onChange={(e) => {
                  validRetryTimeout(e.target.value);
                  setRetryTimeout(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.retryTimeout}
                data-test="megapoint-tab.interval-tries-input"
              />
              <SecondsLabel>Секунды</SecondsLabel>
            </Cell>
          </Row>
        </>
      );
    }
    if (selectedButton === "ESS") {
      return (
        <>
          <Row data-test="ess-tab.first-row">
            <Cell>Адрес сервиса :</Cell>
            <Cell>
              <TextFieldStyled
                value={urls[0] ?? ""}
                onChange={(e) => {
                  setUrls([e.target.value]);
                  setIsErrors(false);
                  // validUrls(0, e.target.value);
                  setErrorsUrls([]);
                }}
                errors={errorsUrls?.length > 0 ? errorsUrls[0] : []}
                dataTest="ess-tab.address-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Логин :</Cell>
            <Cell>
              <TextFieldStyled
                value={login}
                onChange={(e) => {
                  // validUser(e.target.value);
                  setLogin(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.user}
                dataTest="ess-tab.login-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Пароль :</Cell>
            <Cell>
              <TextFieldStyled
                type={isPasswordMode ? "password" : "text"}
                value={password}
                onChange={(e) => {
                  // validPassword(e.target.value);
                  setPassword(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.password}
                dataTest="ess-tab.password-input"
              />
              <Action type="secondary" icon={isPasswordMode ? "viewPassword" : "notViewPassword"} onClick={() => setIsPasswordMode((prev) => !prev)} />
            </Cell>
          </Row>
          <Row>
            <Cell>Количество повторных попыток : </Cell>
            <Cell>
              <TextFieldStyled
                value={retryNumber}
                onChange={(e) => {
                  validRetryNumber(e.target.value);
                  setRetryNumber(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.retryNumber}
                dataTest="ess-tab.count-tries-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Интервал повторных попыток : </Cell>
            <Cell>
              <TextFieldInterval
                value={retryTimeout}
                onChange={(e) => {
                  validRetryTimeout(e.target.value);
                  setRetryTimeout(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.retryTimeout}
                dataTest="ess-tab.interval-tries-input"
              />
              <SecondsLabel>Секунды</SecondsLabel>
            </Cell>
          </Row>
        </>
      );
    }
    if (selectedButton === "OPAM") {
      return (
        <>
          <Row data-test="opam-tab.first-row">
            <Cell>Адрес сервиса :</Cell>
            <Cell>
              <TextFieldStyled
                value={urls[0] ?? ""}
                onChange={(e) => {
                  setUrls([e.target.value]);
                  setIsErrors(false);
                  // validUrls(0, e.target.value);
                  setErrorsUrls([]);
                }}
                errors={errorsUrls?.length > 0 ? errorsUrls[0] : []}
                dataTest="opam-tab.address-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Логин :</Cell>
            <Cell>
              <TextFieldStyled
                value={login}
                onChange={(e) => {
                  // validUser(e.target.value);
                  setLogin(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.user}
                dataTest="opam-tab.login-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Пароль :</Cell>
            <Cell>
              <TextFieldStyled
                type={isPasswordMode ? "password" : "text"}
                value={password}
                onChange={(e) => {
                  // validPassword(e.target.value);
                  setPassword(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.password}
                dataTest="opam-tab.password-input"
              />
              <Action type="secondary" icon={isPasswordMode ? "viewPassword" : "notViewPassword"} onClick={() => setIsPasswordMode((prev) => !prev)} />
            </Cell>
          </Row>
          <Row>
            <Cell>Количество повторных попыток : </Cell>
            <Cell>
              <TextFieldStyled
                value={retryNumber}
                onChange={(e) => {
                  validRetryNumber(e.target.value);
                  setRetryNumber(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.retryNumber}
                dataTest="opam-tab.count-tries-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Интервал повторных попыток : </Cell>
            <Cell>
              <TextFieldInterval
                value={retryTimeout}
                onChange={(e) => {
                  validRetryTimeout(e.target.value);
                  setRetryTimeout(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.retryTimeout}
                dataTest="opam-tab.interval-tries-input"
              />
              <SecondsLabel>Секунды</SecondsLabel>
            </Cell>
          </Row>
        </>
      );
    }
    if (selectedButton === "MEGAPOINT_VOSTOK") {
      return (
        <>
          <ScrollContainer>
            {urls.map((el: any, index: number) => {
              return (
                <Row key={`scroll-container-${index}`}>
                  {index === 0 ? <Cell>Адрес сервиса :</Cell> : <Cell />}
                  <Cell>
                    <TextFieldStyled
                      value={el}
                      onChange={(e) => {
                        setUrls((prev: any) => {
                          return prev.map((item: any, indexx: number) => {
                            if (index === indexx) {
                              return e.target.value;
                            }
                            return item;
                          });
                        });
                        // validUrls(index, e.target.value);
                        setIsErrors(false);
                        setErrorsUrls([]);
                      }}
                      errors={errorsUrls?.length > 0 ? errorsUrls[index] : []}
                      data-test={`megapoint-tab.address-input-${index}`}
                    />
                  </Cell>
                  <Cell>
                    <Action
                      type="secondary"
                      icon="close"
                      onClick={() => {
                        setUrls((prev: any) => {
                          return prev.filter((el: any, indexx: number) => {
                            return indexx !== index;
                          });
                        });
                      }}
                      dataTest="megapoint-tab.remove-address-button"
                    />
                  </Cell>
                </Row>
              );
            })}
          </ScrollContainer>
          <Row data-test="megapoint-tab.first-row">
            <Cell />
            <Cell>
              <TextFieldStyled
                value={addUrl}
                onChange={(e) => {
                  setAddUrlError([]);
                  setAddUrl(e.target.value);
                }}
                errors={addUrlError}
              />
            </Cell>
            <Cell>
              <Button
                title="Добавить"
                onClick={() => {
                  const isValid = isValidUrl.test(addUrl);
                  if (isValid) {
                    setUrls((prev: any) => {
                      return [...prev, addUrl];
                    });
                    setAddUrl("");
                  } else {
                    setAddUrlError(["Не соотетствует формату http или https"]);
                  }
                }}
              />
            </Cell>
          </Row>
          <Row>
            <Cell>ПДГ :</Cell>
            <Cell>
              <TextFieldStyled
                value={pdgTemplate}
                onChange={(e) => {
                  // validPdgTemplate(e.target.value);
                  setPdgTemplate(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.pdgTemplate}
                data-test="megapoint-tab.pdg-path-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>ПЭР :</Cell>
            <Cell>
              <TextFieldStyled
                value={perTemplate}
                onChange={(e) => {
                  // validPerTemplate(e.target.value);
                  setPerTemplate(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.perTemplate}
                data-test="megapoint-tab.per-path-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Количество повторных попыток : </Cell>
            <Cell>
              <TextFieldStyled
                value={retryNumber}
                onChange={(e) => {
                  validRetryNumber(e.target.value);
                  setRetryNumber(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.retryNumber}
                data-test="megapoint-tab.count-tries-input"
              />
            </Cell>
          </Row>
          <Row>
            <Cell>Интервал повторных попыток : </Cell>
            <Cell>
              <TextFieldInterval
                value={retryTimeout}
                onChange={(e) => {
                  validRetryTimeout(e.target.value);
                  setRetryTimeout(e.target.value);
                  setIsErrors(false);
                }}
                errors={errors.retryTimeout}
                data-test="megapoint-tab.interval-tries-input"
              />
              <SecondsLabel>Секунды</SecondsLabel>
            </Cell>
          </Row>
        </>
      );
    }
  };

  const testConnection = async () => {
    setIsErrors(false);
    let objectPOST: any = {};
    if (selectedButton === "CK_11") {
      objectPOST = {
        CK_11: {
          retryNumber,
          retryTimeout,
          retryTimeoutMes: "SEC",
          urls,
          user: login,
          password,
        },
      };
    }
    if (selectedButton === "MODES") {
      objectPOST = {
        MODES: {
          authActive,
          user: login,
          password,
          kind,
          retryNumber,
          retryTimeout,
          retryTimeoutMes: "SEC",
          url: urls[0],
        },
      };
    }
    if (selectedButton === "SRDK") {
      objectPOST = {
        SRDK: {
          password,
          retryNumber,
          retryTimeout,
          retryTimeoutMes: "SEC",
          url: urls[0],
          user: login,
          loginPath,
        },
      };
    }
    if (selectedButton === "EMAIL") {
      objectPOST = {
        EMAIL: {
          host: urls[0],
          password,
          port,
          protocol: protocol === "NO" ? null : protocol,
          retryTimeout,
          retryTimeoutMes: "SEC",
          user: login,
          retryNumber,
        },
      };
    }
    if (selectedButton === "MEGAPOINT") {
      objectPOST = {
        MEGAPOINT: {
          pdgTemplate,
          perTemplate,
          retryNumber,
          retryTimeout,
          retryTimeoutMes: "SEC",
          urls,
        },
      };
    }
    if (selectedButton === "MEGAPOINT_VOSTOK") {
      objectPOST = {
        MEGAPOINT_VOSTOK: {
          pdgTemplate,
          perTemplate,
          retryNumber,
          retryTimeout,
          retryTimeoutMes: "SEC",
          urls,
        },
      };
    }
    if (selectedButton === "ESS") {
      objectPOST = {
        ESS: {
          password,
          retryNumber,
          retryTimeout,
          retryTimeoutMes: "SEC",
          url: urls[0] === null ? "" : urls[0],
          user: login,
        },
      };
    }
    if (selectedButton === "OPAM") {
      objectPOST = {
        OPAM: {
          password,
          retryNumber,
          retryTimeout,
          retryTimeoutMes: "SEC",
          url: urls[0] === null ? "" : urls[0],
          user: login,
        },
      };
    }
    // const isValid = checkErrors();
    // setIsErrors(!isValid);
    // if (isValid) {
    //   await settingsStore.testExternalSystems(objectPOST);
    // }
    await settingsStore.testExternalSystems(objectPOST);
  };

  return (
    <Container>
      <TitleContainer>
        <Tabs
          tabs={names}
          selectedValue={selectedButton}
          onClick={({ value }: { value: any }) => {
            setSelectedButton(value);
            history(`?modeSetting=${modeSetting}&viewPage=${viewPage}&generalView=${generalView}&externalSystemTabId=ess&tab=${value}`);
          }}
        />
      </TitleContainer>
      <Content data-test="general-settings-tabs.container">
        {getContent()}
        <Row>
          <Cell>
            <Button
              message={isTesting ? "Выполняется проверка" : "Тестировать"}
              disabled={isTesting}
              title={isTesting ? <Loader spinnerSize={14} /> : "Тестировать"}
              onClick={() => testConnection()}
              isError={isErrors}
              dataTest="tabs.test-button"
            />
          </Cell>
          <Cell>
            <Button title="Сохранить" disabled={isDisable} onClick={() => saveData()} isError={isErrors} dataTest="tabs.save-button" />
          </Cell>
          <Cell>
            <Button
              title="Отменить"
              // type="secondary"
              disabled={isDisable}
              onClick={() => resetData()}
            />
          </Cell>
        </Row>
      </Content>
    </Container>
  );
});
