import React, { FC } from "react";
import styled, { css } from "styled-components";
import { useLocation, useNavigate } from "react-router-dom";
import queryString from "query-string";
import { ButtonsGroup } from "components/ButtonsGroup";
import { LackOfData } from "./components/LackOfData";
import { NumberPbr } from "./components/NumberPBR";
import { LoadPg } from "./components/LoadPG";
// @ts-ignore
import { ExternalSystem } from "./components/ExternalSystem";
// @ts-ignore
// @ts-ignore
import { StorageDepth } from "./components/StorageDepth";
import { Characteristics } from "./components/Characteristics";
import { Modes } from "./components/Modes";
import { isEast, isModeCenter } from "../../../../utils/getMode";
import { NumberUddg } from "./components/NumberUDDG";

export const Container = styled.div`
  width: 100%;
  height: 100%;
`;

export const ActionBar = styled.div`
  width: 100%;
  height: 24px; //60
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  box-shadow: 0 8px 8px rgba(50, 50, 71, 0.08), 0 8px 16px rgba(50, 50, 71, 0.06);
  border-radius: 6px;
  display: flex;
  align-items: center;
  padding: 15px 20px;
`;

export const ButtonsGroupStyled = styled(ButtonsGroup)`
  margin-left: auto;
  margin-right: 10px;
`;

export const ContentContainer = styled.div<{ innerHeight?: number }>`
  width: 100%;
  border-radius: 6px;
`;

export const Center: FC<any> = (props) => {
  const { innerHeight, isModeCenter, isCenter } = props;
  const location = useLocation();
  const history = useNavigate();

  const {
    viewPage = isEast ? "numberUddg" : "numberPbr",
    modeSetting = "general",
    generalView = "externalSystem",
    externalSystemTabId = "ess",
  }: any = queryString.parse(location.search);

  return (
    <Container>
      <ActionBar>
        <ButtonsGroup
          items={[
            { value: "general", label: "Общие настройки" },
            { value: "pg", label: "Настройки ПГ", dataTest: "settings-header.pg-button" },
          ]}
          selectedValue={modeSetting}
          onClick={(value) => {
            if (value === "pg") {
              history(`?modeSetting=${value}&viewPage=${isEast ? "numberUddg" : "numberPbr"}&generalView=${generalView}&externalSystemTabId=${externalSystemTabId}`);
            } else {
              history(`?modeSetting=${value}&viewPage=${viewPage}&generalView=${generalView}&externalSystemTabId=${externalSystemTabId}`);
            }
          }}
        />
        {modeSetting === "pg" && (
          <ButtonsGroupStyled
            items={
              isEast
                ? [
                    { value: "numberUddg", label: "Номер ПБР [2СЗ]", dataTest: "pg-settings.number-uddg" },
                    { value: "characteristics", label: "Характеристики", dataTest: "pg-settings.characteristics" },
                    { value: "lackOfData", label: "Отсутствие данных", dataTest: "pg-settings.lack-data" },
                    { value: "loadPG", label: "Загрузка ПГ", dataTest: "pg-settings.load-pg" },
                    { value: "modes", label: "MODES-Terminal", dataTest: "pg-settings.modes" },
                  ]
                : [
                    { value: "numberPbr", label: "Номер ПБР [1СЗ]", dataTest: "pg-settings.number-pbr" },
                    { value: "numberUddg", label: "Номер ПБР [2СЗ]", dataTest: "pg-settings.number-uddg" },
                    { value: "characteristics", label: "Характеристики", dataTest: "pg-settings.characteristics" },
                    { value: "lackOfData", label: "Отсутствие данных", dataTest: "pg-settings.lack-data" },
                    { value: "loadPG", label: "Загрузка ПГ", dataTest: "pg-settings.load-pg" },
                    { value: "modes", label: "MODES-Terminal", dataTest: "pg-settings.modes" },
                  ]
            }
            selectedValue={viewPage}
            widthButton={200}
            onClick={(value) => {
              history(`?modeSetting=${modeSetting}&viewPage=${value}&generalView=${generalView}&externalSystemTabId=${externalSystemTabId}`);
            }}
          />
        )}
        {modeSetting === "general" && (
          <ButtonsGroupStyled
            items={
              isModeCenter && isCenter
                ? [{ value: "externalSystem", label: "Взаимодействие с Внешними Системами" }]
                : [
                    { value: "externalSystem", label: "Взаимодействие с Внешними Системами" },
                    { value: "storageDepth", label: "Глубина хранения" },
                  ]
            }
            selectedValue={generalView}
            widthButton={370}
            // fontSize={14}
            onClick={(value) => {
              history(`?modeSetting=${modeSetting}&viewPage=${viewPage}&generalView=${value}&externalSystemTabId=${externalSystemTabId}`);
            }}
          />
        )}
      </ActionBar>
      {modeSetting === "general" && (
        <ContentContainer>
          {generalView === "externalSystem" && (
            <ExternalSystem viewPage={viewPage} modeSetting={modeSetting} generalView={generalView} externalSystemTabId={externalSystemTabId} />
          )}
          {generalView === "storageDepth" && <StorageDepth innerHeight={innerHeight} />}
        </ContentContainer>
      )}
      {modeSetting === "pg" && (
        <ContentContainer>
          {viewPage === "numberPbr" && <NumberPbr />}
          {viewPage === "numberUddg" && <NumberUddg />}
          {viewPage === "lackOfData" && <LackOfData />}
          {viewPage === "loadPG" && <LoadPg innerHeight={innerHeight} />}
          {viewPage === "characteristics" && <Characteristics />}
          {viewPage === "modes" && <Modes />}
        </ContentContainer>
      )}
    </Container>
  );
};
