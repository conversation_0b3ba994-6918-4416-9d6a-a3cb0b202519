import styled, { css } from "styled-components";
import { TextField } from "components/TextField";
import { Checkbox } from "components/Checkbox";
import { Icon } from "components/Icon";
import { Button } from "components/Button";

export const TextFieldStyled = styled(TextField)`
  height: 18px;
`;

export const Container = styled.div`
  height: 97%;
  position: relative;

  .editable-cell {
    position: relative;
  }

  .editable-cell-value-wrap {
    padding: 5px 12px;
    cursor: pointer;
  }

  .editable-row:hover .editable-cell-value-wrap {
    padding: 4px 11px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
  }

  [data-theme="dark"] .editable-row:hover .editable-cell-value-wrap {
    border: 1px solid #434343;
  }
`;

export const MainDoubleContainer = styled.div`
  width: 100%;
  height: 20px;
  display: flex;
  flex-direction: row;
`;

export const PartDoubleContainer = styled.div`
  width: 50%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const CheckboxStyled = styled(Checkbox)`
  margin-right: 10px;
`;

export const CodeLabel = styled.div<{ level?: number; value?: boolean }>`
  padding: 2px;
  border-radius: 4px;
  ${(p) =>
    p.level === 0 &&
    css`
      background-color: ${(p) => p.theme.blueActiveSupport};
      color: ${(p) => p.theme.white};
    `}
  ${(p) =>
    p.level === 1 &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport};
      color: ${(p) => p.theme.white};
    `}
`;

export const Action = styled(Button)`
  width: 160px;
  margin: 0 4px;
  &:hover {
    background-color: transparent;
  }
`;

export const ButtonsRight = styled.div`
  margin-left: auto;
  margin-right: 10px;
  display: flex;
  justify-content: space-between;
`;

export const IconStyled = styled(Icon)<{ name?: string }>`
  margin-left: 10px;
  margin-top: 3px;
  cursor: pointer;
  &:hover {
    color: ${(p) => p.theme.primaryColor};
    transition: all 0.3s;
  }
  ${(p) =>
    p.name === "close" &&
    css`
      margin-top: 1px;
    `}
`;

export const UIDLabel = styled.div``;

export const ErrorContainer = styled.div`
  cursor: pointer;
  color: ${(p) => p.theme.redActiveSupport};
`;

export const ErrorContent = styled.div`
  width: 200px;
  height: 20px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const ErrorsMainContainer = styled.div<{ isError?: boolean }>`
  width: 100%;
  height: 20px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  ${(p) =>
    p.isError &&
    css`
      border: solid 1px red;
    `}
`;
