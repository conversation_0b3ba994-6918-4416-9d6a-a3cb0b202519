import { observer } from "mobx-react";
import { useStores } from "stores/useStore";
import { toJS } from "mobx";
import { Table } from "components/Table";
import React, { useCallback, useEffect, useState } from "react";
import { Button } from "components/Button";
import styled, { css } from "styled-components";
import { Checkbox } from "components/Checkbox";
import { Icon } from "components/Icon";
import { TextField } from "components/TextField";
import { isEast, isModeCenter } from "../../../../../../utils/getMode";
import { isUUID } from "../../../../../../helpers/GenerationUUID";
import { prepareDataTable } from "../../../../../../utils";
import { getWidthModal } from "../../../../../../helpers/adaptive";

export const TextFieldStyled = styled(TextField)`
  height: 18px;
`;

export const Container = styled.div`
  height: 97%;
  position: relative;
`;

export const MainDoubleContainer = styled.div`
  width: 100%;
  height: 20px;
  display: flex;
  flex-direction: row;
`;

export const PartDoubleContainer = styled.div`
  width: 50%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const CheckboxStyled = styled(Checkbox)`
  margin-right: 10px;
`;

export const CodeLabel = styled.div<{ level?: number; value?: boolean }>`
  padding: 2px;
  border-radius: 4px;
  ${(p) =>
    p.level === 0 &&
    css`
      background-color: ${(p) => p.theme.blueActiveSupport};
      color: ${(p) => p.theme.white};
    `}
  ${(p) =>
    p.level === 1 &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport};
      color: ${(p) => p.theme.white};
    `}
`;
export const ButtonStyled = styled(Button)`
  width: auto;
  margin: 0 4px;
  &:hover {
    background-color: transparent;
  }
`;

export const ProtocolButtonContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const Action = styled(Button)`
  width: 160px;
  margin: 0 4px;
`;

export const ButtonsRight = styled.div`
  margin-left: auto;
  margin-right: 10px;
  display: flex;
  //width: 435px;
  justify-content: space-between;
`;

export const IconStyled = styled(Icon)<{ name?: string }>`
  margin-left: 10px;
  margin-top: 3px;
  cursor: pointer;
  &:hover {
    color: ${(p) => p.theme.primaryColor};
    transition: all 0.3s;
  }
  ${(p) =>
    p.name === "close" &&
    css`
      margin-top: 1px;
    `}
`;

export const UIDLabel = styled.div`
  //width: 250px;
  color: ${(p) => p.theme.textColor};
`;

export const ErrorContainer = styled.div`
  cursor: pointer;
  color: ${(p) => p.theme.redActiveSupport};
`;

export const ErrorContent = styled.div`
  width: 200px;
  height: 20px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const ErrorsMainContainer = styled.div<{ isError?: boolean }>`
  width: 100%;
  height: 20px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${(p) => p.theme.textColor};
  ${(p) =>
    p.isError &&
    css`
      border: solid 1px red;
    `}
`;

export const Circle = styled.div<{ status?: "SENDING" | "NOT_FULLY_SENT" | "ERROR" | "DONE" }>`
  width: 12px;
  height: 12px;
  background-color: ${(p) => p.theme.gray};
  border-radius: 50%;
  margin-right: 10px;
  ${(p) =>
    (p.status === "SENDING" || p.status === "NOT_FULLY_SENT") &&
    css`
      background-color: ${(p) => p.theme.orangeActiveSupport};
    `}
  ${(p) =>
    p.status === "ERROR" &&
    css`
      background-color: ${(p) => p.theme.redActiveSupport};
    `}
  ${(p) =>
    p.status === "DONE" &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport};
    `}
`;

export const TooltipContent = styled.div`
  padding: 10px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const ProtocolContainer = styled.div`
  position: absolute;
  width: 400px;
  height: auto;
  min-height: 30px;
  max-height: 300px;
  border: solid 1px ${(p) => p.theme.lightGray};
  background-color: ${(p) => p.theme.white};
  z-index: 1001;
  left: 110px;
  top: 30px;
  border-radius: 6px;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  overflow-x: hidden;
`;

export const RowProtocol = styled.div`
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: solid 1px ${(p) => p.theme.lightGray};
  cursor: default;
  &:hover {
    background-color: ${(p) => p.theme.blueActiveSupport};
    color: ${(p) => p.theme.white};
  }
`;

export const EmptyRowProtocol = styled.div`
  width: 100%;
  min-height: 30px;
`;

export const Actions = styled.div`
  display: flex;
  margin-left: auto;
  margin-right: 10px;
`;

export const TableContainer = styled.div`
  height: calc(100vh - 40px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 78px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 74px);
  }
`;

export const StatusPG = observer(() => {
  const { settingsStore, tableStore, authStore } = useStores();
  const { pgTypeList, getPgTypeList, updatePgTypeList, isLoadingPg } = settingsStore;
  const [columns, setColumns] = useState<any>([]);
  const [dataSource, setDataSource] = useState<any>([]);
  const [columnsMain, setColumnsMain] = useState<any>([]);

  let defaultColumns: any[];

  const [columnOrder, setColumnOrder] = useState<any>([]);
  const [initScreenWidth, setInitScreenWidth] = useState<any>(getWidthModal(document.documentElement.clientWidth));
  const [screenWidth, setScreenWidth] = useState<any>(initScreenWidth);
  const [isEditWidth, setEditWidth] = useState(false);

  const resizeWeb = () => {
    setScreenWidth(getWidthModal(document.documentElement.clientWidth));
  };

  const setDefaultColumns = () => {
    setColumnsMain(() => {
      return columnOrder.map((el: any) => {
        return isEast ? getDefaultColumnsEast().find((item) => item.name === el) : getDefaultColumns().find((item) => item.name === el);
        // return getDefaultColumns().find((item) => item.name === el);
      });
    });
  };

  useEffect(() => {
    window.addEventListener("resize", resizeWeb);
    return () => {
      window.removeEventListener("resize", resizeWeb);
    };
  }, []);

  useEffect(() => {
    if (initScreenWidth !== screenWidth && !isEditWidth) {
      setEditWidth(true);
      setDefaultColumns();
    } else if (initScreenWidth !== screenWidth && isEditWidth) {
      setDefaultColumns();
    } else if (initScreenWidth === screenWidth && isEditWidth) {
      setDefaultColumns();
    }
  }, [screenWidth]);

  const getDefaultColumns = () => {
    if (screenWidth >= 650 && screenWidth < 700) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 210, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 210, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 210, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 210, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 210, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 700 && screenWidth < 800) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 210, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 210, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 210, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 210, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 210, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 800 && screenWidth < 900) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 210, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 210, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 210, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 210, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 210, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 900 && screenWidth < 1024) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 210, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 210, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 210, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 210, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 210, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1024 && screenWidth < 1152) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 210, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 210, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 210, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 210, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 210, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1152 && screenWidth < 1280) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 228, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 228, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 228, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 228, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 228, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1280 && screenWidth < 1400) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 250, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 250, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 250, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 250, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 250, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1400 && screenWidth < 1600) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 274, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 274, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 274, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 274, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 274, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1600 && screenWidth < 1700) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 316, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 316, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 316, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 316, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 316, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1700 && screenWidth < 1800) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 336, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 336, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 336, editable: true, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 336, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 336, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1800 && screenWidth < 1900) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 350, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 350, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 350, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 350, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 350, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1900 && screenWidth < 2000) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 380, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 380, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 380, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 380, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 380, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 2000 && screenWidth < 2100) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 400, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 400, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 400, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 400, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 400, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 2100 && screenWidth < 2200) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 420, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 420, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 420, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 420, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 420, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 2200 && screenWidth < 2560) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 440, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 440, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 440, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 440, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 440, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 2560 && screenWidth < 3000) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 510, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 510, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 510, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 510, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 510, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 3000 && screenWidth < 3840) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 600, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 600, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 600, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 600, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 600, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 3840 && screenWidth < 4000) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 768, isSort: "alphabet" },
        { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 768, editable: true, isSort: "alphabet" },
        { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 768, editable: true, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 768, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 768, editable: true, isSort: "alphabet" },
      ];
    }
    return [
      { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 380, isSort: "alphabet" },
      { name: "PPBR", key: "PPBR", title: "UID для ПГ типа ППБР [1СЗ]", width: 380, editable: true, isSort: "alphabet" },
      { name: "PBR", key: "PBR", title: "UID для ПГ типа ПБР [1СЗ]", width: 380, editable: true, isSort: "alphabet" },
      { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 380, editable: true, isSort: "alphabet" },
      { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 380, editable: true, isSort: "alphabet" },
    ];
  };

  const getDefaultColumnsEast = () => {
    if (screenWidth >= 650 && screenWidth < 700) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 210, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 210, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 210, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 700 && screenWidth < 800) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 230, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 230, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 230, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 800 && screenWidth < 900) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 265, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 265, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 265, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 900 && screenWidth < 1024) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 298, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 298, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 298, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1024 && screenWidth < 1152) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 340, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 340, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 340, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1152 && screenWidth < 1280) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 382, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 382, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 382, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1280 && screenWidth < 1400) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 424, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 424, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 424, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1400 && screenWidth < 1600) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 460, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 460, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 460, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1600 && screenWidth < 1700) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 530, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 530, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 530, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1700 && screenWidth < 1800) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 560, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 560, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 560, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1800 && screenWidth < 1900) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 600, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 600, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 600, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 1900 && screenWidth < 2000) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 630, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 630, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 630, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 2000 && screenWidth < 2100) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 660, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 660, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 660, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 2100 && screenWidth < 2200) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 698, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 698, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 698, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 2200 && screenWidth < 2560) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 730, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 730, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 730, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 2560 && screenWidth < 3000) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 850, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 850, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 850, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 3000 && screenWidth < 3840) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 998, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 998, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 998, editable: true, isSort: "alphabet" },
      ];
    }
    if (screenWidth >= 3840 && screenWidth < 4000) {
      return [
        { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 1280, isSort: "alphabet" },
        { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 1280, editable: true, isSort: "alphabet" },
        { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 1280, editable: true, isSort: "alphabet" },
      ];
    }
    return [
      { name: "param", key: "param", title: "Параметр ОИК СК-11", width: 630, isSort: "alphabet" },
      { name: "DDG", key: "DDG", title: "UID для ПГ типа ППБР [2СЗ]", width: 630, editable: true, isSort: "alphabet" },
      { name: "UDDG", key: "UDDG", title: "UID для ПГ типа ПБР [2СЗ]", width: 630, editable: true, isSort: "alphabet" },
    ];
  };
  const { isCenter } = authStore;

  const getUsualDefaultColumns = () => {
    return isModeCenter ? getDefaultColumns() : getDefaultColumns().filter((el: any) => !(el.name === "DDG" || el.name === "UDDG"));
  };

  defaultColumns = isEast ? getDefaultColumnsEast() : getUsualDefaultColumns();

  useEffect(() => {
    tableStore.getTableParams("98_v4").then((data: any) => {
      if (data) {
        setColumnsMain(data);
      } else {
        setColumnsMain(isEast ? getDefaultColumnsEast() : getUsualDefaultColumns());
      }
    });
  }, []);

  useEffect(() => {
    return () => {
      setFocus({ row: null, cell: null });
      localStorage.removeItem(`rowEdit-PPBR`);
      localStorage.removeItem(`rowEdit-PBR`);
      localStorage.removeItem(`rowEdit-UDDG`);
      localStorage.removeItem(`rowEdit-DDG`);
    };
  }, []);

  useEffect(() => {
    getPgTypeList();
  }, []);

  useEffect(() => {
    if (pgTypeList.length > 0) {
      const tableData = [
        {
          tabId: "endUid",
          key: "endUid",
          param: "Время конца информации плана БР",
          PBR: "",
          PPBR: "",
          DDG: "",
          UDDG: "",
        },
        {
          tabId: "startUid",
          key: "startUid",
          param: "Время начала информации плана БР",
          PBR: "",
          PPBR: "",
          DDG: "",
          UDDG: "",
        },
        {
          tabId: "noUid",
          key: "noUid",
          param: "Факт ПБР",
          PBR: "",
          PPBR: "",
          DDG: "",
          UDDG: "",
        },
        {
          tabId: "actionUid",
          key: "actionUid",
          param: "Факт Последняя операция по плану БР Измерения СРПГ",
          PBR: "",
          PPBR: "",
          DDG: "",
          UDDG: "",
        },
      ];
      pgTypeList.map((item: any) => {
        tableData.map((row: any) => {
          row[item.type] = item[row.key] || "";
        });
      });

      setDataSource(tableData);

      const handleSave = (row: any) => {
        const newData = [...tableData];
        const index = newData.findIndex((item) => row.key === item.key);
        const item = newData[index];
        newData.splice(index, 1, {
          ...item,
          ...row,
        });
        setDataSource(newData);
      };
      const arr: any = [{ name: "param", key: "param", title: "Параметр ОИК СК-11", width: 400, isSort: "alphabet" }];
      pgTypeList.map((item: any) => {
        arr.push({
          name: item.type,
          key: item.type,
          title: `UID для ПГ типа ${item.name}`,
          width: isEast ? 750 : 378,
          editable: true,
          isSort: "alphabet",
        });
      });
      const cols = arr.map((col: any) => {
        if (!col.editable) {
          return col;
        }
        return {
          ...col,
          onCell: (record: any) => ({
            record,
            editable: col.editable,
            dataIndex: col.dataIndex,
            title: col.title,
            handleSave,
          }),
        };
      });
      setColumns(cols);
    }
  }, [pgTypeList]);

  const getError = (value: any) => {
    if (value === "actionUid") {
      return "isActionUid";
    }
    if (value === "endUid") {
      return "isEndUid";
    }
    if (value === "noUid") {
      return "isNoUid";
    }
    if (value === "startUid") {
      return "isStartUid";
    }
  };

  const [focus, setFocus] = useState<any>({ row: null, cell: null });
  const [focusHeader, setFocusHeader] = useState<any>(null);

  const customCell: any[] = [
    {
      name: "PPBR",
      render: (value: any, row: any) => {
        const localCur = JSON.parse(localStorage.getItem(`rowEdit-PPBR`) as string) ?? null;
        const initCurrent = localCur && localCur?.key === row.tabId ? localCur?.value : value ?? "";
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [current, setCurrent] = useState(initCurrent);
        const initFocus = focus.row === row.tabId && "PPBR" === focus.cell && focusHeader === null;
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [isFocus, setIsFocus] = useState(initFocus);
        const originalValue = value ? value : "";
        const isChange = current !== originalValue;
        return (
          <ErrorsMainContainer
            onFocus={() => {
              if (focus.row === null && focus.cell === null) {
                setFocus({ row: row.tabId, cell: "PPBR" });
              }
              setIsFocus(true);
            }}
            onBlur={() => {
              setIsFocus(false);
              localStorage.removeItem(row.tabId);
            }}
          >
            <TextFieldStyled
              errors={current.length > 0 ? (isUUID(current) ? [] : ["Неправильный формат UUID"]) : []}
              value={current ?? ""}
              focus={isFocus}
              data-ppbr={row.tabId}
              maxLength={36}
              isChange={isChange}
              dataTest="status-pg-table.ppbr-input"
              onChange={(e) => {
                setCurrent(e.target.value);
                localStorage.setItem(`rowEdit-PPBR`, JSON.stringify({ key: row.tabId, value: e.target.value }));
              }}
            />
          </ErrorsMainContainer>
        );
      },
    },
    {
      name: "PBR",
      render: (value: any, row: any) => {
        const localCur = JSON.parse(localStorage.getItem(`rowEdit-PBR`) as string) ?? null;
        const initCurrent = localCur && localCur?.key === row.tabId ? localCur?.value : value ?? "";
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [current, setCurrent] = useState(initCurrent);
        const initFocus = focus.row === row.tabId && "PBR" === focus.cell && focusHeader === null;
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [isFocus, setIsFocus] = useState(initFocus);
        const originalValue = value ? value : "";
        const isChange = current !== originalValue;
        return (
          <ErrorsMainContainer
            onFocus={() => {
              if (focus.row === null && focus.cell === null) {
                setFocus({ row: row.tabId, cell: "PBR" });
              }
              setIsFocus(true);
            }}
            onBlur={() => {
              setIsFocus(false);
              localStorage.removeItem(row.tabId);
            }}
          >
            <TextFieldStyled
              value={current}
              focus={isFocus}
              data-pbr={row.tabId}
              errors={current.length > 0 ? (isUUID(current) ? [] : ["Неправильный формат UUID"]) : []}
              maxLength={36}
              isChange={isChange}
              dataTest="status-pg-table.pbr-input"
              onChange={(e) => {
                setCurrent(e.target.value);
                localStorage.setItem(`rowEdit-PBR`, JSON.stringify({ key: row.tabId, value: e.target.value }));
              }}
            />
          </ErrorsMainContainer>
        );
      },
    },
    {
      name: "UDDG",
      render: (value: any, row: any) => {
        const localCur = JSON.parse(localStorage.getItem(`rowEdit-UDDG`) as string) ?? null;
        const initCurrent = localCur && localCur?.key === row.tabId ? localCur?.value : value ?? "";
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [current, setCurrent] = useState(initCurrent);
        const initFocus = focus.row === row.tabId && "UDDG" === focus.cell && focusHeader === null;
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [isFocus, setIsFocus] = useState(initFocus);
        const originalValue = value ? value : "";
        const isChange = current !== originalValue;
        return (
          <ErrorsMainContainer
            onFocus={() => {
              if (focus.row === null && focus.cell === null) {
                setFocus({ row: row.tabId, cell: "UDDG" });
              }
              setIsFocus(true);
            }}
            onBlur={() => {
              setIsFocus(false);
              localStorage.removeItem(row.tabId);
            }}
          >
            <TextFieldStyled
              value={current}
              focus={isFocus}
              maxLength={36}
              data-uddg={row.tabId}
              isChange={isChange}
              dataTest="status-pg-table.uddg-input"
              errors={current.length > 0 ? (isUUID(current) ? [] : ["Неправильный формат UUID"]) : []}
              onChange={(e) => {
                setCurrent(e.target.value);
                localStorage.setItem(`rowEdit-UDDG`, JSON.stringify({ key: row.tabId, value: e.target.value }));
              }}
            />
          </ErrorsMainContainer>
        );
      },
    },
    {
      name: "DDG",
      render: (value: any, row: any) => {
        const localCur = JSON.parse(localStorage.getItem(`rowEdit-DDG`) as string) ?? null;
        const initCurrent = localCur && localCur?.key === row.tabId ? localCur?.value : value ?? "";
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [current, setCurrent] = useState(initCurrent);
        const initFocus = focus.row === row.tabId && "DDG" === focus.cell && focusHeader === null;
        // eslint-disable-next-line react-hooks/rules-of-hooks
        const [isFocus, setIsFocus] = useState(initFocus);
        const originalValue = value ? value : "";
        const isChange = current !== originalValue;
        return (
          <ErrorsMainContainer
            onFocus={() => {
              if (focus.row === null && focus.cell === null) {
                setFocus({ row: row.tabId, cell: "DDG" });
              }
              setIsFocus(true);
            }}
            onBlur={() => {
              setIsFocus(false);
              localStorage.removeItem(row.tabId);
            }}
          >
            <TextFieldStyled
              value={current}
              data-ddg={row.tabId}
              focus={isFocus}
              maxLength={36}
              errors={current.length > 0 ? (isUUID(current) ? [] : ["Неправильный формат UUID"]) : []}
              isChange={isChange}
              dataTest="status-pg-table.ddg-input"
              onChange={(e) => {
                setCurrent(e.target.value);
                localStorage.setItem(`rowEdit-DDG`, JSON.stringify({ key: row.tabId, value: e.target.value }));
              }}
            />
          </ErrorsMainContainer>
        );
      },
    },
  ];
  const onSave = async () => {
    let DDG: any = [];
    const collectionOfElementsDDG = document.querySelectorAll("[data-ddg]");
    for (let i = 0; i < collectionOfElementsDDG.length; i++) {
      // @ts-ignore
      const key = collectionOfElementsDDG[i].getAttribute("data-ddg");
      // @ts-ignore
      DDG[i] = { key, value: collectionOfElementsDDG[i]?.value };
    }
    let PBR: any = [];
    const collectionOfElementsPBR = document.querySelectorAll("[data-pbr]");
    for (let i = 0; i < collectionOfElementsPBR.length; i++) {
      // @ts-ignore
      const key = collectionOfElementsPBR[i].getAttribute("data-pbr");
      // @ts-ignore
      PBR[i] = { key, value: collectionOfElementsPBR[i]?.value };
    }
    let PPBR: any = [];
    const collectionOfElementsPPBR = document.querySelectorAll("[data-ppbr]");
    for (let i = 0; i < collectionOfElementsPPBR.length; i++) {
      // @ts-ignore
      const key = collectionOfElementsPPBR[i].getAttribute("data-ppbr");
      // @ts-ignore
      PPBR[i] = { key, value: collectionOfElementsPPBR[i]?.value };
    }
    let UDDG: any = [];
    const collectionOfElementsUDDG = document.querySelectorAll("[data-uddg]");
    for (let i = 0; i < collectionOfElementsUDDG.length; i++) {
      // @ts-ignore
      const key = collectionOfElementsUDDG[i].getAttribute("data-uddg");
      // @ts-ignore
      UDDG[i] = { key, value: collectionOfElementsUDDG[i]?.value };
    }
    let items = [];
    const types = ["DDG", "PBR", "PPBR", "UDDG"];
    const finalValues: any = { UDDG, PBR, PPBR, DDG };
    items = types.map((el) => {
      const find: any = finalValues[el];
      const endUid = find.find((item: any) => item.key === "endUid")?.value ?? null;
      const startUid = find.find((item: any) => item.key === "startUid")?.value ?? null;
      const noUid = find.find((item: any) => item.key === "noUid")?.value ?? null;
      const actionUid = find.find((item: any) => item.key === "actionUid")?.value ?? null;
      return { type: el, endUid, startUid, noUid, actionUid };
    });
    items = items.filter((el) => {
      return pgTypeList.some((item: any) => item.type == el.type);
    });
    const hasErrors = items.some((el) => {
      const isActionUid = el?.actionUid && el?.actionUid?.length > 0 ? isUUID(el.actionUid) : true;
      const isEndUid = el?.endUid && el?.endUid?.length > 0 ? isUUID(el.endUid) : true;
      const isNoUid = el?.noUid && el?.noUid?.length > 0 ? isUUID(el.noUid) : true;
      const isStartUid = el?.startUid && el?.startUid?.length > 0 ? isUUID(el.startUid) : true;
      return !isActionUid || !isEndUid || !isNoUid || !isStartUid;
    });
    if (!hasErrors) {
      const prepareItems = items.map((el: any) => {
        return {
          ...el,
          actionUid: el.actionUid.length === 0 ? null : el.actionUid,
          endUid: el.endUid.length === 0 ? null : el.endUid,
          noUid: el.noUid.length === 0 ? null : el.noUid,
          startUid: el.startUid.length === 0 ? null : el.startUid,
        };
      });
      await updatePgTypeList(prepareItems)
        .then((isSave: boolean) => {
          if (isSave) {
            setDataSource([]);
            getPgTypeList();
          }
        })
        .then(() => {
          setFocus({ row: null, cell: null });
          localStorage.removeItem(`rowEdit`);
          localStorage.removeItem(`rowEdit-PPBR`);
          localStorage.removeItem(`rowEdit-PBR`);
          localStorage.removeItem(`rowEdit-UDDG`);
          localStorage.removeItem(`rowEdit-DDG`);
        });
    }
  };

  const onReset = () => {
    const tableData = [
      {
        tabId: "endUid",
        key: "endUid",
        param: "Время конца информации плана БР",
        PBR: "",
        PPBR: "",
        DDG: "",
        UDDG: "",
      },
      {
        tabId: "startUid",
        key: "startUid",
        param: "Время начала информации плана БР",
        PBR: "",
        PPBR: "",
        DDG: "",
        UDDG: "",
      },
      {
        tabId: "noUid",
        key: "noUid",
        param: "Факт ПБР",
        PBR: "",
        PPBR: "",
        DDG: "",
        UDDG: "",
      },
      {
        tabId: "actionUid",
        key: "actionUid",
        param: "Факт Последняя операция по плану БР Измерения СРПГ",
        PBR: "",
        PPBR: "",
        DDG: "",
        UDDG: "",
      },
    ];
    pgTypeList.map((item: any) => {
      tableData.map((row: any) => {
        row[item.type] = item[row.key] || "";
      });
    });

    setDataSource(tableData);
    setFocus({ row: null, cell: null });
    localStorage.removeItem(`rowEdit-PPBR`);
    localStorage.removeItem(`rowEdit-PBR`);
    localStorage.removeItem(`rowEdit-UDDG`);
    localStorage.removeItem(`rowEdit-DDG`);
  };

  const isRowEdit =
    (localStorage.getItem(`rowEdit-PPBR`) || localStorage.getItem(`rowEdit-PBR`) || localStorage.getItem(`rowEdit-UDDG`) || localStorage.getItem(`rowEdit-DDG`)) ?? null;
  const isDisable = !Object.values(focus).some((el) => el) && !isRowEdit;

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("98_v4").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "name", direction: "asc" }]);
      }
    });
  }, []);

  return (
    <Container>
      <TableContainer>
        <Table
          columns={columnsMain}
          setColumns={setColumnsMain}
          columnOrder={columnOrder}
          setColumnOrder={setColumnOrder}
          tableData={prepareDataTable(dataSource)}
          defaultColumns={defaultColumns} //columns
          customCells={customCell}
          focusHeader={focusHeader}
          setFocusHeader={setFocusHeader}
          initSorting={initSorting}
          isFocusHeader={true}
          isLoading={isLoadingPg}
          tableKey="98_v4"
          dataTest="status-pg.table"
          dataTestRows="status-pg-table.row"
          headerComponents={
            <Actions>
              <Action title="Сохранить" onClick={() => onSave()} disabled={isDisable} dataTest="status-pg-table-header.save-button" />
              <Action
                title="Отменить"
                // type="secondary"
                onClick={() => onReset()}
                disabled={isDisable}
              />
            </Actions>
          }
        />
      </TableContainer>
    </Container>
  );
});
