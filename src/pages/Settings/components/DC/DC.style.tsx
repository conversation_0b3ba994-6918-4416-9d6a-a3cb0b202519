import styled, { css } from "styled-components";
import { Button } from "components/Button";
import { ButtonsGroup } from "components/ButtonsGroup";
import { SegmentedPicker } from "components/SegmentedPicker";

export const Container = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
`;

export const ActionBar = styled.div`
  width: 100%;
  height: 24px; //60
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  box-shadow: 0 8px 8px rgba(50, 50, 71, 0.08), 0 8px 16px rgba(50, 50, 71, 0.06);
  border-radius: 6px;
  display: flex;
  align-items: center;
  padding: 15px 20px;
`;

export const ButtonsGroupStyled = styled(ButtonsGroup)`
  margin-left: auto;
  margin-right: 10px;
`;

export const TableContainer = styled.div`
  display: flex;
  //height: 880px; //100%
  width: 100%;
  color: ${(p) => p.theme.textColor};
  // background-color: ${(p) => p.theme.backgroundColorSecondary};
  //margin: 10px 0;
  //box-shadow: 0 12px 16px -4px rgb(16 24 40 / 10%), 0px 4px 6px -2px rgb(16 24 40 / 5%);
  border-radius: 8px;
  flex-direction: column;
  @media (min-height: 1900px) and (max-height: 1920px) {
    height: 1000px;
  }
`;

export const Header = styled.div`
  user-select: none;
  font-weight: bold;
  font-size: 14px;
`;

export const HeaderContainer = styled.div`
  width: 100%;
  height: 40px;
  //border-bottom: solid 1px ${(p) => p.theme.lightGray};
  display: flex;
  align-items: center;
  padding: 0 10px;
`;

export const SegmentedPickerStyled = styled(SegmentedPicker)`
  margin-left: 10px;
  width: 600px;
`;

export const Buttons = styled.div`
  //width: 100%;
  display: flex;
  //justify-content: flex-end;
  margin-left: auto;
  margin-right: 10px;
`;

export const ButtonStyled = styled(Button)`
  margin: 10px;
`;

export const SpinnerContainer = styled.div`
  width: 600px;
  margin-left: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e0e2e7;
  border-radius: 10px;
`;

export const Code = styled.div<{ isChilds?: boolean }>`
  margin-left: 10px;
  font-size: 12px;
  font-weight: bold;
  padding: 1px 2px;
  border-radius: 4px;

  ${(p) =>
    p.isChilds &&
    css`
      background-color: ${(props) => props.theme.blueActiveSupport};
      color: ${(props) => props.theme.white};
    `}

  ${(p) =>
    !p.isChilds &&
    css`
      background-color: ${(props) => props.theme.greenActiveSupport};
      color: ${(props) => props.theme.white};
    `}
`;

export const CustomCell = styled.div`
  display: flex;
  align-items: center;
`;
