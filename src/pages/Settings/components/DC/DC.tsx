import React from "react";
import { Container, ActionBar, ButtonsGroupStyled, TableContainer } from "./DC.style";
import queryString from "query-string";
import { observer } from "mobx-react";
import { useNavigate } from "react-router-dom";
import styled, { css } from "styled-components";
// @ts-ignore
import { ExternalSystem } from "../Center/components/ExternalSystem";
import { ButtonsGroup } from "components/ButtonsGroup";
import { StatusPG } from "./components/StatusPG";
import { RecordEg } from "../Center/components/RecordEG";
import { isEast, isModeCenter } from "../../../../utils/getMode";
import { Characteristics } from "../Center/components/Characteristics";
import { NumberPbr } from "../Center/components/NumberPBR";
import { NumberUddg } from "../Center/components/NumberUDDG";
import { Modes } from "../Center/components/Modes";
// @ts-ignore
import { StorageDepth } from "../Center/components/StorageDepth";
import { LackOfData } from "../Center/components/LackOfData";

export const ContentContainer = styled.div<{ innerHeight?: number }>`
  width: 100%;
  border-radius: 6px;
`;

export const DC = observer((props: any) => {
  const { isCenter } = props;
  let history = useNavigate();
  const {
    generalView = "externalSystem",
    viewPage = isEast ? "numberUddg" : "numberPbr",
    modeSetting = "general",
    externalSystemTabId = "ess",
  }: any = queryString.parse(location.search);

  return (
    <Container>
      <ActionBar>
        <ButtonsGroup
          items={[
            { value: "general", label: "Общие настройки" },
            { value: "pg", label: "Настройки ПГ", dataTest: "settings-header.pg-button" },
          ]}
          selectedValue={modeSetting}
          onClick={(value: any) => {
            if (value === "pg") {
              history(
                `?modeSetting=${value}&viewPage=${
                  isCenter ? (isEast ? "numberUddg" : "numberPbr") : "statusPg"
                }&generalView=${generalView}&externalSystemTabId=${externalSystemTabId}`
              );
            } else {
              history(`?modeSetting=${value}&viewPage=${viewPage}&generalView=${generalView}&externalSystemTabId=${externalSystemTabId}`);
            }
          }}
        />
        {modeSetting === "general" && (
          <ButtonsGroupStyled
            items={[
              { value: "externalSystem", label: "Взаимодействие с Внешними Системами" },
              { value: "storageDepth", label: "Глубина хранения", dataTest: "general-settings.storage-depth" },
            ]}
            selectedValue={generalView}
            onClick={(value) => {
              history(`?modeSetting=${modeSetting}&viewPage=${viewPage}&generalView=${value}&externalSystemTabId=${externalSystemTabId}`);
            }}
            widthButton={260}
          />
        )}
        {modeSetting === "pg" && (
          <ButtonsGroupStyled
            items={
              isCenter
                ? isEast
                  ? [
                      { value: "numberUddg", label: "Номер ПБР [2СЗ]", dataTest: "pg-settings.number-uddg" },
                      { value: "characteristics", label: "Характеристики", dataTest: "pg-settings.characteristics" },
                      { value: "statusPg", label: "Статус ПГ в ОИК СК-11", dataTest: "pg-settings.status-pg" },
                      { value: "recordEG", label: "Запись в ёЖ-3", dataTest: "pg-settings.eg3" },
                      { value: "modes", label: "MODES-Terminal", dataTest: "pg-settings.modes" },
                    ]
                  : [
                      { value: "numberPbr", label: "Номер ПБР [1СЗ]", dataTest: "pg-settings.number-pbr" },
                      { value: "characteristics", label: "Характеристики", dataTest: "pg-settings.characteristics" },
                      { value: "lackOfData", label: "Отсутствие данных", dataTest: "pg-settings.lack-data" },
                      { value: "statusPg", label: "Статус ПГ в ОИК СК-11", dataTest: "pg-settings.status-pg" },
                      { value: "recordEG", label: "Запись в ёЖ-3", dataTest: "pg-settings.eg3" },
                      { value: "modes", label: "MODES-Terminal", dataTest: "pg-settings.modes" },
                    ]
                : [
                    { value: "statusPg", label: "Статус ПГ в ОИК СК-11", dataTest: "pg-settings.status-pg" },
                    { value: "recordEG", label: "Запись в ёЖ-3", dataTest: "pg-settings.eg3" },
                  ]
            }
            selectedValue={viewPage}
            widthButton={200}
            onClick={(value) => {
              history(`?modeSetting=${modeSetting}&viewPage=${value}&generalView=${generalView}&externalSystemTabId=${externalSystemTabId}`);
            }}
          />
        )}
      </ActionBar>
      {modeSetting === "general" && (
        <ContentContainer>
          {generalView === "externalSystem" && (
            <ExternalSystem viewPage={viewPage} modeSetting={modeSetting} generalView={generalView} externalSystemTabId={externalSystemTabId} />
          )}
          {generalView === "storageDepth" && <StorageDepth />}
        </ContentContainer>
      )}
      {modeSetting === "pg" && (
        <ContentContainer>
          {viewPage === "numberPbr" && <NumberPbr />}
          {viewPage === "numberUddg" && <NumberUddg />}
          {viewPage === "characteristics" && <Characteristics />}
          {viewPage === "statusPg" && <StatusPG />}
          {viewPage === "recordEG" && <RecordEg />}
          {viewPage === "modes" && <Modes />}
          {viewPage === "lackOfData" && <LackOfData />}
        </ContentContainer>
      )}
    </Container>
  );
});
