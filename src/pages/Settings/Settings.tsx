import React, { useEffect } from "react";
import { DC } from "./components/DC";
import { Center } from "./components/Center";
import { useStores } from "../../stores/useStore";
import { isEast, isModeCenter } from "../../utils/getMode";
import { observer } from "mobx-react";
import { useLocation, useNavigate } from "react-router-dom";
import queryString from "query-string";
import { useState } from "react";

export const Settings = observer(() => {
  const { authStore, nsiStore } = useStores();
  const { isCenter } = authStore;

  const location = useLocation();
  const history = useNavigate();

  const { viewPage = isEast ? "numberUddg" : "numberPbr", externalSystemTabId = "ess" }: any = queryString.parse(location.search);

  // useEffect(() => {
  //   history(`?modeSetting=general&viewPage=${viewPage}&generalView=externalSystem&externalSystemTabId=${externalSystemTabId}`);
  //   return () => {
  //     nsiStore.stopProtocolSettings();
  //     localStorage.removeItem(`table-sort-94`);
  //     localStorage.removeItem(`table-sort-95`);
  //     localStorage.removeItem(`table-sort-91`);
  //     localStorage.removeItem(`table-sort-92`);
  //     localStorage.removeItem(`table-sort-90`);
  //     localStorage.removeItem(`table-sort-93`);
  //     localStorage.removeItem(`table-sort-98`);
  //   };
  // }, [isCenter, isModeCenter]);

  const [isInit, setIsInit] = useState(false);

  useEffect(() => {
    if (isInit) {
      history(`?modeSetting=general&viewPage=${viewPage}&generalView=externalSystem&externalSystemTabId=${externalSystemTabId}`);
    } else {
      setIsInit(true);
    }
  }, [isCenter, isModeCenter]);

  if (isModeCenter && isCenter) {
    return <Center isModeCenter={isModeCenter} isCenter={isCenter} />;
  }
  return <DC isCenter={isCenter} />;
});
