import styled from "styled-components";
import { Button } from "components/Button";
import { TextField } from "components/TextField";
import { ComponentPropsWithRef } from "react";

type DivProps = ComponentPropsWithRef<"div">;

export const LoaderContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background-color: ${(p) => p.theme.backgroundColor};
`;

export const VersionContainer = styled.div`
  position: absolute;
  right: 20px;
  bottom: 10px;
  color: ${(p) => p.theme.textColor};
`;

export const IconContainer = styled.div`
  margin-top: 20px;
`;

export const RowContainer = styled.div`
  margin-top: 10px;
`;

export const Layout = styled.div`
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${(p) => p.theme.backgroundColor};
`;

export const Container = styled.div`
  width: 380px;
  height: 315px;
  //box-shadow: 0 8px 8px rgba(50, 50, 71, 0.08), 0 8px 16px rgba(50, 50, 71, 0.06);
  box-shadow: 0px 5px 18px 12px rgba(34, 60, 80, 0.2);
  border-radius: 20px;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
`;

export const Row = styled.div<DivProps>`
  margin: 10px 0;
`;

export const Input = styled(TextField)`
  width: 315px;
  height: 40px;
`;

export const ButtonStyle = styled(Button)`
  width: 315px;
  height: 40px;
`;
