import React, { useState, useEffect } from "react";
import { Icon } from "components/Icon";
import { Layout, Container, Row, Input, ButtonStyle, IconContainer, RowContainer, VersionContainer, LoaderContainer } from "./Login.style";
import { useNavigate } from "react-router-dom";
import { useStores } from "../../stores/useStore";
import { observer } from "mobx-react";
import packageJson from "../../../package.json";
import { Loader } from "components/Loader";
import { getToken, removeTokens } from "../../utils/localStorage";
import { Spinner } from "components/Spinner";
import { checkTokenTime } from "../../utils/axios";
import { logTokenEvent, maskToken } from "~/utils/tokenLogger";

export const Login = observer(() => {
  const { authStore } = useStores();
  const history = useNavigate();
  const { isLoadingLogin } = authStore;
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(true);

  const replaceList = () => {
    setLoading(true);
    const token = getToken();
    const isAuth = checkTokenTime(token);
    logTokenEvent("Login/replaceList", {
      isAuth,
      token: maskToken(token),
    });
    if (isAuth) {
      let res = "";
      const roles = JSON.parse(localStorage.getItem("roles") as string) || [];
      const [role] = roles;
      if (role === "sys_admin") {
        res = `/settings`;
      }
      if (role === "nsi_admin") {
        res = `/nci`;
      }
      if (role === "engineer") {
        res = `/planned-schedules`;
      }
      if (role === "viewer") {
        res = `/nci`;
      }
      if (role === "nsi_ess_admin") {
        res = `/nci`;
      }
      // res = `/login`;
      history(res);
    }
    setLoading(false);
  };

  useEffect(() => {
    //new
    authStore.stopNotifications();
    //new
    replaceList();
  }, []);

  const authUser = () => {
    const pathname = localStorage.getItem(`pathname`) ?? null;
    const login = localStorage.getItem("login") ?? null;
    authStore.login(username, password).then(({ isAuth, userInfo }: any) => {
      logTokenEvent("Login/authUser", {
        isAuth,
      });
      if (isAuth) {
        if (pathname && userInfo?.login === login) {
          history(pathname);
        } else {
          const role = authStore.userInfo.roles[0];
          if (role === "sys_admin") {
            history(`/settings`);
          }
          if (role === "nsi_admin") {
            history(`/nci`);
          }
          if (role === "engineer") {
            history(`/planned-schedules`);
          }
          if (role === "viewer") {
            history(`/nci`);
          }
          if (role === "nsi_ess_admin") {
            history(`/nci`);
          }
        }
      }
    });
  };

  function clickEnter(event: any) {
    if (event.code === "Enter") {
      removeTokens();
      let login = "";
      let pw = "";
      const collectionOfElements = document.querySelectorAll("[data-text]");
      for (let i = 0; i < collectionOfElements.length; i++) {
        if (i === 0) {
          // @ts-ignore
          login = collectionOfElements[i]?.value;
        }
        if (i === 1) {
          // @ts-ignore
          pw = collectionOfElements[i]?.value;
        }
      }
      const pathname = localStorage.getItem(`pathname`) ?? null;
      const loginLocal = localStorage.getItem("login" as string);
      authStore.login(login, pw).then(({ isAuth, userInfo }: any) => {
        if (isAuth) {
          if (pathname && userInfo.login === loginLocal) {
            history(pathname);
          } else {
            const role = authStore.userInfo.roles[0];
            if (role === "sys_admin") {
              history(`/settings`);
            }
            if (role === "nsi_admin") {
              history(`/nci`);
            }
            if (role === "engineer") {
              history(`/planned-schedules`);
            }
            if (role === "viewer") {
              history(`/nci`);
            }
            if (role === "nsi_ess_admin") {
              history(`/nci`);
            }
          }
        }
      });
    }
  }

  useEffect(() => {
    document.addEventListener("keyup", clickEnter);
    return () => {
      document.removeEventListener("keyup", clickEnter);
    };
  }, []);

  const rowArray = [
    { components: <Input data-text="login" placeholder="Логин" icon="profile" value={username} onChange={(e) => setUsername(e.target.value)} /> },
    { components: <Input data-text="password" placeholder="Пароль" icon="key" type="password" value={password} onChange={(e) => setPassword(e.target.value)} /> },
    { components: <ButtonStyle disabled={isLoadingLogin} onClick={() => authUser()} title={isLoadingLogin ? <Loader /> : <>Войти</>} /> },
  ];

  const getStand = () => {
    if (process.env.STAND === "main") {
      if (process.env.MODE === "center") {
        return `ЦДУ`;
      } else {
        return `ДЦ [1СЗ]`;
      }
    } else {
      if (process.env.MODE === "center") {
        return `ОДУ Востока`;
      } else {
        return `ДЦ [2СЗ]`;
      }
    }
  };

  return (
    <Layout>
      {loading ? (
        <LoaderContainer>
          <Spinner size={100} />
        </LoaderContainer>
      ) : (
        <>
          <Container>
            <IconContainer>
              <Icon name="logo" width={80} />
            </IconContainer>
            <RowContainer>
              {rowArray.map((el, index) => {
                return <Row key={`row-index-${index}`}>{el.components}</Row>;
              })}
            </RowContainer>
          </Container>
          <VersionContainer>
            {/*Версия {packageJson.version} {process.env.MODE === "center" ? "Центр" : "ДЦ"} {process.env.STAND === "main" ? "ЦДУ" : "ОЗ Восток"}*/}
            Версия {packageJson.version} {getStand()}
          </VersionContainer>
        </>
      )}
    </Layout>
  );
});
