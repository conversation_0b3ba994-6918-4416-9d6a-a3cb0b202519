import React, { FC, useEffect, useState } from "react";
import { Combobox } from "components/Combobox";
import { Container, Cell, ModalStyled } from "./ModalEdit.style";
import { ModalEditProps } from "./ModalEdit.types";

export const ModalEdit: FC<ModalEditProps> = (props) => {
  const { onCancel, object, roles, onConfirm } = props;
  const [editObject, setEditObject] = useState<any>({});

  useEffect(() => {
    setEditObject(object);
  }, []);

  return (
    <ModalStyled
      onCancel={onCancel}
      isOverLay
      title={`Редактировать роль`}
      onConfirm={() => onConfirm({ adRoleId: editObject.adRoleId, role: editObject?.role })}
      cancelText="Отменить"
      confirmText="Сохранить"
      isDisabledConfirm={editObject?.role === object?.role || Object.keys(editObject).length === 0}
      width={590}
      height={150}
    >
      <Container>
        <Cell>{editObject?.adGroup}</Cell>
        <Cell>
          <Combobox
            items={roles}
            selectedValue={editObject?.role}
            onChange={({ value }) => {
              setEditObject((prev: any) => {
                return { ...prev, role: value };
              });
            }}
          />
        </Cell>
      </Container>
    </ModalStyled>
  );
};
