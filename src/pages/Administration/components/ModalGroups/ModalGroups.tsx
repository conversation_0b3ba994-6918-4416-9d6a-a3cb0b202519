import React, { FC, useEffect, useState } from "react";
import { Table } from "components/Table";
import { Combobox } from "components/Combobox";
import { useStores } from "stores/useStore";
import { observer } from "mobx-react";
import { Container, ButtonsContainer, TableContainer, Action, ModalStyled, ButtonStyled, ErrorMessage, AdGroupContainer } from "./ModalGroups.style";
import { ModalGroupsProps } from "./ModalGroups.types";
import { defaultColumns, defaultColumnsUnLink } from "./ModalGroups.constants";
import { generateUUID } from "../../../../helpers/GenerationUUID";
import { prepareDataTable } from "../../../../utils";

export const ModalGroups: FC<ModalGroupsProps> = observer((props) => {
  const { onCancel, updateList, roleCode, connected } = props;
  const [columns, setColumns] = useState(defaultColumns);
  const [columnsUnLink, setColumnsUnLink] = useState(defaultColumnsUnLink);

  const [linked, setLinked] = useState<any[]>([]);
  const [originalLinked, setOriginalLinked] = useState<any[]>([]);
  const [unlinked, setUnlinked] = useState<any[]>([]);
  const [errors, setErrors] = useState(false);

  useEffect(() => {
    // setLinked(connected.map((el: any) => ({ adGroup: el.adGroup, dnGroup: el.dnGroup, id: el.tabId, role: el.role })));
    setLinked(connected ? [{ adGroup: connected, dnGroup: connected, id: connected, role: connected, tabId: `${generateUUID()}` }] : []);
    setOriginalLinked([{ adGroup: connected, dnGroup: connected, id: connected, role: connected }]);
    // setOriginalLinked(connected.map((el: any) => ({ adGroup: el.adGroup, dnGroup: el.dnGroup, id: el.tabId, role: el.role })));
  }, []);

  const { administrationStore, tableStore } = useStores();
  const { roles, unlinkedGroups } = administrationStore;

  useEffect(() => {
    setUnlinked(unlinkedGroups.filter((el: any) => el.adGroup !== connected));
  }, []);

  const customCellDown = [
    {
      name: "adGroup",
      render: (value: any) => {
        return <AdGroupContainer>{value.replace("\b", `\ b`)}</AdGroupContainer>;
      },
    },
    {
      name: "action",
      render: (value: any, row: any) => {
        return (
          <>
            <Action
              icon="plus"
              type="secondary"
              onClick={() => {
                setErrors(false);
                setLinked((prev) => {
                  return [...prev, { ...row, role: null }];
                });
                setUnlinked((prev) => {
                  return prev.filter((el) => el.adGroup !== row.adGroup);
                });
              }}
            />
          </>
        );
      },
    },
  ];

  const customCellUp = [
    {
      name: "adGroup",
      render: (value: any) => {
        return <AdGroupContainer>{value?.replace("\b", `\ b`)}</AdGroupContainer>;
      },
    },
    {
      name: "action",
      render: (value: any, row: any) => {
        return (
          <>
            <Action
              icon="minus"
              type="secondary"
              onClick={() => {
                setErrors(false);
                setUnlinked((prev) => {
                  return [...prev, { ...row, role: null }].sort((a, b) => a.id - b.id);
                });
                setLinked((prev) => {
                  return prev.filter((el) => el.adGroup !== row.adGroup);
                });
              }}
            />
          </>
        );
      },
    },
  ];

  const isDisabled =
    linked.length === originalLinked.length &&
    linked.every(function (element: any, index: number) {
      return element.id === originalLinked[index].id;
    });

  const saveData = () => {
    const isValid = linked.length > 1;
    if (!isValid) {
      administrationStore
        .saveGroups(linked, roleCode)
        .then(() => {
          onCancel();
        })
        .then(() => {
          updateList();
        });
    } else {
      setErrors(true);
    }
  };

  const [initSorting, setInitSorting] = useState<any>(null);
  const [initSorting2, setInitSorting2] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("41_v1").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "name", direction: "asc" }]);
      }
    });
    tableStore.getSortParams("41_v2").then((data: any) => {
      if (data) {
        setInitSorting2(data);
      } else {
        setInitSorting2([{ columnName: "name", direction: "asc" }]);
      }
    });
  }, []);

  const [modalHeight, setModalHeight] = useState(640);

  return (
    <ModalStyled isOverLay={true} onCancel={onCancel} title="Назначение групп пользователей" width={1200} height={640} setModalHeight={setModalHeight}>
      <Container height={modalHeight + 60}>
        <TableContainer isError={errors}>
          <Table
            columns={columns}
            setColumns={setColumns}
            tableData={prepareDataTable(linked)}
            isLoading={false}
            tableHeight={330}
            tableKey={`41_v1`}
            defaultColumns={defaultColumns}
            customCells={customCellUp}
            initSorting={initSorting}
          />
        </TableContainer>
        <TableContainer>
          <Table
            columns={columnsUnLink}
            setColumns={setColumnsUnLink}
            tableData={prepareDataTable(unlinked)}
            isLoading={false}
            tableHeight={330}
            tableKey={`41_v2`}
            defaultColumns={defaultColumnsUnLink}
            customCells={customCellDown}
            initSorting={initSorting2}
          />
        </TableContainer>
        <ButtonsContainer>
          {errors && <ErrorMessage>В добавленной группе должнобыть не более 1 объекта AD</ErrorMessage>}
          <ButtonStyled title="Сохранить" disabled={isDisabled} onClick={() => saveData()} isError={errors} />
          <ButtonStyled
            title="Отменить"
            // type="secondary"
            onClick={() => onCancel()}
          />
        </ButtonsContainer>
      </Container>
    </ModalStyled>
  );
});
