import styled, { css } from "styled-components";
import { Modal } from "components/Modal";
import { Button } from "components/Button";

export const AdGroupContainer = styled.div`
  color: ${(p) => p.theme.textColor};
`;

export const ModalStyled = styled(Modal)`
  //max-width: 1200px;
  //width: 90%;
  //height: 60%;
`;

export const Container = styled.div<{ height?: number }>`
  width: 100%;
  //height: 700px;
  height: ${(p) => p.height}px;
  display: flex;
  flex-direction: column;
  padding-top: 20px;
`;

export const TableContainer = styled.div<{ isError?: boolean }>`
  height: 33%; //50%
  border: solid 1px ${(p) => p.theme.lightGray};
  &:last-child {
    border-top: solid 1px transparent;
  }
  ${(p) =>
    p.isError &&
    css`
      border: solid 1px ${(p) => p.theme.redActiveSupport};
    `}
`;

export const ErrorMessage = styled.div`
  color: ${(p) => p.theme.redActiveSupport};
`;

export const ButtonsContainer = styled.div`
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 40px;
`;

export const ButtonStyled = styled(Button)`
  margin: 0 10px;
  width: 160px;
  height: 28px;
`;

export const Action = styled(Button)`
  width: 20px;
  height: 20px;
`;
