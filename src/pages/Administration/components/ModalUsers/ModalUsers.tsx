import React, { FC, useEffect, useMemo, useState } from "react";
import { useStores } from "stores/useStore";
import styled from "styled-components";
import { Modal } from "components/Modal";
import { observer } from "mobx-react";
import { Table } from "components/Table";
import { prepareDataTable } from "../../../../utils";
import { highlightSearch } from "../../../../helpers/highlightSearch";

interface DataType {
  tabId: React.ReactNode;
  adGroup: string;
  users: string[];
  childs?: DataType[];
  level?: number;
}

interface IModalUsersProps {
  role: { code: string; name: string };
  onCancel: () => void;
}

export const Container = styled.div`
  display: flex;
  margin-top: 20px;
  height: 410px;
`;

export const UsersContainer = styled.div`
  padding: 10px;
  color: ${(p) => p.theme.textColor};
`;

export const UserItem = styled.div``;

const prepareData = (arr: any) => {
  return arr.map(({ adGroup, users, groups }: { adGroup: any; users: any; groups: any }, index: number) => {
    const obj: DataType = {
      tabId: adGroup,
      // @ts-ignore
      code: adGroup,
      adGroup,
      users,
    };
    if (groups && groups.length) {
      obj.childs = prepareData(groups);
    } else {
      obj.childs = [];
    }
    return obj;
  });
};

export const ModalUsers: FC<IModalUsersProps> = observer((props) => {
  const { role, onCancel } = props;
  const { administrationStore, tableStore } = useStores();
  const { userGroups, getGroupInfoByRole } = administrationStore;

  let defaultColumns;

  const getDefaultColumns = () => {
    if (innerWidth === 1920) {
      return [
        { name: "adGroup", title: "Группа AD", width: 500 },
        { name: "users", title: "Пользователи", width: 600 },
      ];
    }
    return [
      { name: "adGroup", title: "Группа AD", width: 500 },
      { name: "users", title: "Пользователи", width: 600 },
    ];
  };
  defaultColumns = getDefaultColumns();
  const [columns, setColumns] = useState(defaultColumns);

  const customCell = [
    {
      name: "users",
      render: (values: string[], _: any, __: any, filters: any[]) => {
        const search_word = filters?.find((el: any) => el?.columnName === "users")?.value ?? "";
        return (
          <UsersContainer>
            {values.map((el, index) => {
              return <UserItem key={`user-item-${index}`}>{search_word.length > 0 ? highlightSearch(search_word, el) : el}</UserItem>;
            })}
          </UsersContainer>
        );
      },
    },
  ];

  const tableData = useMemo(() => {
    return prepareData(userGroups);
  }, [userGroups]);

  useEffect(() => {
    if (role.code) {
      getGroupInfoByRole(role.code);
    }
  }, [role.code, getGroupInfoByRole]);

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("modalUser").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "name", direction: "asc" }]);
      }
    });
  }, []);

  const [modalHeight, setModalHeight] = useState(500);

  return (
    <Modal onCancel={onCancel} isOverLay title={role.name} width={1200} height={500} setModalHeight={setModalHeight}>
      <Table
        columns={columns}
        setColumns={setColumns}
        tableData={prepareDataTable(tableData)}
        defaultColumns={defaultColumns}
        customCells={customCell}
        childrenKey={"adGroup"}
        rowHeight={100}
        tableHeight={modalHeight - 120}
        heightContainer={modalHeight - 120}
        tableKey={`modalUser`}
        initSorting={initSorting}
        disabledSearches={["adGroup"]}
        findInCell={"users"}
      />
    </Modal>
  );
});
