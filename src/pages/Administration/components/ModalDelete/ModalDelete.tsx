import React, { <PERSON> } from "react";
import { Modal } from "components/Modal";
import { ModalDeleteProps } from "./ModalDelete.types";
import styled from "styled-components";

export const ModalStyled = styled(Modal)`
  height: 124px;
`;

export const ModalDelete: FC<ModalDeleteProps> = (props) => {
  const { onCancel, object, onConfirm } = props;
  return (
    <ModalStyled
      width={500}
      height={200}
      onCancel={onCancel}
      colorScheme="red"
      title="Удаление"
      isOverLay
      description={`Вы действительно хотите удалить ${object.adGroup}?`}
      onConfirm={onConfirm}
      cancelText="Отменить"
      confirmText="Удалить"
    />
  );
};
