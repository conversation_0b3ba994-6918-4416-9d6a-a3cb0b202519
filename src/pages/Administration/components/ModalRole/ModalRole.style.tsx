import styled, { css } from "styled-components";
import { Modal } from "components/Modal";
import { Icon } from "components/Icon";

export const ModalStyled = styled(Modal)`
  //width: 1200px;
  //height: 700px;
`;

export const Container = styled.div<{ height?: number }>`
  width: 100%;
  height: ${(p) => p.height}px;
  display: flex;
`;

export const LeftContainer = styled.div`
  width: 50%;
  height: 96%;
  padding: 10px;
`;

export const RightContainer = styled.div`
  width: 50%;
  height: 96%;
  padding: 10px;
`;

export const Table = styled.div`
  //border: solid 1px blue;
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  border: solid 1px ${(p) => p.theme.lightGray};
`;

export const Row = styled.div<{ isSelected?: boolean; isDefault?: boolean }>`
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 10px;
  border-top: solid 1px ${(p) => p.theme.lightGray};
  border-left: solid 1px ${(p) => p.theme.lightGray};
  border-right: solid 1px ${(p) => p.theme.lightGray};
  cursor: pointer;

  &:hover {
    background-color: ${(p) => p.theme.lightGray};
  }

  &:first-child {
    border-top: solid 1px transparent;
  }

  &:last-child {
    border-bottom: solid 1px ${(p) => p.theme.lightGray};
  }

  ${(p) =>
    p.isSelected &&
    css`
      background-color: ${(p) => p.theme.blueActiveSupport};
      color: ${(p) => p.theme.white};
      &:hover {
        background-color: ${(p) => p.theme.blueActiveSupport};
        color: ${(p) => p.theme.white};
      }
    `}
  ${(p) =>
    p.isDefault &&
    css`
      background-color: transparent;
      color: ${(p) => p.theme.black};
      cursor: default;
      &:hover {
        background-color: transparent;
        color: ${(p) => p.theme.black};
      }
    `}
`;

export const IconStyled = styled(Icon)<{ isSelected?: boolean }>`
  color: ${(p) => p.theme.blueActiveSupport};
  ${(p) =>
    p.isSelected &&
    css`
      color: ${(p) => p.theme.white};
    `}
`;

export const IconContainer = styled.div`
  width: 30px;
  height: 30px;
  min-width: 30px;
  min-height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 6px;
`;

export const Label = styled.div``;
