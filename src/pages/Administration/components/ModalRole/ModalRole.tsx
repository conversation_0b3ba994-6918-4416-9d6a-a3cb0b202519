import React, { FC, useState, useEffect } from "react";
import { NoData } from "components/NoData";
import { useStores } from "stores/useStore";
import { observer } from "mobx-react";
import { ModalStyled, Container, LeftContainer, IconContainer, RightContainer, IconStyled, Table, Row, Label } from "./ModalRole.style";
import { ModalRoleProps } from "./ModalRole.types";

export const ModalRole: FC<ModalRoleProps> = observer((props) => {
  const { roleCode, onCancel } = props;
  const [selectedRole, setSelectedRole] = useState<any>(null);
  const { administrationStore } = useStores();
  const { roles } = administrationStore;

  useEffect(() => {
    if (roleCode !== null) {
      const currentRole = roles.find((role: any) => role.code === roleCode);
      setSelectedRole(currentRole);
    }
  }, [roleCode, roles]);

  const [modalHeight, setModalHeight] = useState(700);

  return (
    <ModalStyled isOverLay onCancel={onCancel} width={1200} height={700} setModalHeight={setModalHeight}>
      <Container height={modalHeight - 100}>
        <LeftContainer>
          <h3>Список ролей</h3>
          <Table>
            {roles.length > 0 ? (
              <>
                {roles.map((item: any, index: number) => {
                  return (
                    <Row
                      key={`role-${index}`}
                      onClick={() => {
                        setSelectedRole(item);
                      }}
                      isSelected={selectedRole?.code === item.code}
                    >
                      <IconContainer>
                        <IconStyled name="role" width={20} isSelected={selectedRole?.code === item.code} />
                      </IconContainer>
                      <Label>{item.name}</Label>
                    </Row>
                  );
                })}
              </>
            ) : (
              <NoData />
            )}
          </Table>
        </LeftContainer>
        <RightContainer>
          <h3>Список функций</h3>
          <Table>
            {selectedRole ? (
              <>
                {selectedRole?.systemRoles.map((value: any, indexx: number) => {
                  return (
                    <Row isDefault key={`function-${indexx}`}>
                      <IconContainer>
                        <IconStyled name="triangle" width={26} />
                      </IconContainer>
                      <Label>{value}</Label>
                    </Row>
                  );
                })}
              </>
            ) : (
              <NoData />
            )}
          </Table>
        </RightContainer>
      </Container>
    </ModalStyled>
  );
});
