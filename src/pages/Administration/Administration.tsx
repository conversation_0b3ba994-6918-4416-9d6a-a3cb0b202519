import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Table } from "components/Table";
import { ModalRole } from "./components/ModalRole";
import { ModalGroups } from "./components/ModalGroups";
import { useStores } from "stores/useStore";
import { observer } from "mobx-react";
import { ModalEdit } from "./components/ModalEdit";
import { ModalDelete } from "./components/ModalDelete";
import { ModalUsers } from "./components/ModalUsers";
import { Container, TableContainer, ActionsContainer, ActionButton, AdGroupContainer } from "./Administration.style";
import { generateUUID } from "../../helpers/GenerationUUID";
import { prepareDataTable } from "../../utils";
import { getWidthModal } from "../../helpers/adaptive";

export const Administration = observer(() => {
  const { administrationStore, authStore, tableStore } = useStores();
  const { innerHeight, isCenter } = authStore;
  const { ad, roles } = administrationStore;
  const [isModalUsersShow, setIsModalUsersShow] = useState<boolean>(false);
  const [isModalRole, setIsModalRole] = useState(false);
  const [selectedRole, setSelectedRole] = useState<{ code: string; name: string } | null>(null);
  const [isModalGroups, setIsModalGroups] = useState(false);
  const [editObject, setEditObject] = useState<any>({});
  const [deleteObject, setDeleteObject] = useState<any>({});
  const [connected, setConnected] = useState([]);

  let defaultColumns: any[];

  const [columnOrder, setColumnOrder] = useState<any>([]);

  const setDefaultColumns = () => {
    setColumns(() => {
      return columnOrder.map((el: any) => {
        return getDefaultColumns().find((item) => item.name === el);
      });
    });
  };

  const [initScreenWidth, setInitScreenWidth] = useState<any>(getWidthModal(document.documentElement.clientWidth));
  const [screenWidth, setScreenWidth] = useState<any>(initScreenWidth);
  const [isEditWidth, setEditWidth] = useState(false);

  const resizeWeb = () => {
    setScreenWidth(getWidthModal(document.documentElement.clientWidth));
  };

  useEffect(() => {
    window.addEventListener("resize", resizeWeb);
    return () => {
      window.removeEventListener("resize", resizeWeb);
    };
  }, []);

  useEffect(() => {
    if (initScreenWidth !== screenWidth && !isEditWidth) {
      setEditWidth(true);
      setDefaultColumns();
    } else if (initScreenWidth !== screenWidth && isEditWidth) {
      setDefaultColumns();
    } else if (initScreenWidth === screenWidth && isEditWidth) {
      setDefaultColumns();
    }
  }, [screenWidth]);

  const getDefaultColumns = () => {
    if (screenWidth >= 650 && screenWidth < 700) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 200, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 245, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 700 && screenWidth < 800) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 210, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 280, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 800 && screenWidth < 900) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 220, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 370, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 900 && screenWidth < 1024) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 220, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 475, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 1024 && screenWidth < 1152) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 230, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 590, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 1152 && screenWidth < 1280) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 250, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 700, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 1280 && screenWidth < 1400) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 370, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 700, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 1400 && screenWidth < 1600) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 510, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 700, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 1600 && screenWidth < 1700) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 700, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 700, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 1700 && screenWidth < 1800) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 750, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 750, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 1800 && screenWidth < 1900) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 800, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 800, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 1900 && screenWidth < 2000) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 850, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 850, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 2000 && screenWidth < 2100) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 900, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 900, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 2100 && screenWidth < 2200) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 950, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 950, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 2200 && screenWidth < 2560) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 1000, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 1000, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 2560 && screenWidth < 3000) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 1180, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 1180, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 3000 && screenWidth < 3840) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 1180, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 1180, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    if (screenWidth >= 3840 && screenWidth < 4000) {
      return [
        { name: "roleName", title: "Роль СРПГ", width: 1600, isSort: "alphabet" },
        { name: "adGroup", title: "Группа AD", width: 1600, isSort: "alphabet" },
        { name: "actions", title: "Действия", width: 200 },
      ];
    }
    return [
      { name: "roleName", title: "Роль СРПГ", width: 850, isSort: "alphabet" },
      { name: "adGroup", title: "Группа AD", width: 850, isSort: "alphabet" },
      { name: "actions", title: "Действия", width: 200 },
    ];
  };
  defaultColumns = getDefaultColumns();
  const [columns, setColumns] = useState<any>([]);

  useEffect(() => {
    tableStore.getTableParams("47").then((data: any) => {
      if (data) {
        setColumns(data);
      } else {
        setColumns(defaultColumns);
      }
    });
  }, []);

  useEffect(() => {
    administrationStore.init();
    return () => {
      localStorage.removeItem(`table-sort-47`);
    };
  }, [isCenter]);

  const customCell = [
    {
      name: "actions",
      render: (value: any, rowData: any) => {
        return (
          <ActionsContainer>
            <ActionButton type="secondary" icon="file" widthIcon={20} onClick={() => handleRowRoleClick(rowData)} />
            <ActionButton type="secondary" icon="pencil" widthIcon={20} onClick={() => handleRowEditClick(rowData)} />
            <ActionButton type="secondary" icon="role" widthIcon={20} onClick={() => handleRowUsersClick(rowData)} />
          </ActionsContainer>
        );
      },
    },
    {
      name: "adGroup",
      render: (value: any, row: any) => {
        return <AdGroupContainer>{value ? value : ""}</AdGroupContainer>;
      },
    },
  ];

  const deleteSelectedObject = () => {
    administrationStore.deleteSelectedObject(deleteObject.adRoleId).then(() => {
      administrationStore.init();
    });
  };

  const editSelectedObject = (obj: any) => {
    administrationStore.editSelectedObject(obj).then(() => {
      administrationStore.init();
    });
  };

  const tableData = useMemo(() => {
    return ad.map(({ roleCode, roleName, adGroup, childs }: { roleCode: any; roleName: any; adGroup: any; childs: any[] }) => ({
      key: roleCode,
      roleCode,
      roleName,
      adGroup,
      childs,
      tabId: generateUUID(),
    }));
  }, [ad]);

  const handleRowEditClick = useCallback((rowData: any) => {
    const { roleCode, roleName, childs, adGroup } = rowData;
    const connected = childs ?? [];
    // setConnected(connected);
    setConnected(adGroup);
    setSelectedRole({ code: roleCode, name: roleName });
    setIsModalGroups(true);
  }, []);

  const handleRowRoleClick = useCallback((rowData: any) => {
    const { roleCode, roleName } = rowData;
    setSelectedRole({ code: roleCode, name: roleName });
    setIsModalRole(true);
  }, []);

  const handleRowUsersClick = useCallback((rowData: any) => {
    const { roleCode, roleName } = rowData;
    setSelectedRole({ code: roleCode, name: roleName });
    setIsModalUsersShow(true);
  }, []);

  const handleModalUsersClose = useCallback(() => {
    setIsModalUsersShow(false);
  }, []);

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("47").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "name", direction: "asc" }]);
      }
    });
  }, []);

  return (
    <Container>
      {Object.keys(editObject).length > 0 && (
        <ModalEdit
          onCancel={() => setEditObject({})}
          onConfirm={(value: any) => {
            editSelectedObject(value);
            setEditObject({});
          }}
          object={editObject}
          roles={roles.map((item: any) => {
            return { ...item, value: item.code, label: item.name };
          })}
        />
      )}
      {Object.keys(deleteObject).length > 0 && (
        <ModalDelete
          onCancel={() => setDeleteObject({})}
          object={deleteObject}
          onConfirm={() => {
            deleteSelectedObject();
            setDeleteObject({});
          }}
        />
      )}
      <TableContainer>
        <Table
          tableKey="47"
          columns={columns}
          setColumns={setColumns}
          tableData={prepareDataTable(tableData)}
          defaultColumns={defaultColumns}
          customCells={customCell}
          disabledSearches={["actions"]}
          columnOrder={columnOrder}
          setColumnOrder={setColumnOrder}
          initSorting={initSorting}
        />
      </TableContainer>
      {isModalRole && <ModalRole roleCode={selectedRole?.code || ""} onCancel={() => setIsModalRole(false)} />}
      {isModalGroups && (
        <ModalGroups
          updateList={() => {
            administrationStore.init();
          }}
          onCancel={() => {
            setIsModalGroups(false);
            setSelectedRole(null);
          }}
          roleCode={selectedRole?.code}
          connected={connected}
        />
      )}
      {isModalUsersShow && selectedRole !== null && <ModalUsers role={selectedRole} onCancel={handleModalUsersClose} />}
    </Container>
  );
});
