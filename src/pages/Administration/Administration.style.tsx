import styled, { css } from "styled-components";
import { Button } from "components/Button";

export const AdGroupContainer = styled.div`
  color: ${(p) => p.theme.textColor};
`;

export const ActionsContainer = styled.div`
  display: flex;
`;

export const ActionButton = styled(Button)`
  width: 20px;
  margin-right: 10px;
`;

export const Container = styled.div`
  width: 100%;
  height: 97%;
`;

export const ActionBar = styled.div`
  width: 100%;
  height: 24px; //60
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  box-shadow: 0 8px 8px rgba(50, 50, 71, 0.08), 0 8px 16px rgba(50, 50, 71, 0.06);
  border-radius: 6px;
  display: flex;
  align-items: center;
  padding: 0 20px;
`;

export const ButtonStyled = styled(Button)`
  margin: 0 2px;
  //height: 28px; //40px
  width: 160px;
`;

export const TableContainer = styled.div`
  display: flex;
  width: 100%;
  color: ${(p) => p.theme.textColor};
  margin-top: 10px;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  height: calc(100vh - 20px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 58px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 54px);
  }
`;

//  ${(p) =>
//     p.innerHeight <= 864 &&
//     p.innerHeight > 569 &&
//     css`
//       height: 90%;
//     `}
//   ${(p) =>
//     p.innerHeight <= 569 &&
//     p.innerHeight > 467 &&
//     css`
//       height: 85%;
//     `}
//   ${(p) =>
//     p.innerHeight <= 467 &&
//     css`
//       height: 84%;
//     `}
//   ${(p) =>
//     (p.innerHeight === 618 || p.innerHeight === 619 || p.innerHeight === 617) &&
//     css`
//       height: 86%;
//     `}
//   ${(p) =>
//     p.innerHeight === 743 &&
//     css`
//       height: 90%;
//     `}

export const Action = styled(Button)`
  width: 20px;
  height: 20px;
  margin: 0 10px;
  padding-bottom: 5px;
`;

export const ActionContainer = styled.div`
  margin-left: 10px;
  display: flex;
`;
