import { FC, useEffect, useMemo, useState } from "react";
import styled, { css } from "styled-components";
import { Table } from "components/Table";
import { Pagination } from "components/Pagination";
import { filterTreeManyKeys } from "helpers/SearchUtils";
import { observer } from "mobx-react";
import { useStores } from "stores/useStore";
import { NoData } from "components/NoData";
import { Icon } from "components/Icon";
import { prepareDateTable } from "../../../../helpers/DateUtils";
import { Label } from "components/Label";
import { prepareDataTable } from "../../../../utils";
import { getWidthModal } from "../../../../helpers/adaptive";

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  //height: 100%;
  width: 100%;
  color: ${(p) => p.theme.textColor};
  //margin-top: 10px;
  //box-shadow: 0 12px 16px -4px rgb(16 24 40 / 10%), 0px 4px 6px -2px rgb(16 24 40 / 5%);
  //overflow: hidden;
`;

export const InformationContainer = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 100%;
  padding: 0 20px;
  font-size: 12px;
  cursor: default;
  user-select: none;
`;

export const ContentTable = styled.div`
  height: calc(100vh - 40px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 78px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 74px);
  }
`;

export const InfoIcon = styled(Icon)`
  color: ${(p) => p.theme.blueActiveSupport};
  margin-right: 5px;
`;

export const BottomContainer = styled.div`
  display: flex;
`;

export const BottomCell = styled.div`
  width: 50%;
`;

export const MessageContainer = styled.div`
  width: 100%;
  height: 24px;
  display: block;
  overflow: hidden;
  overflow-wrap: normal;
  white-space: nowrap;
`;

export const MessageLabel = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  color: ${(p) => p.theme.textColor};
`;

export const UserActions: FC<any> = observer((props) => {
  const { params } = props;
  const { userActionsStore, authStore, tableStore } = useStores();
  const { isLoadingUsersPage, usersJournal, isOverfilled, usersJournalOriginal } = userActionsStore;
  const { isCenter } = authStore;

  useEffect(() => {
    userActionsStore.initUserPage(params);
  }, [params, isCenter]);

  let defaultColumns: any;

  const [columnOrder, setColumnOrder] = useState<any>([]);

  const setDefaultColumns = () => {
    setColumns(() => {
      return columnOrder.map((el: any) => {
        return getDefaultColumns().find((item) => item.name === el);
      });
    });
  };
  const [initScreenWidth, setInitScreenWidth] = useState<any>(getWidthModal(document.documentElement.clientWidth));
  const [screenWidth, setScreenWidth] = useState<any>(initScreenWidth);
  const [isEditWidth, setEditWidth] = useState(false);

  const resizeWeb = () => {
    setScreenWidth(getWidthModal(document.documentElement.clientWidth));
  };

  useEffect(() => {
    window.addEventListener("resize", resizeWeb);
    return () => {
      window.removeEventListener("resize", resizeWeb);
    };
  }, []);

  useEffect(() => {
    if (initScreenWidth !== screenWidth && !isEditWidth) {
      setEditWidth(true);
      setDefaultColumns();
    } else if (initScreenWidth !== screenWidth && isEditWidth) {
      setDefaultColumns();
    } else if (initScreenWidth === screenWidth && isEditWidth) {
      setDefaultColumns();
    }
  }, [screenWidth]);

  const getDefaultColumns = () => {
    if (screenWidth >= 650 && screenWidth < 700) {
      return [
        { name: "date", title: "Дата и время", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 100, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 225, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 700 && screenWidth < 800) {
      return [
        { name: "date", title: "Дата и время", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 100, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 274, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 800 && screenWidth < 900) {
      return [
        { name: "date", title: "Дата и время", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 100, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 375, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 900 && screenWidth < 1024) {
      return [
        { name: "date", title: "Дата и время", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 100, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 475, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 1024 && screenWidth < 1152) {
      return [
        { name: "date", title: "Дата и время", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 100, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 600, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 1152 && screenWidth < 1280) {
      return [
        { name: "date", title: "Дата и время", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 100, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 730, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 1280 && screenWidth < 1400) {
      return [
        { name: "date", title: "Дата и время", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 100, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 855, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 1400 && screenWidth < 1600) {
      return [
        { name: "date", title: "Дата и время", width: 180, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 180, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 150, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 885, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 1600 && screenWidth < 1700) {
      return [
        { name: "date", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 995, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 1700 && screenWidth < 1800) {
      return [
        { name: "date", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 1090, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 1800 && screenWidth < 1900) {
      return [
        { name: "date", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 1190, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 1900 && screenWidth < 2000) {
      return [
        { name: "date", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 1290, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 2000 && screenWidth < 2100) {
      return [
        { name: "date", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 1390, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 2100 && screenWidth < 2200) {
      return [
        { name: "date", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 1490, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 2200 && screenWidth < 2560) {
      return [
        { name: "date", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 1590, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 2560 && screenWidth < 3000) {
      return [
        { name: "date", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 1955, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 3000 && screenWidth < 3840) {
      return [
        { name: "date", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 2395, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 3840 && screenWidth < 4000) {
      return [
        { name: "date", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userLogin", title: "Учетная запись", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "userIp", title: "IP", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "action", title: "Действие", width: 3225, isSearch: true, position: "left" },
      ];
    }
    return [
      { name: "date", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
      { name: "userLogin", title: "Учетная запись", width: 200, isSearch: true, isSort: "alphabet" },
      { name: "userIp", title: "IP", width: 200, isSearch: true, isSort: "alphabet" },
      { name: "action", title: "Действие", width: 1290, isSearch: true, position: "left" },
    ];
  };

  defaultColumns = getDefaultColumns();

  const [columns, setColumns] = useState<any>([]);

  useEffect(() => {
    tableStore.getTableParams("49").then((data: any) => {
      if (data) {
        setColumns(data);
      } else {
        setColumns(defaultColumns);
      }
    });
  }, []);

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("49").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "date", direction: "desc" }]);
      }
    });
  }, []);

  return (
    <Container>
      <ContentTable>
        {usersJournal.length > 0 ? (
          <Table
            columns={columns}
            setColumns={setColumns}
            tableData={prepareDataTable(usersJournal)}
            originalTableData={usersJournalOriginal}
            isLoading={isLoadingUsersPage}
            tableKey={"49"}
            defaultColumns={defaultColumns}
            columnOrder={columnOrder}
            setColumnOrder={setColumnOrder}
            initSorting={initSorting}
            customCells={[
              {
                name: "date",
                render: (value: any) => {
                  return <Label>{prepareDateTable(value)}</Label>;
                },
              },
            ]}
          />
        ) : (
          <NoData />
        )}
      </ContentTable>
      <BottomContainer>
        <BottomCell>
          {isOverfilled && (
            <InformationContainer>
              <InfoIcon width={12} name="information" />
              <>Внимание выведены не все. Записей более 5000.</>
            </InformationContainer>
          )}
        </BottomCell>
      </BottomContainer>
    </Container>
  );
});
