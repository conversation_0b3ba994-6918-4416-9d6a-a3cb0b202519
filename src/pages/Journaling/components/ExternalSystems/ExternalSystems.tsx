import { FC, useEffect, useMemo, useState } from "react";
import styled, { css } from "styled-components";
import { Table } from "components/Table";
import { Pagination } from "components/Pagination";
import { filterTreeManyKeys } from "helpers/SearchUtils";
import { observer } from "mobx-react";
import { useStores } from "stores/useStore";
import { NoData } from "components/NoData";
import { Icon } from "components/Icon";
import { prepareDateTable } from "../../../../helpers/DateUtils";
import { Label } from "../../../../components/Label";
import { prepareDataTable } from "../../../../utils";
import { getWidthModal } from "../../../../helpers/adaptive";

export const BottomContainer = styled.div`
  display: flex;
`;

export const BottomCell = styled.div`
  width: 50%;
`;

export const MessageLabel = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  color: ${(p) => p.theme.textColor};
`;

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  //height: 100%;
  width: 100%;
  color: ${(p) => p.theme.textColor};
  //margin-top: 10px;
  //box-shadow: 0 12px 16px -4px rgb(16 24 40 / 10%), 0px 4px 6px -2px rgb(16 24 40 / 5%);
  //overflow: hidden;
  //background-color: ${(p) => p.theme.backgroundColorSecondary};
`;

export const InformationContainer = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 20px;
  font-size: 12px;
  cursor: default;
  user-select: none;
  height: 100%;
`;

export const Status = styled.div<{ status?: string }>`
  padding: 2px;
  width: 60px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  ${(p) =>
    p.status === "УСПЕШНО" &&
    css`
      background-color: ${(p) => p.theme.greenActiveSupport};
      color: ${(p) => p.theme.white};
    `}
  ${(p) =>
    p.status === "ОШИБКА" &&
    css`
      background-color: ${(p) => p.theme.redActiveSupport};
      color: ${(p) => p.theme.white};
    `}
`;

export const InfoIcon = styled(Icon)`
  color: ${(p) => p.theme.blueActiveSupport};
  margin-right: 5px;
`;

export const ContentTable = styled.div`
  height: calc(100vh - 40px);
  @media (min-height: 520px) and (max-height: 800px) {
    height: calc(100vh - 78px);
  }
  @media (min-height: 800px) and (max-height: 900px) {
    height: calc(100vh - 74px);
  }
`;

export const MessageContainer = styled.div`
  width: 100%;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: normal;
`;

export const ExternalSystems: FC<any> = observer((props) => {
  const { params } = props;
  const { userActionsStore, authStore, tableStore } = useStores();
  const { isLoadingExternalSystems, externalSystems, isOverfilled, externalSystemsOriginal } = userActionsStore;
  const { isCenter } = authStore;

  useEffect(() => {
    userActionsStore.initExternalSystems(params);
  }, [params, isCenter]);

  let defaultColumns: any;

  const [columnOrder, setColumnOrder] = useState<any>([]);

  const setDefaultColumns = () => {
    setColumns(() => {
      return columnOrder.map((el: any) => {
        return getDefaultColumns().find((item) => item.name === el);
      });
    });
  };
  const [initScreenWidth, setInitScreenWidth] = useState<any>(getWidthModal(document.documentElement.clientWidth));
  const [screenWidth, setScreenWidth] = useState<any>(initScreenWidth);
  const [isEditWidth, setEditWidth] = useState(false);

  const resizeWeb = () => {
    setScreenWidth(getWidthModal(document.documentElement.clientWidth));
  };

  useEffect(() => {
    window.addEventListener("resize", resizeWeb);
    return () => {
      window.removeEventListener("resize", resizeWeb);
    };
  }, []);

  useEffect(() => {
    if (initScreenWidth !== screenWidth && !isEditWidth) {
      setEditWidth(true);
      setDefaultColumns();
    } else if (initScreenWidth !== screenWidth && isEditWidth) {
      setDefaultColumns();
    } else if (initScreenWidth === screenWidth && isEditWidth) {
      setDefaultColumns();
    }
  }, [screenWidth]);

  const getDefaultColumns = () => {
    if (screenWidth >= 650 && screenWidth < 700) {
      return [
        { name: "createdAt", title: "Дата и время", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 160, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 700 && screenWidth < 800) {
      return [
        { name: "createdAt", title: "Дата и время", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 160, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 800 && screenWidth < 900) {
      return [
        { name: "createdAt", title: "Дата и время", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 160, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 900 && screenWidth < 1024) {
      return [
        { name: "createdAt", title: "Дата и время", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 160, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 1024 && screenWidth < 1152) {
      return [
        { name: "createdAt", title: "Дата и время", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 215, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 1152 && screenWidth < 1280) {
      return [
        { name: "createdAt", title: "Дата и время", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 345, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 1280 && screenWidth < 1400) {
      return [
        { name: "createdAt", title: "Дата и время", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 475, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 1400 && screenWidth < 1600) {
      return [
        { name: "createdAt", title: "Дата и время", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 160, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 595, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 1600 && screenWidth < 1700) {
      return [
        { name: "createdAt", title: "Дата и время", width: 168, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 168, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 168, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 168, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 168, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 755, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 1700 && screenWidth < 1800) {
      return [
        { name: "createdAt", title: "Дата и время", width: 180, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 180, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 180, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 180, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 180, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 795, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 1800 && screenWidth < 1900) {
      return [
        { name: "createdAt", title: "Дата и время", width: 190, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 190, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 190, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 190, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 190, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 845, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 1900 && screenWidth < 2000) {
      return [
        { name: "createdAt", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 260, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 830, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 2000 && screenWidth < 2100) {
      return [
        { name: "createdAt", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 260, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 930, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 2100 && screenWidth < 2200) {
      return [
        { name: "createdAt", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 260, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 1030, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 2200 && screenWidth < 2560) {
      return [
        { name: "createdAt", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 260, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 1130, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 2560 && screenWidth < 3000) {
      return [
        { name: "createdAt", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 260, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 1490, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 3000 && screenWidth < 3840) {
      return [
        { name: "createdAt", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 260, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 1935, isSearch: true, position: "left" },
      ];
    }
    if (screenWidth >= 3840 && screenWidth < 4000) {
      return [
        { name: "createdAt", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "system", title: "Система", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "interactionKind", title: "Вид сообщения", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "initiator", title: "Инициатор", width: 260, isSearch: true, isSort: "alphabet" },
        { name: "status", title: "Статус", width: 200, isSearch: true, isSort: "alphabet" },
        { name: "message", title: "Сообщение", width: 2770, isSearch: true, position: "left" },
      ];
    }
    return [
      { name: "createdAt", title: "Дата и время", width: 200, isSearch: true, isSort: "alphabet" },
      { name: "system", title: "Система", width: 200, isSearch: true, isSort: "alphabet" },
      { name: "interactionKind", title: "Вид сообщения", width: 200, isSearch: true, isSort: "alphabet" },
      { name: "initiator", title: "Инициатор", width: 260, isSearch: true, isSort: "alphabet" },
      { name: "status", title: "Статус", width: 200, isSearch: true, isSort: "alphabet" },
      { name: "message", title: "Сообщение", width: 830, isSearch: true, position: "left" },
    ];
  };

  defaultColumns = getDefaultColumns();

  const [columns, setColumns] = useState<any>([]);

  const customCell = [
    {
      name: "status",
      render: (value: any) => {
        return <Status status={value.toUpperCase()}>{value}</Status>;
      },
    },
    {
      name: "createdAt",
      render: (value: any) => {
        return <Label>{prepareDateTable(value)}</Label>;
      },
    },
  ];

  useEffect(() => {
    tableStore.getTableParams("48").then((data: any) => {
      if (data) {
        setColumns(data);
      } else {
        setColumns(defaultColumns);
      }
    });
  }, []);

  const [initSorting, setInitSorting] = useState<any>(null);

  useEffect(() => {
    tableStore.getSortParams("48").then((data: any) => {
      if (data) {
        setInitSorting(data);
      } else {
        setInitSorting([{ columnName: "createdAt", direction: "desc" }]);
      }
    });
  }, []);

  return (
    <Container>
      <ContentTable>
        {externalSystems.length > 0 ? (
          <Table
            columns={columns}
            setColumns={setColumns}
            tableData={prepareDataTable(externalSystems)}
            isLoading={isLoadingExternalSystems}
            tableKey={"48"}
            defaultColumns={defaultColumns}
            columnOrder={columnOrder}
            setColumnOrder={setColumnOrder}
            // setSearchParams={setSearchParams}
            customCells={customCell}
            originalTableData={externalSystemsOriginal}
            initSorting={initSorting}
          />
        ) : (
          <NoData />
        )}
      </ContentTable>
      <BottomContainer>
        <BottomCell>
          {isOverfilled && (
            <InformationContainer>
              <InfoIcon width={12} name="information" />
              <>Внимание выведены не все. Записей более 5000.</>
            </InformationContainer>
          )}
        </BottomCell>
      </BottomContainer>
    </Container>
  );
});
