import React, { createRef, FC, useEffect, useRef, useState } from "react";
import { isModeCenter } from "utils/getMode";
import { useLocation, useNavigate } from "react-router-dom";
import { prepareDate } from "helpers/DateUtils";
import queryString from "query-string";
import { ExternalSystems } from "./components/ExternalSystems";
import { UserActions } from "./components/UserActions";
import { Container, FilterDatePickerStyled, ActionBar, ButtonsGroupStyled, Excel } from "./Journaling.styles";
import { Icon } from "components/Icon";
import { useStores } from "../../stores/useStore";
import { DateRangePicker } from "../../components/DateRangePicker";
import styled, { css } from "styled-components";
import { useOnClickOutside } from "../../hooks/useOnClickOutside";
import { observer } from "mobx-react";

export const SelectedDateContainer = styled.div`
  width: 200px;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 10px;
  border: solid 1px ${(p) => p.theme.lightGray};
  cursor: pointer;
  border-radius: 6px;
`;

export const Arrow = styled.div<{ isOpen?: boolean }>`
  margin-left: auto;
  margin-right: 5px;
  transform: rotate(90deg);
  color: ${(p) => p.theme.gray};

  ${(p) =>
    p.isOpen &&
    css`
      transform: rotate(270deg);
    `}
`;

export const CalendarIcon = styled(Icon)`
  margin: 0 5px;
  color: ${(p) => p.theme.primaryColor};
`;

export const Content = styled.div`
  //height: 908px;
`;

export const DataLabel = styled.div`
  white-space: nowrap;
`;

export const Journaling: FC = observer(() => {
  const location = useLocation();
  const history = useNavigate();
  const { userActionsStore, liveTimerStore } = useStores();
  const buttons = [
    { value: "externalSystems", label: "Журнал взаимодействия с внешними системами" },
    { value: "userActions", label: "Журнал действий пользователей" },
  ];
  // const { DEFAULT_DAY, DEFAULT_MONTH, DEFAULT_YEAR } = liveTimerStore;

  const {viewPage = buttons[0].value }: any = queryString.parse(location.search);

  useEffect(() => {
    return () => {
      localStorage.removeItem(`table-sort-48`);
      localStorage.removeItem(`table-sort-49`);
      localStorage.removeItem(`fromDate`);
      localStorage.removeItem(`toDate`);
    };
  }, []);

  const fromDate = JSON.parse(localStorage.getItem('fromDate')as string) ?? undefined
  const toDate = JSON.parse(localStorage.getItem('toDate')as string) ?? undefined

  const initParams = {
    fromDate: fromDate !== undefined ? new Date(fromDate) : new Date(),
    toDate: toDate !== undefined ? new Date(toDate) : new Date(),
  };

  const [params, setParams] = useState<any>(initParams);

  const handleChangeDate = (value: any, context: any) => {
    if (context.validationError.some((item: any) => item)) return;

    const [from, to] = value;
    setParams((prev: any) => ({
      ...prev,
      fromDate: from,
      toDate: to,
    }));

    localStorage.setItem('fromDate',JSON.stringify(from))
    localStorage.setItem('toDate',JSON.stringify(to))
  };

  return (
    <Container>
      <ActionBar>
        <DateRangePicker dateFrom={params.fromDate} dateTo={params.toDate} handleChangeDate={handleChangeDate} />
        <Excel onClick={() => userActionsStore.exportXLS(viewPage, params.fromDate, params.toDate)}>
          <Icon width={14} name="excel" />
          <>Выгрузка в EXCEL</>
        </Excel>
        <ButtonsGroupStyled
          items={buttons}
          widthButton={400}
          selectedValue={viewPage}
          onClick={(value) => {
            localStorage.removeItem(`fromDate`);
            localStorage.removeItem(`toDate`);
            setParams((prev: any) => ({
              ...prev,
              fromDate: new Date(),
              toDate: new Date(),
            }));
            history(`?viewPage=${value}`);
          }}
        />
      </ActionBar>
      <Content>{viewPage === "externalSystems" ? <ExternalSystems params={params} /> : <UserActions params={params} />}</Content>
    </Container>
  );
});