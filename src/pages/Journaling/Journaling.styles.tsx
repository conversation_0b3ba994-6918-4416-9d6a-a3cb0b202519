import styled from "styled-components";
import { Combobox } from "components/Combobox";
import { ButtonsGroup } from "components/ButtonsGroup";
import { FilterDatePicker } from "components/FilterDatePicker";

export const Container = styled.div`
  width: 100%;
  height: 100%;
`;

export const ComboboxStyled = styled(Combobox)`
  margin-left: 10px;
`;

export const ButtonsGroupStyled = styled(ButtonsGroup)`
  margin-left: auto;
  margin-right: 10px;
`;

export const FilterDatePickerStyled = styled(FilterDatePicker)`
  margin-left: 10px;
`;

export const ActionBar = styled.div`
  width: 100%;
  height: 24px; //60
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  box-shadow: 0 8px 8px rgba(50, 50, 71, 0.08), 0 8px 16px rgba(50, 50, 71, 0.06);
  border-radius: 6px;
  display: flex;
  align-items: center;
  padding: 15px 20px;
  & > div:first-child {
    padding: 0 !important;
  }
`;

export const Excel = styled.div`
  color: ${(p) => p.theme.colorExcel};
  margin: 0 10px;
  cursor: pointer;
  display: flex;
  padding: 8px;
  border-radius: 8px;
  user-select: none;
  transition: all 0.3s;
  &:hover {
    background-color: ${(p) => p.theme.lightGray};
  }
  &:active {
    background-color: ${(p) => p.theme.gray};
  }
`;
