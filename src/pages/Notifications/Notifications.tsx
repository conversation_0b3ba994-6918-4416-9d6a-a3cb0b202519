import React, { useEffect, useState } from "react";
import { Table } from "components/Table";
import { Checkbox } from "components/Checkbox";
import { useStores } from "stores/useStore";
import { observer } from "mobx-react";
import { Container, TableContainer, Buttons, ButtonStyled } from "./Notifications.style";
import { prepareDataTable } from "../../utils";
import { RowNotificationItem } from "~/stores/NotificationsStore/NotificationsStore";

const TABLE_KEY = "notifications";

interface ColumnConfig {
  name: string;
  title: string;
  width: number;
}

const COLUMN_NAMES = ["name", "email"] as const;
type ColumnName = typeof COLUMN_NAMES[number];

export const Notifications = observer(() => {
  const {
    tableStore,
    notificationStore,
    authStore: { isCenter },
    notificationsStore: { hasChanges, notifications, getNotifications, resetChanges, saveData, toggleNotification },
  } = useStores();

  const defaultColumns = [
    {
      name: "name",
      title: "Название",
      width: 1500,
    },
    { name: "email", title: "Email", width: 400 },
  ];

  const [columns, setColumns] = useState<ColumnConfig[]>([]);
  const [columnOrder, setColumnOrder] = useState<ColumnName[]>([]);

  useEffect(() => {
    tableStore.getTableParams(TABLE_KEY).then((data: ColumnConfig[] | null) => {
      if (data) {
        setColumns(data);
      } else {
        setColumns(defaultColumns);
      }
    });
  }, []);

  useEffect(() => {
    getNotifications();
  }, [isCenter]);

  const customCells = [
    {
      name: "name",
      render: (value: string, row: RowNotificationItem) => (
        <div
          style={{
            fontWeight: row.isGroup ? "bold" : "normal",
            width: "100%", // Важно, иначе не сработает выравнивание textAlign
            textAlign: row.isGroup ? "center" : "left",
            paddingLeft: row.isGroup ? 0 : 20,
          }}
        >
          {value}
        </div>
      ),
    },
    {
      name: "email",
      render: (value: boolean | null, row: RowNotificationItem) =>
        row.isGroup ? null : <Checkbox onChange={() => toggleNotification(row.tabId)} status={value ?? false} />,
    },
  ];

  const handleSave = async () => {
    const success = await saveData();

    if (success) {
      notificationStore.addNotification({
        title: "Сохранение",
        description: "Данные сохранены",
        icon: "save",
        type: "information",
      });

      getNotifications();
    }
  };

  return (
    <Container>
      <TableContainer>
        <Table
          columns={columns}
          setColumns={setColumns}
          tableData={prepareDataTable(notifications)}
          isLoading={false}
          tableKey={TABLE_KEY}
          customCells={customCells}
          defaultColumns={defaultColumns}
          disabledSearches={["name", "email"]}
          columnOrder={columnOrder}
          setColumnOrder={setColumnOrder}
          headerComponents={
            <Buttons>
              <ButtonStyled title="Сохранить" disabled={!hasChanges} onClick={handleSave} />
              <ButtonStyled title="Отменить" disabled={!hasChanges} onClick={resetChanges} />
            </Buttons>
          }
        />
      </TableContainer>
    </Container>
  );
});
