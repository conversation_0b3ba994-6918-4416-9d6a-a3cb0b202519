import styled, { css } from "styled-components";
import { Button } from "components/Button";

export const Container = styled.div`
  width: 100%;
  height: 100%;
  background-color: ${(p) => p.theme.backgroundColorSecondary};
  border-radius: 6px;
  color: ${(p) => p.theme.textColor};
  box-shadow: 0 12px 16px -4px rgb(16 24 40 / 10%), 0px 4px 6px -2px rgb(16 24 40 / 5%);
`;

export const Buttons = styled.div`
  display: flex;
  margin-left: auto;
  margin-right: 10px;
`;

export const ButtonStyled = styled(Button)`
  margin: 0 10px;
  width: 160px;
`;

export const TableContainer = styled.div`
  height: 100%;
`;
