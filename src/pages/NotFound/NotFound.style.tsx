import { Button } from "components/Button";
import styled from "styled-components";

export const Container = styled.div`
  width: 400px;
`;

export const Page = styled.div`
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: ${(p) => p.theme.backgroundColor};
`;

export const Number = styled.div`
  font-size: 220px;
  letter-spacing: 15px;
  color: ${(p) => p.theme.blueActiveSupport};
`;

export const HR = styled.div`
  height: 10px;
  width: 100%;
  background-color: ${(p) => p.theme.blueActiveSupport};
`;

export const Title = styled.div`
  text-align: center;
  display: block;
  position: relative;
  letter-spacing: 12px;
  font-size: 4em;
  line-height: 80%;
  color: ${(p) => p.theme.blueActiveSupport};
`;

export const SubTitle = styled.div`
  text-align: center;
  display: block;
  position: relative;
  font-size: 20px;
  color: ${(p) => p.theme.blueActiveSupport};
`;

export const ButtonContainer = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const ButtonStyled = styled(Button)`
  width: 100%;
  margin-top: 15px;
`;
