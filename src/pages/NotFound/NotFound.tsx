import React from "react";
import { useNavigate } from "react-router-dom";
import { Container, Button<PERSON><PERSON>r, Page, Number, ButtonStyled, HR, SubTitle, Title } from "./NotFound.style";

export const NotFound = () => {
  let history = useNavigate();

  return (
    <Page>
      <Container>
        <Number>404</Number>
        <HR />
        <Title>Страница</Title>
        <SubTitle>не найдена</SubTitle>
        <ButtonContainer>
          <ButtonStyled
            title="Вернуться на главную"
            onClick={() => {
              history(`/`);
            }}
          />
        </ButtonContainer>
      </Container>
    </Page>
  );
};
