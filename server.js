// server.js (без express)
const http = require("http");
const httpProxy = require("http-proxy");
const fs = require("fs");
const path = require("path");

const STAND = process.env.STAND || "main";
const MODE = process.env.MODE || "center";

const servers = {
  main: {
    center: "https://srpg-center-dev.ic.ntcees.ln",
    dc: "https://srpg-dc-dev.ic.ntcees.ln",
  },
  east: {
    center: "https://srpg-east-center-dev.ic.ntcees.ln",
    dc: "https://srpg-east-dc-dev.ic.ntcees.ln",
  },
};

const target = servers[STAND][MODE];
const proxy = httpProxy.createProxyServer({ changeOrigin: true, secure: false });

const buildPath = path.resolve(__dirname, "build/build-center"); // ⚠️ поменяй на нужный build

const server = http.createServer((req, res) => {
  // Проксируем API
  if (req.url.startsWith("/srpg-center") || req.url.startsWith("/srpg-control") || req.url.startsWith("/configuration")) {
    proxy.web(req, res, { target });
    return;
  }

  // Раздаём статику
  let filePath = path.join(buildPath, req.url === "/" ? "index.html" : req.url);

  fs.readFile(filePath, (err, data) => {
    if (err) {
      // SPA fallback → index.html
      fs.readFile(path.join(buildPath, "index.html"), (err2, data2) => {
        if (err2) {
          res.writeHead(404);
          res.end("Not found");
        } else {
          res.writeHead(200, { "Content-Type": "text/html" });
          res.end(data2);
        }
      });
    } else {
      // Определяем Content-Type по расширению
      let ext = path.extname(filePath).toLowerCase();
      let contentType = "text/plain";
      if (ext === ".html") contentType = "text/html";
      if (ext === ".js") contentType = "application/javascript";
      if (ext === ".css") contentType = "text/css";
      if (ext === ".json") contentType = "application/json";
      if (ext === ".png") contentType = "image/png";
      if (ext === ".jpg" || ext === ".jpeg") contentType = "image/jpeg";
      if (ext === ".svg") contentType = "image/svg+xml";

      res.writeHead(200, { "Content-Type": contentType });
      res.end(data);
    }
  });
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  console.log(`✅ Running on http://localhost:${PORT} (STAND=${STAND}, MODE=${MODE}, target=${target})`);
});
