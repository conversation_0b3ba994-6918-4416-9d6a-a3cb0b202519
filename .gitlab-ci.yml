include:
#  - 'https://gitlab.com/api/v4/projects/18865583/repository/files/SRPGFrontend%2Eyml/raw?ref=master&private_token=xYEXr6UAvZyNPG1SJPyz&.yaml'
  - "${CI_WORKER_URL}/SRPGFrontend%2Eyml/${CI_WORKER_URL_END}"
  - "${CI_WORKER_URL}/SRPGFrontend_SonarQube%2Eyml/${CI_WORKER_URL_END}"
  - "${CI_WORKER_URL}/vkteams_bot_onfailure%2Eyml/${CI_WORKER_URL_END}"
# You can override the included template(s) by including variable overrides
# SAST customization: https://docs.gitlab.com/ee/user/application_security/sast/#customizing-the-sast-settings
# Secret Detection customization: https://docs.gitlab.com/ee/user/application_security/secret_detection/#customizing-settings
# Dependency Scanning customization: https://docs.gitlab.com/ee/user/application_security/dependency_scanning/#customizing-the-dependency-scanning-settings
# Note that environment variables can be set in several places
# See https://docs.gitlab.com/ee/ci/variables/#cicd-variable-precedence
#stages:
#- test
#sast:
#  stage: test
#include:
#- template: Security/SAST.gitlab-ci.yml
