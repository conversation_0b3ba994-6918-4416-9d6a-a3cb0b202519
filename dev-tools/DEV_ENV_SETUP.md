# Настройка DEV окружения

## Автоматическая смена версии Node.js с помощью FNM

В данном руководстве описаны шаги для автоматической смены версии Node.js при переходе в папку проекта с использованием утилиты [fnm (Fast Node Manager)](https://github.com/Schniz/fnm).

### Установка FNM

Если у вас еще не установлен FNM, выполните следующие шаги для его установки:

- **Используя скрипт (macOS/Linux)**

  ```bash
  curl -fsSL https://fnm.vercel.app/install | bash
  ```

- **Используя Homebrew (macOS/Linux)**

  ```bash
  brew install fnm
  ```

- **Используя Winget (Windows)**

  ```bash
  winget install Schniz.fnm
  ```

- **Используя Scoop (Windows)**

  ```bash
  scoop install fnm
  ```

- **Используя Chocolatey (Windows)**

  ```bash
  choco install fnm
  ```

### Настройка автоматической смены версии Node.js

#### 1. Файл `.nvmrc`

В корне проекта находится файл `.nvmrc`, в котором будет указана версия Node.js, рекомендуемая для использования на данном проекте. Например:

```bash
❯ cat ./front/.nvmrc
v12.22.12
```

#### 2. Настройка автоматического переключения версии Node.js с помощью оболочки

FNM автоматически переключает версию Node.js при переходе в директорию с проектом, в котором есть файл `.nvmrc`. Для этого необходимо добавить команду инициализации FNM в файл конфигурации вашей оболочки.

- **Для `bash`**

Добавьте следующую строку в файл `~/.bashrc`:

```bash
eval "$(fnm env --use-on-cd --shell bash)"
```

- **Для `zsh`**

Добавьте следующую строку в файл `~/.zshrc`:

```bash
eval "$(fnm env --use-on-cd --shell zsh)"
```

- **Для `fish`**

Создайте файл `~/.config/fish/config.fish` и добавьте следующую строку:

```bash
fnm env --use-on-cd --shell fish | source
```

- **Для `PowerShell`**

Добавьте следующую строку в конец файла с вашим профилем:

```bash
fnm env --use-on-cd --shell power-shell | Out-String | Invoke-Expression
```

- Для macOS/Linux, файл с профилем расположен
  
  ```bash
  ~/.config/powershell/Microsoft.PowerShell_profile.ps1
  ```

- На Windows отредактировать файл с профилем можно через PowerShell командой

  ```bash
  notepad $profile
  ```

#### 3. Проверка работоспособности

При переходе в папку проекта с файлом `.nvmrc`, FNM автоматически будет переключать версию Node.js. Пример:

```bash
❯ cd ../front
Using Node v12.22.12
❯ cd ../neptune-web
Using Node v18.20.4
```

Если у вас не установлена версия Node.js, которая указана в файле `.nvmrc`, FNM предложит ее установить. Пример:

```bash
cd ./front
Can't find an installed Node version matching v12.22.12.
Do you want to install it? answer [y/N]: y
```

Чтобы убедиться, что всё работает, выполните следующую команду в терминале после перехода в папку проекта:

```bash
node -v
```

Версия Node.js должна соответствовать той, которая указана в файле `.nvmrc`

### Дополнительно

Рекомендуется добавить флаг `--version-file-strategy=recursive`. Это позволяет FNM рекурсивно искать файл с версией Node.js `.nvmrc` в родительских директориях. Это полезно в том случае, когда вы находитесь в подпапке проекта, но хотите, чтобы FNM автоматически нашёл файл с версией Node.js, указанный в более высоком уровне иерархии директорий. Например:

```bash
repo/
├── package.json
├── .nvmrc <- с версией Node.js: `12.22.12`
└── src/
  └── app/ <- Я здесь
    └── App.tsx
```

По аналогии выше, необходимо добавить строчку в конфигурационный файл вашей оболочки (`~/.bashrc,` `~/.zshrc` и др.):

```bash
eval "$(fnm env --use-on-cd --version-file-strategy=recursive)"
```
