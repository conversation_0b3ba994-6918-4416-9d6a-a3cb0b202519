const { merge } = require("webpack-merge");
const common = require("./webpack.common.js");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const CssMinimizerPlugin = require("css-minimizer-webpack-plugin");
const path = require("path");
const webpack = require("webpack");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");
const CaseSensitivePathsPlugin = require("case-sensitive-paths-webpack-plugin");
const Dotenv = require("dotenv-webpack");
const TerserPlugin = require("terser-webpack-plugin");
const CopyWebpackPlugin = require("copy-webpack-plugin");
const paths = require("./paths");

const getPath = () => {
  if (process.env.STAND === "main") {
    if (process.env.MODE === "center") {
      return path.resolve(__dirname, "../build/build-center");
    }
    if (process.env.MODE === "dc") {
      return path.resolve(__dirname, "../build/build-dc");
    }
  }
  if (process.env.STAND === "east") {
    if (process.env.MODE === "center") {
      return path.resolve(__dirname, "../build/build-east-center");
    }
    if (process.env.MODE === "dc") {
      return path.resolve(__dirname, "../build/build-east-dc");
    }
  }
};

module.exports = merge(common, {
  mode: "production",
  target: "web",
  devtool: "nosources-source-map",
  bail: true,
  entry: {
    app: ["./src/index"],
  },
  output: {
    path: getPath(),
    publicPath: "/",
    filename: "js/[name].[chunkhash:8].js",
    chunkFilename: "js/[name].[chunkhash:8].js",
  },
  plugins: [
    new Dotenv({
      systemvars: true,
    }),
    new CaseSensitivePathsPlugin(),
    new webpack.ProgressPlugin(),
    new CleanWebpackPlugin(),
    new CopyWebpackPlugin({
      patterns: [
        {
          from: paths.public, // Откуда: project_root/public/
          to: ".", // Куда: в корень output.path (определяемый getPath())
          globOptions: {
            ignore: ["**/index.html"],
          },
          noErrorOnMissing: true, // Не прерывать сборку, если в public нет файлов (кроме игнорируемых)
        },
      ],
    }),
    new webpack.DefinePlugin({
      "process.env.NODE_ENV": JSON.stringify(process.env.NODE_ENV),
    }),
    new MiniCssExtractPlugin({
      filename: "[name].css",
      chunkFilename: "[id].css",
    }),
    new HtmlWebpackPlugin({
      inject: true,
      template: path.resolve(paths.public, "index.html"),
      filename: "index.html",
      hash: true,
      minify: {
        removeComments: true,
        collapseWhitespace: true,
        removeRedundantAttributes: true,
        useShortDoctype: true,
        removeEmptyAttributes: true,
        removeStyleLinkTypeAttributes: true,
        keepClosingSlash: true,
        minifyJS: true,
        minifyCSS: true,
        minifyURLs: true,
      },
    }),
    // new TerserPlugin({
    //   minify: TerserPlugin.uglifyJsMinify,
    //   terserOptions: {
    //     compress: {
    //       drop_console: true,
    //     },
    //   },
    // }),
  ],
  module: {
    rules: [
      {
        test: /\.svg$/,
        exclude: /node_modules/,
        use: [
          { loader: "@svgr/webpack" },

          {
            loader: "file-loader",
            options: {
              outputPath: "images",
              name: "[hash].[ext]?[hash]",
            },
          },
        ],
      },
    ],
  },
  optimization: {
    minimize: true,
    minimizer: [new CssMinimizerPlugin()],
    runtimeChunk: {
      name: "runtime",
    },
    splitChunks: {
      chunks: "async",
      minSize: 20000,
      minRemainingSize: 0,
      minChunks: 1,
      maxAsyncRequests: 30,
      maxInitialRequests: 30,
      enforceSizeThreshold: 50000,
      cacheGroups: {
        defaultVendors: {
          test: /[\\/]node_modules[\\/]/,
          priority: -10,
          reuseExistingChunk: true,
        },
        default: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true,
        },
      },
    },
  },
  performance: {
    hints: false,
    maxEntrypointSize: 512000,
    maxAssetSize: 512000,
  },
});
