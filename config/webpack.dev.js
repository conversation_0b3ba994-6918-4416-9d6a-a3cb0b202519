const webpack = require("webpack");
const path = require("path");
const { merge } = require("webpack-merge");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const CaseSensitivePathsPlugin = require("case-sensitive-paths-webpack-plugin");

const Dotenv = require("dotenv-webpack");
const CopyWebpackPlugin = require("copy-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const { getProxy } = require("./proxy");

const paths = require("./paths");
const common = require("./webpack.common.js");

const getPort = () => {
  if (process.env.STAND === "main") {
    if (process.env.MODE === "center") {
      return 3000;
    }
    if (process.env.MODE === "dc") {
      return 3030;
    }
  }
  if (process.env.STAND === "east") {
    if (process.env.MODE === "center") {
      return 3060;
    }
    if (process.env.MODE === "dc") {
      return 3080;
    }
  }
};

module.exports = merge(common, {
  mode: "development",
  devtool: "cheap-module-source-map",
  entry: {
    app: ["./src/index"],
  },

  output: {
    path: paths.build,
    publicPath: "/",
  },

  // Spin up a server for quick development
  devServer: {
    historyApiFallback: true,
    static: {
      directory: paths.build,
    },
    open: false,
    compress: true,
    hot: true,
    port: getPort(),
    proxy: getProxy(process.env.server, process.env.STAND),
  },
  optimization: {
    splitChunks: {
      chunks: "async",
      minSize: 20000,
      minRemainingSize: 0,
      minChunks: 1,
      maxAsyncRequests: 30,
      maxInitialRequests: 30,
      enforceSizeThreshold: 50000,
      cacheGroups: {
        defaultVendors: {
          test: /[\\/]node_modules[\\/]/,
          priority: -10,
          reuseExistingChunk: true,
        },
        default: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true,
        },
      },
    },
  },

  plugins: [
    new Dotenv({
      systemvars: true,
    }),
    new CaseSensitivePathsPlugin(),
    new webpack.ProgressPlugin(),
    new CopyWebpackPlugin({
      patterns: [
        {
          from: paths.public, // Откуда: project_root/public/
          to: ".", // Куда: в корень output.path (т.е. project_root/build/)
          globOptions: {
            ignore: ["**/index.html"],
          },
          noErrorOnMissing: true, // Не падать, если в public нет файлов (кроме игнорируемых)
        },
      ],
    }),
    new webpack.DefinePlugin({
      "process.env.NODE_ENV": JSON.stringify(process.env.NODE_ENV),
    }),

    new MiniCssExtractPlugin({
      filename: "[name].css",
      chunkFilename: "[id].css",
    }),

    new HtmlWebpackPlugin({
      inject: true,
      title: "СРПГ",
      template: path.resolve(paths.public, "index.html"), // template file
      filename: "index.html", // output file
      hash: true,
    }),
  ],
});
