const servers = {
  center: "https://srpg-center-dev.ic.ntcees.ln", //https://srpg-center-dev.ic.ntcees.ln https://srpg-center-test.ic.ntcees.ln
  dc: "https://srpg-dc-dev.ic.ntcees.ln",
};

const serversEast = {
  center: "https://srpg-east-center-dev.ic.ntcees.ln",
  dc: "https://srpg-east-dc-dev.ic.ntcees.ln",
};

module.exports = {
  getProxy: (server, stand) => {
    const target = stand === "east" ? serversEast[server] : servers[server];
    return {
      "/srpg-center": {
        target,
        changeOrigin: true,
        secure: false,
        headers: {
          Connection: "keep-alive",
        },
      },
      "/srpg-control": {
        target,
        changeOrigin: true,
        secure: false,
        headers: {
          Connection: "keep-alive",
        },
      },
      "/configuration": {
        target,
        changeOrigin: true,
        secure: false,
        headers: {
          Connection: "keep-alive",
        },
      },
    };
  },

  getMessage: (server) => {
    return `The dev-server was started in the mode  ${server} - (${servers[server]})`;
  },
};
