const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const path = require("path");
const paths = require("./paths");

module.exports = {
  // Where webpack looks to start building the bundle
  entry: [paths.src + "/Checkbox.tsx"],

  // Where webpack outputs the assets and bundles
  output: {
    filename: "static/js/[name].[hash].js",
    chunkFilename: "static/js/[name].[chunkhash:8].chunk.js",
    publicPath: "/",
  },

  resolve: {
    extensions: [".ts", ".tsx", ".js", ".jsx"],
    // Need for development
    alias: {
      "~": path.resolve(__dirname, "../src/"),
      app: path.resolve(__dirname, "../src/"),
      // modules: path.resolve(__dirname, "../src/modules/"),
      components: path.resolve(__dirname, "../src/components/"),
      assets: path.resolve(__dirname, "../src/assets/"),
      stores: path.resolve(__dirname, "../src/stores/"),
      pages: path.resolve(__dirname, "../src/pages/"),
      hooks: path.resolve(__dirname, "../src/hooks/"),
      helpers: path.resolve(__dirname, "../src/helpers/"),
      api: path.resolve(__dirname, "../src/api/"),
      // layouts: path.resolve(__dirname, "../src/layouts/"),
      // selectors: path.resolve(__dirname, "../src/selectors/"),
      // constants: path.resolve(__dirname, "../src/constants/"),
      utils: path.resolve(__dirname, "../src/utils/"),
      routes: path.resolve(__dirname, "../src/routes/"),
      // "react-dom": "@hot-loader/react-dom",
    },
  },

  // Determine how modules within the project are treated
  module: {
    rules: [
      {
        test: /\.(ts|js)x?$/,
        exclude: /node_modules/,
        use: [{ loader: "babel-loader" }],
      },
      // LESS loader with customization
      {
        test: /\.(le|c)ss$/,
        use: [
          MiniCssExtractPlugin.loader,
          {
            loader: "css-loader",
          },
        ],
      },

      {
        test: /\.svg$/,
        exclude: /node_modules/,
        use: [{ loader: "@svgr/webpack" }, { loader: "url-loader" }],
      },
      // {
      //   test: /\.(woff2|ttf|woff|eot|otf)$/,
      //   exclude: /node_modules/,
      //   use: [
      //     {
      //       loader: "file-loader",
      //       options: {
      //         name: `[name].[ext]`,
      //         outputPath: "fonts",
      //       },
      //     },
      //   ],
      // },
      {
        test: /\.(jpe?g|png|gif)$/i,
        exclude: /node_modules/,
        use: [
          {
            loader: "file-loader",
            options: {
              outputPath: "images",
            },
          },
        ],
      },
    ],
  },
};
