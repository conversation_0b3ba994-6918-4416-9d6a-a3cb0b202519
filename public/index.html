<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="utf-8" />
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" /> -->
    <meta name="theme-color" content="#000000" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png">
    <!-- 180x180 - ставим первым для safari -->
    <link rel="icon" href="/favicon.ico" sizes="any"><!-- 32x32 -->
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <script src="./config.js" type="text/javascript"></script>
    <title>СРПГ</title>
</head>
<body>
<noscript> Для запуска этого приложения необходимо включить JavaScript. </noscript>
<div id="root"></div>
<!--
  This HTML file is a template.
  If you open it directly in the browser, you will see an empty page.

  You can add webfonts, meta tags, or analytics to this file.
  The build step will place the bundled scripts into the <body> tag.

  To begin the development, run `npm start` or `yarn start`.
  To create a production bundle, use `npm run build` or `yarn build`.
-->
</body>
</html>
