# neptune-web

## Установка и запуск проекта
- [ ] Установить [Node-js](https://nodejs.org/en)[В проекте используется верся 16.20.2]
- [ ] Выполните следующую команду чтобы установить все зависимости :
```
npm i
```
- [ ] Выполните следующую команду чтобы запустить проект со всеми стендами:
```
npm run start
```
- [ ] Выполните следующую команду чтобы запустить проект[Центр]:
```
npm run start:center
```
- [ ] Выполните следующую команду чтобы запустить проект[ДЦ]:
```
npm run start:dc
```
- [ ] Выполните следующую команду чтобы запустить проект[Центр-Восток]:
```
npm run start-east:center
```
- [ ] Выполните следующую команду чтобы запустить проект[ДЦ-Восток]:
```
npm run start-east:dc
```