{
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 2020,
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true
    }
  },
  "settings": {
    "react": {
      "version": "detect"
    }
  },
  "extends": [
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
//    "plugin:@typescript-eslint/recommended",
//    "prettier/@typescript-eslint",
//    "plugin:prettier/recommended"
  ],
  "rules": {
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    "no-debugger": "warn",
    "no-alert": "error",
    "no-dupe-keys": "error",
    "no-dupe-args": "error",
    "no-duplicate-case": "error",
    "react/react-in-jsx-scope": "off"
  }
}
